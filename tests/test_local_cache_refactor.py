#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试局部缓存重构的验证脚本
"""

import sys
import ast
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


def test_global_cache_removed():
    """测试全局缓存管理器是否已移除"""
    print("=" * 80)
    print("🗑️ 验证全局缓存管理器已移除")
    print("=" * 80)
    
    try:
        service_file = project_root / "src" / "domain" / "services" / "session_service.py"
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查全局缓存相关代码是否已移除
        removed_patterns = [
            "class SSEDeduplicationCache:",
            "self.sse_cache = SSEDeduplicationCache()",
            "def get_cache(self, session_id: str)",
            "def clear_cache(self, session_id: str)",
            "def cleanup_expired_caches(self)",
            "_caches: Dict[str, Dict[str, Any]]",
            "_cleanup_times: Dict[str, float]"
        ]
        
        found_patterns = []
        for pattern in removed_patterns:
            if pattern in content:
                found_patterns.append(pattern)
        
        if found_patterns:
            print(f"❌ 发现未移除的全局缓存代码: {found_patterns}")
            return False
        else:
            print("✅ 全局缓存管理器已完全移除")
        
        # 检查不需要的导入是否已移除
        removed_imports = [
            "import time",
            "import weakref"
        ]
        
        found_imports = []
        for import_stmt in removed_imports:
            if import_stmt in content:
                found_imports.append(import_stmt)
        
        if found_imports:
            print(f"❌ 发现未移除的导入: {found_imports}")
            return False
        else:
            print("✅ 不需要的导入已移除")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def test_local_cache_implementation():
    """测试局部缓存实现"""
    print(f"\n" + "=" * 80)
    print("📦 验证局部缓存实现")
    print("=" * 80)
    
    try:
        route_file = project_root / "src" / "presentation" / "api" / "routes" / "session_routes.py"
        with open(route_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查局部缓存实现
        local_cache_patterns = [
            "sent_event_ids = set()",  # 局部变量
            "本次连接缓存了",  # 日志说明是本次连接
            "仅与本次连接的历史消息去重",  # 注释说明局部性
            "局部缓存会在函数结束时自动清理"  # 注释说明自动清理
        ]
        
        for pattern in local_cache_patterns:
            if pattern in content:
                print(f"✅ 找到局部缓存实现: {pattern}")
            else:
                print(f"❌ 未找到局部缓存实现: {pattern}")
                return False
        
        # 检查全局缓存调用是否已移除
        removed_global_calls = [
            "session_service.sse_cache.get_cache",
            "session_service.sse_cache.clear_cache",
            "SSECache"
        ]
        
        found_global_calls = []
        for call in removed_global_calls:
            if call in content:
                found_global_calls.append(call)
        
        if found_global_calls:
            print(f"❌ 发现未移除的全局缓存调用: {found_global_calls}")
            return False
        else:
            print("✅ 全局缓存调用已完全移除")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def test_method_signature_update():
    """测试方法签名更新"""
    print(f"\n" + "=" * 80)
    print("📝 验证方法签名更新")
    print("=" * 80)
    
    try:
        service_file = project_root / "src" / "domain" / "services" / "session_service.py"
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查stream_history_messages方法签名更新
        if "AsyncGenerator[Tuple[Dict[str, Any], bool, str], None]" in content:
            print("✅ stream_history_messages返回类型已更新（包含event_id）")
        else:
            print("❌ stream_history_messages返回类型未正确更新")
            return False
        
        # 检查yield语句更新
        if "yield event_data, session_finished and is_last_in_batch and not events_result.get(\"has_more\", False), event_id" in content:
            print("✅ yield语句已更新（包含event_id）")
        else:
            print("❌ yield语句未正确更新")
            return False
        
        # 检查路由中的调用更新
        route_file = project_root / "src" / "presentation" / "api" / "routes" / "session_routes.py"
        with open(route_file, 'r', encoding='utf-8') as f:
            route_content = f.read()
        
        if "async for event_data, is_session_finished, event_id in session_service.stream_history_messages(" in route_content:
            print("✅ 路由中的方法调用已更新")
        else:
            print("❌ 路由中的方法调用未正确更新")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def test_memory_management():
    """测试内存管理改进"""
    print(f"\n" + "=" * 80)
    print("💾 验证内存管理改进")
    print("=" * 80)
    
    try:
        route_file = project_root / "src" / "presentation" / "api" / "routes" / "session_routes.py"
        with open(route_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查局部变量使用
        if "sent_event_ids = set()" in content:
            print("✅ 使用局部变量，避免全局状态")
        else:
            print("❌ 未使用局部变量")
            return False
        
        # 检查自动清理说明
        if "局部缓存会在函数结束时自动清理" in content:
            print("✅ 包含自动清理说明")
        else:
            print("❌ 缺少自动清理说明")
            return False
        
        # 检查是否移除了手动清理代码
        manual_cleanup_patterns = [
            "clear_cache(session_id)",
            "cleanup_expired_caches",
            "time.time() - history_send_time > 300"
        ]
        
        found_manual_cleanup = []
        for pattern in manual_cleanup_patterns:
            if pattern in content:
                found_manual_cleanup.append(pattern)
        
        if found_manual_cleanup:
            print(f"❌ 发现残留的手动清理代码: {found_manual_cleanup}")
            return False
        else:
            print("✅ 手动清理代码已移除")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def test_concurrent_connections():
    """测试并发连接隔离"""
    print(f"\n" + "=" * 80)
    print("🔀 验证并发连接隔离")
    print("=" * 80)
    
    try:
        route_file = project_root / "src" / "presentation" / "api" / "routes" / "session_routes.py"
        with open(route_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查局部缓存实现
        if "sent_event_ids = set()" in content:
            print("✅ 每个连接都有独立的缓存实例")
        else:
            print("❌ 缓存不是独立的")
            return False
        
        # 检查注释说明
        isolation_comments = [
            "局部去重缓存 - 仅在本次连接中有效",
            "仅与本次连接的历史消息去重"
        ]
        
        for comment in isolation_comments:
            if comment in content:
                print(f"✅ 找到隔离说明: {comment}")
            else:
                print(f"❌ 缺少隔离说明: {comment}")
                return False
        
        print("✅ 并发连接隔离验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def test_syntax_correctness():
    """测试语法正确性"""
    print(f"\n" + "=" * 80)
    print("🔧 验证语法正确性")
    print("=" * 80)
    
    try:
        # 检查session_service.py
        service_file = project_root / "src" / "domain" / "services" / "session_service.py"
        with open(service_file, 'r', encoding='utf-8') as f:
            service_content = f.read()
        
        try:
            ast.parse(service_content)
            print("✅ SessionService语法正确")
        except SyntaxError as e:
            print(f"❌ SessionService语法错误: {e}")
            return False
        
        # 检查session_routes.py
        route_file = project_root / "src" / "presentation" / "api" / "routes" / "session_routes.py"
        with open(route_file, 'r', encoding='utf-8') as f:
            route_content = f.read()
        
        try:
            ast.parse(route_content)
            print("✅ SessionRoutes语法正确")
        except SyntaxError as e:
            print(f"❌ SessionRoutes语法错误: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始验证局部缓存重构结果")
    print(f"📅 验证时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 执行各项验证
    results.append(("全局缓存移除", test_global_cache_removed()))
    results.append(("局部缓存实现", test_local_cache_implementation()))
    results.append(("方法签名更新", test_method_signature_update()))
    results.append(("内存管理改进", test_memory_management()))
    results.append(("并发连接隔离", test_concurrent_connections()))
    results.append(("语法正确性", test_syntax_correctness()))
    
    # 总结结果
    print(f"\n" + "=" * 80)
    print("📊 验证结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个验证通过")
    
    if passed == total:
        print("🎉 所有验证都通过了！局部缓存重构成功！")
        print("\n📋 重构总结:")
        print("  1. ✅ 移除了全局缓存管理器，避免不同连接间的状态共享")
        print("  2. ✅ 实现了局部缓存，每个SSE连接都有独立的去重缓存")
        print("  3. ✅ 自动内存管理，缓存在连接结束时自动清理")
        print("  4. ✅ 支持并发连接，不同页面打开同一会话不会互相干扰")
        print("  5. ✅ 保持了所有现有功能，只是改变了缓存的作用域")
        return True
    else:
        print("💔 部分验证失败，请检查上述错误信息")
        return False


if __name__ == "__main__":
    success = main()
    print(f"\n{'='*80}")
    print(f"🏁 验证完成，结果: {'成功' if success else '失败'}")
    print(f"{'='*80}")
    sys.exit(0 if success else 1)
