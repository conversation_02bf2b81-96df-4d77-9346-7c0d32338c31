#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT Service 测试
"""

import pytest
from unittest.mock import Mock, patch
from src.domain.services.ppt_service import PPTService, PPTServiceError
from src.application.ppt_api_models import GetPPTAuthCodeResponse


def test_get_ppt_auth_code():
    """测试获取PPT认证码"""
    # 创建服务实例
    service = PPTService()

    # 调用API
    result = service.get_ppt_auth_code(ali_uid=123456)

    # 检查结果
    assert result is not None
    assert hasattr(result, 'code')
    assert hasattr(result, 'time_expire')
    assert hasattr(result, 'api_key')
    assert hasattr(result, 'channel')

    # 检查类型
    assert isinstance(result, GetPPTAuthCodeResponse)
    assert isinstance(result.code, str)
    assert isinstance(result.time_expire, str)
    assert isinstance(result.api_key, str)
    assert isinstance(result.channel, str)


def test_get_aippt_token():
    """测试获取AIPPT token"""
    service = PPTService()

    token = service._get_aippt_token()

    assert token is not None
    assert isinstance(token, str)
    assert len(token) > 0


@patch('src.domain.services.ppt_service.get_aippt_client')
@patch.object(PPTService, '_get_memory')
def test_save_ppt_success(mock_get_memory, mock_get_aippt_client):
    """测试保存PPT成功场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息
    mock_ppt_info = {
        'name': '测试PPT演示文稿',
        'id': 'ppt_12345'
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock 导出任务
    mock_task_key = 'export_task_12345'
    mock_client.export_ppt.return_value = mock_task_key
    
    # Mock Memory SDK
    mock_memory = Mock()
    mock_memory.add_event.return_value = True
    mock_get_memory.return_value = mock_memory
    
    # Mock Redis token
    with patch('src.domain.services.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # Mock 轮询结果
        with patch.object(PPTService, '_poll_export_result') as mock_poll:
            mock_download_url = 'https://example.com/download/ppt/12345.pptx'
            mock_poll.return_value = mock_download_url
            
            # 执行测试
            service = PPTService()
            result = service.save_ppt(ppt_id="test_ppt_123", session_id="test_session_456")
            
            # 验证结果
            assert result == mock_download_url
            assert isinstance(result, str)
            assert result.startswith('https://')
            
            # 验证调用链
            mock_client.get_ppt_info.assert_called_once_with(ppt_id="test_ppt_123", token='mock_token_12345')
            mock_client.export_ppt.assert_called_once_with(ppt_id="test_ppt_123", token='mock_token_12345')
            mock_poll.assert_called_once_with(mock_task_key, 'mock_token_12345', timeout=60)
            
            # 验证制品事件添加
            mock_memory.add_event.assert_called_once()
            # 获取add_event的调用参数
            call_args = mock_memory.add_event.call_args
            artifact_event = call_args[0][0]  # 第一个参数
            session_id = call_args[0][1]      # 第二个参数
            
            # 验证制品事件参数
            assert session_id == "test_session_456"
            assert artifact_event.session_id == "test_session_456"
            assert artifact_event.artifact_type == "download_url"
            assert artifact_event.file_type == "ppt"
            assert artifact_event.file_name == "测试PPT演示文稿"
            assert artifact_event.content == mock_download_url
            assert artifact_event.description == "PPT作品下载链接"
            assert artifact_event.is_process_file == False


@patch('src.domain.services.ppt_service.get_aippt_client')
def test_save_ppt_export_timeout(mock_get_aippt_client):
    """测试保存PPT导出超时场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息
    mock_ppt_info = {'name': '测试PPT', 'id': 'ppt_12345'}
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock 导出任务
    mock_task_key = 'export_task_12345'
    mock_client.export_ppt.return_value = mock_task_key
    
    # Mock Redis token
    with patch('src.domain.services.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # Mock 轮询结果返回None（超时）
        with patch.object(PPTService, '_poll_export_result') as mock_poll:
            mock_poll.return_value = None  # 模拟超时
            
            # 执行测试并验证异常
            service = PPTService()
            with pytest.raises(PPTServiceError) as exc_info:
                service.save_ppt(ppt_id="test_ppt_123", session_id="test_session_456")
            
            # 验证异常信息
            assert "PPT导出超时" in str(exc_info.value)
            
            # 验证调用链
            mock_client.get_ppt_info.assert_called_once()
            mock_client.export_ppt.assert_called_once()
            mock_poll.assert_called_once()


@patch('src.domain.services.ppt_service.get_aippt_client')
@patch.object(PPTService, '_get_memory')
def test_save_ppt_memory_update_failure(mock_get_memory, mock_get_aippt_client):
    """测试保存PPT成功但制品更新失败的场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息和导出
    mock_client.get_ppt_info.return_value = {'name': '测试PPT'}
    mock_client.export_ppt.return_value = 'task_123'
    
    # Mock Memory SDK失败
    mock_memory = Mock()
    mock_memory.add_event.return_value = False  # 制品更新失败
    mock_get_memory.return_value = mock_memory
    
    # Mock Redis token
    with patch('src.domain.services.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token'
        mock_redis_client.return_value = mock_redis
        
        # Mock 轮询成功
        with patch.object(PPTService, '_poll_export_result') as mock_poll:
            mock_download_url = 'https://example.com/download/test.pptx'
            mock_poll.return_value = mock_download_url
            
            # 执行测试
            service = PPTService()
            result = service.save_ppt(ppt_id="test_ppt_123", session_id="test_session_456")
            
            # 验证PPT保存成功，但制品更新失败不影响主流程
            assert result == mock_download_url
            mock_memory.add_event.assert_called_once()


@patch('src.domain.services.ppt_service.get_aippt_client')
def test_save_ppt_client_error(mock_get_aippt_client):
    """测试保存PPT时客户端错误场景"""
    # Mock 客户端异常
    mock_client = Mock()
    mock_client.get_ppt_info.side_effect = Exception("网络错误")
    mock_get_aippt_client.return_value = mock_client
    
    # Mock Redis token
    with patch('src.domain.services.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试并验证异常
        service = PPTService()
        with pytest.raises(PPTServiceError) as exc_info:
            service.save_ppt(ppt_id="test_ppt_123", session_id="test_session_456")
        
        # 验证异常信息
        assert "保存PPT失败" in str(exc_info.value)
        assert "网络错误" in str(exc_info.value)


@patch.object(PPTService, '_get_memory')
def test_bind_ppt_to_session_success(mock_get_memory):
    """测试绑定PPT到会话成功场景"""
    # Mock Memory SDK
    mock_memory = Mock()
    mock_memory.add_event.return_value = True
    mock_get_memory.return_value = mock_memory
    
    # 执行测试
    service = PPTService()
    result = service.bind_ppt_to_session(
        session_id="test_session_123", 
        ppt_id="test_ppt_456"
    )
    
    # 验证调用
    mock_memory.add_event.assert_called_once()
    
    # 获取add_event的调用参数
    call_args = mock_memory.add_event.call_args
    custom_event = call_args[0][0]  # 第一个参数（CustomEvent对象）
    session_id = call_args[0][1]    # 第二个参数（session_id）
    
    # 验证CustomEvent参数
    assert session_id == "test_session_123"
    assert custom_event.session_id == "test_session_123"
    assert custom_event.name == "ppt_id"
    assert custom_event.content == "test_ppt_456"
    assert hasattr(custom_event, 'run_id')  # run_id应该存在
    assert len(custom_event.run_id) > 0     # run_id不应该为空
    
    # 验证没有返回值（方法没有return语句）
    assert result is None


@patch.object(PPTService, '_get_memory')
def test_bind_ppt_to_session_memory_failure(mock_get_memory):
    """测试绑定PPT到会话时Memory失败场景"""
    # Mock Memory SDK失败
    mock_memory = Mock()
    mock_memory.add_event.side_effect = Exception("Memory服务连接失败")
    mock_get_memory.return_value = mock_memory
    
    # 执行测试并验证异常
    service = PPTService()
    with pytest.raises(Exception) as exc_info:
        service.bind_ppt_to_session(
            session_id="test_session_123", 
            ppt_id="test_ppt_456"
        )
    
    # 验证异常信息
    assert "Memory服务连接失败" in str(exc_info.value)
    
    # 验证Memory被调用
    mock_memory.add_event.assert_called_once()


@patch.object(PPTService, '_get_memory')
def test_bind_ppt_to_session_multiple_calls(mock_get_memory):
    """测试多次绑定PPT到会话（验证run_id唯一性）"""
    # Mock Memory SDK
    mock_memory = Mock()
    mock_memory.add_event.return_value = True
    mock_get_memory.return_value = mock_memory
    
    # 执行多次绑定
    service = PPTService()
    service.bind_ppt_to_session("session_1", "ppt_1")
    service.bind_ppt_to_session("session_2", "ppt_2")
    service.bind_ppt_to_session("session_1", "ppt_3")  # 同一session绑定不同ppt
    
    # 验证调用次数
    assert mock_memory.add_event.call_count == 3
    
    # 获取所有调用的run_id，验证唯一性
    run_ids = []
    for call in mock_memory.add_event.call_args_list:
        custom_event = call[0][0]
        run_ids.append(custom_event.run_id)
    
    # 验证所有run_id都不相同
    assert len(run_ids) == 3
    assert len(set(run_ids)) == 3  # 去重后长度应该相同，说明都是唯一的
    
    # 验证最后一次调用的参数
    last_call_args = mock_memory.add_event.call_args
    last_custom_event = last_call_args[0][0]
    last_session_id = last_call_args[0][1]
    
    assert last_session_id == "session_1"
    assert last_custom_event.session_id == "session_1"
    assert last_custom_event.content == "ppt_3"


@patch('src.domain.services.ppt_service.get_aippt_client')
def test_get_ppt_thumbnail_success(mock_get_aippt_client):
    """测试获取PPT缩略图成功场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息（包含封面图URL）
    mock_ppt_info = {
        'id': 'ppt_12345',
        'name': '测试PPT演示文稿',
        'cover_url': 'https://example.com/covers/ppt_12345_cover.jpg'
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock Redis token
    with patch('src.domain.services.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试
        service = PPTService()
        result = service.get_ppt_thumbnail(ppt_id="test_ppt_123")
        
        # 验证结果
        assert result == 'https://example.com/covers/ppt_12345_cover.jpg'
        assert isinstance(result, str)
        assert result.startswith('https://')
        
        # 验证调用链
        mock_client.get_ppt_info.assert_called_once_with(ppt_id="test_ppt_123", token='mock_token_12345')


@patch('src.domain.services.ppt_service.get_aippt_client')
def test_get_ppt_thumbnail_no_cover_url(mock_get_aippt_client):
    """测试获取PPT缩略图时封面图URL不存在的场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息（不包含封面图URL）
    mock_ppt_info = {
        'id': 'ppt_12345',
        'name': '测试PPT演示文稿'
        # 注意：没有 cover_url 字段
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock Redis token
    with patch('src.domain.services.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试
        service = PPTService()
        result = service.get_ppt_thumbnail(ppt_id="test_ppt_123")
        
        # 验证结果（应该返回空字符串）
        assert result == ''
        assert isinstance(result, str)
        
        # 验证调用链
        mock_client.get_ppt_info.assert_called_once_with(ppt_id="test_ppt_123", token='mock_token_12345')


@patch('src.domain.services.ppt_service.get_aippt_client')
def test_get_ppt_thumbnail_empty_cover_url(mock_get_aippt_client):
    """测试获取PPT缩略图时封面图URL为空的场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息（封面图URL为空）
    mock_ppt_info = {
        'id': 'ppt_12345',
        'name': '测试PPT演示文稿',
        'cover_url': ''  # 空字符串
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock Redis token
    with patch('src.domain.services.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试
        service = PPTService()
        result = service.get_ppt_thumbnail(ppt_id="test_ppt_123")
        
        # 验证结果（应该返回空字符串）
        assert result == ''
        assert isinstance(result, str)
        
        # 验证调用链
        mock_client.get_ppt_info.assert_called_once()


@patch('src.domain.services.ppt_service.get_aippt_client')
def test_get_ppt_thumbnail_client_error(mock_get_aippt_client):
    """测试获取PPT缩略图时客户端错误场景"""
    # Mock 客户端异常
    mock_client = Mock()
    mock_client.get_ppt_info.side_effect = Exception("网络连接失败")
    mock_get_aippt_client.return_value = mock_client
    
    # Mock Redis token
    with patch('src.domain.services.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试并验证异常
        service = PPTService()
        with pytest.raises(PPTServiceError) as exc_info:
            service.get_ppt_thumbnail(ppt_id="test_ppt_123")
        
        # 验证异常信息
        assert "获取PPT缩略图失败" in str(exc_info.value)
        assert "网络连接失败" in str(exc_info.value)


@patch('src.domain.services.ppt_service.get_aippt_client')
def test_get_ppt_thumbnail_token_failure(mock_get_aippt_client):
    """测试获取PPT缩略图时token获取失败场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock Redis token失败（返回None）
    with patch('src.domain.services.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = None  # 缓存中没有token
        mock_redis_client.return_value = mock_redis
        
        # Mock token获取也失败
        with patch.object(PPTService, '_get_aippt_token') as mock_get_token:
            mock_get_token.side_effect = Exception("Token获取失败")
            
            # 执行测试并验证异常
            service = PPTService()
            with pytest.raises(PPTServiceError) as exc_info:
                service.get_ppt_thumbnail(ppt_id="test_ppt_123")
            
            # 验证异常信息
            assert "获取PPT缩略图失败" in str(exc_info.value)
            assert "Token获取失败" in str(exc_info.value)
