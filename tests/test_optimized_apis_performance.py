#!/usr/bin/env python3
"""
优化后API性能测试脚本
测试高优先级异步化API的性能表现
"""

import asyncio
import time
import httpx
import json
from typing import List, Dict, Any, Tuple
import statistics
from concurrent.futures import ThreadPoolExecutor

# 测试配置
BASE_URL = "http://localhost:8000"
CONCURRENT_REQUESTS = 10
REQUESTS_PER_API = 5

# 模拟认证头
TEST_HEADERS = {
    "Content-Type": "application/json",
    "Authorization": "Bearer test-token",
    "X-Ali-Uid": "123456789",
    "X-Wy-Id": "test-wy-id"
}

# 测试用例配置
TEST_CASES = [
    {
        "name": "会话发送消息",
        "method": "POST",
        "url": "/api/sessions/send",
        "data": {
            "Prompt": "测试消息，请简单回复",
            "AgentId": "test-agent",
            "SessionId": None,
            "DesktopId": None,
            "AuthCode": None,
            "Resources": []
        }
    },
    {
        "name": "会话历史查询",
        "method": "GET",
        "url": "/api/sessions/query",
        "params": {
            "session_id": "test-session-123",
            "page_size": 20,
            "next_token": None
        }
    },
    {
        "name": "会话列表查询",
        "method": "GET",
        "url": "/api/sessions/list",
        "params": {
            "page_size": 20,
            "next_token": None,
            "search_keyword": None
        }
    },
    {
        "name": "文件预签名上传",
        "method": "POST",
        "url": "/api/files/presigned-upload",
        "data": {
            "file_info": {
                "file_name": "test.txt",
                "file_size": 1024,
                "file_type": "text/plain"
            },
            "agent_id": "test-agent",
            "session_id": None,
            "file_type": "sessionFile"
        }
    },
    {
        "name": "会话文件列表",
        "method": "POST",
        "url": "/api/files/list",
        "data": {
            "session_id": "test-session-123",
            "artifact_types": ["sessionFile"],
            "max_results": 50,
            "next_token": None,
            "kb_id": None
        }
    }
]


async def send_request(client: httpx.AsyncClient, test_case: Dict[str, Any], request_id: int) -> Dict[str, Any]:
    """发送单个请求"""
    start_time = time.time()
    
    try:
        if test_case["method"] == "GET":
            response = await client.get(
                f"{BASE_URL}{test_case['url']}",
                params=test_case.get("params", {}),
                headers=TEST_HEADERS,
                timeout=30.0
            )
        else:  # POST
            response = await client.post(
                f"{BASE_URL}{test_case['url']}",
                json=test_case.get("data", {}),
                headers=TEST_HEADERS,
                timeout=30.0
            )
        
        end_time = time.time()
        duration = end_time - start_time
        
        return {
            "test_case": test_case["name"],
            "request_id": request_id,
            "status_code": response.status_code,
            "duration": duration,
            "success": response.status_code in [200, 201],
            "response_size": len(response.content),
            "error": None
        }
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        
        return {
            "test_case": test_case["name"],
            "request_id": request_id,
            "status_code": 0,
            "duration": duration,
            "success": False,
            "response_size": 0,
            "error": str(e)
        }


async def run_test_case(test_case: Dict[str, Any], concurrent_count: int, total_requests: int) -> List[Dict[str, Any]]:
    """运行单个测试用例"""
    print(f"🧪 测试 {test_case['name']}: {concurrent_count} 并发, {total_requests} 请求")
    
    async with httpx.AsyncClient() as client:
        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(concurrent_count)
        
        async def limited_request(request_id: int):
            async with semaphore:
                return await send_request(client, test_case, request_id)
        
        # 创建所有任务
        tasks = [limited_request(i) for i in range(total_requests)]
        
        # 执行所有任务
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "test_case": test_case["name"],
                    "request_id": i,
                    "status_code": 0,
                    "duration": 0,
                    "success": False,
                    "response_size": 0,
                    "error": str(result)
                })
            else:
                processed_results.append(result)
        
        total_duration = end_time - start_time
        print(f"   ✅ 完成，总耗时: {total_duration:.2f}s")
        
        return processed_results


def analyze_test_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析测试结果"""
    if not results:
        return {"error": "没有测试结果"}
    
    test_case_name = results[0]["test_case"]
    successful_results = [r for r in results if r["success"]]
    failed_results = [r for r in results if not r["success"]]
    
    if not successful_results:
        return {
            "test_case": test_case_name,
            "total_requests": len(results),
            "successful_requests": 0,
            "failed_requests": len(failed_results),
            "success_rate": 0.0,
            "error": "所有请求都失败了"
        }
    
    durations = [r["duration"] for r in successful_results]
    
    analysis = {
        "test_case": test_case_name,
        "total_requests": len(results),
        "successful_requests": len(successful_results),
        "failed_requests": len(failed_results),
        "success_rate": len(successful_results) / len(results) * 100,
        
        # 响应时间统计
        "avg_duration": statistics.mean(durations),
        "min_duration": min(durations),
        "max_duration": max(durations),
        "median_duration": statistics.median(durations),
        "p95_duration": sorted(durations)[int(len(durations) * 0.95)] if len(durations) > 1 else durations[0],
        
        # 错误统计
        "errors": [r["error"] for r in failed_results if r["error"]]
    }
    
    return analysis


def print_test_analysis(analysis: Dict[str, Any]):
    """打印单个测试分析结果"""
    print(f"\n📊 {analysis['test_case']} - 性能分析")
    print("-" * 50)
    
    print(f"总请求数: {analysis['total_requests']}")
    print(f"成功请求: {analysis['successful_requests']}")
    print(f"失败请求: {analysis['failed_requests']}")
    print(f"成功率: {analysis['success_rate']:.1f}%")
    
    if analysis['successful_requests'] > 0:
        print(f"平均响应时间: {analysis['avg_duration']:.3f}s")
        print(f"最快响应时间: {analysis['min_duration']:.3f}s")
        print(f"最慢响应时间: {analysis['max_duration']:.3f}s")
        print(f"95%响应时间: {analysis['p95_duration']:.3f}s")
        
        # 性能评级
        avg_duration = analysis['avg_duration']
        if avg_duration < 0.1:
            print(f"🚀 性能评级: 优秀 (< 100ms)")
        elif avg_duration < 0.5:
            print(f"✅ 性能评级: 良好 (< 500ms)")
        elif avg_duration < 1.0:
            print(f"⚠️  性能评级: 一般 (< 1s)")
        else:
            print(f"❌ 性能评级: 需要优化 (> 1s)")
    
    if analysis['failed_requests'] > 0:
        print(f"❌ 错误信息:")
        for error in set(analysis['errors']):
            count = analysis['errors'].count(error)
            print(f"  {error} (出现 {count} 次)")


async def main():
    """主函数"""
    print("🧪 优化后API性能测试开始")
    print(f"目标服务: {BASE_URL}")
    print(f"测试配置: {CONCURRENT_REQUESTS} 并发, 每个API {REQUESTS_PER_API} 请求")
    print("="*60)
    
    all_analyses = []
    
    # 运行所有测试用例
    for test_case in TEST_CASES:
        try:
            results = await run_test_case(test_case, CONCURRENT_REQUESTS, REQUESTS_PER_API)
            analysis = analyze_test_results(results)
            all_analyses.append(analysis)
            print_test_analysis(analysis)
        except Exception as e:
            print(f"❌ 测试 {test_case['name']} 失败: {e}")
    
    # 总体性能报告
    print("\n" + "="*60)
    print("📈 总体性能报告")
    print("="*60)
    
    successful_tests = [a for a in all_analyses if a.get('successful_requests', 0) > 0]
    
    if successful_tests:
        avg_durations = [a['avg_duration'] for a in successful_tests]
        overall_avg = statistics.mean(avg_durations)
        overall_success_rate = statistics.mean([a['success_rate'] for a in all_analyses])
        
        print(f"测试的API数量: {len(TEST_CASES)}")
        print(f"成功的API数量: {len(successful_tests)}")
        print(f"整体平均响应时间: {overall_avg:.3f}s")
        print(f"整体成功率: {overall_success_rate:.1f}%")
        
        # 性能排行榜
        print(f"\n🏆 性能排行榜 (按平均响应时间):")
        sorted_tests = sorted(successful_tests, key=lambda x: x['avg_duration'])
        for i, test in enumerate(sorted_tests, 1):
            print(f"  {i}. {test['test_case']}: {test['avg_duration']:.3f}s")
    
    print(f"\n✅ 性能测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
