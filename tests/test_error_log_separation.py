#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试错误日志分离功能
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.shared.logging.logger import setup_logger, RequestContext, logger


def test_error_log_separation():
    """测试错误日志分离功能"""
    print("=== 测试错误日志分离功能 ===")
    
    # 创建临时目录用于测试
    temp_dir = tempfile.mkdtemp()
    main_log_path = os.path.join(temp_dir, "application.log")
    error_log_path = os.path.join(temp_dir, "error.log")
    
    try:
        print(f"临时日志目录: {temp_dir}")
        print(f"主日志文件: {main_log_path}")
        print(f"错误日志文件: {error_log_path}")
        
        # 重新配置日志器
        setup_logger(
            level="DEBUG",
            file_path=main_log_path,
            error_file_path=error_log_path,
            rotation="10 MB",
            retention="7 days"
        )
        
        # 使用请求上下文记录不同级别的日志
        with RequestContext("test-error-separation-001"):
            logger.debug("这是一条调试日志")
            logger.info("这是一条信息日志")
            logger.warning("这是一条警告日志")
            logger.error("这是一条错误日志")
            logger.critical("这是一条严重错误日志")
            
            # 模拟异常日志
            try:
                raise ValueError("这是一个测试异常")
            except Exception as e:
                logger.exception(f"捕获到异常: {e}")
        
        # 再记录一些不同请求ID的日志
        with RequestContext("test-error-separation-002"):
            logger.info("第二个请求的信息日志")
            logger.error("第二个请求的错误日志")
        
        with RequestContext("test-error-separation-003"):
            logger.warning("第三个请求的警告日志")
            logger.critical("第三个请求的严重错误日志")
        
        print("\n--- 日志记录完成，检查文件内容 ---")
        
        # 检查主日志文件
        if os.path.exists(main_log_path):
            print(f"\n✓ 主日志文件存在: {main_log_path}")
            with open(main_log_path, 'r', encoding='utf-8') as f:
                main_content = f.read()
            print(f"主日志文件大小: {len(main_content)} 字符")
            print("主日志文件内容预览:")
            print("-" * 50)
            print(main_content[:1000] + "..." if len(main_content) > 1000 else main_content)
            print("-" * 50)
        else:
            print(f"✗ 主日志文件不存在: {main_log_path}")
        
        # 检查错误日志文件
        if os.path.exists(error_log_path):
            print(f"\n✓ 错误日志文件存在: {error_log_path}")
            with open(error_log_path, 'r', encoding='utf-8') as f:
                error_content = f.read()
            print(f"错误日志文件大小: {len(error_content)} 字符")
            print("错误日志文件内容:")
            print("-" * 50)
            print(error_content)
            print("-" * 50)
        else:
            print(f"✗ 错误日志文件不存在: {error_log_path}")
        
        # 分析日志内容
        print("\n--- 日志内容分析 ---")
        
        if os.path.exists(main_log_path):
            with open(main_log_path, 'r', encoding='utf-8') as f:
                main_lines = f.readlines()
            
            debug_count = sum(1 for line in main_lines if "DEBUG" in line)
            info_count = sum(1 for line in main_lines if "INFO" in line)
            warning_count = sum(1 for line in main_lines if "WARNING" in line)
            error_count = sum(1 for line in main_lines if "ERROR" in line)
            critical_count = sum(1 for line in main_lines if "CRITICAL" in line)
            
            print(f"主日志文件统计:")
            print(f"  DEBUG: {debug_count} 条")
            print(f"  INFO: {info_count} 条")
            print(f"  WARNING: {warning_count} 条")
            print(f"  ERROR: {error_count} 条")
            print(f"  CRITICAL: {critical_count} 条")
            print(f"  总计: {len(main_lines)} 条")
        
        if os.path.exists(error_log_path):
            with open(error_log_path, 'r', encoding='utf-8') as f:
                error_lines = f.readlines()
            
            error_error_count = sum(1 for line in error_lines if "ERROR" in line)
            error_critical_count = sum(1 for line in error_lines if "CRITICAL" in line)
            
            print(f"\n错误日志文件统计:")
            print(f"  ERROR: {error_error_count} 条")
            print(f"  CRITICAL: {error_critical_count} 条")
            print(f"  总计: {len(error_lines)} 条")
            
            # 验证错误日志文件只包含ERROR和CRITICAL级别
            has_debug = any("DEBUG" in line for line in error_lines)
            has_info = any("INFO" in line for line in error_lines)
            has_warning = any("WARNING" in line for line in error_lines)
            
            if not has_debug and not has_info and not has_warning:
                print("✓ 错误日志文件只包含ERROR和CRITICAL级别的日志")
            else:
                print("✗ 错误日志文件包含了其他级别的日志")
                if has_debug:
                    print("  - 包含DEBUG日志")
                if has_info:
                    print("  - 包含INFO日志")
                if has_warning:
                    print("  - 包含WARNING日志")
        
        # 验证request_id是否正确记录
        print("\n--- Request ID 验证 ---")
        if os.path.exists(main_log_path):
            with open(main_log_path, 'r', encoding='utf-8') as f:
                main_content = f.read()
            
            req_001_count = main_content.count("test-error-separation-001")
            req_002_count = main_content.count("test-error-separation-002")
            req_003_count = main_content.count("test-error-separation-003")
            
            print(f"主日志中的request_id统计:")
            print(f"  test-error-separation-001: {req_001_count} 次")
            print(f"  test-error-separation-002: {req_002_count} 次")
            print(f"  test-error-separation-003: {req_003_count} 次")
        
        if os.path.exists(error_log_path):
            with open(error_log_path, 'r', encoding='utf-8') as f:
                error_content = f.read()
            
            error_req_001_count = error_content.count("test-error-separation-001")
            error_req_002_count = error_content.count("test-error-separation-002")
            error_req_003_count = error_content.count("test-error-separation-003")
            
            print(f"\n错误日志中的request_id统计:")
            print(f"  test-error-separation-001: {error_req_001_count} 次")
            print(f"  test-error-separation-002: {error_req_002_count} 次")
            print(f"  test-error-separation-003: {error_req_003_count} 次")
        
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)
        print(f"\n清理临时目录: {temp_dir}")


def test_log_path_generation():
    """测试日志路径生成逻辑"""
    print("\n=== 测试日志路径生成逻辑 ===")
    
    test_cases = [
        ("logs/application.log", "logs/error.log"),
        ("/var/log/alpha-service/application.log", "/var/log/alpha-service/error.log"),
        ("application.log", "error.log"),
        ("logs/app.log", "logs/error.log"),
        ("logs/service.log", "logs/error.log"),
    ]
    
    for main_path, expected_error_path in test_cases:
        # 模拟路径生成逻辑
        if 'application.log' in main_path:
            error_path = main_path.replace('application.log', 'error.log')
        else:
            log_dir = Path(main_path).parent
            error_path = str(log_dir / "error.log")
        
        print(f"主日志: {main_path}")
        print(f"错误日志: {error_path}")
        print(f"期望: {expected_error_path}")
        print(f"匹配: {'✓' if error_path == expected_error_path else '✗'}")
        print("-" * 40)


def main():
    """主测试函数"""
    print("开始测试错误日志分离功能")
    print("=" * 60)
    
    test_log_path_generation()
    test_error_log_separation()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n总结:")
    print("1. 主日志文件 (application.log) 包含所有级别的日志")
    print("2. 错误日志文件 (error.log) 只包含 ERROR 和 CRITICAL 级别的日志")
    print("3. 两个文件都包含完整的 request_id 信息")
    print("4. 日志格式和轮转配置保持一致")


if __name__ == '__main__':
    main()
