curl -X POST "http://localhost:8000/api/files/list" \
    -H "Content-Type: application/json" \
    -d '{
            "session_id":"sess_c0528d1634d2495a8e8dec13f2cb721a", 
            "artifact_types": ["resultArtifact", "processArtifact", "sessionFile"],  
            "max_results":2,
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'


curl -X POST "http://localhost:8000/api/files/list" \
    -H "Content-Type: application/json" \
    -d '{
            "SessionId":"sess_1466084cbfd14c8ca57b38a53f558e4a", 
            "ArtifactTypes": ["resultArtifact", "processArtifact", "sessionFile"],  
            "MaxResults":10,
            "NextToken": "47_1753122301000",
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'


curl -X POST "http://localhost:8000/api/files/list" \
    -H "Content-Type: application/json" \
    -d '{
            "SessionId": "test_session_123", 
            "ArtifactTypes": ["resultArtifact", "processArtifact", "sessionFile"],  
            "MaxResults": 100
    }'


# 获取文件下载链接 - 单个文件
curl -X POST "http://localhost:8000/api/files/download-urls?expires=3600" \
    -H "Content-Type: application/json" \
    -d '{
            "artifact_ids": ["artifact-b623f62246d049c4b0e489494d8879ae"],
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'


curl -X POST "http://localhost:8000/api/files/get-artifact-preview" \
    -H "Content-Type: application/json" \
    -d '{
            "artifact_id": "artifact-b623f62246d049c4b0e489494d8879ae",
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'

# 获取文件上传地址
curl -X POST "http://localhost:8000/api/files/get-upload-url" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "config_fixed_test_session",
    "file_info": {
      "FileName": "test_document.pdf",
      "FileSize": 1048576,
      "FileType": "pdf"
    },
    "file_type": "sessionFile",
    "loginToken": "mock_login_token",
    "regionId": "mock_region_id"
  }'

# 更新上传结果
curl -X POST "http://localhost:8000/api/files/confirm-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "config_fixed_test_session",
    "file_id": "37",
    "etag": "mock_etag",
    "loginToken": "mock_login_token",
    "regionId": "mock_region_id"
  }'

# 重命名文件 - 单个文件
curl -X POST "http://localhost:8000/api/files/rename" \
    -H "Content-Type: application/json" \
    -d '{
            "ArtifactId": "artifact-52b15da6666f4ee6a21ada644747460f",
            "NewFileName": "renamed_document.pdf",
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'

# 重命名文件 - 测试边界情况（文件名包含中文）
curl -X POST "http://localhost:8000/api/files/rename?loginToken=mock_login_token&regionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "FileId": 48,
            "NewFileName": "我的测试文档.docx"
    }'

# 重命名文件 - 测试长文件名
curl -X POST "http://localhost:8000/api/files/rename?loginToken=mock_login_token&regionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "FileId": 49,
            "NewFileName": "这是一个非常长的文件名用来测试系统对长文件名的处理能力.txt"
    }'

# 重命名文件 - 测试错误情况（无效文件ID）
curl -X POST "http://localhost:8000/api/files/rename?loginToken=mock_login_token&regionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "FileId": 99999,
            "NewFileName": "non_existent_file.txt"
    }'

# 重命名文件 - 测试错误情况（空文件名）
curl -X POST "http://localhost:8000/api/files/rename?loginToken=mock_login_token&regionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "FileId": 47,
            "NewFileName": ""
    }'

# 重命名文件 - 测试错误情况（包含非法字符）
curl -X POST "http://localhost:8000/api/files/rename?loginToken=mock_login_token&regionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "FileId": 47,
            "NewFileName": "invalid<>file|name?.txt"
    }'


# 查询知识库列表 - 第一页
curl -X GET "http://localhost:8000/api/knowledge_base/list?max_result=10&next_token=" \
    -H "accept: application/json" \
    -H "Content-Type: application/json" \
    -H "X-Login-Token: mock_login_token" \
    -H "X-Region-Id: mock_region_id"

# =================================
# 批量删除文件API测试用例
# =================================

# 批量删除文件 - 正常删除多个文件
curl -X POST "http://localhost:8000/api/files/batch-delete" \
    -H "Content-Type: application/json" \
    -d '{
            "artifact_ids": ["artifact-a1d9b71efe8e4045856dae679d540759"],
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'

# 批量删除文件 - 仅删除数据库记录，不删除OSS文件
curl -X POST "http://localhost:8000/api/files/batch-delete" \
    -H "Content-Type: application/json" \
    -d '{
            "FileIds": [10, 11, 12],
            "DeleteFromOss": false,
            "loginToken": "mock_login_token",
            "regionId": "mock_region_id"
    }'

# 批量删除文件 - 删除单个文件
curl -X POST "http://localhost:8000/api/files/batch-delete" \
    -H "Content-Type: application/json" \
    -d '{
            "ArtifactIds": ["artifact-52b15da6666f4ee6a21ada644747460f"],
            "DeleteFromOss": true,
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'

# 批量删除文件 - 删除不存在的文件ID
curl -X POST "http://localhost:8000/api/files/batch-delete" \
    -H "Content-Type: application/json" \
    -d '{
            "FileIds": [99999, 88888, 77777],
            "DeleteFromOss": true,
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'

# 批量删除文件 - 测试错误情况（空文件ID列表）
curl -X POST "http://localhost:8000/api/files/batch-delete" \
    -H "Content-Type: application/json" \
    -d '{
            "FileIds": [],
            "DeleteFromOss": true,
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'

# 批量删除文件 - 测试错误情况（无效的文件ID）
curl -X POST "http://localhost:8000/api/files/batch-delete" \
    -H "Content-Type: application/json" \
    -d '{
            "FileIds": [0, -1, 5],
            "DeleteFromOss": true,
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'

# 批量删除文件 - 混合存在和不存在的文件ID
curl -X POST "http://localhost:8000/api/files/batch-delete" \
    -H "Content-Type: application/json" \
    -d '{
            "FileIds": [1, 99999, 2, 88888, 3],
            "DeleteFromOss": true,
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'

# 批量删除文件 - 大批量删除（接近限制）
curl -X POST "http://localhost:8000/api/files/batch-delete" \
    -H "Content-Type: application/json" \
    -d '{
            "FileIds": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
            "DeleteFromOss": true,
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'