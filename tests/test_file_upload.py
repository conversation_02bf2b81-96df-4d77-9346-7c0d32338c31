#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传功能测试脚本
"""
import requests
import json
import time
from io import BytesIO


def test_file_upload():
    """测试文件上传接口"""
    base_url = "http://localhost:8000"
    
    print("⚠️  请确保:")
    print("   1. 已手动执行建表语句创建 alpha_files 表")
    print("   2. 使用 daily 环境启动服务:")
    print("      source venv/bin/activate")
    print("      export ENV_FOR_DYNACONF=daily")
    print("      python start_service.py")
    print("   3. 服务已正常启动在 http://localhost:8000")
    print()
    
    # 创建测试文件内容
    test_content = "这是一个测试文件的内容\n测试文件上传功能\n当前时间: " + time.strftime("%Y-%m-%d %H:%M:%S")
    test_file = BytesIO(test_content.encode('utf-8'))
    
    # 准备上传数据
    files = {
        'file': ('test.txt', test_file, 'text/plain')
    }
    data = {
        'session_id': 'test_session_123',
        'file_type': 'sessionFile'
    }
    
    print("🚀 开始测试文件上传...")
    
    try:
        # 1. 测试文件上传
        print("1. 上传文件...")
        response = requests.post(f"{base_url}/api/files/upload", files=files, data=data)
        
        if response.status_code == 200:
            upload_result = response.json()
            print(f"✅ 文件上传成功:")
            print(f"   - 文件ID: {upload_result['file_id']}")
            print(f"   - 会话ID: {upload_result['session_id']}")
            print(f"   - 文件名: {upload_result['file_name']}")
            print(f"   - 文件大小: {upload_result['file_size']} bytes")
            print(f"   - 上传状态: {upload_result['upload_status']}")
            
            file_id = upload_result['file_id']
            
            # 2. 测试状态查询
            print("\n2. 查询文件状态...")
            time.sleep(1)  # 等待一秒
            
            status_response = requests.get(f"{base_url}/api/files/status", params={'file_id': file_id})
            
            if status_response.status_code == 200:
                status_result = status_response.json()
                print(f"✅ 文件状态查询成功:")
                print(f"   - 文件ID: {status_result['file_id']}")
                print(f"   - 文件名: {status_result['file_name']}")
                print(f"   - 文件大小: {status_result['file_size']} bytes")
                print(f"   - 已上传大小: {status_result['uploaded_size']} bytes")
                print(f"   - 上传进度: {status_result['upload_progress']}%")
                print(f"   - 上传状态: {status_result['upload_status']}")
                print(f"   - 创建时间: {status_result['created_time']}")
                
                if status_result['url']:
                    print(f"   - 下载URL: {status_result['url']}")
                
                if status_result['error_message']:
                    print(f"   - 错误信息: {status_result['error_message']}")
                
            else:
                print(f"❌ 文件状态查询失败: {status_response.status_code}")
                print(f"   错误信息: {status_response.text}")
                
        else:
            print(f"❌ 文件上传失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务已启动:")
        print("   source venv/bin/activate")
        print("   export ENV_FOR_DYNACONF=daily")
        print("   python start_service.py")
    except Exception as e:
        print(f"❌ 测试异常: {e}")


def test_health_check():
    """测试健康检查接口"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试健康检查...")
    
    try:
        response = requests.get(f"{base_url}/api/sessions/health")
        
        if response.status_code == 200:
            health_result = response.json()
            print(f"✅ 健康检查成功:")
            print(f"   - 状态: {health_result['status']}")
            print(f"   - 时间戳: {health_result['timestamp']}")
            print(f"   - 版本: {health_result['version']}")
            
            if 'sessions' in health_result:
                sessions = health_result['sessions']
                print(f"   - 会话总数: {sessions.get('total', 0)}")
                print(f"   - 今日创建: {sessions.get('today_created', 0)}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务已启动")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")


if __name__ == "__main__":
    print("=" * 60)
    print("Alpha Service 文件上传功能测试")
    print("=" * 60)
    
    # 先测试健康检查
    # test_health_check()
    
    # 再测试文件上传
    test_file_upload()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
