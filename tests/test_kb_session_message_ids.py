#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：测试新的get_kb_session_message_ids方法和API接口
"""

import sys
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


def test_knowledge_service_method():
    """测试KnowledgeService中的get_kb_session_message_ids方法"""
    print("=" * 80)
    print("🧪 测试KnowledgeService.get_kb_session_message_ids方法")
    print("=" * 80)
    
    try:
        from domain.services.knowledge_service import knowledgebase_service
        from domain.services.auth_service import AuthContext
        
        # 创建测试用的认证上下文
        auth_context = AuthContext(
            ali_uid="test_ali_uid",
            wy_id="test_wy_id",
            user_name="test_user"
        )
        
        # 测试参数
        test_kb_id = "test_kb_id"
        test_session_id = "test_session_id"
        
        print(f"📋 测试参数:")
        print(f"  - 知识库ID: {test_kb_id}")
        print(f"  - 会话ID: {test_session_id}")
        print(f"  - 用户: {auth_context.user_name}")
        
        print(f"\n🔍 调用get_kb_session_message_ids方法...")
        
        # 调用方法
        message_ids = knowledgebase_service.get_kb_session_message_ids(
            auth_context=auth_context,
            kb_id=test_kb_id,
            session_id=test_session_id,
        )
        
        print(f"✅ 方法调用成功")
        print(f"📄 返回结果:")
        print(f"  - 消息ID数量: {len(message_ids)}")
        print(f"  - 消息ID列表: {message_ids}")
        
        return True
        
    except Exception as e:
        print(f"❌ 方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_endpoint():
    """测试新的API接口"""
    print(f"\n" + "=" * 80)
    print("🌐 测试API接口 /api/knowledge_base/session/message/ids")
    print("=" * 80)
    
    try:
        import requests
        
        # API基础URL（需要根据实际情况调整）
        base_url = "http://localhost:8000"
        endpoint = f"{base_url}/api/knowledge_base/session/message/ids"
        
        # 测试参数
        params = {
            "kb_id": "test_kb_id",
            "session_id": "test_session_id"
        }
        
        # 测试请求头（需要根据实际认证方式调整）
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_token"  # 需要替换为实际的认证token
        }
        
        print(f"📋 请求信息:")
        print(f"  - URL: {endpoint}")
        print(f"  - 参数: {params}")
        print(f"  - 请求头: {headers}")
        
        print(f"\n🔍 发送API请求...")
        
        # 发送GET请求
        response = requests.get(
            endpoint,
            params=params,
            headers=headers,
            timeout=10
        )
        
        print(f"📊 响应信息:")
        print(f"  - 状态码: {response.status_code}")
        print(f"  - 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ API调用成功")
            print(f"📄 响应内容:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            return True
        else:
            print(f"❌ API调用失败")
            print(f"📄 错误响应:")
            print(response.text)
            return False
            
    except ImportError:
        print("⚠️ 需要安装requests库: pip install requests")
        print("💡 跳过API测试")
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_method_validation():
    """测试方法的参数验证"""
    print(f"\n" + "=" * 80)
    print("🔍 测试方法参数验证")
    print("=" * 80)
    
    try:
        from domain.services.knowledge_service import knowledgebase_service
        from domain.services.auth_service import AuthContext
        from domain.utils.exceptions import ParameterException
        
        auth_context = AuthContext(
            ali_uid="test_ali_uid",
            wy_id="test_wy_id",
            user_name="test_user"
        )
        
        # 测试1: 空的kb_id
        print("🧪 测试1: 空的kb_id")
        try:
            knowledgebase_service.get_kb_session_message_ids(
                auth_context=auth_context,
                kb_id="",
                session_id="test_session_id",
            )
            print("❌ 应该抛出参数异常")
            return False
        except ParameterException:
            print("✅ 正确抛出参数异常")
        except Exception as e:
            print(f"⚠️ 抛出了其他异常: {e}")
        
        # 测试2: 空的session_id
        print("\n🧪 测试2: 空的session_id")
        try:
            knowledgebase_service.get_kb_session_message_ids(
                auth_context=auth_context,
                kb_id="test_kb_id",
                session_id="",
            )
            print("❌ 应该抛出参数异常")
            return False
        except ParameterException:
            print("✅ 正确抛出参数异常")
        except Exception as e:
            print(f"⚠️ 抛出了其他异常: {e}")
        
        # 测试3: None参数
        print("\n🧪 测试3: None参数")
        try:
            knowledgebase_service.get_kb_session_message_ids(
                auth_context=auth_context,
                kb_id=None,
                session_id="test_session_id",
            )
            print("❌ 应该抛出参数异常")
            return False
        except (ParameterException, TypeError):
            print("✅ 正确抛出异常")
        except Exception as e:
            print(f"⚠️ 抛出了其他异常: {e}")
        
        print("\n✅ 参数验证测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 参数验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_response_model():
    """测试响应模型"""
    print(f"\n" + "=" * 80)
    print("📋 测试响应模型")
    print("=" * 80)
    
    try:
        from application.rag_api_models import KnowledgeBaseSessionMessageIdsResponse
        
        # 测试1: 正常数据
        print("🧪 测试1: 正常数据")
        test_data = ["msg_1", "msg_2", "msg_3"]
        response = KnowledgeBaseSessionMessageIdsResponse(message_ids=test_data)
        print(f"✅ 响应模型创建成功: {response}")
        print(f"📄 JSON序列化: {response.model_dump_json()}")
        
        # 测试2: 空列表
        print("\n🧪 测试2: 空列表")
        empty_response = KnowledgeBaseSessionMessageIdsResponse(message_ids=[])
        print(f"✅ 空列表响应模型创建成功: {empty_response}")
        
        # 测试3: 验证字段
        print("\n🧪 测试3: 验证字段")
        assert hasattr(response, 'message_ids'), "缺少message_ids字段"
        assert isinstance(response.message_ids, list), "message_ids应该是列表类型"
        print("✅ 字段验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 响应模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 开始测试新的get_kb_session_message_ids功能")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 测试1: 响应模型
    results.append(("响应模型测试", test_response_model()))
    
    # 测试2: 参数验证
    results.append(("参数验证测试", test_method_validation()))
    
    # 测试3: 服务方法
    results.append(("服务方法测试", test_knowledge_service_method()))
    
    # 测试4: API接口
    results.append(("API接口测试", test_api_endpoint()))
    
    # 总结结果
    print(f"\n" + "=" * 80)
    print("📊 测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        return True
    else:
        print("💔 部分测试失败，请检查上述错误信息")
        return False


if __name__ == "__main__":
    success = main()
    print(f"\n{'='*80}")
    print(f"🏁 测试完成，结果: {'成功' if success else '失败'}")
    print(f"{'='*80}")
    sys.exit(0 if success else 1)
