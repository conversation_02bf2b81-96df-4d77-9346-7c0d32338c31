import pytest
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.presentation.api.dependencies.api_common_utils import package_api_result, OK_STATUS


def test_package_api_result_with_no_parameters():
    """测试不带任何参数调用 package_api_result"""
    result = package_api_result()
    
    expected = {
        "code": "",
        "data": {},
        "message": "",
        "request_id": "",
        "status": OK_STATUS,
        "success": False,
    }
    
    assert result == expected


def test_package_api_result_with_partial_parameters():
    """测试只传递部分参数"""
    result = package_api_result(
        code="SUCCESS",
        data={"key": "value"},
        success=True
    )
    
    expected = {
        "code": "SUCCESS",
        "data": {"key": "value"},
        "message": "",
        "request_id": "",
        "status": OK_STATUS,
        "success": True,
    }
    
    assert result == expected


def test_package_api_result_with_none_values():
    """测试传递 None 值"""
    result = package_api_result(
        code=None,
        data=None,
        message=None,
        request_id=None,
        status=None,
        success=None
    )
    
    expected = {
        "code": "",
        "data": {},
        "message": "",
        "request_id": "",
        "status": OK_STATUS,
        "success": False,
    }
    
    assert result == expected


def test_package_api_result_with_dumps():
    """测试 dumps 参数"""
    result = package_api_result(
        code="SUCCESS",
        data={"key": "value"},
        dumps=True
    )
    
    # 验证返回的是 JSON 字符串
    assert isinstance(result, str)
    assert '"code": "SUCCESS"' in result
    assert '"key": "value"' in result


def test_package_api_result_with_all_parameters():
    """测试传递所有参数"""
    result = package_api_result(
        code="SUCCESS",
        data={"key": "value"},
        message="操作成功",
        request_id="req-123",
        status=200,
        success=True,
        dumps=False
    )
    
    expected = {
        "code": "SUCCESS",
        "data": {"key": "value"},
        "message": "操作成功",
        "request_id": "req-123",
        "status": 200,
        "success": True,
    }
    
    assert result == expected


if __name__ == "__main__":
    # 运行测试
    test_package_api_result_with_no_parameters()
    test_package_api_result_with_partial_parameters()
    test_package_api_result_with_none_values()
    test_package_api_result_with_dumps()
    test_package_api_result_with_all_parameters()
    print("所有测试通过！") 