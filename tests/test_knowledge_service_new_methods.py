"""
测试知识库服务的新方法
测试 get_knowledge_base、update_knowledge_base、delete_knowledge_base
"""

import pytest
import uuid
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from src.domain.services.knowledge_service import KnowledgeService
from src.infrastructure.database.models.knowledgebase_models import KnowledgeBaseModel
from src.domain.utils.check_utils import ClientException
from src.application.rag_api_models import (
    KnowledgeBaseDetailResponse,
    KnowledgeBaseUpdateResponse,
    KnowledgeBaseDeleteResponse
)


class TestKnowledgeServiceNewMethods:
    """测试知识库服务的新方法"""

    def setup_method(self):
        """设置测试环境"""
        self.service = KnowledgeService()
        self.test_kb_id = f"kb_{uuid.uuid4().hex[:12]}"
        self.test_ali_uid = 123456789
        self.test_wy_id = "wy_user_001"
        self.test_name = "测试知识库"
        self.test_description = "这是一个测试知识库"
        
        # 创建测试知识库模型
        self.test_knowledge_base = KnowledgeBaseModel(
            id=1,
            kb_id=self.test_kb_id,
            name=self.test_name,
            description=self.test_description,
            owner_ali_uid=self.test_ali_uid,
            owner_wy_id=self.test_wy_id,
            is_deleted=0,
            gmt_created=datetime.now(),
            gmt_modified=datetime.now()
        )

    def test_get_knowledge_base_success(self):
        """测试成功获取知识库详情"""
        # Mock repository
        self.service.knowledgebase_repository.get_knowledge_base_by_id = Mock(
            return_value=self.test_knowledge_base
        )
        
        # 执行测试
        result = self.service.get_knowledge_base(
            self.test_kb_id, self.test_ali_uid, self.test_wy_id
        )
        
        # 验证结果
        assert result is not None
        assert result.kb_id == self.test_kb_id
        assert result.name == self.test_name
        assert result.description == self.test_description
        assert result.owner_ali_uid == self.test_ali_uid
        assert result.owner_wy_id == self.test_wy_id

    def test_get_knowledge_base_not_found(self):
        """测试获取不存在的知识库"""
        # Mock repository 返回 None
        self.service.knowledgebase_repository.get_knowledge_base_by_id = Mock(
            return_value=None
        )
        
        # 执行测试，应该抛出异常
        with pytest.raises(ClientException):
            self.service.get_knowledge_base(
                self.test_kb_id, self.test_ali_uid, self.test_wy_id
            )

    def test_get_knowledge_base_no_permission(self):
        """测试无权限获取知识库"""
        # Mock repository
        self.service.knowledgebase_repository.get_knowledge_base_by_id = Mock(
            return_value=self.test_knowledge_base
        )
        
        # 执行测试，使用不同的用户ID
        with pytest.raises(ValueError, match="无权限访问该知识库"):
            self.service.get_knowledge_base(
                self.test_kb_id, 999999, "different_user"
            )

    @patch('src.domain.services.knowledge_service.create_rag_client')
    def test_update_knowledge_base_success(self, mock_create_rag_client):
        """测试成功更新知识库"""
        # Mock RAG client
        mock_rag_client = Mock()
        mock_rag_client.update_kb.return_value = Mock(body=Mock())
        mock_create_rag_client.return_value = mock_rag_client
        
        # Mock repository
        self.service.knowledgebase_repository.get_knowledge_base_by_id = Mock(
            return_value=self.test_knowledge_base
        )
        self.service.knowledgebase_repository.list_knowledge_bases = Mock(
            return_value=[]
        )
        self.service.knowledgebase_repository.update_knowledge_base = Mock(
            return_value=self.test_knowledge_base
        )
        
        # 执行测试
        result = self.service.update_knowledge_base(
            self.test_kb_id, self.test_ali_uid, self.test_wy_id,
            name="新名称", description="新描述"
        )
        
        # 验证结果
        assert result is not None
        assert result.kb_id == self.test_kb_id
        assert result.name == self.test_name
        assert result.description == self.test_description

    def test_update_knowledge_base_not_found(self):
        """测试更新不存在的知识库"""
        # Mock repository 返回 None
        self.service.knowledgebase_repository.get_knowledge_base_by_id = Mock(
            return_value=None
        )
        
        # 执行测试，应该抛出异常
        with pytest.raises(ClientException):
            self.service.update_knowledge_base(
                self.test_kb_id, self.test_ali_uid, self.test_wy_id,
                name="新名称"
            )

    def test_update_knowledge_base_no_permission(self):
        """测试无权限更新知识库"""
        # Mock repository
        self.service.knowledgebase_repository.get_knowledge_base_by_id = Mock(
            return_value=self.test_knowledge_base
        )
        
        # 执行测试，使用不同的用户ID
        with pytest.raises(ValueError, match="无权限修改该知识库"):
            self.service.update_knowledge_base(
                self.test_kb_id, 999999, "different_user",
                name="新名称"
            )

    def test_update_knowledge_base_name_conflict(self):
        """测试更新知识库名称冲突"""
        # Mock repository
        self.service.knowledgebase_repository.get_knowledge_base_by_id = Mock(
            return_value=self.test_knowledge_base
        )
        # 模拟名称已存在（返回非空列表表示名称已存在）
        self.service.knowledgebase_repository.list_knowledge_bases = Mock(
            return_value=[{"name": "新名称", "kb_id": "different_kb"}]
        )
        
        # 执行测试，应该抛出异常
        with pytest.raises(ClientException):
            self.service.update_knowledge_base(
                self.test_kb_id, self.test_ali_uid, self.test_wy_id,
                name="新名称"
            )

    @patch('src.domain.services.knowledge_service.create_rag_client')
    def test_update_knowledge_base_non_selective(self, mock_create_rag_client):
        """测试非选择性更新（None值会置空字段）"""
        # Mock RAG client
        mock_rag_client = Mock()
        mock_rag_client.update_kb.return_value = Mock(body=Mock())
        mock_create_rag_client.return_value = mock_rag_client
        
        # Mock repository
        self.service.knowledgebase_repository.get_knowledge_base_by_id = Mock(
            return_value=self.test_knowledge_base
        )
        self.service.knowledgebase_repository.list_knowledge_bases = Mock(
            return_value=[]
        )
        self.service.knowledgebase_repository.update_knowledge_base = Mock(
            return_value=self.test_knowledge_base
        )
        
        # 执行测试 - 非选择性更新，传入 None 值
        result = self.service.update_knowledge_base(
            self.test_kb_id, self.test_ali_uid, self.test_wy_id,
            name=None, description=None, is_update_selective=False
        )
        
        # 验证结果
        assert result is not None
        assert result.kb_id == self.test_kb_id
        
        # 验证 RAG 客户端被调用时传入 None 值
        mock_rag_client.update_kb.assert_called_once_with(
            kb_id=self.test_kb_id,
            name=None,
            description=None
        )
        
        # 验证数据库更新被调用时 is_update_selective=False
        self.service.knowledgebase_repository.update_knowledge_base.assert_called_once_with(
            kb_id=self.test_kb_id,
            name=None,
            description=None,
            is_update_selective=False
        )

    @patch('src.domain.services.knowledge_service.create_rag_client')
    def test_delete_knowledge_base_success(self, mock_create_rag_client):
        """测试成功删除知识库"""
        # Mock RAG client
        mock_rag_client = Mock()
        mock_rag_client.delete_kb.return_value = Mock(body=Mock())
        mock_create_rag_client.return_value = mock_rag_client
        
        # Mock repository
        self.service.knowledgebase_repository.get_knowledge_base_by_id = Mock(
            return_value=self.test_knowledge_base
        )
        self.service.knowledgebase_repository.soft_delete_knowledge_base = Mock(
            return_value=True
        )
        
        # 执行测试
        result = self.service.delete_knowledge_base(
            self.test_kb_id, self.test_ali_uid, self.test_wy_id
        )
        
        # 验证结果
        assert result is not None
        assert result.kb_id == self.test_kb_id
        assert result.success is True

    def test_delete_knowledge_base_not_found(self):
        """测试删除不存在的知识库"""
        # Mock repository 返回 None
        self.service.knowledgebase_repository.get_knowledge_base_by_id = Mock(
            return_value=None
        )
        
        # 执行测试，应该抛出异常
        with pytest.raises(ClientException):
            self.service.delete_knowledge_base(
                self.test_kb_id, self.test_ali_uid, self.test_wy_id
            )

    def test_delete_knowledge_base_no_permission(self):
        """测试无权限删除知识库"""
        # Mock repository
        self.service.knowledgebase_repository.get_knowledge_base_by_id = Mock(
            return_value=self.test_knowledge_base
        )
        
        # 执行测试，使用不同的用户ID
        with pytest.raises(ValueError, match="无权限删除该知识库"):
            self.service.delete_knowledge_base(
                self.test_kb_id, 999999, "different_user"
            )

    def test_parameter_validation(self):
        """测试参数验证"""
        # 测试空参数 - 这些应该抛出 ClientException
        with pytest.raises(ClientException):
            self.service.get_knowledge_base("", self.test_ali_uid, self.test_wy_id)
        
        with pytest.raises(ClientException):
            self.service.get_knowledge_base(self.test_kb_id, None, self.test_wy_id)
        
        with pytest.raises(ClientException):
            self.service.get_knowledge_base(self.test_kb_id, self.test_ali_uid, "")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"]) 