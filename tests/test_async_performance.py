#!/usr/bin/env python3
"""
异步性能测试脚本
测试 /sessions/send 接口的异步化效果
"""

import asyncio
import time
import httpx
import json
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor
import statistics

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_SESSIONS_COUNT = 10
CONCURRENT_REQUESTS = 5

# 测试数据
TEST_REQUEST = {
    "Prompt": "测试消息，请简单回复",
    "AgentId": "test-agent",
    "SessionId": None,  # 创建新会话
    "DesktopId": None,
    "AuthCode": None,
    "Resources": []
}

# 模拟认证头
TEST_HEADERS = {
    "Content-Type": "application/json",
    "Authorization": "Bearer test-token",
    "X-Ali-Uid": "123456789",
    "X-Wy-Id": "test-wy-id"
}


async def send_single_request(client: httpx.AsyncClient, request_id: int) -> Dict[str, Any]:
    """发送单个请求"""
    start_time = time.time()
    
    try:
        response = await client.post(
            f"{BASE_URL}/api/sessions/send",
            json=TEST_REQUEST,
            headers=TEST_HEADERS,
            timeout=30.0
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        return {
            "request_id": request_id,
            "status_code": response.status_code,
            "duration": duration,
            "success": response.status_code == 200,
            "response_size": len(response.content),
            "error": None
        }
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        
        return {
            "request_id": request_id,
            "status_code": 0,
            "duration": duration,
            "success": False,
            "response_size": 0,
            "error": str(e)
        }


async def run_concurrent_test(concurrent_count: int, total_requests: int) -> List[Dict[str, Any]]:
    """运行并发测试"""
    print(f"🚀 开始并发测试: {concurrent_count} 并发, 总计 {total_requests} 请求")
    
    async with httpx.AsyncClient() as client:
        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(concurrent_count)
        
        async def limited_request(request_id: int):
            async with semaphore:
                return await send_single_request(client, request_id)
        
        # 创建所有任务
        tasks = [limited_request(i) for i in range(total_requests)]
        
        # 执行所有任务
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "request_id": i,
                    "status_code": 0,
                    "duration": 0,
                    "success": False,
                    "response_size": 0,
                    "error": str(result)
                })
            else:
                processed_results.append(result)
        
        total_duration = end_time - start_time
        print(f"✅ 并发测试完成，总耗时: {total_duration:.2f}s")
        
        return processed_results


def analyze_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析测试结果"""
    successful_results = [r for r in results if r["success"]]
    failed_results = [r for r in results if not r["success"]]
    
    if not successful_results:
        return {
            "total_requests": len(results),
            "successful_requests": 0,
            "failed_requests": len(failed_results),
            "success_rate": 0.0,
            "error": "所有请求都失败了"
        }
    
    durations = [r["duration"] for r in successful_results]
    response_sizes = [r["response_size"] for r in successful_results]
    
    analysis = {
        "total_requests": len(results),
        "successful_requests": len(successful_results),
        "failed_requests": len(failed_results),
        "success_rate": len(successful_results) / len(results) * 100,
        
        # 响应时间统计
        "avg_duration": statistics.mean(durations),
        "min_duration": min(durations),
        "max_duration": max(durations),
        "median_duration": statistics.median(durations),
        "p95_duration": sorted(durations)[int(len(durations) * 0.95)] if len(durations) > 1 else durations[0],
        
        # 响应大小统计
        "avg_response_size": statistics.mean(response_sizes),
        "total_response_size": sum(response_sizes),
        
        # 错误统计
        "errors": [r["error"] for r in failed_results if r["error"]]
    }
    
    return analysis


def print_analysis(analysis: Dict[str, Any]):
    """打印分析结果"""
    print("\n" + "="*60)
    print("📊 性能测试结果分析")
    print("="*60)
    
    print(f"总请求数: {analysis['total_requests']}")
    print(f"成功请求: {analysis['successful_requests']}")
    print(f"失败请求: {analysis['failed_requests']}")
    print(f"成功率: {analysis['success_rate']:.1f}%")
    
    if analysis['successful_requests'] > 0:
        print(f"\n⏱️  响应时间统计:")
        print(f"  平均响应时间: {analysis['avg_duration']:.3f}s")
        print(f"  最快响应时间: {analysis['min_duration']:.3f}s")
        print(f"  最慢响应时间: {analysis['max_duration']:.3f}s")
        print(f"  中位数响应时间: {analysis['median_duration']:.3f}s")
        print(f"  95%响应时间: {analysis['p95_duration']:.3f}s")
        
        print(f"\n📦 响应大小统计:")
        print(f"  平均响应大小: {analysis['avg_response_size']:.0f} bytes")
        print(f"  总响应大小: {analysis['total_response_size']} bytes")
    
    if analysis['failed_requests'] > 0:
        print(f"\n❌ 错误信息:")
        for error in set(analysis['errors']):
            count = analysis['errors'].count(error)
            print(f"  {error} (出现 {count} 次)")


async def main():
    """主函数"""
    print("🧪 异步性能测试开始")
    print(f"目标服务: {BASE_URL}")
    print(f"测试配置: {CONCURRENT_REQUESTS} 并发, {TEST_SESSIONS_COUNT} 请求")
    
    # 运行测试
    results = await run_concurrent_test(CONCURRENT_REQUESTS, TEST_SESSIONS_COUNT)
    
    # 分析结果
    analysis = analyze_results(results)
    
    # 打印结果
    print_analysis(analysis)
    
    # 性能评估
    if analysis['successful_requests'] > 0:
        avg_duration = analysis['avg_duration']
        if avg_duration < 0.1:
            print(f"\n🚀 性能评级: 优秀 (平均响应时间 < 100ms)")
        elif avg_duration < 0.5:
            print(f"\n✅ 性能评级: 良好 (平均响应时间 < 500ms)")
        elif avg_duration < 1.0:
            print(f"\n⚠️  性能评级: 一般 (平均响应时间 < 1s)")
        else:
            print(f"\n❌ 性能评级: 需要优化 (平均响应时间 > 1s)")


if __name__ == "__main__":
    asyncio.run(main())
