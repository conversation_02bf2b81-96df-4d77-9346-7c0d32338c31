#!/usr/bin/env python3
"""
测试 /sessions/send 接口
测试发送消息到Agent，包含知识库资源
"""

import asyncio
import sys
import os
import json
import httpx
from datetime import datetime
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from src.application.api_models import SendMessageRequest, SessionResource, ResourceType
    from src.domain.services.auth_service import AuthContext
    print("✓ 成功导入所需模块")
except ImportError as e:
    print(f"✗ 导入模块失败: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_CONFIG = {
    "ali_uid": "1550203943326350",
    "wy_id": "test_user",
    "login_token": "test_token_123",
    "session_id": "test_session_001",
    "region_id": "cn-hangzhou"
}

class SessionsSendTester:
    """会话发送接口测试器"""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def create_test_headers(self) -> Dict[str, str]:
        """创建测试请求头"""
        return {
            "Content-Type": "application/json",
            "loginToken": TEST_CONFIG["login_token"],
            "sessionId": TEST_CONFIG["session_id"],
            "regionId": TEST_CONFIG["region_id"],
            "User-Agent": "SessionsSendTester/1.0"
        }
    
    def create_send_message_request(
        self,
        prompt: str,
        agent_id: str,
        session_id: Optional[str] = None,
        desktop_id: Optional[str] = None,
        auth_code: Optional[str] = None,
        resources: Optional[list] = None
    ) -> Dict[str, Any]:
        """创建发送消息请求数据"""
        request_data = {
            "Prompt": prompt,
            "AgentId": agent_id,
        }
        
        if session_id:
            request_data["SessionId"] = session_id
        
        if desktop_id:
            request_data["DesktopId"] = desktop_id
            
        if auth_code:
            request_data["AuthCode"] = auth_code
            
        if resources:
            request_data["Resources"] = resources
            
        return request_data
    
    async def test_send_message_with_knowledge_base(self):
        """测试发送消息（包含知识库资源）"""
        print("\n=== 测试发送消息（包含知识库资源） ===")
        
        try:
            # 构建知识库资源
            resources = [
                {
                    "ResourceId": "kb-cefe0324-9850-47fa-8dcd-5f1e87b5d8da",
                    "Type": "knowledge_base"
                }
            ]
            
            # 构建请求数据
            request_data = self.create_send_message_request(
                prompt="请基于知识库内容回答：什么是人工智能？",
                agent_id="agent_gpt4",
                session_id=None,  # 创建新会话
                desktop_id="agentbay",
                auth_code="test_auth_code",
                resources=resources
            )
            
            print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
            
            # 发送请求
            headers = self.create_test_headers()
            response = await self.client.post(
                f"{self.base_url}/api/sessions/send",
                json=request_data,
                headers=headers
            )
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"✓ 请求成功")
                print(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                
                # 验证响应格式
                if "data" in response_data:
                    data = response_data["data"]
                    if "session_id" in data and "round_id" in data:
                        print(f"✓ 响应格式正确")
                        print(f"  - 会话ID: {data['session_id']}")
                        print(f"  - 轮次ID: {data['round_id']}")
                        return data
                    else:
                        print(f"✗ 响应数据格式不正确，缺少session_id或round_id")
                else:
                    print(f"✗ 响应数据格式不正确，缺少data字段")
            else:
                print(f"✗ 请求失败")
                try:
                    error_data = response.json()
                    print(f"错误信息: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
                except:
                    print(f"错误信息: {response.text}")
                    
        except Exception as e:
            print(f"✗ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_send_message_without_resources(self):
        """测试发送消息（不包含资源）"""
        print("\n=== 测试发送消息（不包含资源） ===")
        
        try:
            # 构建请求数据
            request_data = self.create_send_message_request(
                prompt="你好，请介绍一下你自己",
                agent_id="agent_gpt4",
                session_id=None,  # 创建新会话
                desktop_id="agentbay"
            )
            
            print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
            
            # 发送请求
            headers = self.create_test_headers()
            response = await self.client.post(
                f"{self.base_url}/api/sessions/send",
                json=request_data,
                headers=headers
            )
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"✓ 请求成功")
                print(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                return response_data.get("data")
            else:
                print(f"✗ 请求失败")
                try:
                    error_data = response.json()
                    print(f"错误信息: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
                except:
                    print(f"错误信息: {response.text}")
                    
        except Exception as e:
            print(f"✗ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_send_message_to_existing_session(self, session_id: str):
        """测试发送消息到已存在的会话"""
        print(f"\n=== 测试发送消息到已存在的会话 (session_id: {session_id}) ===")
        
        try:
            # 构建请求数据
            request_data = self.create_send_message_request(
                prompt="请继续我们之前的对话，告诉我更多细节",
                agent_id="agent_gpt4",
                session_id=session_id,
                desktop_id="agentbay"
            )
            
            print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
            
            # 发送请求
            headers = self.create_test_headers()
            response = await self.client.post(
                f"{self.base_url}/api/sessions/send",
                json=request_data,
                headers=headers
            )
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"✓ 请求成功")
                print(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                return response_data.get("data")
            else:
                print(f"✗ 请求失败")
                try:
                    error_data = response.json()
                    print(f"错误信息: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
                except:
                    print(f"错误信息: {response.text}")
                    
        except Exception as e:
            print(f"✗ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_invalid_requests(self):
        """测试无效请求"""
        print("\n=== 测试无效请求 ===")
        
        test_cases = [
            {
                "name": "缺少Prompt",
                "data": {
                    "AgentId": "agent_gpt4"
                }
            },
            {
                "name": "缺少AgentId", 
                "data": {
                    "Prompt": "测试消息"
                }
            },
            {
                "name": "空Prompt",
                "data": {
                    "Prompt": "",
                    "AgentId": "agent_gpt4"
                }
            },
            {
                "name": "无效的资源类型",
                "data": {
                    "Prompt": "测试消息",
                    "AgentId": "agent_gpt4",
                    "Resources": [
                        {
                            "ResourceId": "invalid_resource",
                            "Type": "invalid_type"
                        }
                    ]
                }
            }
        ]
        
        headers = self.create_test_headers()
        
        for test_case in test_cases:
            print(f"\n--- 测试: {test_case['name']} ---")
            try:
                response = await self.client.post(
                    f"{self.base_url}/api/sessions/send",
                    json=test_case["data"],
                    headers=headers
                )
                
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code != 200:
                    print(f"✓ 正确返回错误状态码")
                    try:
                        error_data = response.json()
                        print(f"错误信息: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
                    except:
                        print(f"错误信息: {response.text}")
                else:
                    print(f"✗ 应该返回错误但返回了成功")
                    
            except Exception as e:
                print(f"✗ 测试异常: {e}")

async def test_health_check():
    """测试服务健康状态"""
    print("=== 测试服务健康状态 ===")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(f"{BASE_URL}/health")
            
            if response.status_code == 200:
                print("✓ 服务运行正常")
                health_data = response.json()
                print(f"健康状态: {json.dumps(health_data, ensure_ascii=False, indent=2)}")
                return True
            else:
                print(f"✗ 服务健康检查失败: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"✗ 无法连接到服务: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始测试 /sessions/send 接口")
    print(f"服务地址: {BASE_URL}")
    print(f"测试配置: {json.dumps(TEST_CONFIG, ensure_ascii=False, indent=2)}")
    print("=" * 60)
    
    # 1. 健康检查
    if not await test_health_check():
        print("服务不可用，退出测试")
        return
    
    # 2. 执行接口测试
    async with SessionsSendTester(BASE_URL) as tester:
        # 测试发送消息（包含知识库资源）
        result1 = await tester.test_send_message_with_knowledge_base()
        
        # 测试发送消息（不包含资源）
        result2 = await tester.test_send_message_without_resources()
        
        # 如果有成功的会话，测试发送消息到已存在的会话
        if result1 and "session_id" in result1:
            await tester.test_send_message_to_existing_session(result1["session_id"])
        elif result2 and "session_id" in result2:
            await tester.test_send_message_to_existing_session(result2["session_id"])
        
        # 测试无效请求
        await tester.test_invalid_requests()
    
    print("\n" + "=" * 60)
    print("测试完成")

if __name__ == "__main__":
    asyncio.run(main())
