#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试异步消息发送接口的行为
"""

import sys
import os
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


async def test_async_send_message():
    """测试异步消息发送功能"""
    print("=== 测试异步消息发送接口 ===")
    
    # 模拟异步消息发送函数
    async def mock_send_message_slow(*args, **kwargs):
        """模拟耗时的消息发送过程"""
        print(f"[模拟] 开始发送消息...")
        await asyncio.sleep(2)  # 模拟2秒的处理时间
        print(f"[模拟] 消息发送完成")
        return "sess_123", "round_456"
    
    # 模拟快速的会话创建
    def mock_create_session(*args, **kwargs):
        """模拟快速的会话创建"""
        class MockSession:
            def __init__(self):
                self.session_id = "sess_new_123"
        return MockSession()
    
    # 测试场景1：创建新会话并异步发送消息
    print("\n--- 场景1：创建新会话 ---")
    start_time = time.time()
    
    # 模拟接口逻辑
    session_id = None
    if not session_id:
        # 创建新会话（快速）
        session = mock_create_session()
        session_id = session.session_id
        print(f"✓ 创建新会话: {session_id}")
    
    # 立即返回会话ID
    response_time = time.time() - start_time
    print(f"✓ 立即返回会话ID: {session_id} (耗时: {response_time:.3f}s)")
    
    # 创建异步任务（不等待完成）
    task = asyncio.create_task(mock_send_message_slow(session_id=session_id))
    print("✓ 异步任务已创建，不等待完成")
    
    # 模拟接口返回
    api_response = {
        "code": 200,
        "success": True,
        "data": {
            "session_id": session_id
        }
    }
    
    total_response_time = time.time() - start_time
    print(f"✓ 接口响应完成 (总耗时: {total_response_time:.3f}s)")
    print(f"✓ 返回数据: {api_response}")
    
    # 等待异步任务完成（仅用于测试观察）
    print("\n--- 等待异步任务完成（仅用于观察） ---")
    await task
    final_time = time.time() - start_time
    print(f"✓ 异步任务完成 (总耗时: {final_time:.3f}s)")
    
    # 测试场景2：使用现有会话
    print("\n--- 场景2：使用现有会话 ---")
    start_time = time.time()
    
    existing_session_id = "sess_existing_456"
    print(f"✓ 使用现有会话: {existing_session_id}")
    
    # 立即返回
    response_time = time.time() - start_time
    print(f"✓ 立即返回会话ID: {existing_session_id} (耗时: {response_time:.3f}s)")
    
    # 创建异步任务
    task2 = asyncio.create_task(mock_send_message_slow(session_id=existing_session_id))
    print("✓ 异步任务已创建")
    
    total_response_time = time.time() - start_time
    print(f"✓ 接口响应完成 (总耗时: {total_response_time:.3f}s)")
    
    # 等待异步任务完成
    await task2
    final_time = time.time() - start_time
    print(f"✓ 异步任务完成 (总耗时: {final_time:.3f}s)")


async def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    # 模拟会话创建失败
    def mock_create_session_fail(*args, **kwargs):
        raise Exception("会话创建失败")
    
    # 模拟消息发送失败
    async def mock_send_message_fail(*args, **kwargs):
        await asyncio.sleep(1)
        raise Exception("消息发送失败")
    
    print("\n--- 场景1：会话创建失败（同步错误） ---")
    try:
        session = mock_create_session_fail()
    except Exception as e:
        print(f"✓ 会话创建失败被正确捕获: {e}")
        print("✓ 接口应该返回错误响应，不继续执行")
    
    print("\n--- 场景2：消息发送失败（异步错误） ---")
    session_id = "sess_test"
    print(f"✓ 会话创建成功: {session_id}")
    print("✓ 立即返回会话ID给前端")
    
    # 异步任务中的错误
    async def async_task_with_error():
        try:
            await mock_send_message_fail()
        except Exception as e:
            print(f"✓ 异步任务中的错误被正确处理: {e}")
            print("✓ 错误不影响接口响应，只记录日志")
    
    task = asyncio.create_task(async_task_with_error())
    await task


def test_performance_comparison():
    """性能对比测试"""
    print("\n=== 性能对比 ===")
    
    # 模拟原来的同步方式
    def sync_approach():
        start = time.time()
        # 会话创建
        time.sleep(0.1)  # 模拟会话创建耗时
        # 消息发送
        time.sleep(2.0)  # 模拟消息发送耗时
        end = time.time()
        return end - start
    
    # 模拟新的异步方式
    def async_approach():
        start = time.time()
        # 会话创建
        time.sleep(0.1)  # 模拟会话创建耗时
        # 立即返回，不等待消息发送
        end = time.time()
        return end - start
    
    sync_time = sync_approach()
    async_time = async_approach()
    
    print(f"同步方式响应时间: {sync_time:.3f}s")
    print(f"异步方式响应时间: {async_time:.3f}s")
    print(f"性能提升: {((sync_time - async_time) / sync_time * 100):.1f}%")


async def main():
    """主测试函数"""
    await test_async_send_message()
    await test_error_handling()
    test_performance_comparison()
    
    print("\n=== 测试总结 ===")
    print("✓ 接口能够立即返回会话ID")
    print("✓ 消息发送在后台异步执行")
    print("✓ 同步错误正确处理并返回给前端")
    print("✓ 异步错误不影响接口响应")
    print("✓ 显著提升接口响应速度")


if __name__ == '__main__':
    asyncio.run(main())
