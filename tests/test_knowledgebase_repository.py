"""
知识库Repository测试
测试KnowledgeBaseRepository的各种CRUD操作
"""

import pytest
import uuid
from unittest.mock import Mock, patch
from datetime import datetime
from typing import Optional

from src.infrastructure.database.repositories.knowledgebase_repository import KnowledgeBaseRepository
from src.infrastructure.database.models.knowledgebase_models import KnowledgeBaseModel


class TestKnowledgeBaseRepository:
    """知识库Repository测试类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.repository = KnowledgeBaseRepository()
        self.mock_db_manager = Mock()
        self.repository.db_manager = self.mock_db_manager
        
        # 创建测试数据
        self.test_kb_id = f"kb_{uuid.uuid4().hex[:12]}"
        self.test_owner_ali_uid = 123456789
        self.test_owner_wy_id = "wy_user_001"
        self.test_name = "测试知识库"
        self.test_description = "这是一个测试知识库"
        
        # 创建测试模型实例
        self.test_knowledge_base = KnowledgeBaseModel(
            id=1,
            kb_id=self.test_kb_id,
            name=self.test_name,
            description=self.test_description,
            owner_ali_uid=self.test_owner_ali_uid,
            owner_wy_id=self.test_owner_wy_id,
            is_deleted=0,
            gmt_created=datetime.now(),
            gmt_modified=datetime.now()
        )
    
    def test_create_knowledge_base_success(self):
        """测试成功创建知识库"""
        # 模拟数据库会话
        mock_session = Mock()
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # 执行创建
        result = self.repository.create_knowledge_base(
            kb_id=self.test_kb_id,
            name=self.test_name,
            owner_ali_uid=self.test_owner_ali_uid,
            owner_wy_id=self.test_owner_wy_id,
            description=self.test_description
        )
        
        # 验证结果
        assert result is not None
        assert result.kb_id == self.test_kb_id
        assert result.name == self.test_name
        assert result.description == self.test_description
        assert result.owner_ali_uid == self.test_owner_ali_uid
        assert result.owner_wy_id == self.test_owner_wy_id
        assert result.is_deleted == 0
        
        # 验证数据库操作
        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()
    
    def test_create_knowledge_base_duplicate_id(self):
        """测试创建重复ID的知识库"""
        # 模拟数据库会话和完整性错误
        mock_session = Mock()
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        mock_session.add.side_effect = Exception("Duplicate entry")
        
        # 执行创建并验证异常
        with pytest.raises(Exception):
            self.repository.create_knowledge_base(
                kb_id=self.test_kb_id,
                name=self.test_name,
                owner_ali_uid=self.test_owner_ali_uid,
                owner_wy_id=self.test_owner_wy_id
            )
    
    def test_get_knowledge_base_by_id_success(self):
        """测试成功获取知识库"""
        # 模拟数据库会话
        mock_session = Mock()
        mock_session.query.return_value.filter.return_value.first.return_value = self.test_knowledge_base
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # 执行获取
        result = self.repository.get_knowledge_base_by_id(self.test_kb_id)
        
        # 验证结果
        assert result is not None
        assert result.kb_id == self.test_kb_id
        assert result.name == self.test_name
    
    def test_get_knowledge_base_by_id_not_found(self):
        """测试获取不存在的知识库"""
        # 模拟数据库会话返回None
        mock_session = Mock()
        mock_session.query.return_value.filter.return_value.first.return_value = None
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # 执行获取
        result = self.repository.get_knowledge_base_by_id("non_existent_kb")
        
        # 验证结果
        assert result is None
    
    def test_get_knowledge_bases_by_owner_success(self):
        """测试成功获取用户的知识库列表"""
        # 创建测试数据列表
        test_knowledge_bases = [
            self.test_knowledge_base,
            KnowledgeBaseModel(
                id=2,
                kb_id=f"kb_{uuid.uuid4().hex[:12]}",
                name="第二个知识库",
                description="第二个测试知识库",
                owner_ali_uid=self.test_owner_ali_uid,
                owner_wy_id=self.test_owner_wy_id,
                is_deleted=0,
                gmt_created=datetime.now(),
                gmt_modified=datetime.now()
            )
        ]
        
        # 模拟数据库会话
        mock_session = Mock()
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = test_knowledge_bases
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # 执行获取
        result = self.repository.get_knowledge_bases_by_owner(
            owner_ali_uid=self.test_owner_ali_uid,
            owner_wy_id=self.test_owner_wy_id,
            limit=10,
            offset=0
        )
        
        # 验证结果
        assert len(result) == 2
        assert result[0].kb_id == self.test_kb_id
        assert result[1].name == "第二个知识库"
    
    def test_update_knowledge_base_success(self):
        """测试成功更新知识库"""
        # 模拟数据库会话
        mock_session = Mock()
        mock_session.query.return_value.filter.return_value.first.return_value = self.test_knowledge_base
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # 执行更新
        result = self.repository.update_knowledge_base(
            kb_id=self.test_kb_id,
            name="更新后的名称",
            description="更新后的描述"
        )
        
        # 验证结果
        assert result is not None
        assert result.name == "更新后的名称"
        assert result.description == "更新后的描述"
    
    def test_update_knowledge_base_not_found(self):
        """测试更新不存在的知识库"""
        # 模拟数据库会话返回None
        mock_session = Mock()
        mock_session.query.return_value.filter.return_value.first.return_value = None
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # 执行更新
        result = self.repository.update_knowledge_base(
            kb_id="non_existent_kb",
            name="新名称"
        )
        
        # 验证结果
        assert result is None
    
    def test_soft_delete_knowledge_base_success(self):
        """测试成功软删除知识库"""
        # 模拟数据库会话
        mock_session = Mock()
        mock_session.query.return_value.filter.return_value.first.return_value = self.test_knowledge_base
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # 执行软删除
        result = self.repository.soft_delete_knowledge_base(self.test_kb_id)
        
        # 验证结果
        assert result is True
        assert self.test_knowledge_base.is_deleted == 1
    
    def test_soft_delete_knowledge_base_not_found(self):
        """测试软删除不存在的知识库"""
        # 模拟数据库会话返回None
        mock_session = Mock()
        mock_session.query.return_value.filter.return_value.first.return_value = None
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # 执行软删除
        result = self.repository.soft_delete_knowledge_base("non_existent_kb")
        
        # 验证结果
        assert result is False
    
    def test_check_knowledge_base_exists_true(self):
        """测试检查知识库存在性 - 存在"""
        # 模拟数据库会话
        mock_session = Mock()
        mock_session.query.return_value.filter.return_value.first.return_value = self.test_knowledge_base
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # 执行检查
        result = self.repository.check_knowledge_base_exists(self.test_kb_id)
        
        # 验证结果
        assert result is True
    
    def test_check_knowledge_base_exists_false(self):
        """测试检查知识库存在性 - 不存在"""
        # 模拟数据库会话返回None
        mock_session = Mock()
        mock_session.query.return_value.filter.return_value.first.return_value = None
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # 执行检查
        result = self.repository.check_knowledge_base_exists("non_existent_kb")
        
        # 验证结果
        assert result is False
    
    def test_count_knowledge_bases_by_owner(self):
        """测试统计用户知识库数量"""
        # 模拟数据库会话
        mock_session = Mock()
        mock_session.query.return_value.filter.return_value.count.return_value = 5
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # 执行统计
        result = self.repository.count_knowledge_bases_by_owner(
            owner_ali_uid=self.test_owner_ali_uid,
            owner_wy_id=self.test_owner_wy_id
        )
        
        # 验证结果
        assert result == 5
    
    def test_search_knowledge_bases(self):
        """测试搜索知识库"""
        # 创建测试数据列表
        test_knowledge_bases = [
            self.test_knowledge_base,
            KnowledgeBaseModel(
                id=2,
                kb_id=f"kb_{uuid.uuid4().hex[:12]}",
                name="搜索测试知识库",
                description="搜索测试",
                owner_ali_uid=self.test_owner_ali_uid,
                owner_wy_id=self.test_owner_wy_id,
                is_deleted=0,
                gmt_created=datetime.now(),
                gmt_modified=datetime.now()
            )
        ]
        
        # 模拟数据库会话
        mock_session = Mock()
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = test_knowledge_bases
        self.mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # 执行搜索
        result = self.repository.list_knowledge_bases(
            owner_ali_uid=self.test_owner_ali_uid,
            owner_wy_id=self.test_owner_wy_id,
            name="测试",
            limit=5
        )
        
        # 验证结果
        assert len(result) == 2
        assert any(kb.name == "搜索测试知识库" for kb in result)


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 