#!/usr/bin/env python3
"""
测试 chunk_parse 路径修复逻辑
"""

def test_chunk_parse_path_processing():
    """测试路径处理逻辑"""
    
    def process_chunk_parse_path(chunk_parse_path: str) -> str:
        """
        处理RAG服务返回的路径，可能包含错误的bucket前缀
        """
        actual_object_key = chunk_parse_path
        
        if chunk_parse_path.startswith('doc_details/'):
            # 去掉错误的bucket前缀，保留实际的对象键名
            actual_object_key = chunk_parse_path[len('doc_details/'):]
            print(f"检测到错误的bucket前缀，修正路径: {chunk_parse_path} -> {actual_object_key}")
        elif '/' in chunk_parse_path and not chunk_parse_path.startswith('http'):
            # 如果路径包含其他可能的bucket前缀，尝试智能处理
            parts = chunk_parse_path.split('/', 1)
            if len(parts) == 2 and not parts[0].startswith('20'):  # 不是日期开头的路径
                actual_object_key = parts[1]
                print(f"检测到可能的bucket前缀，修正路径: {chunk_parse_path} -> {actual_object_key}")
        
        return actual_object_key
    
    # 测试用例
    test_cases = [
        # 错误的 doc_details 前缀
        "doc_details/20250805/document-eed541e2-f2a0-4a63-a0c1-693312c04601/chunk_parse_result.json",
        # 其他可能的错误前缀
        "wrong_bucket/20250805/document-xxx/chunk_parse_result.json",
        # 正确的路径（以日期开头）
        "20250805/document-eed541e2-f2a0-4a63-a0c1-693312c04601/chunk_parse_result.json",
        # HTTP URL（不应该处理）
        "https://oss-cn-hangzhou.aliyuncs.com/bucket/path/file.json",
        # 简单文件名
        "chunk_parse_result.json"
    ]
    
    print("=" * 80)
    print("测试 chunk_parse 路径处理逻辑")
    print("=" * 80)
    
    for i, test_path in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入路径: {test_path}")
        result = process_chunk_parse_path(test_path)
        print(f"输出路径: {result}")
        print("-" * 40)
    
    # 验证特定的错误案例
    error_path = "doc_details/20250805/document-eed541e2-f2a0-4a63-a0c1-693312c04601/chunk_parse_result.json"
    expected_result = "20250805/document-eed541e2-f2a0-4a63-a0c1-693312c04601/chunk_parse_result.json"
    actual_result = process_chunk_parse_path(error_path)
    
    print(f"\n验证错误案例:")
    print(f"输入: {error_path}")
    print(f"期望: {expected_result}")
    print(f"实际: {actual_result}")
    print(f"结果: {'✅ 通过' if actual_result == expected_result else '❌ 失败'}")

if __name__ == "__main__":
    test_chunk_parse_path_processing()
