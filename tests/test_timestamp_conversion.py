#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试时间戳转换功能
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.domain.services.session_service import SessionService


def test_timestamp_conversion():
    """测试时间戳转换功能"""
    print("=== 测试时间戳转换功能 ===")
    
    # 创建SessionService实例
    service = SessionService()
    
    # 测试用例
    test_cases = [
        # (输入值, 描述)
        (datetime(2023, 1, 1, 12, 0, 0), "datetime对象"),
        (1672574400, "秒级时间戳"),
        (1672574400000, "毫秒级时间戳"),
        ("1672574400", "秒级时间戳字符串"),
        ("1672574400000", "毫秒级时间戳字符串"),
        ("2023-01-01T12:00:00Z", "ISO格式字符串"),
        ("2023-01-01T12:00:00+00:00", "ISO格式字符串带时区"),
        (None, "None值"),
        ("invalid", "无效字符串"),
        (1.5, "浮点数"),
    ]
    
    for value, description in test_cases:
        try:
            result = service._convert_to_timestamp(value)
            print(f"✓ {description}: {value} -> {result}")
        except Exception as e:
            print(f"✗ {description}: {value} -> 异常: {e}")
    
    print("\n=== 测试消息字段提取 ===")
    
    # 测试消息字段提取
    class MockMessage:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
    
    # 创建包含datetime时间戳的消息
    dt = datetime(2023, 1, 1, 12, 0, 0)
    message = MockMessage(
        message_id='msg_123',
        role='user',
        content='Hello world',
        timestamp=dt
    )
    
    result = service._extract_message_fields(message)
    print(f"原始时间戳: {dt} (类型: {type(dt)})")
    print(f"转换后时间戳: {result.get('timestamp')} (类型: {type(result.get('timestamp'))})")
    
    # 验证转换是否正确
    expected = int(dt.timestamp() * 1000)
    actual = result.get('timestamp')
    if actual == expected:
        print("✓ 时间戳转换正确")
    else:
        print(f"✗ 时间戳转换错误: 期望 {expected}, 实际 {actual}")


if __name__ == '__main__':
    test_timestamp_conversion()
