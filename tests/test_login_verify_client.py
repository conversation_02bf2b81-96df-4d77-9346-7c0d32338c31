# # -*- coding: utf-8 -*-
# """
# 测试LoginVerifyClient的功能
# """
# import pytest
# import asyncio
# from unittest.mock import Mock, patch, MagicMock
# from src.popclients.login_verify_client import LoginVerifyClient, LoginVerifyClientError
# from alibabacloud_wuyingaiinner20250718.models import (
#     VerifyLoginTokenResponse,
#     VerifyLoginTokenResponseBody,
#     VerifyLoginTokenResponseBodyData,
# )
#
#
# class TestLoginVerifyClient:
#     """测试LoginVerifyClient类"""
#
#     def setup_method(self):
#         """测试前的设置"""
#         self.access_key_id = "test_access_key_id"
#         self.access_key_secret = "test_access_key_secret"
#         self.endpoint = "test-endpoint.aliyuncs.com"
#
#     def test_init_with_credentials(self):
#         """测试使用凭证初始化客户端"""
#         client = LoginVerifyClient(
#             access_key_id=self.access_key_id,
#             access_key_secret=self.access_key_secret,
#             endpoint=self.endpoint
#         )
#
#         assert client.access_key_id == self.access_key_id
#         assert client.access_key_secret == self.access_key_secret
#         assert client.endpoint == self.endpoint
#         assert client.connect_timeout == 10000
#         assert client.read_timeout == 10000
#
#     def test_init_without_credentials_raises_error(self):
#         """测试没有凭证时抛出异常"""
#         with patch('src.popclients.login_verify_client.env_manager') as mock_env:
#             mock_env.get_env_var.return_value = None
#
#             with pytest.raises(LoginVerifyClientError):
#                 LoginVerifyClient()
#
#     @patch('src.popclients.login_verify_client.client.Client')
#     def test_verify_login_token_success(self, mock_client_class):
#         """测试成功验证登录令牌"""
#         # 创建模拟响应
#         mock_response = VerifyLoginTokenResponse()
#         mock_response.body = VerifyLoginTokenResponseBody()
#         mock_response.body.success = True
#         mock_response.body.code = "200"
#         mock_response.body.message = "Success"
#         mock_response.body.request_id = "test-request-id"
#
#         # 创建模拟数据
#         mock_data = VerifyLoginTokenResponseBodyData()
#         mock_data.ali_uid = *********
#         mock_data.wy_id = "test-wy-id"
#         mock_data.end_user_id = "test-user"
#         mock_data.account_type = "ALIYUN"
#         mock_data.login_type = "PASSWORD"
#         mock_data.api_key_id = "test-api-key"
#
#         mock_extras = VerifyLoginTokenResponseBodyDataExtras()
#         mock_extras.model = "test-model"
#         mock_data.extras = mock_extras
#
#         mock_response.body.data = mock_data
#
#         # 设置模拟客户端
#         mock_client_instance = Mock()
#         mock_client_instance.verify_login_token.return_value = mock_response
#         mock_client_class.return_value = mock_client_instance
#
#         # 创建客户端并测试
#         client = LoginVerifyClient(
#             access_key_id=self.access_key_id,
#             access_key_secret=self.access_key_secret,
#             endpoint=self.endpoint
#         )
#
#         response = client.verify_login_token("test-session-id")
#
#         assert response.body.success is True
#         assert response.body.data.ali_uid == *********
#         assert response.body.data.wy_id == "test-wy-id"
#
#         # 验证调用参数
#         mock_client_instance.verify_login_token.assert_called_once()
#         call_args = mock_client_instance.verify_login_token.call_args[0][0]
#         assert call_args.session_id == "test-session-id"
#
#     @patch('src.popclients.login_verify_client.client.Client')
#     async def test_verify_login_token_async_success(self, mock_client_class):
#         """测试异步成功验证登录令牌"""
#         # 创建模拟响应
#         mock_response = VerifyLoginTokenResponse()
#         mock_response.body = VerifyLoginTokenResponseBody()
#         mock_response.body.success = True
#
#         # 设置模拟客户端
#         mock_client_instance = Mock()
#         mock_client_instance.verify_login_token_async = AsyncMock(return_value=mock_response)
#         mock_client_class.return_value = mock_client_instance
#
#         # 创建客户端并测试
#         client = LoginVerifyClient(
#             access_key_id=self.access_key_id,
#             access_key_secret=self.access_key_secret,
#             endpoint=self.endpoint
#         )
#
#         response = await client.verify_login_token_async("test-session-id")
#
#         assert response.body.success is True
#         mock_client_instance.verify_login_token_async.assert_called_once()
#
#     @patch('src.popclients.login_verify_client.client.Client')
#     def test_verify_login_token_failure(self, mock_client_class):
#         """测试验证登录令牌失败"""
#         # 设置模拟客户端抛出异常
#         mock_client_instance = Mock()
#         mock_client_instance.verify_login_token.side_effect = Exception("API Error")
#         mock_client_class.return_value = mock_client_instance
#
#         # 创建客户端并测试
#         client = LoginVerifyClient(
#             access_key_id=self.access_key_id,
#             access_key_secret=self.access_key_secret,
#             endpoint=self.endpoint
#         )
#
#         with pytest.raises(LoginVerifyClientError):
#             client.verify_login_token("test-session-id")
#
#     def test_get_user_info_from_response_success(self):
#         """测试从响应中提取用户信息"""
#         # 创建模拟响应
#         mock_response = VerifyLoginTokenResponse()
#         mock_response.body = VerifyLoginTokenResponseBody()
#         mock_response.body.success = True
#
#         mock_data = VerifyLoginTokenResponseBodyData()
#         mock_data.ali_uid = *********
#         mock_data.wy_id = "test-wy-id"
#         mock_data.end_user_id = "test-user"
#         mock_data.account_type = "ALIYUN"
#         mock_data.login_type = "PASSWORD"
#         mock_data.api_key_id = "test-api-key"
#
#         mock_extras = VerifyLoginTokenResponseBodyDataExtras()
#         mock_extras.model = "test-model"
#         mock_data.extras = mock_extras
#
#         mock_response.body.data = mock_data
#
#         client = LoginVerifyClient(
#             access_key_id=self.access_key_id,
#             access_key_secret=self.access_key_secret,
#             endpoint=self.endpoint
#         )
#
#         user_info = client.get_user_info_from_response(mock_response)
#
#         assert user_info is not None
#         assert user_info["ali_uid"] == *********
#         assert user_info["wy_id"] == "test-wy-id"
#         assert user_info["end_user_id"] == "test-user"
#         assert user_info["account_type"] == "ALIYUN"
#         assert user_info["login_type"] == "PASSWORD"
#         assert user_info["api_key_id"] == "test-api-key"
#         assert user_info["extras"]["model"] == "test-model"
#
#     def test_get_user_info_from_response_failure(self):
#         """测试从失败响应中提取用户信息"""
#         # 创建失败的模拟响应
#         mock_response = VerifyLoginTokenResponse()
#         mock_response.body = VerifyLoginTokenResponseBody()
#         mock_response.body.success = False
#
#         client = LoginVerifyClient(
#             access_key_id=self.access_key_id,
#             access_key_secret=self.access_key_secret,
#             endpoint=self.endpoint
#         )
#
#         user_info = client.get_user_info_from_response(mock_response)
#
#         assert user_info is None
#
#     def test_get_client_info(self):
#         """测试获取客户端信息"""
#         client = LoginVerifyClient(
#             access_key_id=self.access_key_id,
#             access_key_secret=self.access_key_secret,
#             endpoint=self.endpoint,
#             connect_timeout=5000,
#             read_timeout=8000
#         )
#
#         info = client.get_client_info()
#
#         assert info["access_key_id"] == self.access_key_id
#         assert info["endpoint"] == self.endpoint
#         assert info["connect_timeout"] == 5000
#         assert info["read_timeout"] == 8000
#         assert info["version"] == "20250718"
#
#     def test_str_representation(self):
#         """测试字符串表示"""
#         client = LoginVerifyClient(
#             access_key_id=self.access_key_id,
#             access_key_secret=self.access_key_secret,
#             endpoint=self.endpoint
#         )
#
#         str_repr = str(client)
#         assert "LoginVerifyClient" in str_repr
#         assert self.endpoint in str_repr
#         assert self.access_key_id in str_repr
#         assert "20250718" in str_repr
#
#
# class AsyncMock(MagicMock):
#     """异步Mock类"""
#     async def __call__(self, *args, **kwargs):
#         return super(AsyncMock, self).__call__(*args, **kwargs)
