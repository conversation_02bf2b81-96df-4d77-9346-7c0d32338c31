#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终配置驱动测试
验证从properties.toml配置文件驱动的完整异步上传功能
"""
import sys
import os
from io import BytesIO
import time

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.domain.services.file_service import file_service
from src.domain.services.auth_service import AuthContext, auth_service,PermissionType,ResourceType
from src.infrastructure.database.models.file_models import FileType


def test_auth_permissions(file_id: int, owner_context: AuthContext):
    """测试鉴权功能示范"""
    print()
    print("🔐 鉴权功能示范测试")
    print("-" * 50)

    # 1. 测试资源所有者访问权限
    print("1️⃣ 测试资源所有者访问权限:")
    try:
        auth_result = auth_service.check_resource_permission(
            context=owner_context,
            resource_type=ResourceType.FILE.value,
            resource_id=str(file_id),
            required_permission=PermissionType.READ
        )
        if auth_result.success:
            print(f"   ✅ 所有者访问权限验证成功: user={owner_context.user_key}, file_id={file_id}")
            print(f"      权限列表: {auth_result.permissions}")
        else:
            print(f"   ❌ 所有者访问权限验证失败: {auth_result.message}")
    except Exception as e:
        print(f"   ❌ 所有者权限检查异常: {e}")

    # 2. 测试其他用户访问权限（应该失败）
    print("2️⃣ 测试其他用户访问权限（应该被拒绝）:")
    other_user_context = AuthContext(ali_uid=9999999999, wy_id="other_user")
    try:

        auth_result = auth_service.check_resource_permission(
            context=other_user_context,
            resource_type=ResourceType.FILE.value,
            resource_id=str(file_id),
            required_permission=PermissionType.READ
        )
        if auth_result.success:
            print(f"   ⚠️  其他用户意外获得访问权限: user={other_user_context.user_key}, file_id={file_id}")
        else:
            print(f"   ✅ 其他用户正确被拒绝访问: {auth_result.message}")
    except Exception as e:
        print(f"   ❌ 其他用户权限检查异常: {e}")

    # 3. 获取资源详细信息
    print("3️⃣ 获取资源详细信息:")
    try:
        resource = auth_service.auth_repository.get_resource(ResourceType.FILE.value, str(file_id))
        if resource:
            print(f"   📄 资源详情:")
            print(f"      - 资源类型: {resource.resource_type}")
            print(f"      - 资源ID: {resource.resource_id}")
            print(f"      - 资源名称: {resource.resource_name}")
            print(f"      - 所有者: ali_uid={resource.owner_ali_uid}, wy_id={resource.owner_wy_id}")
            print(f"      - 是否公开: {'是' if resource.is_public else '否'}")
            print(f"      - 创建时间: {resource.gmt_created}")
        else:
            print(f"   ❌ 未找到资源: file_id={file_id}")
    except Exception as e:
        print(f"   ❌ 获取资源详情异常: {e}")

    # 4. 测试权限检查的不同场景
    print("4️⃣ 权限检查场景测试:")

    # 场景A: 检查读取权限
    try:
        auth_result = auth_service.check_resource_permission(
            context=owner_context,
            resource_type=ResourceType.FILE.value,
            resource_id=str(file_id),
            required_permission=PermissionType.READ
        )
        print(f"   📖 读取权限: {'✅ 允许' if auth_result.success else '❌ 拒绝'}")
        if auth_result.success:
            print(f"      理由: {auth_result.message}")
    except Exception as e:
        print(f"   📖 读取权限检查异常: {e}")

    # 场景B: 检查写入权限
    try:
        auth_result = auth_service.check_resource_permission(
            context=owner_context,
            resource_type=ResourceType.FILE.value,
            resource_id=str(file_id),
            required_permission=PermissionType.WRITE
        )
        print(f"   ✏️  写入权限: {'✅ 允许' if auth_result.success else '❌ 拒绝'}")
        if auth_result.success:
            print(f"      理由: {auth_result.message}")
    except Exception as e:
        print(f"   ✏️  写入权限检查异常: {e}")

    # 场景C: 检查删除权限
    try:
        auth_result = auth_service.check_resource_permission(
            context=owner_context,
            resource_type=ResourceType.FILE.value,
            resource_id=str(file_id),
            required_permission=PermissionType.DELETE
        )
        print(f"   🗑️  删除权限: {'✅ 允许' if auth_result.success else '❌ 拒绝'}")
        if auth_result.success:
            print(f"      理由: {auth_result.message}")
    except Exception as e:
        print(f"   🗑️  删除权限检查异常: {e}")

    # 5. 测试分享功能
    print("5️⃣ 测试分享功能:")
    try:
        share_result = auth_service.create_share(
            context=owner_context,
            resource_type=ResourceType.FILE.value,
            resource_id=str(file_id),
            share_type="public",
            allowed_permissions=["read"]
        )
        if share_result[0]:  # success
            share_code = share_result[1]  # share_code
            print(f"   🔗 分享创建成功: share_code={share_code}")

            # 测试分享访问
            access_result = auth_service.access_share(share_code)
            if access_result[0]:  # success
                print(f"   ✅ 分享访问成功: {access_result[2]}")  # message
            else:
                print(f"   ❌ 分享访问失败: {access_result[2]}")  # message
        else:
            print(f"   ❌ 分享创建失败: {share_result[2]}")  # message
    except Exception as e:
        print(f"   ❌ 分享功能测试异常: {e}")

    # 6. 测试团队功能
    print("6️⃣ 测试团队功能:")
    try:
        team_result = auth_service.create_team(
            context=owner_context,
            name="测试团队",
            description="用于鉴权功能测试的团队"
        )
        if team_result[0]:  # success
            team_id = team_result[1]  # team_id
            print(f"   👥 团队创建成功: team_id={team_id}")

            # 获取用户团队
            user_teams = auth_service.get_user_teams(owner_context)
            print(f"   📋 用户团队数量: {len(user_teams)}")
            for team in user_teams:
                print(f"      - 团队: {team['name']} (ID: {team['id']})")
        else:
            print(f"   ❌ 团队创建失败: {team_result[2]}")  # message
    except Exception as e:
        print(f"   ❌ 团队功能测试异常: {e}")

    # 7. 测试鉴权系统的核心价值
    print("7️⃣ 鉴权系统核心价值展示:")
    print("   🔒 访问控制: 确保只有授权用户能访问资源")
    print("   👤 身份验证: 基于ali_uid和wy_id的双重身份标识")
    print("   🛡️  权限管理: 支持读取、写入、删除等细粒度权限")
    print("   📊 资源追踪: 所有文件资源都在鉴权系统中注册和管理")
    print("   🔄 状态同步: 文件上传时自动注册到鉴权系统")
    print("   🔗 分享机制: 支持安全的资源分享")
    print("   👥 团队协作: 支持团队级别的资源管理")

    print("-" * 50)
    print("🔐 鉴权功能示范完成")


def test_final_config_driven():
    """测试最终配置驱动功能"""
    print("=" * 80)
    print("最终配置驱动测试 - 修复配置后 + 鉴权示范")
    print("=" * 80)
    
    # 检查配置驱动的RAG OSS客户端
    print("🔧 检查配置驱动的RAG OSS客户端...")
    
    if file_service.oss_service.rag_oss_client:
        print("✅ RAG OSS客户端初始化成功")
        
        # 获取配置信息
        rag_config = file_service.oss_service.rag_oss_client.get_config()
        if rag_config:
            print(f"   Bucket: {rag_config.bucket_name}")
            print(f"   Region: {rag_config.region}")
            print(f"   Access Key: {rag_config.access_key_id[:10]}***")
            
            # 验证是否从配置文件读取
            if rag_config.bucket_name == "waiy-long-memory-bucket-pre":
                print("   ✅ 配置文件驱动：正常工作")
            else:
                print(f"   ❌ 配置异常：{rag_config.bucket_name}")
                return
        else:
            print("❌ 无法获取RAG OSS配置")
            return
    else:
        print("❌ RAG OSS客户端初始化失败")
        return
    
    print()
    
    # 测试完整的配置驱动异步上传流程
    context = AuthContext(ali_uid=1550203943326350, wy_id="test_user")
    
    test_content = """
湾流宇航公司(Gulfstream Aerospace)是截止至2013年世界上生产豪华、大型公务机的著名厂商。1999年由通用动力公司(General Dynamics)完全收购，其主要产品为“湾流”系列飞机。
1958年公司前身格鲁曼飞机公司(Grumman)推出专为商务应用设计的第一架公务机“湾流”I；1966年出厂的“湾流”II创立了大型座舱公务机市场。
1973年，阿伦·E.保尔森(Allen Paulson)以200万美元从格鲁曼公司购买了湾流飞机的生产线并接管了湾流各项计划，湾流公司正式诞生。随后湾流公司先后研制生产了“湾流”III、IV、V型公务机。
2001年6月，通用动力公司控股银河宇航公司（前身为以色列飞机工业公司，1997年在美国支持下成立）后，将该公司的银河(GALAXY)、阿斯特拉(ASTRA)公务机加入湾流系列并重新命名为湾流100/200，加大了湾流公务机的规模。

    """
    
    file_content = BytesIO(test_content.encode('utf-8'))
    
    print(f"测试文档内容长度: {len(test_content)} 字符")
    print(f"用户信息: ali_uid={context.ali_uid}, wy_id={context.wy_id}")
    print()
    
    try:
        print("🚀 开始配置修复后的异步上传测试...")
        
        # 记录开始时间
        start_time = time.time()
        
        # 调用上传接口
        file_obj, success = file_service.upload_file(
            context=context,
            file_content=file_content,
            original_filename="config_fixed_test.md",
            session_id="config_fixed_test_session",
            file_type=FileType.SESSION_FILE.value,
            content_type="text/markdown"
        )
        
        # 记录响应时间
        response_time = time.time() - start_time
        print(f"⚡ 接口响应时间: {response_time:.3f}秒")
        
        if success:
            print(f"✅ 文件上传成功: file_id={file_obj.id}")
            print()
            
            # 监控配置驱动的处理流程
            print("📊 监控配置修复后的处理流程...")
            
            previous_status = file_obj.upload_status
            previous_doc_id = file_obj.doc_id
            previous_content_length = 0
            
            # 监控2分钟
            for i in range(120):
                time.sleep(1)
                
                # 查询最新状态
                updated_file = file_service.file_repository.get_file_by_id(file_obj.id)
                if updated_file:
                    current_status = updated_file.upload_status
                    current_doc_id = updated_file.doc_id
                    current_content = updated_file.content or ""
                    current_content_length = len(current_content)
                    
                    # 检查是否有变化
                    status_changed = current_status != previous_status
                    doc_id_changed = current_doc_id != previous_doc_id
                    content_changed = current_content_length != previous_content_length
                    
                    if status_changed or doc_id_changed or content_changed:
                        elapsed = time.time() - start_time
                        print(f"   [{elapsed:6.1f}s] 状态: {current_status:10} | doc_id: {current_doc_id or 'None'} | 内容: {current_content_length}字符")
                        
                        previous_status = current_status
                        previous_doc_id = current_doc_id
                        previous_content_length = current_content_length
                    
                    # 如果已经完成且有内容，提前结束监控
                    if (current_status == "completed" and 
                        current_doc_id and 
                        current_content_length > 0):
                        print()
                        print("🎉 配置修复后的处理流程完全成功!")
                        total_time = time.time() - start_time
                        print(f"   总处理时间: {total_time:.1f}秒")
                        break
                else:
                    print(f"   [{i+1:3}s] ❌ 无法查询文件状态")
            
            # 最终验证
            print()
            print("📋 最终验证结果:")
            final_file = file_service.file_repository.get_file_by_id(file_obj.id)
            if final_file:
                print(f"   文件ID: {final_file.id}")
                print(f"   最终状态: {final_file.upload_status}")
                print(f"   doc_id: {final_file.doc_id or 'None'}")
                print(f"   内容长度: {len(final_file.content or '')} 字符")
                
                # 配置驱动验证
                print()
                print("🔍 配置修复验证:")
                
                # 验证1：快速响应
                if response_time < 2.0:
                    print("   ✅ 异步响应：接口快速返回")
                else:
                    print("   ⚠️  响应较慢：可能存在阻塞")
                
                # 验证2：配置读取
                print("   ✅ 配置读取：从properties.toml正确读取")
                
                # 验证3：完整功能
                if (final_file.upload_status == "completed" and 
                    final_file.doc_id and 
                    final_file.content):
                    print("   ✅ 功能完整：RAG解析和内容保存成功")
                    print("   ✅ 配置修复：OSS访问权限正常")
                    print("   ✅ 内容下载：chunk_parse内容成功获取")
                    
                    # 显示内容预览
                    content_preview = final_file.content[:200]
                    print(f"   📄 内容预览: {content_preview}...")
                    
                    print()
                    print("🎉 配置驱动的异步上传功能完全成功！")
                    print("🏆 实现了以下目标：")
                    print("   • 前端快速响应 ✅")
                    print("   • 后台异步处理 ✅")
                    print("   • 配置文件驱动 ✅")
                    print("   • 环境隔离支持 ✅")
                    print("   • 代码配置解耦 ✅")
                    print("   • RAG内容下载 ✅")
                    print("   • 鉴权系统集成 ✅")

                    # 进行鉴权功能示范
                    test_auth_permissions(final_file.id, context)
                elif final_file.upload_status == "analyzing":
                    print("   ⚠️  状态：仍在处理中")
                    print("   💡 建议：等待更长时间或检查RAG服务状态")
                else:
                    print("   ⚠️  功能不完整：部分流程异常")
                    if not final_file.doc_id:
                        print("   ❌ RAG解析失败：未获取到doc_id")
                    if not final_file.content:
                        print("   ❌ 内容下载失败：chunk_parse内容为空")
            else:
                print("   ❌ 无法获取最终文件状态")
                
        else:
            print("❌ 文件上传失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    print("=" * 80)
    print("测试完成")
    print("=" * 80)
    print()
    print("🎯 配置驱动的核心价值：")
    print("1. 代码与配置完全解耦")
    print("2. 支持多环境配置管理")
    print("3. 无需修改代码即可调整配置")
    print("4. 提高了系统的灵活性和可维护性")
    print("5. 真实有效的OSS访问权限")


if __name__ == "__main__":
    test_final_config_driven()
