#!/usr/bin/env python3
"""
调试 OSS bucket 配置问题
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def test_oss_bucket_debug():
    """调试 OSS bucket 配置"""
    
    print("=" * 80)
    print("调试 OSS bucket 配置")
    print("=" * 80)
    
    try:
        # 导入必要的模块
        from src.infrastructure.oss.oss_service import oss_service
        
        print("检查 RAG OSS 客户端配置:")
        
        if oss_service.rag_oss_client:
            print("✅ RAG OSS 客户端已初始化")
            
            # 获取配置
            rag_config = oss_service.rag_oss_client.get_config()
            print(f"  配置信息:")
            print(f"    - bucket_name: {rag_config.bucket_name}")
            print(f"    - region: {rag_config.region}")
            print(f"    - endpoint: {rag_config.endpoint}")
            
            # 获取 bucket 对象
            bucket = oss_service.rag_oss_client.get_bucket()
            print(f"  Bucket 对象信息:")
            print(f"    - bucket.bucket_name: {bucket.bucket_name}")
            print(f"    - bucket.endpoint: {bucket.endpoint}")
            
            # 测试一个简单的操作
            print(f"\n测试 bucket 操作:")
            try:
                # 尝试列出 bucket 中的对象（限制1个）
                result = bucket.list_objects(max_keys=1)
                objects = list(result.object_list)
                print(f"✅ Bucket 连接正常，找到 {len(objects)} 个对象")
                if objects:
                    print(f"  示例对象: {objects[0].key}")
            except Exception as e:
                print(f"❌ Bucket 操作失败: {e}")
                
                # 分析错误信息
                error_str = str(e)
                if 'NoSuchBucket' in error_str:
                    print("  错误类型: Bucket 不存在")
                    if 'BucketName' in error_str:
                        # 尝试从错误信息中提取 bucket 名称
                        import re
                        bucket_match = re.search(r"'BucketName': '([^']+)'", error_str)
                        if bucket_match:
                            error_bucket = bucket_match.group(1)
                            print(f"  错误中的 bucket 名称: {error_bucket}")
                            print(f"  配置中的 bucket 名称: {rag_config.bucket_name}")
                            if error_bucket != rag_config.bucket_name:
                                print(f"  ❌ Bucket 名称不匹配！")
                            else:
                                print(f"  ✅ Bucket 名称匹配")
                elif 'AccessDenied' in error_str:
                    print("  错误类型: 访问被拒绝")
                else:
                    print(f"  其他错误: {error_str}")
            
            # 测试下载一个不存在的文件
            print(f"\n测试下载不存在的文件:")
            test_key = "test/nonexistent_file.txt"
            try:
                result = oss_service.download_rag_file_content(test_key)
                if result:
                    print(f"意外成功: {len(result)}")
                else:
                    print("✅ 正确返回 None（文件不存在）")
            except Exception as e:
                print(f"下载异常: {e}")
                
                # 分析错误信息
                error_str = str(e)
                if 'NoSuchKey' in error_str:
                    print("  ✅ 正确的错误类型: 文件不存在")
                elif 'NoSuchBucket' in error_str:
                    print("  ❌ 错误类型: Bucket 不存在（这不应该发生）")
                    # 提取错误中的 bucket 名称
                    import re
                    bucket_match = re.search(r"'BucketName': '([^']+)'", error_str)
                    if bucket_match:
                        error_bucket = bucket_match.group(1)
                        print(f"    错误中的 bucket: {error_bucket}")
                        print(f"    应该使用的 bucket: {rag_config.bucket_name}")
                        
                        # 检查是否是路径解析问题
                        if error_bucket == test_key.split('/')[0]:
                            print(f"    ❌ 问题确认: OSS SDK 把路径的第一部分当作了 bucket 名称")
                            print(f"    这表明 bucket 对象配置有问题")
        else:
            print("❌ RAG OSS 客户端未初始化")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_oss_bucket_debug()
    sys.exit(0 if success else 1)
