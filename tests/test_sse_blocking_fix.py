#!/usr/bin/env python3
"""
测试SSE历史消息加载的阻塞修复效果
同时测试多个SSE连接和普通API请求，验证是否存在阻塞
"""

import asyncio
import time
import httpx
import json
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor

# 测试配置
BASE_URL = "http://localhost:8000"
SSE_CONNECTIONS = 3  # SSE连接数
API_REQUESTS = 5     # 普通API请求数
TEST_DURATION = 20   # 测试持续时间

# 模拟认证头
TEST_HEADERS = {
    "Content-Type": "application/json",
    "Authorization": "Bearer test-token",
    "X-Ali-Uid": "123456789",
    "X-Wy-Id": "test-wy-id"
}

SSE_HEADERS = {
    "Accept": "text/event-stream",
    "Cache-Control": "no-cache",
    "Authorization": "Bearer test-token",
    "X-Ali-Uid": "123456789",
    "X-Wy-Id": "test-wy-id"
}


async def sse_connection_test(connection_id: int, session_id: str, duration: int) -> Dict[str, Any]:
    """测试SSE连接（包含历史消息加载）"""
    start_time = time.time()
    messages_received = 0
    errors = []
    
    try:
        async with httpx.AsyncClient(timeout=httpx.Timeout(duration + 5)) as client:
            url = f"{BASE_URL}/api/sessions/stream"
            # 使用特殊的last_event_id触发历史消息加载
            params = {
                "session_id": session_id,
                "last_event_id": "asdf"  # 触发历史消息加载
            }
            
            print(f"🔗 SSE连接 {connection_id}: 开始连接并加载历史消息...")
            
            async with client.stream("GET", url, params=params, headers=SSE_HEADERS) as response:
                if response.status_code != 200:
                    errors.append(f"连接失败: {response.status_code}")
                    return {"connection_id": connection_id, "success": False, "errors": errors}
                
                # 监听消息
                timeout_task = asyncio.create_task(asyncio.sleep(duration))
                
                async def listen_messages():
                    nonlocal messages_received
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            messages_received += 1
                            if messages_received <= 5:  # 只打印前5条消息
                                print(f"📨 SSE连接 {connection_id}: 收到消息 #{messages_received}")
                
                listen_task = asyncio.create_task(listen_messages())
                
                # 等待超时或连接结束
                done, pending = await asyncio.wait(
                    [timeout_task, listen_task],
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # 取消未完成的任务
                for task in pending:
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                
    except Exception as e:
        errors.append(f"SSE异常: {str(e)}")
    
    end_time = time.time()
    duration_actual = end_time - start_time
    
    return {
        "connection_id": connection_id,
        "session_id": session_id,
        "duration": duration_actual,
        "messages_received": messages_received,
        "errors": errors,
        "success": len(errors) == 0
    }


async def api_request_test(request_id: int) -> Dict[str, Any]:
    """测试普通API请求（用于检测是否被SSE阻塞）"""
    start_time = time.time()
    
    try:
        async with httpx.AsyncClient(timeout=httpx.Timeout(10)) as client:
            # 测试会话列表API
            url = f"{BASE_URL}/api/sessions/list"
            params = {"page_size": 10}
            
            response = await client.get(url, params=params, headers=TEST_HEADERS)
            
            end_time = time.time()
            duration = end_time - start_time
            
            success = response.status_code == 200
            if success:
                print(f"✅ API请求 {request_id}: 成功 ({duration:.3f}s)")
            else:
                print(f"❌ API请求 {request_id}: 失败 {response.status_code} ({duration:.3f}s)")
            
            return {
                "request_id": request_id,
                "duration": duration,
                "status_code": response.status_code,
                "success": success,
                "error": None
            }
            
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"❌ API请求 {request_id}: 异常 {str(e)} ({duration:.3f}s)")
        
        return {
            "request_id": request_id,
            "duration": duration,
            "status_code": 0,
            "success": False,
            "error": str(e)
        }


async def continuous_api_requests(num_requests: int, interval: float = 2.0) -> List[Dict[str, Any]]:
    """持续发送API请求，检测响应时间变化"""
    results = []
    
    for i in range(num_requests):
        result = await api_request_test(i + 1)
        results.append(result)
        
        if i < num_requests - 1:  # 最后一次不需要等待
            await asyncio.sleep(interval)
    
    return results


async def run_blocking_test():
    """运行阻塞测试"""
    print("🧪 SSE阻塞修复效果测试")
    print(f"配置: {SSE_CONNECTIONS}个SSE连接, {API_REQUESTS}个API请求")
    print(f"测试时长: {TEST_DURATION}秒")
    print("="*60)
    
    # 创建任务
    tasks = []
    
    # SSE连接任务（包含历史消息加载）
    for i in range(SSE_CONNECTIONS):
        session_id = f"test-session-{i}-{int(time.time())}"
        task = sse_connection_test(i + 1, session_id, TEST_DURATION)
        tasks.append(("sse", task))
    
    # 持续API请求任务
    api_task = continuous_api_requests(API_REQUESTS, interval=3.0)
    tasks.append(("api", api_task))
    
    # 并发执行所有任务
    print("🚀 开始并发测试...")
    start_time = time.time()
    
    results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    print(f"\n✅ 测试完成，总耗时: {total_duration:.2f}秒")
    
    # 分析结果
    sse_results = []
    api_results = []
    
    for i, (task_type, _) in enumerate(tasks):
        result = results[i]
        if isinstance(result, Exception):
            print(f"❌ 任务异常: {result}")
            continue
            
        if task_type == "sse":
            sse_results.append(result)
        elif task_type == "api":
            api_results.extend(result)
    
    return sse_results, api_results


def analyze_blocking_results(sse_results: List[Dict[str, Any]], api_results: List[Dict[str, Any]]):
    """分析阻塞测试结果"""
    print("\n" + "="*60)
    print("📊 阻塞测试结果分析")
    print("="*60)
    
    # SSE连接分析
    print(f"\n🔗 SSE连接分析:")
    successful_sse = [r for r in sse_results if r["success"]]
    print(f"  成功连接: {len(successful_sse)}/{len(sse_results)}")
    
    if successful_sse:
        avg_messages = sum(r["messages_received"] for r in successful_sse) / len(successful_sse)
        print(f"  平均接收消息数: {avg_messages:.1f}")
        
        for result in successful_sse:
            print(f"  连接 {result['connection_id']}: {result['messages_received']} 消息, {result['duration']:.2f}s")
    
    # API请求分析
    print(f"\n🌐 API请求分析:")
    successful_api = [r for r in api_results if r["success"]]
    print(f"  成功请求: {len(successful_api)}/{len(api_results)}")
    
    if successful_api:
        durations = [r["duration"] for r in successful_api]
        avg_duration = sum(durations) / len(durations)
        max_duration = max(durations)
        min_duration = min(durations)
        
        print(f"  平均响应时间: {avg_duration:.3f}s")
        print(f"  最快响应时间: {min_duration:.3f}s")
        print(f"  最慢响应时间: {max_duration:.3f}s")
        
        # 检查是否有明显的阻塞
        if max_duration > avg_duration * 3:
            print(f"  ⚠️  检测到可能的阻塞: 最慢请求是平均时间的 {max_duration/avg_duration:.1f} 倍")
        else:
            print(f"  ✅ 响应时间稳定，无明显阻塞")
    
    # 总体评估
    print(f"\n🎯 总体评估:")
    if len(successful_sse) == len(sse_results) and len(successful_api) == len(api_results):
        if successful_api and max(r["duration"] for r in successful_api) < 1.0:
            print("  🚀 优秀: 所有连接成功，API响应时间稳定")
        else:
            print("  ✅ 良好: 所有连接成功，但API响应时间较慢")
    else:
        print("  ⚠️  需要优化: 存在连接失败或请求失败")


async def main():
    """主函数"""
    try:
        sse_results, api_results = await run_blocking_test()
        analyze_blocking_results(sse_results, api_results)
    except Exception as e:
        print(f"❌ 测试异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
