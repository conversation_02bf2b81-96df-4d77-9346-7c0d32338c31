#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通用公共参数功能 - 推荐方案
支持GET和POST请求，支持查询参数和请求头两种方式
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_get_with_query_params():
    """测试GET请求 - 查询参数方式"""
    print("=== 测试GET请求 - 查询参数方式 ===")
    
    url = f"{BASE_URL}/api/files/session/test_session"
    params = {
        "file_types": "sessionFile",
        "limit": 10,
        "offset": 0,
        # 公共参数
        "loginToken": "test_token_123",
        "sessionId": "test_session_456", 
        "regionId": "test_region_789"
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"请求失败: {e}")


def test_get_with_headers():
    """测试GET请求 - 请求头方式"""
    print("\n=== 测试GET请求 - 请求头方式 ===")
    
    url = f"{BASE_URL}/api/files/session/test_session"
    params = {
        "file_types": "sessionFile",
        "limit": 10,
        "offset": 0
    }
    headers = {
        "X-Login-Token": "test_token_123",
        "X-Session-Id": "test_session_456",
        "X-Region-Id": "test_region_789"
    }
    
    try:
        response = requests.get(url, params=params, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"请求失败: {e}")


def test_post_with_headers():
    """测试POST请求 - 请求头方式"""
    print("\n=== 测试POST请求 - 请求头方式 ===")
    
    url = f"{BASE_URL}/api/files/download-urls"
    data = {
        "file_ids": [1, 2, 3],
        "expires": 7200
    }
    headers = {
        "Content-Type": "application/json",
        "X-Login-Token": "test_token_123",
        "X-Session-Id": "test_session_456",
        "X-Region-Id": "test_region_789"
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"请求失败: {e}")


def test_post_with_query_params():
    """测试POST请求 - 查询参数方式"""
    print("\n=== 测试POST请求 - 查询参数方式 ===")
    
    url = f"{BASE_URL}/api/files/download-urls"
    params = {
        "loginToken": "test_token_123",
        "sessionId": "test_session_456",
        "regionId": "test_region_789"
    }
    data = {
        "file_ids": [1, 2, 3],
        "expires": 7200
    }
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, params=params, json=data, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"请求失败: {e}")


def test_post_with_body_params():
    """测试POST请求 - 请求体包含公共参数"""
    print("\n=== 测试POST请求 - 请求体包含公共参数 ===")
    
    url = f"{BASE_URL}/api/files/download-urls"
    data = {
        "file_ids": [1, 2, 3],
        "expires": 7200,
        # 公共参数直接在请求体中
        "loginToken": "test_token_123",
        "sessionId": "test_session_456",
        "regionId": "test_region_789"
    }
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"请求失败: {e}")


def test_mixed_priority():
    """测试混合方式 - 验证优先级"""
    print("\n=== 测试混合方式 - 验证优先级 ===")
    print("查询参数优先级 > 请求头优先级")
    
    url = f"{BASE_URL}/api/files/session/test_session"
    params = {
        "file_types": "sessionFile",
        "limit": 10,
        "offset": 0,
        # 查询参数中的公共参数
        "loginToken": "query_token",
        "sessionId": "query_session",
        "regionId": "query_region"
    }
    headers = {
        # 请求头中的公共参数（应该被查询参数覆盖）
        "X-Login-Token": "header_token",
        "X-Session-Id": "header_session",
        "X-Region-Id": "header_region"
    }
    
    try:
        response = requests.get(url, params=params, headers=headers)
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {result}")
        
        # 验证优先级
        if "query" in str(result):
            print("✅ 查询参数优先级正确")
        else:
            print("❌ 优先级可能有问题")
            
    except Exception as e:
        print(f"请求失败: {e}")


if __name__ == "__main__":
    print("开始测试通用公共参数功能...")
    print("请确保服务已启动在 http://localhost:8000")
    
    # 运行所有测试
    test_get_with_query_params()
    test_get_with_headers()
    test_post_with_headers()
    test_post_with_query_params()
    test_post_with_body_params()
    test_mixed_priority()
    
    print("\n测试完成！")
