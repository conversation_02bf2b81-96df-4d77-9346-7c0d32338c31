#!/usr/bin/env python3
"""
SSE接口性能测试脚本
测试 /sessions/stream 接口的并发连接能力和实时性
"""

import asyncio
import time
import httpx
import json
from typing import List, Dict, Any
import statistics
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:8000"
CONCURRENT_CONNECTIONS = 20  # 并发SSE连接数
TEST_DURATION = 30  # 测试持续时间（秒）

# 模拟认证头
TEST_HEADERS = {
    "Accept": "text/event-stream",
    "Cache-Control": "no-cache",
    "Authorization": "Bearer test-token",
    "X-Ali-Uid": "123456789",
    "X-Wy-Id": "test-wy-id"
}


class SSEConnectionTester:
    """SSE连接测试器"""
    
    def __init__(self, connection_id: int, session_id: str):
        self.connection_id = connection_id
        self.session_id = session_id
        self.messages_received = 0
        self.heartbeats_received = 0
        self.errors = []
        self.start_time = None
        self.end_time = None
        self.first_message_time = None
        self.last_message_time = None
        
    async def connect_and_listen(self, duration: int) -> Dict[str, Any]:
        """连接SSE并监听消息"""
        self.start_time = time.time()
        
        try:
            async with httpx.AsyncClient(timeout=httpx.Timeout(duration + 10)) as client:
                url = f"{BASE_URL}/api/sessions/stream"
                params = {"session_id": self.session_id}
                
                print(f"🔗 连接 {self.connection_id}: 开始连接 {url}")
                
                async with client.stream(
                    "GET", 
                    url, 
                    params=params, 
                    headers=TEST_HEADERS
                ) as response:
                    
                    if response.status_code != 200:
                        error_msg = f"连接失败: {response.status_code}"
                        self.errors.append(error_msg)
                        print(f"❌ 连接 {self.connection_id}: {error_msg}")
                        return self._get_result()
                    
                    print(f"✅ 连接 {self.connection_id}: 连接成功，开始监听...")
                    
                    # 监听消息，直到超时
                    timeout_task = asyncio.create_task(asyncio.sleep(duration))
                    listen_task = asyncio.create_task(self._listen_messages(response))
                    
                    # 等待任一任务完成
                    done, pending = await asyncio.wait(
                        [timeout_task, listen_task],
                        return_when=asyncio.FIRST_COMPLETED
                    )
                    
                    # 取消未完成的任务
                    for task in pending:
                        task.cancel()
                        try:
                            await task
                        except asyncio.CancelledError:
                            pass
                    
                    self.end_time = time.time()
                    print(f"🏁 连接 {self.connection_id}: 测试完成")
                    
        except Exception as e:
            self.end_time = time.time()
            error_msg = f"连接异常: {str(e)}"
            self.errors.append(error_msg)
            print(f"❌ 连接 {self.connection_id}: {error_msg}")
        
        return self._get_result()
    
    async def _listen_messages(self, response):
        """监听SSE消息"""
        try:
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    await self._process_message(line[6:])  # 去掉 "data: " 前缀
                    
        except Exception as e:
            error_msg = f"消息监听异常: {str(e)}"
            self.errors.append(error_msg)
            print(f"⚠️  连接 {self.connection_id}: {error_msg}")
    
    async def _process_message(self, data: str):
        """处理接收到的消息"""
        try:
            current_time = time.time()
            
            if self.first_message_time is None:
                self.first_message_time = current_time
                print(f"📨 连接 {self.connection_id}: 收到第一条消息")
            
            self.last_message_time = current_time
            
            # 尝试解析JSON
            try:
                message = json.loads(data)
                if message.get("type") == "heartbeat":
                    self.heartbeats_received += 1
                else:
                    self.messages_received += 1
                    print(f"📨 连接 {self.connection_id}: 收到消息 #{self.messages_received}")
            except json.JSONDecodeError:
                # 非JSON消息也计数
                self.messages_received += 1
                
        except Exception as e:
            error_msg = f"消息处理异常: {str(e)}"
            self.errors.append(error_msg)
    
    def _get_result(self) -> Dict[str, Any]:
        """获取测试结果"""
        duration = (self.end_time - self.start_time) if self.end_time and self.start_time else 0
        first_message_delay = (self.first_message_time - self.start_time) if self.first_message_time and self.start_time else None
        
        return {
            "connection_id": self.connection_id,
            "session_id": self.session_id,
            "duration": duration,
            "messages_received": self.messages_received,
            "heartbeats_received": self.heartbeats_received,
            "total_events": self.messages_received + self.heartbeats_received,
            "first_message_delay": first_message_delay,
            "errors": self.errors,
            "success": len(self.errors) == 0 and duration > 0
        }


async def run_sse_performance_test(concurrent_connections: int, duration: int) -> List[Dict[str, Any]]:
    """运行SSE性能测试"""
    print(f"🚀 开始SSE性能测试")
    print(f"并发连接数: {concurrent_connections}")
    print(f"测试持续时间: {duration}秒")
    print(f"目标服务: {BASE_URL}")
    print("="*60)
    
    # 创建测试器
    testers = []
    for i in range(concurrent_connections):
        session_id = f"test-session-{i}-{int(time.time())}"
        tester = SSEConnectionTester(i + 1, session_id)
        testers.append(tester)
    
    # 并发运行所有连接
    start_time = time.time()
    tasks = [tester.connect_and_listen(duration) for tester in testers]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    end_time = time.time()
    
    # 处理异常结果
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            processed_results.append({
                "connection_id": i + 1,
                "session_id": f"test-session-{i}",
                "duration": 0,
                "messages_received": 0,
                "heartbeats_received": 0,
                "total_events": 0,
                "first_message_delay": None,
                "errors": [str(result)],
                "success": False
            })
        else:
            processed_results.append(result)
    
    total_duration = end_time - start_time
    print(f"\n✅ 测试完成，总耗时: {total_duration:.2f}秒")
    
    return processed_results


def analyze_sse_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析SSE测试结果"""
    successful_connections = [r for r in results if r["success"]]
    failed_connections = [r for r in results if not r["success"]]
    
    if not successful_connections:
        return {
            "total_connections": len(results),
            "successful_connections": 0,
            "failed_connections": len(failed_connections),
            "success_rate": 0.0,
            "error": "所有连接都失败了"
        }
    
    # 统计数据
    durations = [r["duration"] for r in successful_connections]
    total_messages = [r["messages_received"] for r in successful_connections]
    total_heartbeats = [r["heartbeats_received"] for r in successful_connections]
    first_message_delays = [r["first_message_delay"] for r in successful_connections if r["first_message_delay"] is not None]
    
    analysis = {
        "total_connections": len(results),
        "successful_connections": len(successful_connections),
        "failed_connections": len(failed_connections),
        "success_rate": len(successful_connections) / len(results) * 100,
        
        # 连接持续时间
        "avg_duration": statistics.mean(durations) if durations else 0,
        "min_duration": min(durations) if durations else 0,
        "max_duration": max(durations) if durations else 0,
        
        # 消息统计
        "total_messages_all": sum(total_messages),
        "total_heartbeats_all": sum(total_heartbeats),
        "avg_messages_per_connection": statistics.mean(total_messages) if total_messages else 0,
        "avg_heartbeats_per_connection": statistics.mean(total_heartbeats) if total_heartbeats else 0,
        
        # 响应时间
        "avg_first_message_delay": statistics.mean(first_message_delays) if first_message_delays else None,
        "max_first_message_delay": max(first_message_delays) if first_message_delays else None,
        
        # 错误统计
        "all_errors": [error for r in failed_connections for error in r["errors"]]
    }
    
    return analysis


def print_sse_analysis(analysis: Dict[str, Any]):
    """打印SSE分析结果"""
    print("\n" + "="*60)
    print("📊 SSE性能测试结果分析")
    print("="*60)
    
    print(f"总连接数: {analysis['total_connections']}")
    print(f"成功连接: {analysis['successful_connections']}")
    print(f"失败连接: {analysis['failed_connections']}")
    print(f"连接成功率: {analysis['success_rate']:.1f}%")
    
    if analysis['successful_connections'] > 0:
        print(f"\n⏱️  连接持续时间:")
        print(f"  平均持续时间: {analysis['avg_duration']:.2f}s")
        print(f"  最短持续时间: {analysis['min_duration']:.2f}s")
        print(f"  最长持续时间: {analysis['max_duration']:.2f}s")
        
        print(f"\n📨 消息统计:")
        print(f"  总消息数: {analysis['total_messages_all']}")
        print(f"  总心跳数: {analysis['total_heartbeats_all']}")
        print(f"  平均每连接消息数: {analysis['avg_messages_per_connection']:.1f}")
        print(f"  平均每连接心跳数: {analysis['avg_heartbeats_per_connection']:.1f}")
        
        if analysis['avg_first_message_delay'] is not None:
            print(f"\n🚀 响应时间:")
            print(f"  平均首消息延迟: {analysis['avg_first_message_delay']:.3f}s")
            print(f"  最大首消息延迟: {analysis['max_first_message_delay']:.3f}s")
        
        # 性能评级
        success_rate = analysis['success_rate']
        if success_rate >= 95:
            print(f"\n🚀 连接稳定性: 优秀 (成功率 {success_rate:.1f}%)")
        elif success_rate >= 90:
            print(f"\n✅ 连接稳定性: 良好 (成功率 {success_rate:.1f}%)")
        elif success_rate >= 80:
            print(f"\n⚠️  连接稳定性: 一般 (成功率 {success_rate:.1f}%)")
        else:
            print(f"\n❌ 连接稳定性: 需要优化 (成功率 {success_rate:.1f}%)")
    
    if analysis['failed_connections'] > 0:
        print(f"\n❌ 错误信息:")
        error_counts = {}
        for error in analysis['all_errors']:
            error_counts[error] = error_counts.get(error, 0) + 1
        
        for error, count in error_counts.items():
            print(f"  {error} (出现 {count} 次)")


async def main():
    """主函数"""
    print("🧪 SSE接口性能测试开始")
    
    # 运行测试
    results = await run_sse_performance_test(CONCURRENT_CONNECTIONS, TEST_DURATION)
    
    # 分析结果
    analysis = analyze_sse_results(results)
    
    # 打印结果
    print_sse_analysis(analysis)
    
    # 性能建议
    if analysis['successful_connections'] > 0:
        success_rate = analysis['success_rate']
        avg_delay = analysis.get('avg_first_message_delay', 0)
        
        print(f"\n💡 性能建议:")
        if success_rate >= 95 and (avg_delay is None or avg_delay < 0.1):
            print("  🚀 SSE性能优秀，可以支持更高并发")
        elif success_rate >= 90:
            print("  ✅ SSE性能良好，建议监控高峰期表现")
        else:
            print("  ⚠️  SSE性能需要优化，检查服务器资源和网络")


if __name__ == "__main__":
    asyncio.run(main())
