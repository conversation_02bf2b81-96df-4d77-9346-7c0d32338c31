#!/usr/bin/env python3
"""
测试 RAG OSS 配置和连接
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def test_rag_oss_configuration():
    """测试 RAG OSS 配置"""
    
    print("=" * 80)
    print("测试 RAG OSS 配置")
    print("=" * 80)
    
    try:
        # 导入配置管理器
        from src.shared.config.environments import env_manager
        
        # 获取当前环境配置
        config = env_manager.get_config()
        
        print(f"当前环境: {env_manager.current_env.value}")
        print(f"RAG OSS 配置:")
        print(f"  - rag_oss_enabled: {getattr(config, 'rag_oss_enabled', 'N/A')}")
        print(f"  - rag_oss_bucket_name: {getattr(config, 'rag_oss_bucket_name', 'N/A')}")
        print(f"  - rag_oss_region: {getattr(config, 'rag_oss_region', 'N/A')}")
        print(f"  - rag_oss_endpoint: {getattr(config, 'rag_oss_endpoint', 'N/A')}")
        print(f"  - ram_role_arn: {getattr(config, 'ram_role_arn', 'N/A')}")
        print(f"  - region_id: {getattr(config, 'region_id', 'N/A')}")
        print(f"  - app_group: {getattr(config, 'app_group', 'N/A')}")
        print(f"  - akless_env: {getattr(config, 'akless_env', 'N/A')}")
        
        # 检查必要的配置项
        bucket_name = getattr(config, 'rag_oss_bucket_name', '')
        if not bucket_name:
            print("❌ 错误: rag_oss_bucket_name 未配置")
            return False
        
        if bucket_name == 'doc_details':
            print("❌ 错误: rag_oss_bucket_name 配置为错误的值 'doc_details'")
            return False
        
        print(f"✅ RAG OSS bucket 配置正确: {bucket_name}")
        
        # 测试 OSS 服务初始化
        print("\n" + "-" * 40)
        print("测试 OSS 服务初始化")
        print("-" * 40)
        
        from src.infrastructure.oss.oss_service import oss_service
        
        if oss_service.rag_oss_client:
            print("✅ RAG OSS 客户端初始化成功")
            
            # 获取 RAG OSS 配置信息
            rag_config = oss_service.rag_oss_client.get_config()
            if rag_config:
                print(f"  - Bucket: {rag_config.bucket_name}")
                print(f"  - Region: {rag_config.region}")
                print(f"  - Endpoint: {rag_config.endpoint}")
                
                if rag_config.bucket_name == bucket_name:
                    print("✅ RAG OSS 客户端 bucket 配置一致")
                else:
                    print(f"❌ RAG OSS 客户端 bucket 配置不一致: 期望 {bucket_name}, 实际 {rag_config.bucket_name}")
                    return False
            else:
                print("❌ 无法获取 RAG OSS 配置")
                return False
        else:
            print("❌ RAG OSS 客户端未初始化")
            return False
        
        # 测试路径处理
        print("\n" + "-" * 40)
        print("测试路径处理逻辑")
        print("-" * 40)
        
        test_path = "doc_details/20250805/document-eed541e2-f2a0-4a63-a0c1-693312c04601/chunk_parse_result.json"
        expected_path = "20250805/document-eed541e2-f2a0-4a63-a0c1-693312c04601/chunk_parse_result.json"
        
        # 模拟路径处理逻辑
        actual_object_key = test_path
        if test_path.startswith('doc_details/'):
            actual_object_key = test_path[len('doc_details/'):]
        
        print(f"原始路径: {test_path}")
        print(f"处理后路径: {actual_object_key}")
        
        if actual_object_key == expected_path:
            print("✅ 路径处理逻辑正确")
        else:
            print(f"❌ 路径处理逻辑错误: 期望 {expected_path}, 实际 {actual_object_key}")
            return False
        
        print("\n" + "=" * 80)
        print("✅ 所有测试通过！RAG OSS 配置正确")
        print("=" * 80)
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_rag_oss_configuration()
    sys.exit(0 if success else 1)
