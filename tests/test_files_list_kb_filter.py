#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 /files/list 接口的知识库过滤功能
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


class TestFilesListKbFilter(unittest.TestCase):
    """测试文件列表知识库过滤功能"""

    def setUp(self):
        """测试前准备"""
        from src.domain.services.file_service import FileService
        from src.application.file_api_models import FileInfoResponse
        
        self.file_service = FileService()
        
        # 创建模拟的文件信息
        self.mock_files = [
            FileInfoResponse(
                file_id="file_001",
                artifact_id="artifact_001",
                file_name="文件1.pdf",
                file_size=1024,
                content_type="application/pdf",
                doc_id="doc_001",
                artifact_type="sessionFile",
                upload_status="completed",
                download_url="http://example.com/file1",
                gmt_created="2025-08-04T10:00:00Z",
                gmt_modified="2025-08-04T10:00:00Z",
                is_in_kb=False
            ),
            FileInfoResponse(
                file_id="file_002",
                artifact_id="artifact_002",
                file_name="文件2.docx",
                file_size=2048,
                content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                doc_id="doc_002",
                artifact_type="sessionFile",
                upload_status="completed",
                download_url="http://example.com/file2",
                gmt_created="2025-08-04T10:00:00Z",
                gmt_modified="2025-08-04T10:00:00Z",
                is_in_kb=False
            ),
            FileInfoResponse(
                file_id="file_003",
                artifact_id="artifact_003",
                file_name="文件3.txt",
                file_size=512,
                content_type="text/plain",
                doc_id="doc_003",
                artifact_type="sessionFile",
                upload_status="completed",
                download_url="http://example.com/file3",
                gmt_created="2025-08-04T10:00:00Z",
                gmt_modified="2025-08-04T10:00:00Z",
                is_in_kb=False
            )
        ]

    @patch('src.domain.services.knowledge_service.knowledgebase_service')
    def test_set_files_kb_relationship_with_kb_id(self, mock_kb_service):
        """测试带知识库ID的文件关系设置"""
        # 模拟知识库服务返回
        mock_kb_service.is_knowledge_base_file.return_value = {
            "file_001": True,
            "file_002": False,
            "file_003": True
        }
        
        # 调用方法
        self.file_service.set_files_kb_relationship(
            files=self.mock_files,
            session_id="sess_test_123",
            kb_id="kb_test_456"
        )
        
        # 验证结果
        self.assertTrue(self.mock_files[0].is_in_kb)   # file_001 -> True
        self.assertFalse(self.mock_files[1].is_in_kb)  # file_002 -> False
        self.assertTrue(self.mock_files[2].is_in_kb)   # file_003 -> True
        
        # 验证知识库服务被正确调用
        mock_kb_service.is_knowledge_base_file.assert_called_once_with(
            kb_id="kb_test_456",
            session_id="sess_test_123",
            file_id_list=["file_001", "file_002", "file_003"]
        )

    def test_set_files_kb_relationship_without_kb_id(self):
        """测试不带知识库ID的文件关系设置"""
        # 调用方法（不传kb_id）
        self.file_service.set_files_kb_relationship(
            files=self.mock_files,
            session_id="sess_test_123",
            kb_id=None
        )
        
        # 验证所有文件的is_in_kb都为False
        for file_info in self.mock_files:
            self.assertFalse(file_info.is_in_kb)

    def test_set_files_kb_relationship_empty_files(self):
        """测试空文件列表的情况"""
        # 调用方法
        self.file_service.set_files_kb_relationship(
            files=[],
            session_id="sess_test_123",
            kb_id="kb_test_456"
        )
        
        # 应该正常执行，不抛出异常
        self.assertTrue(True)

    @patch('src.domain.services.knowledge_service.knowledgebase_service')
    def test_set_files_kb_relationship_service_failure(self, mock_kb_service):
        """测试知识库服务调用失败的情况"""
        # 模拟知识库服务抛出异常
        mock_kb_service.is_knowledge_base_file.side_effect = Exception("KB service error")
        
        # 调用方法
        self.file_service.set_files_kb_relationship(
            files=self.mock_files,
            session_id="sess_test_123",
            kb_id="kb_test_456"
        )
        
        # 验证异常被正确处理，所有文件的is_in_kb都设置为False
        for file_info in self.mock_files:
            self.assertFalse(file_info.is_in_kb)

    def test_file_info_response_model(self):
        """测试FileInfoResponse模型的is_in_kb字段"""
        from src.application.file_api_models import FileInfoResponse
        
        # 测试默认值
        file_info_default = FileInfoResponse(
            file_id="test_file",
            file_name="test.pdf",
            artifact_type="sessionFile",
            upload_status="completed",
            gmt_created="2025-08-04T10:00:00Z",
            gmt_modified="2025-08-04T10:00:00Z"
        )
        self.assertFalse(file_info_default.is_in_kb)
        
        # 测试设置为True
        file_info_true = FileInfoResponse(
            file_id="test_file",
            file_name="test.pdf",
            artifact_type="sessionFile",
            upload_status="completed",
            gmt_created="2025-08-04T10:00:00Z",
            gmt_modified="2025-08-04T10:00:00Z",
            is_in_kb=True
        )
        self.assertTrue(file_info_true.is_in_kb)

    def test_session_files_request_model(self):
        """测试SessionFilesRequest模型的kb_id字段"""
        from src.application.file_api_models import SessionFilesRequest
        
        # 测试不带kb_id
        request_without_kb = SessionFilesRequest(
            session_id="sess_123"
        )
        self.assertIsNone(request_without_kb.kb_id)
        
        # 测试带kb_id
        request_with_kb = SessionFilesRequest(
            session_id="sess_123",
            kb_id="kb_456"
        )
        self.assertEqual(request_with_kb.kb_id, "kb_456")

    def test_from_orm_model_with_is_in_kb(self):
        """测试from_orm_model方法支持is_in_kb参数"""
        from src.application.file_api_models import FileInfoResponse
        
        # 创建模拟的ORM对象
        from datetime import datetime
        class MockOrmObj:
            def __init__(self):
                self.id = 123
                self.artifact_id = "artifact_123"
                self.title = "test_file.pdf"
                self.file_size = 1024
                self.content_type = "application/pdf"
                self.doc_id = "doc_123"
                self.type = "sessionFile"
                self.upload_status = "completed"
                self.download_url = "http://example.com/file"
                self.gmt_created = datetime.now()
                self.gmt_modified = datetime.now()
        
        mock_orm = MockOrmObj()
        
        # 测试默认is_in_kb=False
        file_info_default = FileInfoResponse.from_orm_model(mock_orm)
        self.assertFalse(file_info_default.is_in_kb)
        
        # 测试设置is_in_kb=True
        file_info_true = FileInfoResponse.from_orm_model(mock_orm, is_in_kb=True)
        self.assertTrue(file_info_true.is_in_kb)


def test_api_structure():
    """测试API结构"""
    print("=== 测试API结构 ===")
    
    # 检查file_routes.py中的修改
    routes_file = "src/presentation/api/routes/file_routes.py"
    with open(routes_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ('知识库关系设置调用', 'file_service.set_files_kb_relationship'),
        ('传递kb_id参数', 'kb_id=request.kb_id'),
        ('传递session_id参数', 'session_id=request.session_id')
    ]
    
    for check_name, check_pattern in checks:
        if check_pattern in content:
            print(f"✓ {check_name}: 存在")
        else:
            print(f"❌ {check_name}: 不存在")


def test_integration_scenario():
    """集成测试场景"""
    print("\n=== 集成测试场景 ===")
    
    # 模拟完整的API调用流程
    print("1. 客户端调用 POST /files/list 带 kb_id 参数")
    print("2. 接口获取会话文件列表")
    print("3. 调用知识库服务判断文件是否属于知识库")
    print("4. 为每个文件设置 is_in_kb 字段")
    print("5. 返回包含知识库信息的文件列表")
    
    # 预期的API请求格式
    expected_request = {
        "session_id": "sess_123",
        "kb_id": "kb_456",
        "max_results": 20
    }
    
    # 预期的API响应格式
    expected_response = {
        "code": "OK",
        "success": True,
        "data": {
            "data": [
                {
                    "file_id": "file_001",
                    "file_name": "文件1.pdf",
                    "is_in_kb": True
                },
                {
                    "file_id": "file_002",
                    "file_name": "文件2.docx",
                    "is_in_kb": False
                }
            ],
            "max_result": 2,
            "next_token": None
        }
    }
    
    print(f"\n预期请求格式:")
    import json
    print(json.dumps(expected_request, indent=2, ensure_ascii=False))
    
    print(f"\n预期响应格式:")
    print(json.dumps(expected_response, indent=2, ensure_ascii=False))


def main():
    """主测试函数"""
    print("开始测试 /files/list 知识库过滤功能")
    print("=" * 60)
    
    # 运行单元测试
    unittest.main(verbosity=2, exit=False)
    
    # 运行结构测试
    test_api_structure()
    
    # 运行集成测试
    test_integration_scenario()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("\n功能总结:")
    print("✅ /files/list 接口支持 kb_id 参数")
    print("✅ 当提供 kb_id 时，调用知识库服务判断文件关系")
    print("✅ 每个文件都会添加 is_in_kb 字段")
    print("✅ 支持异常处理，确保接口稳定性")
    print("✅ 与 /sessions/list 接口逻辑保持一致")


if __name__ == '__main__':
    main()
