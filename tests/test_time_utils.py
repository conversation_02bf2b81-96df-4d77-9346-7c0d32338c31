#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间工具类
提供时间格式转换功能
"""

from datetime import datetime, timezone
from typing import Optional


class TimeUtils:
    """时间工具类"""

    @staticmethod
    def to_iso8601_utc(dt: datetime) -> str:
        """
        将 datetime 转换为 ISO 8601 格式的字符串，使用 UTC+0 时间
        
        Args:
            dt: datetime 对象
            
        Returns:
            str: ISO 8601 格式的字符串，如 "2017-12-08T22:40Z"
            
        Raises:
            ValueError: 如果输入的 dt 为 None
        """
        if dt is None:
            raise ValueError("datetime 对象不能为 None")
        
        # 如果 datetime 没有时区信息，假设为 UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        
        # 转换为 UTC 时间
        utc_dt = dt.astimezone(timezone.utc)
        
        # 格式化为 ISO 8601 格式，去掉微秒，使用 Z 表示 UTC
        return utc_dt.strftime("%Y-%m-%dT%H:%MZ")
    
    @staticmethod
    def from_iso8601_utc(iso_str: str) -> datetime:
        """
        将 ISO 8601 格式的字符串转换为 datetime 对象
        
        Args:
            iso_str: ISO 8601 格式的字符串，如 "2017-12-08T22:40Z"
            
        Returns:
            datetime: 对应的 datetime 对象（UTC 时区）
            
        Raises:
            ValueError: 如果字符串格式不正确
        """
        if not iso_str:
            raise ValueError("ISO 8601 字符串不能为空")
        
        try:
            # 解析 ISO 8601 格式的字符串
            dt = datetime.fromisoformat(iso_str.replace('Z', '+00:00'))
            return dt
        except ValueError as e:
            raise ValueError(f"无效的 ISO 8601 格式: {iso_str}") from e
    
    @staticmethod
    def now_iso8601_utc() -> str:
        """
        获取当前时间的 ISO 8601 格式字符串（UTC）
        
        Returns:
            str: 当前时间的 ISO 8601 格式字符串
        """
        return TimeUtils.to_iso8601_utc(datetime.now(timezone.utc))
    
    @staticmethod
    def format_datetime(dt: datetime, format_str: str = "%Y-%m-%dT%H:%MZ") -> str:
        """
        将 datetime 格式化为指定格式的字符串
        
        Args:
            dt: datetime 对象
            format_str: 格式字符串，默认为 ISO 8601 格式
            
        Returns:
            str: 格式化后的字符串
        """
        if dt is None:
            raise ValueError("datetime 对象不能为 None")
        
        # 如果 datetime 没有时区信息，假设为 UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        
        # 转换为 UTC 时间
        utc_dt = dt.astimezone(timezone.utc)
        
        return utc_dt.strftime(format_str)


# 创建全局实例，方便直接调用
time_utils = TimeUtils()
```

## 功能特点

1. **ISO 8601 标准格式**：输出格式为 `yyyy-MM-ddTHH:mmZ`
2. **UTC+0 时间**：所有时间都转换为 UTC 时区
3. **自动时区处理**：
   - 如果输入 datetime 没有时区信息，假设为 UTC
   - 如果输入 datetime 有其他时区，自动转换为 UTC
4. **微秒处理**：自动移除微秒部分
5. **错误处理**：对 None 值和无效格式进行适当的错误处理
6. **双向转换**：支持 datetime 到字符串和字符串到 datetime 的转换
7. **全局实例**：提供 `time_utils` 全局实例，方便直接调用

## 使用示例

```python
from src.domain.utils.time_utils import time_utils
from datetime import datetime, timezone

# 转换 datetime 为 ISO 8601 格式
dt = datetime(2017, 12, 8, 22, 40, 0, tzinfo=timezone.utc)
iso_str = time_utils.to_iso8601_utc(dt)
print(iso_str)  # 输出: 2017-12-08T22:40Z

# 获取当前时间的 ISO 8601 格式
current_iso = time_utils.now_iso8601_utc()
print(current_iso)  # 输出当前时间的 ISO 8601 格式
```

这个工具类完全符合你的需求，提供了标准的 ISO 8601 格式转换功能。

```python:tests/test_time_utils.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间工具类测试
"""

import pytest
from datetime import datetime, timezone
from src.domain.utils.time_utils import TimeUtils, time_utils


class TestTimeUtils:
    """时间工具类测试"""

    def test_to_iso8601_utc_with_timezone(self):
        """测试带时区的 datetime 转换"""
        dt = datetime(2017, 12, 8, 22, 40, 0, tzinfo=timezone.utc)
        result = TimeUtils.to_iso8601_utc(dt)
        assert result == "2017-12-08T22:40Z"

    def test_to_iso8601_utc_without_timezone(self):
        """测试不带时区的 datetime 转换"""
        dt = datetime(2017, 12, 8, 22, 40, 0)
        result = TimeUtils.to_iso8601_utc(dt)
        assert result == "2017-12-08T22:40Z"

    def test_to_iso8601_utc_with_different_timezone(self):
        """测试不同时区的 datetime 转换"""
        # 北京时间 UTC+8
        beijing_tz = timezone.utc.replace(hour=8)
        dt = datetime(2017, 12, 8, 22, 40, 0, tzinfo=beijing_tz)
        result = TimeUtils.to_iso8601_utc(dt)
        # 北京时间 22:40 转换为 UTC 应该是 14:40
        assert result == "2017-12-08T14:40Z"

    def test_from_iso8601_utc(self):
        """测试 ISO 8601 字符串转 datetime"""
        iso_str = "2017-12-08T22:40Z"
        result = TimeUtils.from_iso8601_utc(iso_str)
        expected = datetime(2017, 12, 8, 22, 40, 0, tzinfo=timezone.utc)
        assert result == expected

    def test_now_iso8601_utc(self):
        """测试获取当前时间的 ISO 8601 格式"""
        result = TimeUtils.now_iso8601_utc()
        # 验证格式是否正确
        assert len(result) == 16  # "2017-12-08T22:40Z" 长度为 16
        assert result.endswith('Z')
        assert 'T' in result

    def test_format_datetime(self):
        """测试自定义格式"""
        dt = datetime(2017, 12, 8, 22, 40, 0, tzinfo=timezone.utc)
        result = TimeUtils.format_datetime(dt, "%Y年%m月%d日")
        assert result == "2017年12月08日"

    def test_none_datetime_error(self):
        """测试 None 值错误处理"""
        with pytest.raises(ValueError, match="datetime 对象不能为 None"):
            TimeUtils.to_iso8601_utc(None)

    def test_empty_iso_string_error(self):
        """测试空字符串错误处理"""
        with pytest.raises(ValueError, match="ISO 8601 字符串不能为空"):
            TimeUtils.from_iso8601_utc("")

    def test_invalid_iso_format_error(self):
        """测试无效格式错误处理"""
        with pytest.raises(ValueError, match="无效的 ISO 8601 格式"):
            TimeUtils.from_iso8601_utc("invalid-format")

    def test_global_instance(self):
        """测试全局实例"""
        dt = datetime(2017, 12, 8, 22, 40, 0, tzinfo=timezone.utc)
        result = time_utils.to_iso8601_utc(dt)
        assert result == "2017-12-08T22:40Z"

    def test_microseconds_removal(self):
        """测试微秒被移除"""
        dt = datetime(2017, 12, 8, 22, 40, 0, 123456, tzinfo=timezone.utc)
        result = TimeUtils.to_iso8601_utc(dt)
        assert result == "2017-12-08T22:40Z"  # 微秒被移除

    def test_round_trip_conversion(self):
        """测试往返转换"""
        original_dt = datetime(2017, 12, 8, 22, 40, 0, tzinfo=timezone.utc)
        iso_str = TimeUtils.to_iso8601_utc(original_dt)
        converted_dt = TimeUtils.from_iso8601_utc(iso_str)
        assert converted_dt == original_dt


if __name__ == "__main__":
    pytest.main([__file__])
```
