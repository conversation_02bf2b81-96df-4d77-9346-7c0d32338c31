#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试会话历史记录中知识库集成功能
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.presentation.api.routes.session_routes import _convert_event_to_dict


class MockEvent:
    """模拟事件对象"""
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)


class TestSessionHistoryKbIntegration(unittest.TestCase):
    """测试会话历史记录知识库集成功能"""

    def test_convert_event_to_dict_with_dict_input(self):
        """测试字典输入的转换"""
        event_dict = {
            'event_id': 'evt_123',
            'type': 'message',
            'content': 'Hello world',
            'message_id': 'msg_456'
        }
        
        result = _convert_event_to_dict(event_dict)
        
        # 验证结果是字典的副本
        self.assertEqual(result, event_dict)
        self.assertIsNot(result, event_dict)  # 确保是副本，不是同一个对象

    def test_convert_event_to_dict_with_object_input(self):
        """测试对象输入的转换"""
        event_obj = MockEvent(
            event_id='evt_789',
            type='assistant_message',
            content='AI response',
            message_id='msg_101',
            timestamp=1234567890
        )
        
        result = _convert_event_to_dict(event_obj)
        
        # 验证结果包含所有属性
        expected_keys = ['event_id', 'type', 'content', 'message_id', 'timestamp']
        for key in expected_keys:
            self.assertIn(key, result)
            self.assertEqual(result[key], getattr(event_obj, key))

    def test_convert_event_to_dict_with_mixed_attributes(self):
        """测试包含多种属性名称的对象"""
        event_obj = MockEvent(
            event_id='evt_mixed',
            eventId='evt_mixed_alt',  # 不同的命名风格
            type='user_message',
            message_id='msg_mixed',
            messageId='msg_mixed_alt',
            custom_field='custom_value'
        )
        
        result = _convert_event_to_dict(event_obj)
        
        # 验证所有属性都被包含
        self.assertIn('event_id', result)
        self.assertIn('eventId', result)
        self.assertIn('message_id', result)
        self.assertIn('messageId', result)
        self.assertIn('custom_field', result)

    def test_convert_event_to_dict_with_none_values(self):
        """测试包含None值的对象"""
        event_obj = MockEvent(
            event_id='evt_none',
            type='message',
            content=None,  # None值应该被跳过
            message_id='msg_none'
        )
        
        result = _convert_event_to_dict(event_obj)
        
        # 验证None值不被包含
        self.assertNotIn('content', result)
        self.assertIn('event_id', result)
        self.assertIn('message_id', result)

    def test_convert_event_to_dict_with_private_attributes(self):
        """测试包含私有属性的对象"""
        event_obj = MockEvent(
            event_id='evt_private',
            type='message',
            _private_attr='should_be_skipped'
        )
        
        result = _convert_event_to_dict(event_obj)
        
        # 验证私有属性被跳过
        self.assertNotIn('_private_attr', result)
        self.assertIn('event_id', result)
        self.assertIn('type', result)

    @patch('src.presentation.api.routes.session_routes.knowledgebase_service')
    def test_event_wrapping_with_kb_integration(self, mock_kb_service):
        """测试事件包装与知识库集成"""
        # 模拟知识库服务返回
        mock_kb_service.is_knowledge_base_message.return_value = {
            'msg_123': True,
            'msg_456': False
        }
        
        # 创建模拟事件
        events = [
            MockEvent(event_id='evt_1', message_id='msg_123', type='user_message'),
            MockEvent(event_id='evt_2', message_id='msg_456', type='assistant_message'),
            {'event_id': 'evt_3', 'message_id': 'msg_789', 'type': 'system_message'}
        ]
        
        # 模拟处理逻辑
        wrapped_events = []
        message_ids = []
        event_message_map = {}
        
        # 提取消息ID
        for event in events:
            if hasattr(event, 'message_id'):
                message_id = event.message_id
            elif isinstance(event, dict):
                message_id = event.get('message_id')
            else:
                continue
                
            if message_id:
                message_ids.append(message_id)
                if hasattr(event, 'event_id'):
                    event_id = event.event_id
                elif isinstance(event, dict):
                    event_id = event.get('event_id')
                else:
                    continue
                    
                if event_id:
                    event_message_map[event_id] = message_id
        
        # 调用知识库服务
        kb_results = mock_kb_service.is_knowledge_base_message.return_value
        
        # 包装事件
        for event in events:
            event_dict = _convert_event_to_dict(event)
            event_id = event_dict.get('event_id')
            
            is_in_kb = False
            if event_id and event_id in event_message_map:
                message_id = event_message_map[event_id]
                is_in_kb = kb_results.get(message_id, False)
            
            event_dict['is_in_kb'] = is_in_kb
            wrapped_events.append(event_dict)
        
        # 验证结果
        self.assertEqual(len(wrapped_events), 3)
        self.assertTrue(wrapped_events[0]['is_in_kb'])   # msg_123 -> True
        self.assertFalse(wrapped_events[1]['is_in_kb'])  # msg_456 -> False
        self.assertFalse(wrapped_events[2]['is_in_kb'])  # msg_789 -> False (not in kb_results)


if __name__ == '__main__':
    unittest.main()
