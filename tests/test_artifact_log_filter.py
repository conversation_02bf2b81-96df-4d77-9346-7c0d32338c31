#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试artifact_id已存在日志过滤功能
"""
import sys
import os
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

def test_artifact_log_filter():
    """测试artifact_id已存在的日志过滤"""
    print("=== 测试artifact_id已存在日志过滤 ===")
    
    # 创建临时日志文件
    with tempfile.NamedTemporaryFile(mode='w+', suffix='.log', delete=False) as temp_log:
        temp_log_path = temp_log.name
    
    try:
        # 导入并设置日志
        from shared.logging.logger import setup_logger, get_logger
        
        # 设置日志到临时文件
        setup_logger(
            level="DEBUG",
            file_path=temp_log_path,
            error_file_path=temp_log_path.replace('.log', '_error.log')
        )
        
        # 获取日志器
        logger = get_logger("test_artifact_filter")
        
        print("开始测试日志过滤...")
        
        # 测试正常的ERROR日志（应该被记录）
        logger.error("[FileDB] 创建文件记录失败: 数据库连接失败")
        logger.error("[MessageProcessor] 制品文件上传异常: 网络错误")
        
        # 测试artifact_id已存在的ERROR日志（应该被过滤）
        logger.error("[FileDB] 创建文件记录失败: artifact_id已存在: artifact-123456")
        logger.error("[MessageProcessor] 制品文件上传异常: artifact_id已存在: artifact-789012")
        
        # 测试其他级别的日志（应该被记录）
        logger.warning("[FileDB] 创建文件记录失败: artifact_id已存在: artifact-warning")
        logger.info("这是一条普通的INFO日志")
        
        # 读取日志文件内容
        with open(temp_log_path, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        print("\n生成的日志内容:")
        print(log_content)
        
        # 验证过滤效果
        lines = log_content.strip().split('\n')
        error_lines = [line for line in lines if 'ERROR' in line]
        artifact_error_lines = [line for line in lines if 'ERROR' in line and 'artifact_id已存在' in line]
        
        print(f"\n总日志行数: {len(lines)}")
        print(f"ERROR级别日志行数: {len(error_lines)}")
        print(f"包含'artifact_id已存在'的ERROR日志行数: {len(artifact_error_lines)}")
        
        # 检查过滤效果
        if len(artifact_error_lines) == 0:
            print("✅ 过滤成功：artifact_id已存在的ERROR日志已被过滤")
        else:
            print("❌ 过滤失败：仍有artifact_id已存在的ERROR日志")
            for line in artifact_error_lines:
                print(f"  未过滤的日志: {line}")
        
        # 检查WARNING级别的artifact_id已存在日志是否保留
        warning_artifact_lines = [line for line in lines if 'WARNING' in line and 'artifact_id已存在' in line]
        if len(warning_artifact_lines) > 0:
            print("✅ WARNING级别的artifact_id已存在日志正常保留")
        else:
            print("❌ WARNING级别的artifact_id已存在日志被误过滤")
        
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_log_path)
            error_log_path = temp_log_path.replace('.log', '_error.log')
            if os.path.exists(error_log_path):
                os.unlink(error_log_path)
        except:
            pass

if __name__ == "__main__":
    test_artifact_log_filter()
