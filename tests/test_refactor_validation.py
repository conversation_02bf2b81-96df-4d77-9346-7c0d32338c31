#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证重构结果的简化测试脚本
"""

import sys
import ast
import inspect
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


def test_knowledge_service_method_exists():
    """测试KnowledgeService中是否存在新方法"""
    print("=" * 80)
    print("🔍 验证KnowledgeService中的新方法")
    print("=" * 80)
    
    try:
        # 读取文件内容
        service_file = project_root / "src" / "domain" / "services" / "knowledge_service.py"
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查方法是否存在
        if "def get_kb_session_message_ids(" in content:
            print("✅ 找到get_kb_session_message_ids方法")
        else:
            print("❌ 未找到get_kb_session_message_ids方法")
            return False
        
        # 检查方法签名
        if "auth_context: AuthContext" in content and "kb_id: str" in content and "session_id: str" in content:
            print("✅ 方法参数签名正确")
        else:
            print("❌ 方法参数签名不正确")
            return False
        
        # 检查返回类型
        if "-> List[str]:" in content:
            print("✅ 返回类型注解正确")
        else:
            print("❌ 返回类型注解不正确")
            return False
        
        # 检查关键逻辑
        if "CheckUtils.parameter_not_empty(kb_id" in content:
            print("✅ 包含kb_id参数验证")
        else:
            print("❌ 缺少kb_id参数验证")
            return False
        
        if "CheckUtils.parameter_not_empty(session_id" in content:
            print("✅ 包含session_id参数验证")
        else:
            print("❌ 缺少session_id参数验证")
            return False
        
        if "self.kb_sessions_repository.get_session(" in content:
            print("✅ 包含会话获取逻辑")
        else:
            print("❌ 缺少会话获取逻辑")
            return False
        
        if "CheckUtils.object_exists(session" in content:
            print("✅ 包含会话存在性检查")
        else:
            print("❌ 缺少会话存在性检查")
            return False
        
        if "message_id_list.split(\",\")" in content:
            print("✅ 包含消息ID列表解析逻辑")
        else:
            print("❌ 缺少消息ID列表解析逻辑")
            return False
        
        print("✅ KnowledgeService方法验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def test_api_model_exists():
    """测试API模型是否存在"""
    print(f"\n" + "=" * 80)
    print("📋 验证API响应模型")
    print("=" * 80)
    
    try:
        # 读取文件内容
        model_file = project_root / "src" / "application" / "rag_api_models.py"
        with open(model_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查响应模型是否存在
        if "class KnowledgeBaseSessionMessageIdsResponse(" in content:
            print("✅ 找到KnowledgeBaseSessionMessageIdsResponse类")
        else:
            print("❌ 未找到KnowledgeBaseSessionMessageIdsResponse类")
            return False
        
        # 检查字段定义
        if "message_ids: List[str]" in content:
            print("✅ 找到message_ids字段定义")
        else:
            print("❌ 未找到message_ids字段定义")
            return False
        
        # 检查字段描述
        if "消息ID列表" in content:
            print("✅ 包含字段描述")
        else:
            print("❌ 缺少字段描述")
            return False
        
        print("✅ API模型验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def test_api_route_exists():
    """测试API路由是否存在"""
    print(f"\n" + "=" * 80)
    print("🌐 验证API路由")
    print("=" * 80)
    
    try:
        # 读取文件内容
        route_file = project_root / "src" / "presentation" / "api" / "routes" / "rag_routes.py"
        with open(route_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查路由装饰器
        if '@router.get("/knowledge_base/session/message/ids")' in content:
            print("✅ 找到API路由装饰器")
        else:
            print("❌ 未找到API路由装饰器")
            return False
        
        # 检查函数定义
        if "async def get_kb_session_message_ids(" in content:
            print("✅ 找到API函数定义")
        else:
            print("❌ 未找到API函数定义")
            return False
        
        # 检查参数定义
        if "kb_id: str = Query(..., description=" in content:
            print("✅ 找到kb_id参数定义")
        else:
            print("❌ 未找到kb_id参数定义")
            return False
        
        if "session_id: str = Query(..., description=" in content:
            print("✅ 找到session_id参数定义")
        else:
            print("❌ 未找到session_id参数定义")
            return False
        
        # 检查服务调用
        if "knowledgebase_service.get_kb_session_message_ids(" in content:
            print("✅ 找到服务方法调用")
        else:
            print("❌ 未找到服务方法调用")
            return False
        
        # 检查响应模型使用
        if "KnowledgeBaseSessionMessageIdsResponse(" in content:
            print("✅ 找到响应模型使用")
        else:
            print("❌ 未找到响应模型使用")
            return False
        
        # 检查错误处理
        if "handle_exception(e, request_id)" in content:
            print("✅ 包含错误处理")
        else:
            print("❌ 缺少错误处理")
            return False
        
        print("✅ API路由验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def test_imports_and_syntax():
    """测试导入和语法"""
    print(f"\n" + "=" * 80)
    print("🔧 验证导入和语法")
    print("=" * 80)
    
    try:
        # 检查knowledge_service.py的导入
        service_file = project_root / "src" / "domain" / "services" / "knowledge_service.py"
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "from typing import List" in content:
            print("✅ KnowledgeService包含List导入")
        else:
            print("❌ KnowledgeService缺少List导入")
            return False
        
        # 尝试解析语法
        try:
            ast.parse(content)
            print("✅ KnowledgeService语法正确")
        except SyntaxError as e:
            print(f"❌ KnowledgeService语法错误: {e}")
            return False
        
        # 检查API模型文件
        model_file = project_root / "src" / "application" / "rag_api_models.py"
        with open(model_file, 'r', encoding='utf-8') as f:
            model_content = f.read()
        
        try:
            ast.parse(model_content)
            print("✅ API模型语法正确")
        except SyntaxError as e:
            print(f"❌ API模型语法错误: {e}")
            return False
        
        # 检查路由文件
        route_file = project_root / "src" / "presentation" / "api" / "routes" / "rag_routes.py"
        with open(route_file, 'r', encoding='utf-8') as f:
            route_content = f.read()
        
        try:
            ast.parse(route_content)
            print("✅ API路由语法正确")
        except SyntaxError as e:
            print(f"❌ API路由语法错误: {e}")
            return False
        
        print("✅ 导入和语法验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def test_method_documentation():
    """测试方法文档"""
    print(f"\n" + "=" * 80)
    print("📚 验证方法文档")
    print("=" * 80)
    
    try:
        service_file = project_root / "src" / "domain" / "services" / "knowledge_service.py"
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查方法文档字符串
        if '"""' in content and "获取指定知识库关联会话的所有消息ID列表" in content:
            print("✅ 包含方法描述")
        else:
            print("❌ 缺少方法描述")
            return False
        
        # 检查参数文档
        if "Args:" in content and "auth_context: 认证上下文" in content:
            print("✅ 包含参数文档")
        else:
            print("❌ 缺少参数文档")
            return False
        
        # 检查返回值文档
        if "Returns:" in content and "List[str]: 消息ID列表" in content:
            print("✅ 包含返回值文档")
        else:
            print("❌ 缺少返回值文档")
            return False
        
        # 检查异常文档
        if "Raises:" in content and "ParameterException" in content:
            print("✅ 包含异常文档")
        else:
            print("❌ 缺少异常文档")
            return False
        
        print("✅ 方法文档验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始验证重构结果")
    print(f"📅 验证时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 执行各项验证
    results.append(("KnowledgeService方法", test_knowledge_service_method_exists()))
    results.append(("API响应模型", test_api_model_exists()))
    results.append(("API路由", test_api_route_exists()))
    results.append(("导入和语法", test_imports_and_syntax()))
    results.append(("方法文档", test_method_documentation()))
    
    # 总结结果
    print(f"\n" + "=" * 80)
    print("📊 验证结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个验证通过")
    
    if passed == total:
        print("🎉 所有验证都通过了！重构成功！")
        print("\n📋 重构总结:")
        print("  1. ✅ 在KnowledgeService中创建了get_kb_session_message_ids方法")
        print("  2. ✅ 添加了KnowledgeBaseSessionMessageIdsResponse响应模型")
        print("  3. ✅ 在rag_routes中添加了新的API接口")
        print("  4. ✅ 包含了完整的参数验证和错误处理")
        print("  5. ✅ 添加了详细的方法文档和API注释")
        return True
    else:
        print("💔 部分验证失败，请检查上述错误信息")
        return False


if __name__ == "__main__":
    import time
    success = main()
    print(f"\n{'='*80}")
    print(f"🏁 验证完成，结果: {'成功' if success else '失败'}")
    print(f"{'='*80}")
    sys.exit(0 if success else 1)
