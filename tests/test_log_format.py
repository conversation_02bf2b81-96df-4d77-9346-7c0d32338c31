#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志格式修改
"""

import sys
import os
import tempfile
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


def test_log_format():
    """测试日志格式"""
    print("=== 测试日志格式修改 ===")
    
    # 创建临时日志文件
    with tempfile.NamedTemporaryFile(mode='w+', suffix='.log', delete=False) as temp_file:
        temp_log_path = temp_file.name
    
    try:
        # 设置环境变量，模拟服务器环境
        os.environ["ENV_FOR_DYNACONF"] = "pre"
        
        # 导入并设置日志
        from src.shared.logging.logger import setup_logger, get_logger, RequestContext
        
        # 设置日志到临时文件
        setup_logger(
            level="INFO",
            file_path=temp_log_path
        )
        
        # 获取日志器
        logger = get_logger("test_module")
        
        # 在请求上下文中记录日志
        with RequestContext("test-request-123"):
            logger.info("这是一条测试日志消息")
            logger.error("这是一条错误日志消息")
        
        # 读取日志文件内容
        with open(temp_log_path, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        print("生成的日志内容:")
        print(log_content)
        
        # 验证日志格式
        lines = log_content.strip().split('\n')
        if lines and lines[0]:
            first_line = lines[0]
            print(f"\n第一行日志: {first_line}")
            
            # 检查分隔符数量
            pipe_count = first_line.count(' | ')
            print(f"分隔符 ' | ' 数量: {pipe_count}")
            
            # 期望的格式: 时间 | 级别 | 请求ID | 模块:函数:行号 | 消息
            expected_pipes = 4
            if pipe_count == expected_pipes:
                print("✅ 日志格式正确，包含4个分隔符")
                
                # 分析各个部分
                parts = first_line.split(' | ')
                if len(parts) == 5:
                    time_part = parts[0]
                    level_part = parts[1]
                    request_id_part = parts[2]
                    location_part = parts[3]
                    message_part = parts[4]
                    
                    print(f"  时间部分: {time_part}")
                    print(f"  级别部分: {level_part}")
                    print(f"  请求ID部分: {request_id_part}")
                    print(f"  位置部分: {location_part}")
                    print(f"  消息部分: {message_part}")
                    
                    # 验证位置部分格式
                    if ':' in location_part and location_part.count(':') >= 2:
                        print("✅ 位置部分格式正确 (模块:函数:行号)")
                    else:
                        print("❌ 位置部分格式不正确")
                        
                    return True
                else:
                    print(f"❌ 分割后的部分数量不正确: {len(parts)}")
                    return False
            else:
                print(f"❌ 分隔符数量不正确，期望 {expected_pipes}，实际 {pipe_count}")
                return False
        else:
            print("❌ 没有生成日志内容")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_log_path)
        except:
            pass
        
        # 恢复环境变量
        if "ENV_FOR_DYNACONF" in os.environ:
            del os.environ["ENV_FOR_DYNACONF"]


def test_format_comparison():
    """对比修改前后的格式"""
    print("\n=== 日志格式对比 ===")
    
    print("修改前的格式:")
    print("2025-08-04 15:57:00 | INFO     | 3a3e7bb8-ea97-4ceb-b36a-d1d284933a17 | src.domain.services.knowledge_service:update_knowledge_base_status:1506 - [KnowledgeService] 更新会话状态")
    
    print("\n修改后的格式:")
    print("2025-08-04 15:57:00 | INFO     | 3a3e7bb8-ea97-4ceb-b36a-d1d284933a17 | src.domain.services.knowledge_service:update_knowledge_base_status:1506 | [KnowledgeService] 更新会话状态")
    
    print("\n✅ 主要变化:")
    print("  • 在 '模块:函数:行号' 后面添加了 ' | ' 分隔符")
    print("  • 将 ' - ' 改为 ' | '，保持格式一致性")
    print("  • 便于日志解析和字段提取")
    
    print("\n📋 日志字段结构:")
    print("  1. 时间戳")
    print("  2. 日志级别")
    print("  3. 请求ID")
    print("  4. 代码位置 (模块:函数:行号)")
    print("  5. 日志消息")


def test_log_parsing():
    """测试日志解析"""
    print("\n=== 测试日志解析 ===")
    
    # 模拟新格式的日志行
    sample_log = "2025-08-04 15:57:00 | INFO     | 3a3e7bb8-ea97-4ceb-b36a-d1d284933a17 | src.domain.services.knowledge_service:update_knowledge_base_status:1506 | [KnowledgeService] 更新会话状态: session_id=sess_8c7bd6d3a63c45a0bcd248873333d306, session_status=success"
    
    print(f"示例日志: {sample_log}")
    
    # 解析日志
    parts = sample_log.split(' | ')
    if len(parts) >= 5:
        timestamp = parts[0]
        level = parts[1].strip()
        request_id = parts[2]
        location = parts[3]
        message = ' | '.join(parts[4:])  # 消息可能包含分隔符
        
        print(f"\n解析结果:")
        print(f"  时间戳: {timestamp}")
        print(f"  级别: {level}")
        print(f"  请求ID: {request_id}")
        print(f"  位置: {location}")
        print(f"  消息: {message}")
        
        # 进一步解析位置信息
        if ':' in location:
            location_parts = location.split(':')
            if len(location_parts) >= 3:
                module = ':'.join(location_parts[:-2])
                function = location_parts[-2]
                line = location_parts[-1]
                
                print(f"\n位置详细信息:")
                print(f"  模块: {module}")
                print(f"  函数: {function}")
                print(f"  行号: {line}")
        
        print("\n✅ 日志解析成功，格式规范")
        return True
    else:
        print(f"❌ 日志格式不正确，分割后只有 {len(parts)} 个部分")
        return False


def main():
    """主测试函数"""
    print("开始测试日志格式修改")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("日志格式生成", test_log_format),
        ("格式对比", test_format_comparison),
        ("日志解析", test_log_parsing)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("🏁 测试总结")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        if result is not None:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status} {test_name}")
            if result:
                passed += 1
            else:
                failed += 1
        else:
            print(f"ℹ️  信息 {test_name}")
    
    print(f"\n📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("\n🎉 日志格式修改成功！")
        print("\n✅ 修改总结:")
        print("  • 在代码位置后添加了 ' | ' 分隔符")
        print("  • 统一了日志格式，便于解析")
        print("  • 同时修改了服务器环境和开发环境的格式")
        print("  • 保持了向后兼容性")
        
        print("\n📋 使用建议:")
        print("  • 重启服务以应用新的日志格式")
        print("  • 更新日志解析脚本以适应新格式")
        print("  • 可以使用 ' | ' 作为字段分隔符进行解析")
    else:
        print("\n⚠️  有测试失败，请检查修改")
    
    return failed == 0


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
