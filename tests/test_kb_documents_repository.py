#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库文档Repository测试
"""

import pytest
import uuid
from datetime import datetime, timedelta
from src.infrastructure.database.repositories.kb_documents_repository import kb_documents_repository


class TestKbDocumentsRepository:
    """知识库文档Repository测试"""

    def test_create_kb_document(self):
        """测试创建知识库文档"""
        doc_id = f"test_doc_{uuid.uuid4().hex[:12]}"
        
        document = kb_documents_repository.create_kb_document(
            doc_id=doc_id,
            oss_bucket="test-bucket",
            oss_object_name="test/example.pdf",
            document_title="测试文档",
            file_size=1024 * 1024,
        )
        
        assert document is not None
        assert document['doc_id'] == doc_id
        assert document['document_title'] == "测试文档"
        assert document['file_size'] == 1024 * 1024
        
        # 清理
        kb_documents_repository.delete_kb_document(doc_id)

    def test_get_kb_document_by_id(self):
        """测试根据ID获取文档"""
        doc_id = f"test_doc_{uuid.uuid4().hex[:12]}"
        
        # 创建文档
        kb_documents_repository.create_kb_document(
            doc_id=doc_id,
            oss_bucket="test-bucket",
            oss_object_name="test/example.pdf",
            document_title="测试文档",
            file_size=1024 * 1024,
        )
        
        # 获取文档
        document = kb_documents_repository.get_kb_document_by_id(doc_id)
        
        assert document is not None
        assert document.doc_id == doc_id
        assert document.document_title == "测试文档"
        
        # 清理
        kb_documents_repository.delete_kb_document(doc_id)

    def test_update_kb_document(self):
        """测试更新文档"""
        doc_id = f"test_doc_{uuid.uuid4().hex[:12]}"
        
        # 创建文档
        kb_documents_repository.create_kb_document(
            doc_id=doc_id,
            oss_bucket="test-bucket",
            oss_object_name="test/example.pdf",
            document_title="原始标题",
            file_size=1024 * 1024,
        )
        
        # 更新文档
        updated_document = kb_documents_repository.update_kb_document(
            doc_id=doc_id,
            document_title="更新后的标题",
            file_size=2048 * 1024,
        )
        
        assert updated_document is not None
        assert updated_document.document_title == "更新后的标题"
        assert updated_document.file_size == 2048 * 1024
        
        # 清理
        kb_documents_repository.delete_kb_document(doc_id)

    def test_delete_kb_document(self):
        """测试删除文档"""
        doc_id = f"test_doc_{uuid.uuid4().hex[:12]}"
        
        # 创建文档
        kb_documents_repository.create_kb_document(
            doc_id=doc_id,
            oss_bucket="test-bucket",
            oss_object_name="test/example.pdf",
            document_title="测试文档",
            file_size=1024 * 1024,
        )
        
        # 删除文档
        success = kb_documents_repository.delete_kb_document(doc_id)
        assert success is True
        
        # 验证文档已被删除
        document = kb_documents_repository.get_kb_document_by_id(doc_id)
        assert document is None

    def test_search_kb_documents(self):
        """测试搜索文档"""
        doc_id = f"test_doc_{uuid.uuid4().hex[:12]}"
        
        # 创建文档
        kb_documents_repository.create_kb_document(
            doc_id=doc_id,
            oss_bucket="test-bucket",
            oss_object_name="test/example.pdf",
            document_title="测试搜索文档",
            file_size=1024 * 1024,
        )
        
        # 搜索文档
        documents = kb_documents_repository.list_kb_documents(
            document_title="测试搜索",
            limit=10
        )
        
        assert len(documents) >= 1
        found_doc = next((doc for doc in documents if doc['doc_id'] == doc_id), None)
        assert found_doc is not None
        
        # 清理
        kb_documents_repository.delete_kb_document(doc_id)

    def test_count_kb_documents(self):
        """测试统计文档数量"""
        doc_id = f"test_doc_{uuid.uuid4().hex[:12]}"
        
        # 创建文档
        kb_documents_repository.create_kb_document(
            doc_id=doc_id,
            oss_bucket="test-bucket",
            oss_object_name="test/example.pdf",
            document_title="测试文档",
            file_size=1024 * 1024,
        )
        
        # 统计数量
        total_count = kb_documents_repository.count_kb_documents()
        bucket_count = kb_documents_repository.count_kb_documents(oss_bucket="test-bucket")
        
        assert total_count >= 1
        assert bucket_count >= 1
        
        # 清理
        kb_documents_repository.delete_kb_document(doc_id)

    def test_get_documents_by_oss_bucket(self):
        """测试根据OSS存储桶获取文档"""
        doc_id = f"test_doc_{uuid.uuid4().hex[:12]}"
        
        # 创建文档
        kb_documents_repository.create_kb_document(
            doc_id=doc_id,
            oss_bucket="test-bucket",
            oss_object_name="test/example.pdf",
            document_title="测试文档",
            file_size=1024 * 1024,
        )
        
        # 获取存储桶文档
        documents = kb_documents_repository.get_documents_by_oss_bucket("test-bucket")
        
        assert len(documents) >= 1
        found_doc = next((doc for doc in documents if doc['doc_id'] == doc_id), None)
        assert found_doc is not None
        
        # 清理
        kb_documents_repository.delete_kb_document(doc_id)

    def test_get_large_documents(self):
        """测试获取大文件文档"""
        doc_id = f"test_doc_{uuid.uuid4().hex[:12]}"
        
        # 创建大文件文档
        kb_documents_repository.create_kb_document(
            doc_id=doc_id,
            oss_bucket="test-bucket",
            oss_object_name="test/large.pdf",
            document_title="大文件文档",
            file_size=5 * 1024 * 1024,  # 5MB
        )
        
        # 获取大于1MB的文档
        large_documents = kb_documents_repository.get_large_documents(1024 * 1024)
        
        assert len(large_documents) >= 1
        found_doc = next((doc for doc in large_documents if doc['doc_id'] == doc_id), None)
        assert found_doc is not None
        
        # 清理
        kb_documents_repository.delete_kb_document(doc_id)

    def test_duplicate_doc_id_error(self):
        """测试重复文档ID错误"""
        doc_id = f"test_doc_{uuid.uuid4().hex[:12]}"
        
        # 创建第一个文档
        kb_documents_repository.create_kb_document(
            doc_id=doc_id,
            oss_bucket="test-bucket",
            oss_object_name="test/example1.pdf",
            document_title="第一个文档",
            file_size=1024 * 1024,
        )
        
        # 尝试创建相同ID的文档
        with pytest.raises(ValueError, match="文档ID已存在"):
            kb_documents_repository.create_kb_document(
                doc_id=doc_id,
                oss_bucket="test-bucket",
                oss_object_name="test/example2.pdf",
                document_title="第二个文档",
                file_size=2048 * 1024,
            )
        
        # 清理
        kb_documents_repository.delete_kb_document(doc_id)


if __name__ == "__main__":
    pytest.main([__file__]) 