#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的文档处理流程
验证从文件上传到markdown内容保存的完整流程
"""
import sys
import os
from io import BytesIO
import time

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.domain.services.file_service import file_service
from src.domain.services.auth_service import AuthContext
from src.infrastructure.database.models.file_models import FileType


def test_complete_document_processing():
    """测试完整的文档处理流程"""
    print("=" * 80)
    print("测试完整的文档处理流程")
    print("=" * 80)
    
    context = AuthContext(ali_uid=1550203943326350, wy_id="test_user")
    
    # 创建一个较长的测试文档，确保有足够的内容进行解析
    test_content = """
# 产品介绍文档

## 概述
这是一个AI智能助手产品的详细介绍文档。

## 产品特性

### 1. 文档解析能力
- 支持多种文件格式：PDF、Word、TXT、Markdown
- 高精度的文档内容提取
- 智能结构化解析
- 支持图表和表格识别

### 2. 智能问答功能
- 基于大语言模型的问答能力
- 上下文理解和记忆
- 多轮对话支持
- 个性化回答风格

### 3. 知识管理
- 智能知识库构建
- 文档分类和标签
- 知识图谱构建
- 相似内容推荐

## 技术架构

### 后端架构
- 微服务架构设计
- 分布式存储系统
- 高可用负载均衡
- 实时数据处理

### AI模型
- 大语言模型集成
- 向量数据库
- 语义搜索引擎
- 机器学习管道

## 使用场景

### 企业应用
1. 内部知识管理
2. 客服智能问答
3. 文档自动化处理
4. 业务流程优化

### 个人应用
1. 学习资料整理
2. 研究文献管理
3. 个人知识库
4. 智能笔记助手

## 联系方式
- 邮箱：<EMAIL>
- 电话：400-123-4567
- 地址：北京市朝阳区科技园区
- 网站：https://www.example.com

## 版本信息
- 当前版本：v2.0
- 发布日期：2025年7月21日
- 更新内容：增强文档解析能力，优化问答效果
    """
    
    file_content = BytesIO(test_content.encode('utf-8'))
    
    print(f"测试文档内容长度: {len(test_content)} 字符")
    print(f"用户信息: ali_uid={context.ali_uid}, wy_id={context.wy_id}")
    print()
    
    try:
        print("🚀 开始完整的文档处理流程...")
        print("期望流程：")
        print("1. 文件上传 → OSS存储")
        print("2. RAG解析 → 获取doc_id")
        print("3. 状态监控 → 等待chunk_parse完成")
        print("4. 内容下载 → 获取markdown_parse内容")
        print("5. 内容保存 → 更新数据库content字段")
        print("6. 流程完成 → 状态为completed")
        print()
        
        # 记录开始时间
        start_time = time.time()
        
        # 上传文件
        file_obj, success = file_service.upload_file(
            context=context,
            file_content=file_content,
            original_filename="complete_processing_test.md",
            session_id="complete_processing_session",
            file_type=FileType.SESSION_FILE.value,
            content_type="text/markdown"
        )
        
        if success:
            print(f"✅ 文件上传成功: file_id={file_obj.id}")
            print()
            
            # 长时间监控文件状态变化
            print("📊 监控完整处理流程...")
            previous_status = None
            previous_doc_id = None
            previous_content_length = 0
            
            # 监控更长时间，因为文档处理可能需要较长时间
            for i in range(120):  # 监控2分钟
                time.sleep(1)
                
                # 获取最新文件状态
                updated_file = file_service.file_repository.get_file_by_id(file_obj.id)
                if updated_file:
                    current_status = updated_file.upload_status
                    current_doc_id = updated_file.doc_id
                    current_content = updated_file.content or ""
                    current_content_length = len(current_content)
                    
                    # 检查是否有变化
                    status_changed = current_status != previous_status
                    doc_id_changed = current_doc_id != previous_doc_id
                    content_changed = current_content_length != previous_content_length
                    
                    if status_changed or doc_id_changed or content_changed:
                        elapsed = time.time() - start_time
                        print(f"   [{elapsed:6.1f}s] 状态: {current_status:10} | doc_id: {current_doc_id or 'None'} | 内容长度: {current_content_length}")
                        
                        previous_status = current_status
                        previous_doc_id = current_doc_id
                        previous_content_length = current_content_length
                    
                    # 如果已经完成且有内容，提前结束监控
                    if (current_status == "completed" and 
                        current_doc_id and 
                        current_content_length > 0):
                        print()
                        print("🎉 完整文档处理流程完成!")
                        total_time = time.time() - start_time
                        print(f"   总耗时: {total_time:.1f}秒")
                        break
                    elif current_status == "completed" and current_doc_id:
                        # 完成但没有内容，可能还在处理中
                        if i > 60:  # 1分钟后如果还没有内容就提示
                            print(f"   ⚠️  已完成但内容为空，继续等待...")
                else:
                    print(f"   [{i+1:3}s] ❌ 无法获取文件状态")
            
            # 最终状态检查
            print()
            print("📋 最终状态检查:")
            final_file = file_service.file_repository.get_file_by_id(file_obj.id)
            if final_file:
                print(f"   文件ID: {final_file.id}")
                print(f"   文件名: {final_file.title}")
                print(f"   状态: {final_file.upload_status}")
                print(f"   进度: {final_file.upload_progress}%")
                print(f"   doc_id: {final_file.doc_id or 'None'}")
                print(f"   内容长度: {len(final_file.content or '')} 字符")
                print(f"   创建时间: {final_file.gmt_created}")
                print(f"   修改时间: {final_file.gmt_modified}")
                
                # 验证结果
                if final_file.upload_status == "completed":
                    if final_file.doc_id and final_file.content:
                        print("   ✅ 完美：文件完成、doc_id已保存、markdown内容已获取")
                        print(f"   🎯 内容预览: {final_file.content[:200]}...")
                    elif final_file.doc_id:
                        print("   ⚠️  部分成功：有doc_id但缺少markdown内容")
                    else:
                        print("   ❌ 失败：缺少doc_id")
                else:
                    print(f"   ⚠️  状态异常：{final_file.upload_status}")
            else:
                print("   ❌ 无法获取最终文件状态")
                
        else:
            print("❌ 文件上传失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    print("=" * 80)
    print("测试完成")
    print("=" * 80)
    print()
    print("🔍 关键验证点:")
    print("1. 查看 '[FileService] 开始监控文档处理状态' 日志")
    print("2. 查看 '[FileService] 检查文档状态' 日志")
    print("3. 查看 '[FileService] 文档已完成chunk_parse阶段' 日志")
    print("4. 查看 '[FileService] markdown内容下载成功' 日志")
    print("5. 查看 '[FileService] markdown内容保存成功' 日志")
    print("6. 确认最终文件记录中包含完整的markdown内容")


if __name__ == "__main__":
    test_complete_document_processing()
