#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 request_id 在日志中的显示
"""

import sys
import os
import uuid
from unittest.mock import Mock

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.shared.logging.logger import RequestContext, request_id_var, logger
from src.presentation.api.dependencies.api_common_utils import get_request_id_dependency, get_request_id_dependency


def test_request_context():
    """测试 RequestContext 上下文管理器"""
    print("=== 测试 RequestContext 上下文管理器 ===")
    
    # 测试默认值
    print(f"默认 request_id: {request_id_var.get()}")
    
    # 测试设置 request_id
    test_request_id = "test-req-123"
    with RequestContext(test_request_id):
        print(f"上下文中的 request_id: {request_id_var.get()}")
        logger.info("这是一条测试日志，应该包含 request_id")
        logger.warning("这是一条警告日志")
        logger.error("这是一条错误日志")
    
    # 测试上下文退出后
    print(f"上下文退出后的 request_id: {request_id_var.get()}")


def test_get_request_id_dependency_function():
    """测试 get_request_id_dependency 函数的多种来源"""
    print("\n=== 测试 get_request_id_dependency 函数 ===")
    
    # 模拟 FastAPI Request 对象
    class MockRequest:
        def __init__(self, headers=None, query_params=None):
            self.headers = headers or {}
            self.query_params = query_params or {}
    
    # 测试1: 从请求头获取
    print("\n--- 测试1: 从请求头获取 ---")
    request_with_header = MockRequest(headers={'X-Request-ID': 'header-req-456'})
    request_id = get_request_id_dependency(request_with_header)
    print(f"从请求头获取的 request_id: {request_id}")
    
    # 测试2: 从查询参数获取
    print("\n--- 测试2: 从查询参数获取 ---")
    request_with_query = MockRequest(query_params={'request_id': 'query-req-789'})
    request_id = get_request_id_dependency(request_with_query)
    print(f"从查询参数获取的 request_id: {request_id}")
    
    # 测试3: 从上下文获取
    print("\n--- 测试3: 从上下文获取 ---")
    with RequestContext("context-req-101112"):
        request_id = get_request_id_dependency()  # 不传 request 参数
        print(f"从上下文获取的 request_id: {request_id}")
    
    # 测试4: 生成新的 UUID
    print("\n--- 测试4: 生成新的 UUID ---")
    request_empty = MockRequest()
    request_id = get_request_id_dependency(request_empty)
    print(f"生成的新 request_id: {request_id}")
    print(f"是否为有效 UUID: {len(request_id) == 36 and '-' in request_id}")


def test_get_request_id_dependency():
    """测试 get_request_id_dependency 依赖注入函数"""
    print("\n=== 测试 get_request_id_dependency 依赖注入函数 ===")
    
    class MockRequest:
        def __init__(self, headers=None, query_params=None):
            self.headers = headers or {}
            self.query_params = query_params or {}
    
    # 测试依赖注入函数
    request = MockRequest(headers={'X-Request-ID': 'dep-req-131415'})
    
    print(f"调用前的上下文 request_id: {request_id_var.get()}")
    
    # 调用依赖注入函数
    request_id = get_request_id_dependency(request)
    print(f"依赖注入返回的 request_id: {request_id}")
    print(f"调用后的上下文 request_id: {request_id_var.get()}")
    
    # 测试日志是否包含正确的 request_id
    logger.info("依赖注入后的测试日志")


def test_logging_with_different_request_ids():
    """测试不同 request_id 的日志输出"""
    print("\n=== 测试不同 request_id 的日志输出 ===")
    
    request_ids = [
        "req-001",
        "req-002", 
        "req-003"
    ]
    
    for req_id in request_ids:
        with RequestContext(req_id):
            logger.info(f"处理请求: {req_id}")
            logger.debug(f"调试信息: {req_id}")
            logger.warning(f"警告信息: {req_id}")


def test_nested_request_contexts():
    """测试嵌套的请求上下文"""
    print("\n=== 测试嵌套的请求上下文 ===")
    
    with RequestContext("outer-req-161718"):
        logger.info("外层上下文日志")
        print(f"外层上下文 request_id: {request_id_var.get()}")
        
        with RequestContext("inner-req-192021"):
            logger.info("内层上下文日志")
            print(f"内层上下文 request_id: {request_id_var.get()}")
        
        logger.info("回到外层上下文日志")
        print(f"回到外层上下文 request_id: {request_id_var.get()}")


def main():
    """主测试函数"""
    print("开始测试 request_id 日志功能")
    print("=" * 50)
    
    test_request_context()
    test_get_request_id_dependency_function()
    test_get_request_id_dependency()
    test_logging_with_different_request_ids()
    test_nested_request_contexts()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n注意观察上面的日志输出，每条日志都应该包含对应的 request_id")


if __name__ == '__main__':
    main()
