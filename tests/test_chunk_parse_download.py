#!/usr/bin/env python3
"""
测试 chunk_parse 文件下载功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def test_chunk_parse_download():
    """测试 chunk_parse 文件下载"""
    
    print("=" * 80)
    print("测试 chunk_parse 文件下载功能")
    print("=" * 80)
    
    try:
        # 导入必要的模块
        from src.infrastructure.oss.oss_service import oss_service
        
        # 测试路径（模拟错误的路径）
        error_path = "doc_details/20250805/document-eed541e2-f2a0-4a63-a0c1-693312c04601/chunk_parse_result.json"
        correct_path = "20250805/document-eed541e2-f2a0-4a63-a0c1-693312c04601/chunk_parse_result.json"
        
        print(f"原始错误路径: {error_path}")
        print(f"修正后路径: {correct_path}")
        
        # 测试路径修正逻辑
        def process_chunk_parse_path(chunk_parse_path: str) -> str:
            """处理RAG服务返回的路径，可能包含错误的bucket前缀"""
            actual_object_key = chunk_parse_path
            
            if chunk_parse_path.startswith('doc_details/'):
                # 去掉错误的bucket前缀，保留实际的对象键名
                actual_object_key = chunk_parse_path[len('doc_details/'):]
                print(f"检测到错误的bucket前缀，修正路径: {chunk_parse_path} -> {actual_object_key}")
            elif '/' in chunk_parse_path and not chunk_parse_path.startswith('http'):
                # 如果路径包含其他可能的bucket前缀，尝试智能处理
                parts = chunk_parse_path.split('/', 1)
                if len(parts) == 2 and not parts[0].startswith('20'):  # 不是日期开头的路径
                    actual_object_key = parts[1]
                    print(f"检测到可能的bucket前缀，修正路径: {chunk_parse_path} -> {actual_object_key}")
            
            return actual_object_key
        
        # 应用路径修正
        processed_path = process_chunk_parse_path(error_path)
        
        print(f"\n处理结果:")
        print(f"  输入: {error_path}")
        print(f"  输出: {processed_path}")
        print(f"  期望: {correct_path}")
        print(f"  匹配: {'✅' if processed_path == correct_path else '❌'}")
        
        # 检查 OSS 服务状态
        print(f"\nOSS 服务状态:")
        if oss_service.rag_oss_client:
            print("✅ RAG OSS 客户端已初始化")
            
            rag_config = oss_service.rag_oss_client.get_config()
            if rag_config:
                print(f"  - Bucket: {rag_config.bucket_name}")
                print(f"  - Region: {rag_config.region}")
                print(f"  - Endpoint: {rag_config.endpoint}")
            
            # 测试下载方法（不实际下载，只测试方法调用）
            print(f"\n测试下载方法调用:")
            try:
                # 这里只是测试方法是否可以调用，不期望成功下载
                # 因为测试文件可能不存在
                result = oss_service.download_rag_file_content(processed_path)
                if result:
                    print(f"✅ 下载成功，内容长度: {len(result)}")
                else:
                    print("ℹ️ 下载返回空（文件可能不存在，这是正常的）")
            except Exception as e:
                print(f"ℹ️ 下载异常（文件可能不存在，这是正常的）: {e}")
            
            # 测试预签名URL生成
            print(f"\n测试预签名URL生成:")
            try:
                presigned_url = oss_service.generate_rag_presigned_url(processed_path, expires_in=3600)
                if presigned_url:
                    print(f"✅ 预签名URL生成成功")
                    print(f"  URL: {presigned_url[:100]}...")
                else:
                    print("❌ 预签名URL生成失败")
            except Exception as e:
                print(f"❌ 预签名URL生成异常: {e}")
        else:
            print("❌ RAG OSS 客户端未初始化")
            return False
        
        print(f"\n" + "=" * 80)
        print("✅ chunk_parse 下载功能测试完成")
        print("修复后的代码应该能够正确处理错误的 bucket 前缀")
        print("=" * 80)
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_chunk_parse_download()
    sys.exit(0 if success else 1)
