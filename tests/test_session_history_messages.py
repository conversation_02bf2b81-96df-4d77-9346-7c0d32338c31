#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 SessionService 中 get_history_messages 方法的知识库集成功能
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.domain.services.session_service import SessionService


class MockMessage:
    """模拟消息对象"""
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)


class TestSessionHistoryMessages(unittest.TestCase):
    """测试会话历史消息功能"""

    def setUp(self):
        """测试前准备"""
        self.session_service = SessionService()
        
        # 模拟 memory_sdk
        self.session_service.memory_sdk = Mock()

    def test_extract_message_fields_with_object(self):
        """测试从对象中提取消息字段"""
        message_obj = MockMessage(
            message_id='msg_123',
            role='user',
            content='Hello world',
            timestamp=1234567890,
            type='text',
            custom_field='custom_value'
        )
        
        result = self.session_service._extract_message_fields(message_obj)
        
        # 验证基本字段被正确提取
        self.assertEqual(result['message_id'], 'msg_123')
        self.assertEqual(result['role'], 'user')
        self.assertEqual(result['content'], 'Hello world')
        self.assertEqual(result['timestamp'], 1234567890)
        self.assertEqual(result['type'], 'text')
        self.assertEqual(result['custom_field'], 'custom_value')

    def test_extract_message_fields_with_dict(self):
        """测试从字典中提取消息字段"""
        message_dict = {
            'messageId': 'msg_456',  # 不同的命名风格
            'role': 'assistant',
            'content': 'AI response',
            'created_at': 1234567890,  # 不同的时间戳字段名
            'extra_field': 'extra_value'
        }
        
        result = self.session_service._extract_message_fields(message_dict)
        
        # 验证字段映射正确
        self.assertEqual(result['message_id'], 'msg_456')  # messageId -> message_id
        self.assertEqual(result['role'], 'assistant')
        self.assertEqual(result['content'], 'AI response')
        self.assertEqual(result['timestamp'], 1234567890)  # created_at -> timestamp
        self.assertEqual(result['extra_field'], 'extra_value')

    def test_extract_message_fields_with_none_values(self):
        """测试处理None值"""
        message_dict = {
            'message_id': 'msg_789',
            'role': 'user',
            'content': None,  # None值应该被跳过
            'timestamp': 1234567890,
            'empty_field': ''  # 空字符串应该被保留
        }
        
        result = self.session_service._extract_message_fields(message_dict)
        
        # 验证None值被跳过，空字符串被保留
        self.assertNotIn('content', result)
        self.assertIn('empty_field', result)
        self.assertEqual(result['empty_field'], '')

    @patch('src.domain.services.session_service.knowledgebase_service')
    def test_get_history_messages_with_kb_id(self, mock_kb_service):
        """测试带知识库ID的历史消息获取"""
        # 模拟 memory_sdk 返回
        mock_messages = [
            MockMessage(message_id='msg_1', role='user', content='Question 1', timestamp=1000),
            MockMessage(message_id='msg_2', role='assistant', content='Answer 1', timestamp=2000),
            {'message_id': 'msg_3', 'role': 'user', 'content': 'Question 2', 'timestamp': 3000}
        ]
        
        self.session_service.memory_sdk.list_messages.return_value = {
            'messages': mock_messages,
            'next_token': 'token_123',
            'has_more': True,
            'total_count': 3
        }
        
        # 模拟知识库服务返回
        mock_kb_service.is_knowledge_base_message.return_value = {
            'msg_1': True,
            'msg_2': False,
            'msg_3': True
        }
        
        # 调用方法
        result = self.session_service.get_history_messages(
            session_id='sess_test',
            page_size=10,
            kb_id='kb_test'
        )
        
        # 验证结果
        self.assertIn('messages', result)
        self.assertEqual(len(result['messages']), 3)
        
        # 验证知识库判断结果
        messages = result['messages']
        self.assertTrue(messages[0]['is_in_kb'])   # msg_1 -> True
        self.assertFalse(messages[1]['is_in_kb'])  # msg_2 -> False
        self.assertTrue(messages[2]['is_in_kb'])   # msg_3 -> True
        
        # 验证分页信息
        self.assertEqual(result['next_token'], 'token_123')
        self.assertTrue(result['has_more'])
        
        # 验证知识库服务被正确调用
        mock_kb_service.is_knowledge_base_message.assert_called_once_with(
            kb_id='kb_test',
            session_id='sess_test',
            message_id_list=['msg_1', 'msg_2', 'msg_3']
        )

    def test_get_history_messages_without_kb_id(self):
        """测试不带知识库ID的历史消息获取"""
        # 模拟 memory_sdk 返回
        mock_messages = [
            MockMessage(message_id='msg_1', role='user', content='Question 1'),
            {'message_id': 'msg_2', 'role': 'assistant', 'content': 'Answer 1'}
        ]
        
        self.session_service.memory_sdk.list_messages.return_value = {
            'messages': mock_messages,
            'next_token': None,
            'has_more': False
        }
        
        # 调用方法（不传kb_id）
        result = self.session_service.get_history_messages(
            session_id='sess_test',
            page_size=10
        )
        
        # 验证结果
        messages = result['messages']
        self.assertEqual(len(messages), 2)
        
        # 验证所有消息的is_in_kb都为False
        for message in messages:
            self.assertFalse(message['is_in_kb'])

    @patch('src.domain.services.session_service.knowledgebase_service')
    def test_get_history_messages_kb_service_failure(self, mock_kb_service):
        """测试知识库服务调用失败的情况"""
        # 模拟 memory_sdk 返回
        mock_messages = [
            MockMessage(message_id='msg_1', role='user', content='Question 1')
        ]
        
        self.session_service.memory_sdk.list_messages.return_value = {
            'messages': mock_messages
        }
        
        # 模拟知识库服务抛出异常
        mock_kb_service.is_knowledge_base_message.side_effect = Exception("KB service error")
        
        # 调用方法
        result = self.session_service.get_history_messages(
            session_id='sess_test',
            kb_id='kb_test'
        )
        
        # 验证异常被捕获，is_in_kb设置为False
        messages = result['messages']
        self.assertEqual(len(messages), 1)
        self.assertFalse(messages[0]['is_in_kb'])

    def test_get_history_messages_empty_result(self):
        """测试空消息列表的情况"""
        # 模拟 memory_sdk 返回空结果
        self.session_service.memory_sdk.list_messages.return_value = {
            'messages': [],
            'next_token': None,
            'has_more': False
        }
        
        # 调用方法
        result = self.session_service.get_history_messages(
            session_id='sess_test',
            kb_id='kb_test'
        )
        
        # 验证返回原始结果
        self.assertEqual(result['messages'], [])

    def test_convert_to_timestamp_with_datetime(self):
        """测试datetime对象转换为时间戳"""
        dt = datetime(2023, 1, 1, 12, 0, 0)
        result = self.session_service._convert_to_timestamp(dt)

        # 验证转换为毫秒级时间戳
        expected = int(dt.timestamp() * 1000)
        self.assertEqual(result, expected)

    def test_convert_to_timestamp_with_number(self):
        """测试数字时间戳转换"""
        # 秒级时间戳
        seconds_timestamp = 1672574400  # 2023-01-01 12:00:00 UTC
        result = self.session_service._convert_to_timestamp(seconds_timestamp)
        self.assertEqual(result, seconds_timestamp * 1000)

        # 毫秒级时间戳
        ms_timestamp = 1672574400000
        result = self.session_service._convert_to_timestamp(ms_timestamp)
        self.assertEqual(result, ms_timestamp)

    def test_convert_to_timestamp_with_string(self):
        """测试字符串时间戳转换"""
        # 数字字符串
        result = self.session_service._convert_to_timestamp("1672574400")
        self.assertEqual(result, 1672574400000)

        # ISO格式字符串
        iso_string = "2023-01-01T12:00:00Z"
        result = self.session_service._convert_to_timestamp(iso_string)
        self.assertIsInstance(result, int)
        self.assertGreater(result, 0)

    def test_convert_to_timestamp_with_invalid_value(self):
        """测试无效值的时间戳转换"""
        # None值
        result = self.session_service._convert_to_timestamp(None)
        self.assertIsNone(result)

        # 无效字符串
        result = self.session_service._convert_to_timestamp("invalid")
        self.assertIsNone(result)

    def test_extract_message_fields_with_datetime_timestamp(self):
        """测试提取包含datetime时间戳的消息字段"""
        dt = datetime(2023, 1, 1, 12, 0, 0)
        message_obj = MockMessage(
            message_id='msg_123',
            role='user',
            content='Hello world',
            timestamp=dt
        )

        result = self.session_service._extract_message_fields(message_obj)

        # 验证时间戳被转换为数字
        self.assertIsInstance(result['timestamp'], int)
        self.assertEqual(result['timestamp'], int(dt.timestamp() * 1000))


if __name__ == '__main__':
    unittest.main()
