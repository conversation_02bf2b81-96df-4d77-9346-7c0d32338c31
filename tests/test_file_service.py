#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件服务功能测试脚本
"""
import requests
import json
import time
from io import BytesIO
import random


# def test_file_upload_and_progress():
#     """测试文件上传和进度查询"""
#     base_url = "http://localhost:8000"
    
#     print("⚠️  请确保:")
#     print("   1. 已手动执行建表语句创建 alpha_files 表")
#     print("   2. 使用 daily 环境启动服务:")
#     print("      source venv/bin/activate")
#     print("      export ENV_FOR_DYNACONF=daily")
#     print("      python start_service.py")
#     print("   3. 服务已正常启动在 http://localhost:8000")
#     print()
    
#     # 创建测试文件内容
#     test_content = f"""这是一个测试文件的内容
# 测试文件上传功能
# 当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
# 文件大小测试内容，用于验证上传进度跟踪功能。
# """ * 100  # 增大文件内容以便观察进度
    
#     test_file = BytesIO(test_content.encode('utf-8'))
    
#     # 准备上传数据
#     files = {
#         'file': ('test_large.txt', test_file, 'text/plain')
#     }
#     data = {
#         'ali_uid': '123456789',
#         'wy_id': 'test_wy_user',
#         'session_id': 'test_session_123',
#         'file_type': 'sessionFile'
#     }
    
#     print("🚀 开始测试文件上传...")
    
#     try:
#         # 1. 测试文件上传
#         print("1. 上传文件...")
#         response = requests.post(f"{base_url}/api/files/upload", files=files, data=data)
        
#         if response.status_code == 200:
#             upload_result = response.json()
#             print(f"✅ 文件上传请求成功:")
#             print(f"   - 文件ID: {upload_result['file_id']}")
#             print(f"   - 会话ID: {upload_result['session_id']}")
#             print(f"   - 文件名: {upload_result['file_name']}")
#             print(f"   - 文件大小: {upload_result['file_size']} bytes")
#             print(f"   - 初始状态: {upload_result['upload_status']}")
            
#             file_id = upload_result['file_id']
            
#             # 2. 循环查询上传进度
#             print("\n2. 监控上传进度...")
#             max_attempts = 10
#             attempt = 0
            
#             while attempt < max_attempts:
#                 time.sleep(1)  # 等待1秒
#                 attempt += 1
                
#                 status_response = requests.get(f"{base_url}/api/files/status", params={'file_id': file_id})
                
#                 if status_response.status_code == 200:
#                     status_result = status_response.json()
#                     progress = status_result['upload_progress']
#                     status = status_result['upload_status']
                    
#                     print(f"   进度查询 {attempt}: {progress}% - 状态: {status}")
                    
#                     if status == 'completed':
#                         print(f"✅ 文件上传完成!")
#                         print(f"   - 最终文件大小: {status_result['file_size']} bytes")
#                         print(f"   - 完成时间: {status_result['completed_time']}")
#                         if status_result['url']:
#                             print(f"   - 下载URL: {status_result['url']}")
#                         break
#                     elif status == 'failed':
#                         print(f"❌ 文件上传失败: {status_result['error_message']}")
#                         break
#                 else:
#                     print(f"❌ 状态查询失败: {status_response.status_code}")
#                     break
            
#             return file_id
            
#         else:
#             print(f"❌ 文件上传失败: {response.status_code}")
#             print(f"   错误信息: {response.text}")
#             return None
            
#     except requests.exceptions.ConnectionError:
#         print("❌ 连接失败，请确保服务已启动")
#         return None
#     except Exception as e:
#         print(f"❌ 测试异常: {e}")
#         return None


def test_session_files(session_id: str):
    """测试获取会话文件列表"""
    base_url = "http://localhost:8000"
    
    print("\n3. 测试获取会话文件列表...")
    
    try:
        # 获取所有文件
        response = requests.get(f"{base_url}/api/files/session/{session_id}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取会话文件成功:\n{json.dumps(result, ensure_ascii=False, indent=2)}")
            # print(f"   - 会话ID: {result['session_id']}")
            # print(f"   - 文件总数: {result['total_count']}")
            # print(f"   - 统计信息: {result['statistics']}")
            
            # for i, file_info in enumerate(result['files']):
            #     print(f"   文件 {i+1}:")
            #     print(f"     - ID: {file_info['file_id']}")
            #     print(f"     - 名称: {file_info['title']}")
            #     print(f"     - 大小: {file_info['file_size']} bytes")
            #     print(f"     - 类型: {file_info['type']}")
            #     print(f"     - 状态: {file_info['upload_status']}")
            #     print(f"     - 进度: {file_info['upload_progress']}%")
            
            return [int(f['file_id']) for f in result['files']]
        else:
            print(f"❌ 获取会话文件失败: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 获取会话文件异常: {e}")
        return []


def test_download_urls(file_ids: list):
    """测试批量获取下载链接"""
    base_url = "http://localhost:8000"
    
    if not file_ids:
        print("\n⚠️  没有文件ID，跳过下载链接测试")
        return
    
    print(f"\n4. 测试批量获取下载链接...")
    
    try:
        response = requests.post(
            f"{base_url}/api/files/download-urls?expires=7200",
            json=file_ids
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取下载链接成功:\n{json.dumps(result, ensure_ascii=False, indent=2)}")
            # print(f"   - 请求文件数: {len(result['requested_file_ids'])}")
            # print(f"   - 成功数量: {result['success_count']}")
            # print(f"   - 失败数量: {result['failed_count']}")
            # print(f"   - 链接有效期: {result['expires_in']} 秒")
            
            # for link in result['download_links']:
            #     print(f"   成功文件:")
            #     print(f"     - ID: {link['file_id']}")
            #     print(f"     - 名称: {link['file_name']}")
            #     print(f"     - 下载链接: {link['download_url'][:100]}...")
            
            # for failed in result['failed_files']:
            #     print(f"   失败文件:")
            #     print(f"     - ID: {failed['file_id']}")
            #     print(f"     - 名称: {failed['file_name']}")
            #     print(f"     - 错误: {failed['error']}")
        else:
            print(f"❌ 获取下载链接失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 获取下载链接异常: {e}")


def test_health_check():
    """测试健康检查接口"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试健康检查...")
    
    try:
        response = requests.get(f"{base_url}/api/sessions/health")
        
        if response.status_code == 200:
            health_result = response.json()
            print(f"✅ 健康检查成功:")
            print(f"   - 状态: {health_result['status']}")
            print(f"   - 时间戳: {health_result['timestamp']}")
            print(f"   - 版本: {health_result['version']}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务已启动")
        return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False


def test_presigned_upload_url():
    """
    测试获取预签名上传链接接口 /api/files/get-upload-url
    """
    base_url = "http://localhost:8000"
    print("\n5. 测试获取预签名上传链接...")
    # 构造请求体
    file_name = f"test_presigned_{random.randint(1000,9999)}.txt"
    payload = {
        "file_info": {
            "FileName": file_name,
            "FileSize": 1234,
            "FileType": "txt"
        },
        "file_type": "sessionFile"
    }
    headers = {"Content-Type": "application/json"}
    try:
        response = requests.post(f"{base_url}/api/files/get-upload-url", json=payload, headers=headers)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 预签名上传链接获取成功:")
            print(f"   - file_id: {result['file_id']}")
            print(f"   - session_id: {result['session_id']}")
            print(f"   - upload_url: {result['upload_url'][:80]}...")
            print(f"   - expires_in: {result['expires_in']}")
            print(f"   - file_name: {result['file_name']}")
            print(f"   - file_size: {result['file_size']}")
            print(f"   - upload_method: {result['upload_method']}")
            return result['file_id'], result['session_id']
        else:
            print(f"❌ 预签名上传链接获取失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None, None
    except Exception as e:
        print(f"❌ 预签名上传接口异常: {e}")
        return None, None

def test_confirm_presigned_upload(file_id):
    """
    测试确认预签名上传接口 /api/files/confirm-upload
    """
    base_url = "http://localhost:8000"
    print("\n6. 测试确认预签名上传...")
    payload = {
        "file_id": str(file_id),
        "etag": "dummy-etag-value"
    }
    headers = {"Content-Type": "application/json"}
    try:
        response = requests.post(f"{base_url}/api/files/confirm-upload", json=payload, headers=headers)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 预签名上传确认成功:")
            print(f"   - file_id: {result['file_id']}")
            print(f"   - status: {result['status']}")
            print(f"   - message: {result['message']}")
        else:
            print(f"❌ 预签名上传确认失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"❌ 预签名上传确认接口异常: {e}")


if __name__ == "__main__":
    print("=" * 70)
    print("Alpha Service 文件服务功能测试")
    print("=" * 70)
    
    # 先测试健康检查
    # if not test_health_check():
    #     print("\n❌ 服务未正常运行，请检查服务状态")
    #     exit(1)
    
    # 测试文件上传和进度
    # file_id = test_file_upload_and_progress()
    
    # if file_id:
    # 测试获取会话文件列表
    file_ids = test_session_files("test_session_123")
    
    # 测试批量获取下载链接
    # test_download_urls(file_ids)

    # # 新增：测试预签名上传和确认
    # presigned_file_id, presigned_session_id = test_presigned_upload_url()
    # if presigned_file_id:
    #     test_confirm_presigned_upload(presigned_file_id)
    
    print("\n" + "=" * 70)
    print("测试完成")
    print("=" * 70)
