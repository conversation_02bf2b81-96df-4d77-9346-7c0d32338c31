# -*- coding: utf-8 -*-
"""
测试简化鉴权导入功能
"""
import pytest


class TestSimplifiedAuthImport:
    """测试简化鉴权导入功能"""
    
    def test_basic_import(self):
        """测试基本导入功能"""
        # 测试能否正常导入
        from src.domain.services.auth import auth_service, AuthContext
        
        assert auth_service is not None
        assert AuthContext is not None
    
    def test_enum_import(self):
        """测试枚举类型导入"""
        from src.domain.services.auth import PermissionType, ResourceType, ShareType
        
        assert PermissionType.READ.value == "read"
        assert ResourceType.FILE.value == "file"
        assert ShareType.PRIVATE.value == "private"
    
    def test_permission_constants_import(self):
        """测试权限常量导入"""
        from src.domain.services.auth import Permissions, Permission
        
        # 测试完整常量
        assert Permissions.READ == "read"
        assert Permissions.WRITE == "write"
        assert set(Permissions.OWNER) == {"read", "write", "delete", "share", "admin"}
        
        # 测试简短别名
        assert Permission.READ.value == "read"
        assert Permission.WRITE.value == "write"
        assert set(Permission.OWNER) == {"read", "write", "delete", "share", "admin"}
    
    def test_resource_type_aliases(self):
        """测试资源类型别名"""
        from src.domain.services.auth import Resource, ResourceType
        
        assert Resource.FILE == ResourceType.FILE.value
        assert Resource.SESSION == ResourceType.SESSION.value
        assert Resource.KNOWLEDGE_BASE == ResourceType.KNOWLEDGE_BASE.value
    
    def test_utility_functions_import(self):
        """测试工具函数导入"""
        from src.domain.services.auth import (
            check_permission, has_permission, has_all_permissions,
            PermissionUtils, normalize_permission
        )
        
        assert callable(check_permission)
        assert callable(has_permission)
        assert callable(has_all_permissions)
        assert PermissionUtils is not None
        assert callable(normalize_permission)
    
    def test_convenience_functions_import(self):
        """测试便捷函数导入"""
        from src.domain.services.auth import (
            get_current_user, require_auth,
            require_file_read_permission, require_file_write_permission
        )
        
        assert callable(get_current_user)
        assert callable(require_auth)
        assert callable(require_file_read_permission)
        assert callable(require_file_write_permission)
    
    def test_all_in_one_import(self):
        """测试一次性导入所有内容"""
        from src.domain.services.auth import (
            # 核心
            auth_service, AuthContext, PermissionParam,
            # 枚举
            PermissionType, ResourceType, ShareType,
            # 常量和别名
            Permissions, Permission, Resource,
            # 工具函数
            check_permission, has_permission, PermissionUtils,
            # 便捷函数
            require_file_read_permission, get_current_user
        )
        
        # 验证所有导入都成功
        assert auth_service is not None
        assert AuthContext is not None
        assert PermissionType is not None
        assert ResourceType is not None
        assert Permissions is not None
        assert Permission is not None
        assert Resource is not None
        assert callable(check_permission)
        assert callable(has_permission)
        assert callable(require_file_read_permission)
    
    def test_permission_aliases_equivalence(self):
        """测试权限别名的等价性"""
        from src.domain.services.auth import PermissionType, Permission, Permissions
        
        # 测试枚举别名
        assert Permission.READ == PermissionType.READ
        assert Permission.WRITE == PermissionType.WRITE
        assert Permission.DELETE == PermissionType.DELETE
        assert Permission.SHARE == PermissionType.SHARE
        assert Permission.ADMIN == PermissionType.ADMIN
        
        # 测试权限组合别名
        assert Permission.OWNER == Permissions.OWNER
        assert Permission.PUBLIC == Permissions.PUBLIC
        assert Permission.READ_ONLY == Permissions.READ_ONLY
        assert Permission.READ_WRITE == Permissions.READ_WRITE
    
    def test_convenience_functions_work(self):
        """测试便捷函数是否正常工作"""
        from src.domain.services.auth import has_permission, Permission
        
        user_permissions = ["read", "write"]
        
        # 测试使用枚举参数
        assert has_permission(user_permissions, Permission.READ) == True
        assert has_permission(user_permissions, Permission.WRITE) == True
        assert has_permission(user_permissions, Permission.DELETE) == False
        
        # 测试使用字符串参数
        assert has_permission(user_permissions, "read") == True
        assert has_permission(user_permissions, "delete") == False
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 旧的导入方式应该仍然有效
        from src.domain.services.auth_service import auth_service as old_auth_service
        from src.domain.services.auth import auth_service as new_auth_service
        
        # 应该是同一个实例
        assert old_auth_service is new_auth_service
    
    def test_mixed_usage_patterns(self):
        """测试混合使用模式"""
        from src.domain.services.auth import (
            auth_service, AuthContext, PermissionType, Permission, Resource, check_permission
        )
        
        context = AuthContext(ali_uid=123, wy_id="test_user")
        
        # 这些调用应该都是等价的（虽然在测试环境中可能会失败，但语法应该正确）
        try:
            # 使用完整枚举
            result1 = auth_service.check_resource_permission(
                context, Resource.FILE, "123", PermissionType.READ
            )
            
            # 使用简短别名
            result2 = auth_service.check_resource_permission(
                context, Resource.FILE, "123", Permission.READ
            )
            
            # 使用便捷函数
            result3 = check_permission(context, Resource.FILE, "123", Permission.READ)
            
            # 使用字符串
            result4 = auth_service.check_resource_permission(
                context, ResourceType.FILE, "123", PermissionType.READ
            )
            
            # 在实际环境中，这些结果应该是相同的
            # 在测试环境中，我们只验证调用不会出现语法错误
            
        except Exception as e:
            # 在测试环境中可能会因为缺少数据库等而失败，这是正常的
            # 我们主要测试导入和语法是否正确
            pass
    
    def test_module_all_attribute(self):
        """测试模块的__all__属性"""
        import src.domain.services.auth as auth_module
        
        # 检查__all__是否定义
        assert hasattr(auth_module, '__all__')
        assert isinstance(auth_module.__all__, list)
        assert len(auth_module.__all__) > 0
        
        # 检查主要的导出项是否在__all__中
        expected_exports = [
            'auth_service', 'AuthContext', 'PermissionType', 
            'ResourceType', 'Permissions', 'P', 'R'
        ]
        
        for export in expected_exports:
            assert export in auth_module.__all__, f"{export} should be in __all__"


if __name__ == "__main__":
    pytest.main([__file__])
