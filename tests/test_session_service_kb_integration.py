#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 SessionService 中知识库集成功能
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.domain.services.session_service import SessionService, SessionInfo, SessionListParams
from src.domain.services.auth_service import AuthContext
from src.infrastructure.database.models.session_models import SessionModel


class TestSessionServiceKbIntegration(unittest.TestCase):
    """测试 SessionService 知识库集成功能"""

    def setUp(self):
        """测试前准备"""
        self.session_service = SessionService()
        
        # 创建模拟的认证上下文
        self.auth_context = AuthContext(
            ali_uid=12345,
            wy_id="test_wy_id"
        )
        
        # 创建模拟的会话模型
        self.mock_session_model = SessionModel(
            id=1,
            session_id="sess_test123",
            ali_uid="12345",
            agent_id="agent_123",
            title="测试会话",
            status="ACTIVE",
            gmt_create=datetime.now(),
            gmt_modified=datetime.now(),
            meta_data={"total_rounds": 5, "last_user_prompt": "Hello"},
            wy_id="test_wy_id"
        )

    def test_session_info_with_is_in_kb_field(self):
        """测试 SessionInfo 对象包含 is_in_kb 字段"""
        # 测试默认值 False
        session_info = SessionInfo(self.mock_session_model)
        self.assertFalse(session_info.is_in_kb)
        
        # 测试设置为 True
        session_info_kb = SessionInfo(self.mock_session_model, is_in_kb=True)
        self.assertTrue(session_info_kb.is_in_kb)
        
        # 测试 to_dict 方法包含 isInKb 字段
        session_dict = session_info_kb.to_dict()
        self.assertIn("isInKb", session_dict)
        self.assertTrue(session_dict["isInKb"])

    @patch('src.domain.services.session_service.knowledgebase_service')
    @patch('src.domain.services.session_service.session_db_service')
    def test_get_user_sessions_with_kb_id(self, mock_session_db, mock_kb_service):
        """测试带 kb_id 参数的 get_user_sessions 方法"""
        # 准备模拟数据
        mock_session_models = [
            self.mock_session_model,
            SessionModel(
                id=2,
                session_id="sess_test456",
                ali_uid="12345",
                agent_id="agent_123",
                title="测试会话2",
                status="ACTIVE",
                gmt_create=datetime.now(),
                gmt_modified=datetime.now(),
                meta_data={},
                wy_id="test_wy_id"
            )
        ]
        
        # 模拟数据库查询
        mock_session_db.list_sessions.return_value = mock_session_models
        mock_session_db.count_sessions.return_value = 2
        
        # 模拟知识库服务返回
        mock_kb_service.is_knowledge_base_session.return_value = {
            "sess_test123": True,
            "sess_test456": False
        }
        
        # 创建查询参数
        params = SessionListParams(page_size=10)
        
        # 调用方法
        result = self.session_service.get_user_sessions(
            context=self.auth_context,
            params=params,
            kb_id="kb_test123"
        )
        
        # 验证结果
        self.assertEqual(len(result.sessions), 2)
        self.assertTrue(result.sessions[0].is_in_kb)  # sess_test123 应该为 True
        self.assertFalse(result.sessions[1].is_in_kb)  # sess_test456 应该为 False
        
        # 验证知识库服务被正确调用
        mock_kb_service.is_knowledge_base_session.assert_called_once_with(
            kb_id="kb_test123",
            session_id_list=["sess_test123", "sess_test456"]
        )

    @patch('src.domain.services.session_service.session_db_service')
    def test_get_user_sessions_without_kb_id(self, mock_session_db):
        """测试不带 kb_id 参数的 get_user_sessions 方法"""
        # 准备模拟数据
        mock_session_models = [self.mock_session_model]
        
        # 模拟数据库查询
        mock_session_db.list_sessions.return_value = mock_session_models
        mock_session_db.count_sessions.return_value = 1
        
        # 创建查询参数
        params = SessionListParams(page_size=10)
        
        # 调用方法（不传 kb_id）
        result = self.session_service.get_user_sessions(
            context=self.auth_context,
            params=params
        )
        
        # 验证结果
        self.assertEqual(len(result.sessions), 1)
        self.assertFalse(result.sessions[0].is_in_kb)  # 没有 kb_id 时应该为 False

    def test_session_info_to_dict_includes_is_in_kb(self):
        """测试 SessionInfo.to_dict() 包含 isInKb 字段"""
        session_info = SessionInfo(self.mock_session_model, is_in_kb=True)
        result_dict = session_info.to_dict()
        
        # 验证字典包含所有必要字段
        expected_fields = [
            "sessionId", "title", "agentId", "aliUid", "wyId", "status",
            "gmtCreate", "gmtModified", "totalRounds", "lastUserPrompt",
            "metadata", "isInKb"
        ]
        
        for field in expected_fields:
            self.assertIn(field, result_dict)
        
        # 验证 isInKb 字段值正确
        self.assertTrue(result_dict["isInKb"])


if __name__ == '__main__':
    unittest.main()
