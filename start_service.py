#!/usr/bin/env python3
"""
Alpha Service 启动脚本
"""

# 在导入任何其他模块之前，先设置早期日志拦截
import src.shared.logging.early_intercept

import asyncio
import uvicorn

# 初始化日志配置
from src.shared.logging.logger import logger

async def start_service():
    """启动服务"""
    try:
        logger.info("🚀 Alpha Service 启动中...")

        # 初始化无AK认证环境（在导入应用之前）
        try:
            from src.shared.auth import init_akless_auth
            logger.info("初始化无AK认证环境...")
            akless_success = init_akless_auth()
            if akless_success:
                logger.info("✅ 无AK认证环境初始化成功")
            else:
                logger.warning("⚠️ 无AK认证环境初始化失败，RAG客户端可能无法正常工作")
        except Exception as e:
            logger.error(f"无AK认证环境初始化异常: {e}")
            # 不抛出异常，允许应用继续启动

        # 导入应用
        from src.presentation.api.pythonic_server import pythonic_app as app

        # 配置uvicorn
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            reload=False,
            workers=8,
            access_log=False  # 禁用访问日志，避免健康检查日志
        )
        server = uvicorn.Server(config)
        await server.serve()

    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(start_service()) 