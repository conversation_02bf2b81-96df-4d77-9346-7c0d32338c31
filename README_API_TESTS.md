# API 测试脚本使用说明

本目录包含了用于测试 `/api/sessions/send` 接口的测试脚本，特别是测试 `desktopId` 功能。

## 📁 文件说明

### 测试脚本
- `test_api_sessions_send.py` - 完整的API测试脚本
- `test_desktop_id_simple.py` - 简化版桌面ID测试脚本
- `test_config_template.py` - 配置文件模板

### 文档
- `README_API_TESTS.md` - 本说明文件

## 🚀 快速开始

### 1. 准备配置文件

```bash
# 复制配置模板
cp test_config_template.py test_config.py

# 编辑配置文件，填写您的登录令牌
vim test_config.py  # 或使用其他编辑器
```

### 2. 配置登录信息

在 `test_config.py` 中填写：

```python
# 必填：您的登录令牌
LOGIN_TOKEN = "your_actual_login_token_here"

# 可选：API服务器地址（如果不是本地）
BASE_URL = "http://your-api-server:8000"
```

### 3. 运行测试

```bash
# 运行简化版测试（推荐）
python test_desktop_id_simple.py

# 或运行完整测试
python test_api_sessions_send.py
```

## 🧪 测试内容

### Desktop ID 功能测试

测试不同类型的桌面ID：

1. **普通桌面ID** - `desktop_001`
2. **AgentBay桌面** - `agentbay`
3. **自定义桌面ID** - `desktop_test_123`
4. **无桌面ID** - `None`

### 会话管理测试

1. **新会话创建** - 不传递 `session_id`
2. **会话延续** - 传递已存在的 `session_id`
3. **资源传递** - 测试 `resources` 参数
4. **错误处理** - 测试各种错误情况

### STS Token 集成测试

当传递 `desktop_id` 时，测试：
- STS Token 自动获取
- Redis 缓存机制
- 运行时资源配置

## 📋 测试参数说明

### SendMessageRequest 参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `Prompt` | string | ✅ | 用户消息内容 |
| `AgentId` | string | ✅ | Agent ID |
| `SessionId` | string | ❌ | 会话ID，空则创建新会话 |
| `DesktopId` | string | ❌ | 桌面ID，触发STS Token获取 |
| `AuthCode` | string | ❌ | 认证码 |
| `Resources` | array | ❌ | 资源列表 |

### 响应格式

```json
{
  "Code": 200,
  "Msg": "success",
  "Data": {
    "SessionId": "session_12345",
    "RoundId": "round_67890"
  }
}
```

## 🔧 配置选项

### 基本配置

```python
# API服务器地址
BASE_URL = "http://localhost:8000"

# 登录令牌
LOGIN_TOKEN = "your_token_here"

# 请求超时（秒）
REQUEST_TIMEOUT = 30.0
```

### 测试数据配置

```python
# 测试用的桌面ID
TEST_DESKTOP_IDS = [
    "desktop_001",
    "agentbay",
    "desktop_test_123",
    None
]

# 测试用的Agent ID
TEST_AGENT_IDS = [
    "test_agent",
    "desktop_agent",
    "chat_agent"
]
```

### 高级配置

```python
# 测试间隔时间
TEST_INTERVAL = 1.0

# 是否启用详细日志
VERBOSE_LOGGING = True

# 是否保存测试结果
SAVE_RESULTS = True
RESULTS_FILE = "test_results.json"
```

## 📊 测试结果示例

### 成功输出

```
✅ 已加载 test_config.py 配置文件
🚀 开始 Desktop ID 功能测试
=== 检查配置 ===
✅ 配置检查通过

=== 开始测试不同的桌面ID ===

1. 测试桌面ID: desktop_001
----------------------------------------
发送请求: Desktop ID = desktop_001
✅ 请求成功
会话ID: session_abc123
轮次ID: round_def456

2. 测试桌面ID: agentbay
----------------------------------------
发送请求: Desktop ID = agentbay
✅ 请求成功
会话ID: session_xyz789
轮次ID: round_uvw012

🎉 所有测试通过！Desktop ID 功能正常工作
```

### 错误输出

```
❌ 请求失败: 401
错误响应: {"Code": 401, "Msg": "Unauthorized"}

⚠️ 部分测试失败，请检查日志
```

## 🐛 故障排除

### 常见问题

1. **401 Unauthorized**
   - 检查 `LOGIN_TOKEN` 是否正确
   - 确认令牌是否已过期

2. **Connection Error**
   - 检查 `BASE_URL` 是否正确
   - 确认API服务器是否运行

3. **Timeout Error**
   - 增加 `REQUEST_TIMEOUT` 值
   - 检查网络连接

4. **JSON Decode Error**
   - 检查API响应格式
   - 查看原始响应内容

### 调试技巧

1. **启用详细日志**：
   ```python
   VERBOSE_LOGGING = True
   ```

2. **查看原始响应**：
   ```python
   logger.info(f"原始响应: {response.text}")
   ```

3. **保存测试结果**：
   ```python
   SAVE_RESULTS = True
   RESULTS_FILE = "debug_results.json"
   ```

## 🔍 测试重点

### Desktop ID 功能验证

1. **STS Token 获取**：
   - 当传递 `desktop_id` 时，系统应自动获取STS Token
   - Token应该被正确缓存（1小时）

2. **运行时资源配置**：
   - `desktop` 类型资源应使用STS Token
   - `agentbay` 类型资源应使用原始auth_code

3. **错误处理**：
   - STS Token获取失败时应回退到auth_code
   - 网络错误应有适当的错误信息

### 会话管理验证

1. **新会话创建**：
   - 不传递 `session_id` 应创建新会话
   - 返回有效的 `session_id` 和 `round_id`

2. **会话延续**：
   - 传递已存在的 `session_id` 应延续会话
   - 返回相同的 `session_id` 和新的 `round_id`

## 📝 注意事项

1. **安全性**：
   - 不要在代码中硬编码生产环境的登录令牌
   - 使用配置文件或环境变量

2. **测试环境**：
   - 建议在测试环境中运行，避免影响生产数据
   - 使用测试专用的Agent ID和桌面ID

3. **频率限制**：
   - 注意API的频率限制
   - 适当设置测试间隔时间

4. **资源清理**：
   - 测试完成后清理创建的会话
   - 避免产生过多测试数据

## 🤝 贡献

如果您发现问题或有改进建议，请：

1. 查看现有的测试用例
2. 添加新的测试场景
3. 完善错误处理逻辑
4. 更新文档说明

---

**Happy Testing! 🎉**
