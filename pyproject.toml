[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

# 添加 pip 配置，优先使用预编译包
[tool.pip]
prefer-binary = true
only-binary = ["grpcio", "grpcio-tools", "protobuf"]

[project]
name = "alpha-service"
version = "0.1.0"
description = "Alpha Service - Python微服务项目"
readme = "README.md"
authors = [
    {name = "ShenLang", email = "<EMAIL>"}
]
license = {text = "Proprietary"}
requires-python = ">=3.9"
dependencies = [
    # 核心框架依赖
    "fastapi>=0.100.0",
    "uvicorn>=0.20.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",

    "keycenter-office==2.3.9",
    "waiy-memory==0.1.48",
    "alibabacloud-credentials",
    "alibabacloud-tea-openapi",
    "alibabacloud-tea-util",
    "alibabacloud_rocketmq20220801",
    "alibabacloud-ons20190214",
    
    "alibabacloud_wuyingaiinner20250708==1.2.0",
    
    # Web框架相关
    "flask>=2.0.0",                # Flask应用框架
    
    # 配置管理
    "dynaconf>=3.2.0",             # 多环境配置管理
    
    # 配置中心相关
    "nacos-sdk-python==0.1.16",   # Diamond配置中心
    "python-dotenv",               # 环境变量支持
    
    # KeyCenter加解密相关
    "python-gnupg",                # GPG加密支持
    "gmssl",                       # GM SSL支持
    "pycryptodomex",               # 加密算法库
    
    # 数据库相关
    "pymysql",                     # MySQL连接
    "sqlalchemy>=1.4.0",          # ORM框架
    
    # 日志和监控
    "logfire>=3.16.1",
    "opentelemetry-instrumentation-fastapi",
    "loguru",
    "rich>=12.0.0",
    
    # Session管理和SSE相关
    "sse-starlette>=1.6.0",       # SSE支持
    "asyncio",                     # 异步任务
    "aiofiles",                    # 异步文件操作
    "redis>=4.0.0",                # Redis缓存，用于Session状态存储
    
    # AI/机器学习相关
    "mem0ai",                        # 记忆存储框架
    "langchain-community",         # LangChain社区扩展

    # 阿里云OSS相关
    "oss2",                        # 阿里云OSS SDK
    "aliyun-akless-credential-provider-python-sdk==1.10.1",  # 阿里云无密钥认证
    "python-multipart",

    # 阿里云无影AI内部服务相关
    #"alibabacloud-wuyingaiinner20250708",  # 无影AI内部服务SDK
    "alibabacloud-wuyingaiinner20250709==1.2.5",  # 无影AI内部服务SDK
    
    # 日期时间处理
    "python-dateutil",             # 日期解析库
    
    # 系统/进程管理
    "circus",                      # 进程管理工具
    "uvloop",                      # 高性能事件循环
    
    # 配置文件处理
    "pyyaml",                      # YAML配置文件支持
    
    # 其他工具
    "httpx",
    "requests",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.19.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "mypy>=0.990",
]

[project.scripts]
alpha-app = "main:main"
alpha-api = "api:main" 