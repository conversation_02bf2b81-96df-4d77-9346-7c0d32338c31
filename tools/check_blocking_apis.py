#!/usr/bin/env python3
"""
FastAPI阻塞API检查工具
自动扫描项目中可能导致阻塞的API，并提供修复建议
"""

import os
import re
import ast
from typing import List, Dict, Tuple
from pathlib import Path

class BlockingAPIChecker:
    """阻塞API检查器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.routes_dir = self.project_root / "src" / "presentation" / "api" / "routes"
        
        # 可能导致阻塞的函数调用模式
        self.blocking_patterns = [
            r'time\.sleep\(',
            r'requests\.(get|post|put|delete|patch)',
            r'open\(',
            r'\.read\(\)',
            r'\.write\(',
            r'subprocess\.',
            r'os\.system\(',
            r'\.execute\(',  # 可能的同步数据库调用
            r'\.query\(',    # 可能的同步数据库调用
        ]
        
        # 已知的慢服务调用
        self.slow_service_patterns = [
            r'ppt_service\.',
            r'knowledgebase_service\.',
            r'file_service\.',
            r'oss_service\.',
            r'rag_client\.',
        ]
    
    def scan_routes_directory(self) -> List[Dict]:
        """扫描路由目录中的所有Python文件"""
        results = []
        
        if not self.routes_dir.exists():
            print(f"❌ 路由目录不存在: {self.routes_dir}")
            return results
        
        for py_file in self.routes_dir.glob("*.py"):
            if py_file.name.startswith("__"):
                continue
                
            file_results = self.check_file(py_file)
            if file_results:
                results.extend(file_results)
        
        return results
    
    def check_file(self, file_path: Path) -> List[Dict]:
        """检查单个文件中的阻塞API"""
        results = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析AST
            tree = ast.parse(content)
            
            # 查找路由函数
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    # 检查是否是路由函数
                    if self.is_route_function(node):
                        issues = self.check_function(node, content, file_path)
                        if issues:
                            results.extend(issues)
        
        except Exception as e:
            print(f"⚠️  解析文件失败 {file_path}: {e}")
        
        return results
    
    def is_route_function(self, node: ast.FunctionDef) -> bool:
        """判断是否是路由函数"""
        for decorator in node.decorator_list:
            if isinstance(decorator, ast.Attribute):
                if isinstance(decorator.value, ast.Name) and decorator.value.id == "router":
                    return True
            elif isinstance(decorator, ast.Call):
                if isinstance(decorator.func, ast.Attribute):
                    if isinstance(decorator.func.value, ast.Name) and decorator.func.value.id == "router":
                        return True
        return False
    
    def check_function(self, node: ast.FunctionDef, content: str, file_path: Path) -> List[Dict]:
        """检查函数中的阻塞问题"""
        issues = []
        
        # 获取函数源码
        lines = content.split('\n')
        func_start = node.lineno - 1
        func_end = node.end_lineno if hasattr(node, 'end_lineno') else len(lines)
        func_content = '\n'.join(lines[func_start:func_end])
        
        # 检查是否是async函数
        is_async = isinstance(node, ast.AsyncFunctionDef) or 'async def' in func_content
        
        if is_async:
            # 检查async函数中的阻塞调用
            blocking_calls = self.find_blocking_calls(func_content)
            if blocking_calls:
                issues.append({
                    'file': str(file_path.relative_to(self.project_root)),
                    'function': node.name,
                    'line': node.lineno,
                    'type': 'async_with_blocking',
                    'severity': 'high',
                    'blocking_calls': blocking_calls,
                    'suggestion': 'change_to_def'
                })
        
        # 检查慢服务调用
        slow_calls = self.find_slow_service_calls(func_content)
        if slow_calls:
            severity = 'high' if any('ppt_service' in call for call in slow_calls) else 'medium'
            issues.append({
                'file': str(file_path.relative_to(self.project_root)),
                'function': node.name,
                'line': node.lineno,
                'type': 'slow_service_call',
                'severity': severity,
                'slow_calls': slow_calls,
                'suggestion': 'change_to_def' if is_async else 'already_sync'
            })
        
        return issues
    
    def find_blocking_calls(self, func_content: str) -> List[str]:
        """查找阻塞调用"""
        blocking_calls = []
        
        for pattern in self.blocking_patterns:
            matches = re.findall(pattern, func_content)
            blocking_calls.extend(matches)
        
        return list(set(blocking_calls))
    
    def find_slow_service_calls(self, func_content: str) -> List[str]:
        """查找慢服务调用"""
        slow_calls = []
        
        for pattern in self.slow_service_patterns:
            matches = re.findall(pattern + r'\w+', func_content)
            slow_calls.extend(matches)
        
        return list(set(slow_calls))
    
    def generate_report(self, issues: List[Dict]) -> str:
        """生成检查报告"""
        if not issues:
            return "✅ 未发现阻塞API问题！"
        
        report = []
        report.append("🔍 FastAPI阻塞API检查报告")
        report.append("=" * 50)
        
        # 按严重程度分组
        high_issues = [i for i in issues if i['severity'] == 'high']
        medium_issues = [i for i in issues if i['severity'] == 'medium']
        
        if high_issues:
            report.append(f"\n🚨 高风险问题 ({len(high_issues)}个):")
            for issue in high_issues:
                report.append(f"  📁 {issue['file']}:{issue['line']}")
                report.append(f"     函数: {issue['function']}")
                if issue['type'] == 'async_with_blocking':
                    report.append(f"     问题: async函数中有阻塞调用")
                    report.append(f"     阻塞调用: {', '.join(issue['blocking_calls'])}")
                elif issue['type'] == 'slow_service_call':
                    report.append(f"     问题: 慢服务调用")
                    report.append(f"     慢调用: {', '.join(issue['slow_calls'])}")
                report.append(f"     建议: {self.get_suggestion(issue['suggestion'])}")
                report.append("")
        
        if medium_issues:
            report.append(f"\n⚠️  中风险问题 ({len(medium_issues)}个):")
            for issue in medium_issues:
                report.append(f"  📁 {issue['file']}:{issue['line']}")
                report.append(f"     函数: {issue['function']}")
                report.append(f"     慢调用: {', '.join(issue.get('slow_calls', []))}")
                report.append(f"     建议: {self.get_suggestion(issue['suggestion'])}")
                report.append("")
        
        # 修复建议
        report.append("\n🔧 快速修复指南:")
        report.append("1. 高风险问题：立即修复，将 'async def' 改为 'def'")
        report.append("2. 中风险问题：优先修复，评估是否需要异步化")
        report.append("3. 详细指南：docs/FastAPI_Performance_Optimization_Guide.md")
        
        return "\n".join(report)
    
    def get_suggestion(self, suggestion_type: str) -> str:
        """获取修复建议"""
        suggestions = {
            'change_to_def': '将 async def 改为 def',
            'already_sync': '已经是同步函数，考虑优化底层调用',
            'use_to_thread': '使用 asyncio.to_thread() 包装同步调用'
        }
        return suggestions.get(suggestion_type, '需要进一步分析')


def main():
    """主函数"""
    print("🔍 开始检查FastAPI阻塞API...")
    
    checker = BlockingAPIChecker()
    issues = checker.scan_routes_directory()
    report = checker.generate_report(issues)
    
    print(report)
    
    # 保存报告到文件
    report_file = Path("blocking_api_report.txt")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📄 报告已保存到: {report_file}")
    
    # 返回退出码
    high_issues = [i for i in issues if i['severity'] == 'high']
    if high_issues:
        print(f"\n❌ 发现 {len(high_issues)} 个高风险问题，请立即修复！")
        return 1
    else:
        print(f"\n✅ 未发现高风险问题")
        return 0


if __name__ == "__main__":
    exit(main())
