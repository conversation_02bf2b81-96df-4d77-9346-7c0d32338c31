#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行 FileService.get_download_urls 的测试
支持 pytest 和直接运行两种方式
"""

import sys
import os
import subprocess

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(__file__))


def run_pytest_tests():
    """运行 pytest 测试"""
    print("运行 pytest 单元测试...")
    print("=" * 50)
    
    try:
        # 运行 pytest
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/test_file_service_download_urls.py", 
            "-v", "--tb=short"
        ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
            
        print(f"返回码: {result.returncode}")
        
    except Exception as e:
        print(f"运行 pytest 失败: {e}")


def run_simple_test():
    """运行简单的直接测试"""
    print("\n运行直接调用测试...")
    print("=" * 50)
    
    try:
        # 运行简单测试脚本
        result = subprocess.run([
            sys.executable, "test_download_urls_simple.py"
        ], cwd=os.path.dirname(__file__))
        
        print(f"返回码: {result.returncode}")
        
    except Exception as e:
        print(f"运行简单测试失败: {e}")


def check_environment():
    """检查测试环境"""
    print("检查测试环境...")
    print("=" * 50)
    
    # 检查必要的文件
    required_files = [
        "src/domain/services/file_service.py",
        "src/application/file_api_models.py",
        "tests/test_file_service_download_urls.py",
        "test_download_urls_simple.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("缺少以下文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("所有必要文件都存在 ✓")
    
    # 检查 Python 路径
    print(f"Python 路径: {sys.executable}")
    print(f"工作目录: {os.getcwd()}")
    print(f"Python 路径列表:")
    for path in sys.path[:5]:  # 只显示前5个
        print(f"  - {path}")
    
    return True


def main():
    """主函数"""
    print("FileService.get_download_urls 测试运行器")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        print("环境检查失败，退出测试")
        return
    
    print("\n选择测试方式:")
    print("1. 运行 pytest 单元测试（推荐）")
    print("2. 运行直接调用测试")
    print("3. 运行所有测试")
    print("4. 仅检查环境")
    
    try:
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == "1":
            run_pytest_tests()
        elif choice == "2":
            run_simple_test()
        elif choice == "3":
            run_pytest_tests()
            run_simple_test()
        elif choice == "4":
            print("环境检查已完成")
        else:
            print("无效选择，运行所有测试...")
            run_pytest_tests()
            run_simple_test()
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"运行测试时出错: {e}")


if __name__ == "__main__":
    main()
