[circus]
check_delay=5
endpoint=tcp://127.0.0.1:5555
pubsub_endpoint=tcp://127.0.0.1:5556
statsd=true
pidfile=/home/<USER>/circus.pid


[watcher:main]
copy_env=True
copy_path=True
working_dir=/home/<USER>/wuying-alpha-service/target/wuying-alpha-service/
use_sockets=True
cmd=python3 start_service.py
env.ENV_FOR_DYNACONF=pre
stop_signal=SIGTERM
stdout_stream.class=FileStream
stdout_stream.filename=/home/<USER>/wuying-alpha-service/logs/application.log
stdout_stream.max_bytes=104857600
stdout_stream.time_format = %Y-%m-%d %H:%M:%S
stdout_stream.backup_count=30
stderr_stream.class=FileStream
stderr_stream.filename=/home/<USER>/wuying-alpha-service/logs/error.log
stderr_stream.max_bytes=104857600
stderr_stream.backup_count=30
stderr_stream.time_format = %Y-%m-%d %H:%M:%S
