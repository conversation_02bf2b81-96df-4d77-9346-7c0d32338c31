# 错误日志分离配置指南

## 概述

现在服务支持将错误日志单独输出到 `error.log` 文件中，同时保持所有日志在主日志文件 `application.log` 中的完整记录。

## 功能特性

### 1. 双文件日志输出
- **主日志文件** (`application.log`)：包含所有级别的日志（DEBUG, INFO, WARNING, ERROR, CRITICAL）
- **错误日志文件** (`error.log`)：只包含错误级别的日志（ERROR, CRITICAL）

### 2. 自动路径生成
- 如果主日志文件是 `application.log`，错误日志文件自动设置为 `error.log`
- 如果主日志文件是其他名称，错误日志文件在同目录下创建为 `error.log`

### 3. 完整的 Request ID 支持
- 两个日志文件都包含完整的 `request_id` 信息
- 可以通过 `request_id` 在两个文件中追踪同一个请求

## 日志文件示例

### 主日志文件 (application.log)
```
2025-08-02 18:11:37 | DEBUG    | req-001 | module:function:line - 调试信息
2025-08-02 18:11:37 | INFO     | req-001 | module:function:line - 处理请求开始
2025-08-02 18:11:37 | WARNING  | req-001 | module:function:line - 警告信息
2025-08-02 18:11:37 | ERROR    | req-001 | module:function:line - 错误信息
2025-08-02 18:11:37 | CRITICAL | req-001 | module:function:line - 严重错误
```

### 错误日志文件 (error.log)
```
2025-08-02 18:11:37 | ERROR    | req-001 | module:function:line - 错误信息
2025-08-02 18:11:37 | CRITICAL | req-001 | module:function:line - 严重错误
```

## 配置方式

### 1. 自动配置（推荐）
服务启动时会自动根据主日志文件路径生成错误日志文件路径：

```python
# 在 src/shared/logging/logger.py 中自动配置
main_log_path = "logs/application.log"
error_log_path = "logs/error.log"  # 自动生成
```

### 2. 手动配置
如果需要自定义错误日志文件路径，可以修改 `init_logger_with_config` 函数：

```python
setup_logger(
    level="INFO",
    file_path="logs/application.log",
    error_file_path="logs/custom_error.log",  # 自定义路径
    rotation="100 MB",
    retention="30 days"
)
```

## 路径生成规则

| 主日志文件路径 | 错误日志文件路径 |
|---------------|-----------------|
| `logs/application.log` | `logs/error.log` |
| `/var/log/alpha-service/application.log` | `/var/log/alpha-service/error.log` |
| `application.log` | `error.log` |
| `logs/app.log` | `logs/error.log` |
| `logs/service.log` | `logs/error.log` |

## 使用场景

### 1. 错误监控
只需要监控 `error.log` 文件即可获取所有错误信息：

```bash
# 实时监控错误日志
tail -f logs/error.log

# 统计错误数量
grep -c "ERROR\|CRITICAL" logs/error.log

# 查找特定时间的错误
grep "2025-08-02 18:" logs/error.log
```

### 2. 日志分析
可以分别分析不同类型的日志：

```bash
# 分析所有日志
grep "req-123" logs/application.log

# 只分析错误日志
grep "req-123" logs/error.log

# 统计请求的错误率
total=$(grep -c "req-123" logs/application.log)
errors=$(grep -c "req-123" logs/error.log)
echo "错误率: $((errors * 100 / total))%"
```

### 3. 告警配置
可以基于错误日志文件配置告警：

```bash
# 检查最近5分钟的错误数量
errors=$(find logs/error.log -mmin -5 -exec wc -l {} \;)
if [ $errors -gt 10 ]; then
    echo "错误数量过多: $errors"
    # 发送告警
fi
```

## 日志轮转配置

两个日志文件使用相同的轮转配置：

- **轮转大小**：100 MB
- **保留时间**：30 天
- **压缩方式**：ZIP 压缩
- **文件命名**：
  - `application.log.2025-08-02_18-00-00_0.zip`
  - `error.log.2025-08-02_18-00-00_0.zip`

## 性能影响

### 1. 磁盘空间
- 错误日志文件通常比主日志文件小很多
- 只有 ERROR 和 CRITICAL 级别的日志会被重复写入
- 对于正常运行的服务，额外的磁盘占用很小

### 2. 写入性能
- 使用 loguru 的高效写入机制
- 异步写入，不阻塞业务逻辑
- 性能影响可以忽略不计

## 故障排查

### 1. 检查日志文件是否正常创建
```bash
ls -la logs/
# 应该看到 application.log 和 error.log 两个文件
```

### 2. 验证错误日志内容
```bash
# 检查错误日志是否只包含 ERROR 和 CRITICAL
grep -v "ERROR\|CRITICAL" logs/error.log
# 如果有输出，说明配置有问题
```

### 3. 验证 Request ID
```bash
# 检查两个文件中的 request_id 是否一致
grep "req-123" logs/application.log | wc -l
grep "req-123" logs/error.log | wc -l
```

## 最佳实践

### 1. 监控建议
- 主要监控 `error.log` 文件的增长
- 设置错误日志文件大小告警
- 定期检查错误日志的内容

### 2. 日志保留策略
- 错误日志可以保留更长时间（如 90 天）
- 主日志可以保留较短时间（如 30 天）
- 根据磁盘空间调整保留策略

### 3. 日志分析工具
- 使用 ELK Stack 分析日志
- 配置 Grafana 监控错误率
- 使用 logrotate 管理日志轮转

## 配置验证

运行测试脚本验证配置是否正确：

```bash
cd /Users/<USER>/projects/alpha-service
source venv/bin/activate
python tests/test_error_log_separation.py
```

测试会验证：
- 日志文件是否正确创建
- 错误日志是否只包含 ERROR 和 CRITICAL 级别
- Request ID 是否正确记录
- 日志格式是否一致

## 注意事项

1. **文件权限**：确保应用有权限写入日志目录
2. **磁盘空间**：监控磁盘空间，避免日志文件占满磁盘
3. **日志轮转**：确保日志轮转正常工作
4. **备份策略**：重要的错误日志应该定期备份

## 故障恢复

如果日志文件出现问题：

1. **重启服务**：日志系统会自动重新创建文件
2. **检查权限**：确保目录和文件权限正确
3. **清理空间**：如果磁盘满了，清理旧的日志文件
4. **恢复配置**：检查日志配置是否被意外修改
