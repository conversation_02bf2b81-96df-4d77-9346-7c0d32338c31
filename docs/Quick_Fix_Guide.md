# FastAPI 慢API快速修复指南

## 🚀 5分钟快速修复

### 第一步：运行检查工具

```bash
# 在项目根目录运行
python tools/check_blocking_apis.py
```

### 第二步：查看检查结果

工具会输出类似这样的报告：
```
🚨 高风险问题 (3个):
  📁 src/presentation/api/routes/ppt_routes.py:44
     函数: save_ppt
     问题: 慢服务调用
     慢调用: ppt_service.save_ppt
     建议: 将 async def 改为 def

  📁 src/presentation/api/routes/rag_routes.py:42
     函数: create_knowledge_base
     问题: 慢服务调用
     慢调用: knowledgebase_service.create_knowledge_base
     建议: 将 async def 改为 def
```

### 第三步：修复问题API

**最简单的修复方法**：删除 `async` 关键字

```python
# ❌ 修复前
@router.post("/save")
async def save_ppt(...):
    response = ppt_service.save_ppt(...)
    return package_api_result(data=response)

# ✅ 修复后
@router.post("/save")
def save_ppt(...):  # 删除 async 关键字
    response = ppt_service.save_ppt(...)
    return package_api_result(data=response)
```

### 第四步：验证修复效果

```bash
# 重新运行检查
python tools/check_blocking_apis.py

# 运行性能测试
python tests/test_optimized_apis_performance.py
```

## 📋 修复优先级

### 🔥 立即修复（高风险）
- PPT相关接口 - 可能阻塞60秒
- 任何包含轮询、长时间等待的接口

### ⚠️ 优先修复（中风险）
- 知识库操作 - 阻塞1-2秒
- 文件批量操作 - 阻塞几秒到几十秒

### ✅ 可选修复（低风险）
- 简单的数据库查询 - 阻塞100-500ms
- 文件重命名等轻量操作

## 🛠️ 修复模板

### 模板1：纯同步操作

```python
# 适用于：文件处理、数据库操作、外部API调用
@router.post("/your-api")
def your_api(...):  # 使用 def
    result = sync_operation(...)
    return {"result": result}
```

### 模板2：混合操作

```python
# 适用于：需要混合异步和同步操作
@router.post("/your-api")
async def your_api(...):  # 保持 async def
    # 异步操作
    user = await get_user_async(user_id)
    
    # 同步操作放到线程池
    result = await asyncio.to_thread(sync_operation, data)
    
    return {"result": result}
```

## ⚠️ 注意事项

1. **测试验证**：修改后在测试环境验证功能正常
2. **性能监控**：观察 `X-Process-Time` 响应头
3. **并发测试**：确保高并发下其他API不受影响
4. **代码审查**：让团队成员review修改

## 📞 需要帮助？

1. 查看详细文档：`docs/FastAPI_Performance_Optimization_Guide.md`
2. 运行性能测试：`tests/test_optimized_apis_performance.py`
3. 联系架构团队进行代码审查

---

**记住**：删除 `async` 关键字是最简单有效的修复方法！
