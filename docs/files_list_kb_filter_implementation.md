# /files/list 接口知识库过滤功能实现

## 功能概述

为 `/files/list` 接口添加了知识库过滤功能，仿照 `/sessions/list` 和 `/sessions/query` 接口的逻辑，当提供 `kb_id` 参数时，会调用知识库服务判断文件是否属于指定知识库，并为每个文件添加 `is_in_kb` 字段。

## 实现详情

### 1. 数据模型修改

#### FileInfoResponse 模型扩展
**文件**: `src/application/file_api_models.py`

```python
class FileInfoResponse(BaseModel):
    """文件信息"""
    file_id: str = Field(..., description="文件ID")
    artifact_id: Optional[str] = Field(None, description="制品ID，用于标识制品文件")
    file_name: str = Field(..., description="文件名")
    file_size: Optional[int] = Field(None, description="文件大小（字节）")
    content_type: Optional[str] = Field(None, description="文件MIME类型")
    doc_id: Optional[str] = Field(None, description="RAG解析返回的文档ID")
    artifact_type: str = Field(..., description="文件类型")
    upload_status: str = Field(..., description="上传状态: uploading/analyzing/completed/failed")
    download_url: Optional[str] = Field(None, description="下载链接")
    gmt_created: str = Field(..., description="创建时间")
    gmt_modified: str = Field(..., description="修改时间")
    is_in_kb: bool = Field(False, description="是否在知识库中")  # 新增字段
```

#### from_orm_model 方法更新
```python
@classmethod
def from_orm_model(cls, orm_obj, is_in_kb: bool = False):
    return cls(
        # ... 其他字段
        is_in_kb=is_in_kb,  # 新增参数
    )
```

#### SessionFilesRequest 模型扩展
```python
class SessionFilesRequest(BaseModel):
    """会话文件请求"""
    session_id: str = Field(...,  description="会话ID")
    artifact_types: Optional[List[str]] = Field(None,  description="文件类型")
    max_results: int = Field(100,  description="最大结果数")
    next_token: Optional[str] = Field(None,  description="下一页标记")
    kb_id: Optional[str] = Field(None, description="知识库ID，查询是否在该知识库中")  # 新增字段
```

### 2. 业务服务层扩展

#### FileService 新增方法
**文件**: `src/domain/services/file_service.py`

```python
def set_files_kb_relationship(self, files: List, session_id: str, kb_id: Optional[str] = None) -> None:
    """
    为文件列表设置知识库关系字段
    
    Args:
        files: 文件信息列表（FileInfoResponse对象）
        session_id: 会话ID
        kb_id: 知识库ID，如果为空则所有文件的is_in_kb都设置为False
    """
    if not files:
        logger.debug("[FileService] 文件列表为空，跳过知识库关系设置")
        return
    
    if not kb_id:
        # 如果没有提供kb_id，为所有文件设置is_in_kb为False
        for file_info in files:
            file_info.is_in_kb = False
        logger.debug(f"[FileService] 未提供kb_id，为 {len(files)} 个文件设置is_in_kb=False")
        return
    
    try:
        # 提取所有文件ID
        file_id_list = [file_info.file_id for file_info in files]
        logger.info(f"[FileService] 检查 {len(file_id_list)} 个文件是否属于知识库: kb_id={kb_id}, session_id={session_id}")
        
        # 调用知识库服务判断文件是否属于知识库
        from .knowledge_service import knowledgebase_service
        kb_file_results = knowledgebase_service.is_knowledge_base_file(
            kb_id=kb_id,
            session_id=session_id,
            file_id_list=file_id_list
        )
        
        logger.info(f"[FileService] 知识库文件判断结果: {len(kb_file_results)} 个文件有结果")
        
        # 为每个文件设置is_in_kb字段
        for file_info in files:
            file_info.is_in_kb = kb_file_results.get(file_info.file_id, False)
        
        logger.info(f"[FileService] 已为所有文件设置is_in_kb字段")
        
    except Exception as e:
        logger.error(f"[FileService] 知识库文件判断失败: kb_id={kb_id}, session_id={session_id}, error={e}")
        # 如果判断失败，为所有文件设置is_in_kb为False
        for file_info in files:
            file_info.is_in_kb = False
```

### 3. API 接口修改

**文件**: `src/presentation/api/routes/file_routes.py`

#### 核心逻辑
```python
@router.post("/files/list")
async def list_session_files(
    request: SessionFilesRequest,  # 已包含 kb_id 字段
    context: AuthContext = Depends(require_auth),
    common_params: CommonParams = UniversalCommonParams,
    request_id: str = Depends(get_request_id_dependency)
):
    # ... 原有逻辑
    
    # 获取文件列表
    result = file_service.get_session_files(
        owner_ali_uid=context.ali_uid,
        owner_wy_id=context.wy_id,
        session_id=request.session_id,
        file_types=file_types_list,
        max_result=request.max_results,
        next_token=request.next_token
    )

    # 设置文件的知识库关系
    if result.data:
        file_service.set_files_kb_relationship(
            files=result.data, 
            session_id=request.session_id,
            kb_id=request.kb_id
        )

    # ... 返回结果
```

### 4. 知识库服务集成

**文件**: `src/domain/services/knowledge_service.py`

使用现有的 `is_knowledge_base_file` 方法：
```python
def is_knowledge_base_file(
    self, kb_id: str, session_id: str, file_id_list: List[str]
) -> Dict[str, bool]:
    """
    判断文件是否属于知识库
    
    Args:
        kb_id: 知识库ID
        session_id: 会话ID
        file_id_list: 文件ID列表
        
    Returns:
        Dict[str, bool]: 键为file_id，值为是否属于知识库
    """
```

## API 使用示例

### 请求示例

```bash
# 不带知识库过滤
POST /files/list
{
  "session_id": "sess_123",
  "max_results": 20
}

# 带知识库过滤
POST /files/list
{
  "session_id": "sess_123",
  "kb_id": "kb_456",
  "max_results": 20
}
```

### 响应示例

```json
{
  "code": "OK",
  "success": true,
  "data": {
    "data": [
      {
        "file_id": "file_001",
        "artifact_id": "artifact_001",
        "file_name": "文档1.pdf",
        "file_size": 1024000,
        "content_type": "application/pdf",
        "doc_id": "doc_001",
        "artifact_type": "sessionFile",
        "upload_status": "completed",
        "download_url": "https://example.com/file1.pdf",
        "gmt_created": "2025-08-04T10:00:00Z",
        "gmt_modified": "2025-08-04T10:30:00Z",
        "is_in_kb": true
      },
      {
        "file_id": "file_002",
        "artifact_id": "artifact_002",
        "file_name": "图片1.jpg",
        "file_size": 512000,
        "content_type": "image/jpeg",
        "doc_id": null,
        "artifact_type": "sessionFile",
        "upload_status": "completed",
        "download_url": "https://example.com/image1.jpg",
        "gmt_created": "2025-08-04T09:00:00Z",
        "gmt_modified": "2025-08-04T09:30:00Z",
        "is_in_kb": false
      }
    ],
    "max_result": 2,
    "next_token": null
  }
}
```

## 处理逻辑流程

1. **接收请求参数**
   - 解析 `SessionFilesRequest`，包含可选的 `kb_id` 参数
   - 其他参数保持不变

2. **调用文件服务**
   - 调用 `file_service.get_session_files()` 获取会话文件列表
   - 获取 `SessionFilesResponse` 对象

3. **知识库关系判断**
   - 如果有文件数据且 `kb_id` 不为空：
     - 提取所有文件ID列表
     - 调用 `knowledgebase_service.is_knowledge_base_file(kb_id, session_id, file_id_list)`
     - 根据返回结果为每个文件设置 `is_in_kb` 字段
   - 如果 `kb_id` 为空：
     - 为所有文件设置 `is_in_kb = False`

4. **异常处理**
   - 如果知识库服务调用失败，记录错误日志
   - 为所有文件设置 `is_in_kb = False`，确保接口正常返回

5. **返回结果**
   - 返回包含 `isInKb` 字段的文件列表

## 错误处理

### 知识库服务异常
```python
try:
    kb_file_results = knowledgebase_service.is_knowledge_base_file(
        kb_id=kb_id,
        session_id=session_id,
        file_id_list=file_id_list
    )
except Exception as e:
    logger.error(f"[FileService] 知识库文件判断失败: kb_id={kb_id}, session_id={session_id}, error={e}")
    # 设置默认值，确保接口稳定性
    for file_info in files:
        file_info.is_in_kb = False
```

### 参数验证
- `kb_id` 为可选参数，可以为空
- 当 `kb_id` 为空时，所有文件的 `is_in_kb` 都设置为 `False`
- 当没有文件数据时，不调用知识库服务

## 性能考虑

1. **批量查询**：一次调用查询所有文件的知识库关系，避免循环调用
2. **异常容错**：知识库服务异常不影响主要功能
3. **条件执行**：只有在有文件数据且提供 `kb_id` 时才调用知识库服务

## 测试验证

### 功能测试
✅ FileInfoResponse 类支持 `is_in_kb` 字段  
✅ SessionFilesRequest 类支持 `kb_id` 字段  
✅ 知识库服务方法存在且参数正确  
✅ 文件服务支持新的设置方法  
✅ API 路由包含所有必要的代码片段  
✅ from_orm_model 方法支持 `is_in_kb` 参数  
✅ 逻辑流程设计合理  

### 测试用例
- 带 `kb_id` 参数的请求
- 不带 `kb_id` 参数的请求
- 知识库服务异常的情况
- 空文件列表的情况

## 与现有功能的一致性

### 与其他接口对比
| 特性 | /sessions/query | /sessions/list | /files/list |
|------|----------------|----------------|-------------|
| kb_id 参数支持 | ✅ | ✅ | ✅ |
| 知识库服务调用 | ✅ | ✅ | ✅ |
| 异常处理 | ✅ | ✅ | ✅ |
| 返回字段一致性 | `is_in_kb` | `isInKb` | `is_in_kb` |

### 保持一致的设计模式
- 相同的参数命名：`kb_id`
- 相同的服务调用模式：调用知识库服务判断关系
- 相同的异常处理策略
- 相同的日志记录格式
- 相同的字段命名：`is_in_kb`

## 部署说明

### 无需额外配置
- 使用现有的知识库服务
- 使用现有的数据模型
- 无需数据库变更

### 向后兼容
- 新增的 `kb_id` 参数为可选
- 现有客户端调用不受影响
- 新字段 `is_in_kb` 对现有客户端透明

## 总结

成功为 `/files/list` 接口添加了知识库过滤功能，实现了：

1. ✅ **完整的参数支持**：新增 `kb_id` 可选参数
2. ✅ **知识库关系判断**：调用 `knowledge_service.is_knowledge_base_file`
3. ✅ **数据字段扩展**：为每个文件添加 `is_in_kb` 字段
4. ✅ **异常处理机制**：确保服务稳定性
5. ✅ **性能优化**：批量查询，避免循环调用
6. ✅ **向后兼容**：不影响现有功能
7. ✅ **一致性设计**：与其他接口保持一致
8. ✅ **业务逻辑封装**：在服务层处理复杂逻辑，保持路由层简洁

该功能现在可以支持前端根据知识库关系过滤和显示文件列表，提升用户体验。
