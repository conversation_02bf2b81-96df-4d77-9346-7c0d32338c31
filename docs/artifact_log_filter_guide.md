# Artifact ID 重复日志过滤指南

## 问题描述

在处理制品文件时，经常会出现 `artifact_id已存在` 的错误日志，这些日志通常是由于重试、重复处理等正常业务场景导致的，不应该作为ERROR级别记录。

## 解决方案

### 1. 日志级别调整

对于 `artifact_id已存在` 的错误，我们将其从 ERROR 级别降级为 WARNING 级别：

**文件：** `src/infrastructure/database/repositories/file_repository.py`
```python
except Exception as e:
    # 如果是artifact_id已存在的错误，降级为warning
    if "artifact_id已存在" in str(e):
        logger.warning(f"[FileDB] 创建文件记录失败: {e}")
    else:
        logger.error(f"[FileDB] 创建文件记录失败: {e}")
    raise
```

**文件：** `src/domain/services/message_processor.py`
```python
except Exception as upload_error:
    # 如果是artifact_id已存在的错误，降级为warning
    if "artifact_id已存在" in str(upload_error):
        logger.warning(f"[MessageProcessor] 制品文件上传异常: {upload_error}")
    else:
        logger.error(f"[MessageProcessor] 制品文件上传异常: {upload_error}")
```

### 2. 日志过滤器

在日志配置中添加了专门的过滤器，完全过滤掉 `artifact_id已存在` 的 ERROR 级别日志：

**文件：** `src/shared/logging/logger.py`
```python
def artifact_exists_filter(record):
    """过滤artifact_id已存在的错误日志"""
    message = record.get("message", "")
    # 如果是artifact_id已存在的错误，且级别是ERROR，则过滤掉
    if (record["level"].name == "ERROR" and 
        "artifact_id已存在" in message and 
        ("[FileDB]" in message or "[MessageProcessor]" in message)):
        return False
    return True
```

## 效果验证

运行测试脚本验证过滤效果：

```bash
python tests/test_artifact_log_filter.py
```

**预期结果：**
- ✅ `artifact_id已存在` 的 ERROR 日志被完全过滤
- ✅ 其他 ERROR 日志正常记录
- ✅ `artifact_id已存在` 的 WARNING 日志正常保留
- ✅ 其他级别日志不受影响

## 配置说明

### 过滤规则

1. **完全过滤：** ERROR 级别的 `artifact_id已存在` 日志
2. **保留记录：** WARNING/INFO 级别的 `artifact_id已存在` 日志
3. **正常记录：** 所有其他类型的日志

### 适用范围

- `[FileDB]` 模块的 artifact_id 重复错误
- `[MessageProcessor]` 模块的 artifact_id 重复错误
- 只影响 ERROR 级别，不影响其他日志级别

## 业务影响

- **减少噪音：** 消除了大量重复的错误日志
- **保持可观测性：** 重要的错误信息仍然会被记录
- **向下兼容：** 不影响现有的日志分析和监控
- **灵活控制：** 可以通过修改过滤器规则调整过滤策略

## 注意事项

1. 如果需要调试 artifact_id 重复问题，可以临时禁用过滤器
2. 过滤器只影响日志输出，不影响异常抛出和业务逻辑
3. WARNING 级别的相同日志仍会被记录，便于问题排查
