# 日志格式更新 - 添加分隔符

## 修改概述

为了便于日志解析和展示，在日志格式中的代码位置（模块:函数:行号）后面添加了 ` | ` 分隔符，将原来的 ` - ` 改为 ` | `，保持格式一致性。

## 修改详情

### 修改文件
**文件**: `src/shared/logging/logger.py`

### 修改前后对比

#### 修改前的格式
```
2025-08-04 15:57:00 | INFO     | 3a3e7bb8-ea97-4ceb-b36a-d1d284933a17 | src.domain.services.knowledge_service:update_knowledge_base_status:1506 - [KnowledgeService] 更新会话状态
```

#### 修改后的格式
```
2025-08-04 16:01:15 | INFO     | test-request-123 | __main__:test_log_format:42 | 这是一条测试日志消息
```

### 具体修改内容

#### 1. 服务器环境格式（无颜色）
```python
# 修改前
format_string = (
    "{time:YYYY-MM-DD HH:mm:ss} | "
    "{level: <8} | "
    "{extra[request_id]} | "
    "{name}:{function}:{line} - "
    "{message}"
)

# 修改后
format_string = (
    "{time:YYYY-MM-DD HH:mm:ss} | "
    "{level: <8} | "
    "{extra[request_id]} | "
    "{name}:{function}:{line} | "
    "{message}"
)
```

#### 2. 开发环境格式（带颜色）
```python
# 修改前
format_string = (
    "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
    "<level>{level: <8}</level> | "
    "<magenta>{extra[request_id]}</magenta> | "
    "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
    "<level>{message}</level>"
)

# 修改后
format_string = (
    "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
    "<level>{level: <8}</level> | "
    "<magenta>{extra[request_id]}</magenta> | "
    "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
    "<level>{message}</level>"
)
```

## 日志字段结构

修改后的日志包含5个字段，用 ` | ` 分隔：

1. **时间戳**: `2025-08-04 16:01:15`
2. **日志级别**: `INFO     ` (8字符宽度，左对齐)
3. **请求ID**: `test-request-123`
4. **代码位置**: `__main__:test_log_format:42` (模块:函数:行号)
5. **日志消息**: `这是一条测试日志消息`

## 解析示例

### Python 解析代码
```python
def parse_log_line(log_line):
    """解析日志行"""
    parts = log_line.split(' | ')
    if len(parts) >= 5:
        return {
            'timestamp': parts[0],
            'level': parts[1].strip(),
            'request_id': parts[2],
            'location': parts[3],
            'message': ' | '.join(parts[4:])  # 消息可能包含分隔符
        }
    return None

# 示例使用
log_line = "2025-08-04 16:01:15 | INFO     | test-request-123 | __main__:test_log_format:42 | 这是一条测试日志消息"
parsed = parse_log_line(log_line)
print(parsed)
# 输出: {'timestamp': '2025-08-04 16:01:15', 'level': 'INFO', 'request_id': 'test-request-123', 'location': '__main__:test_log_format:42', 'message': '这是一条测试日志消息'}
```

### Shell 解析代码
```bash
#!/bin/bash
# 解析日志文件

parse_log_line() {
    local log_line="$1"
    
    # 使用 awk 按分隔符分割
    echo "$log_line" | awk -F ' \\| ' '{
        print "时间戳: " $1
        print "级别: " $2
        print "请求ID: " $3
        print "位置: " $4
        print "消息: " $5
        for(i=6; i<=NF; i++) print $i
    }'
}

# 示例使用
log_line="2025-08-04 16:01:15 | INFO     | test-request-123 | __main__:test_log_format:42 | 这是一条测试日志消息"
parse_log_line "$log_line"
```

## 优势和好处

### 1. 统一的分隔符
- 所有字段都使用 ` | ` 分隔，格式一致
- 避免了混合使用 ` | ` 和 ` - ` 的情况
- 提高了日志格式的规范性

### 2. 便于解析
- 可以使用简单的字符串分割进行解析
- 支持各种日志分析工具
- 便于编写日志处理脚本

### 3. 向后兼容
- 只是增加了一个分隔符，不影响现有功能
- 日志内容和结构保持不变
- 现有的日志查看方式仍然有效

### 4. 便于监控和告警
- 可以更容易地提取特定字段
- 支持基于字段的过滤和统计
- 便于集成到监控系统

## 应用场景

### 1. 日志分析
```bash
# 提取特定请求ID的所有日志
grep "request-123" logs/application.log | cut -d'|' -f5

# 统计各级别日志数量
awk -F' \\| ' '{print $2}' logs/application.log | sort | uniq -c

# 提取特定模块的日志
awk -F' \\| ' '$4 ~ /knowledge_service/ {print $5}' logs/application.log
```

### 2. 监控脚本
```python
import re
from collections import defaultdict

def analyze_logs(log_file):
    """分析日志文件"""
    stats = defaultdict(int)
    
    with open(log_file, 'r') as f:
        for line in f:
            parts = line.strip().split(' | ')
            if len(parts) >= 5:
                level = parts[1].strip()
                stats[level] += 1
    
    return stats

# 使用示例
stats = analyze_logs('logs/application.log')
print(f"错误日志数量: {stats['ERROR']}")
```

### 3. ELK Stack 配置
```yaml
# Logstash 配置示例
filter {
  if [fields][log_type] == "alpha-service" {
    grok {
      match => { 
        "message" => "%{TIMESTAMP_ISO8601:timestamp} \| %{WORD:level}\s* \| %{DATA:request_id} \| %{DATA:location} \| %{GREEDYDATA:log_message}"
      }
    }
    
    mutate {
      split => { "location" => ":" }
      add_field => { 
        "module" => "%{[location][0]}"
        "function" => "%{[location][1]}"
        "line" => "%{[location][2]}"
      }
    }
  }
}
```

## 部署说明

### 1. 重启服务
修改日志格式后需要重启服务以应用新配置：

```bash
# 重启服务
sudo systemctl restart alpha-service

# 或者使用 circus 重启
circusctl restart main
```

### 2. 验证格式
重启后检查日志格式是否正确：

```bash
# 查看最新日志
tail -f logs/application.log

# 检查分隔符数量
tail -n 1 logs/application.log | grep -o ' | ' | wc -l
# 应该输出 4
```

### 3. 更新日志处理脚本
如果有现有的日志处理脚本，需要更新以适应新格式：

```bash
# 旧的解析方式（可能需要更新）
awk -F' - ' '{print $2}' logs/application.log

# 新的解析方式
awk -F' \\| ' '{print $5}' logs/application.log
```

## 测试验证

### 测试结果
运行 `tests/test_log_format.py` 的结果：

```
✅ 通过 日志格式生成
✅ 通过 日志解析
📊 测试结果: 2 通过, 0 失败
🎉 日志格式修改成功！
```

### 验证要点
1. ✅ 分隔符数量正确（4个 ` | `）
2. ✅ 字段结构完整（5个字段）
3. ✅ 位置格式正确（模块:函数:行号）
4. ✅ 消息内容完整
5. ✅ 解析功能正常

## 注意事项

### 1. 消息内容中的分隔符
如果日志消息本身包含 ` | ` 字符，解析时需要特别处理：

```python
# 正确的解析方式
parts = log_line.split(' | ')
if len(parts) >= 5:
    message = ' | '.join(parts[4:])  # 重新组合消息部分
```

### 2. 兼容性考虑
- 新格式与旧格式在字段数量上有差异
- 更新日志处理工具时需要考虑兼容性
- 建议保留一段时间的旧格式支持

### 3. 性能影响
- 格式修改对性能影响微乎其微
- 日志写入性能保持不变
- 解析性能可能略有提升（统一分隔符）

## 总结

这次日志格式更新成功地：

1. ✅ **统一了分隔符格式**：所有字段都使用 ` | ` 分隔
2. ✅ **提高了解析便利性**：便于各种工具和脚本处理
3. ✅ **保持了向后兼容**：不影响现有功能
4. ✅ **增强了可读性**：格式更加规范和一致
5. ✅ **支持自动化处理**：便于监控和分析系统集成

现在的日志格式更加规范，便于解析和自动化处理，同时保持了良好的可读性。
