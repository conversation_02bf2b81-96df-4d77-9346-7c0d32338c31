# API 异步化优化指南

## 🎯 优化目标

将项目中的混合同步/异步API改为纯异步，提升性能和并发处理能力。

## 📋 优化清单

### 🔥 高优先级（立即优化）

#### 1. `/sessions/send` - ✅ 已完成
- **状态**: 已异步化
- **改进**: 使用 `get_or_create_session_domain_async` 和 `get_session_with_permission_check_async`

#### 2. `/sessions/query` - ⚠️ 需要优化
```python
# 当前实现
@router.get("/sessions/query")
async def query_session_history(...):
    result = session_service.get_session_history(...)  # 同步调用

# 优化方案
@router.get("/sessions/query")
async def query_session_history(...):
    result = await asyncio.to_thread(
        session_service.get_session_history,
        session_id=session_id,
        page_size=page_size,
        next_token=next_token
    )
```

#### 3. `/sessions/list` - ⚠️ 需要优化
```python
# 当前实现
result = session_service.get_user_sessions(current_user, params, kb_id=kb_id)

# 优化方案
result = await asyncio.to_thread(
    session_service.get_user_sessions,
    current_user, params, kb_id=kb_id
)
```

#### 4. `/files/presigned-upload` - ⚠️ 需要优化
```python
# 当前实现
file_obj, upload_url, expires_in, upload_headers = file_service.create_presigned_upload(...)

# 优化方案
file_obj, upload_url, expires_in, upload_headers = await asyncio.to_thread(
    file_service.create_presigned_upload,
    context=context,
    file_name=request.file_info.file_name,
    file_size=request.file_info.file_size,
    file_type=request.file_info.file_type,
    session_id=session_id,
    agent_id=request.agent_id,
    upload_file_type=request.file_type or "sessionFile"
)
```

#### 5. `/files/list` - ⚠️ 需要优化
```python
# 当前实现
result = file_service.get_session_files(...)

# 优化方案
result = await asyncio.to_thread(
    file_service.get_session_files,
    owner_ali_uid=context.ali_uid,
    owner_wy_id=context.wy_id,
    session_id=request.session_id,
    file_types=file_types_list,
    max_result=request.max_results,
    next_token=request.next_token
)
```

### 🔥 中优先级（后续优化）

#### 6. 知识库相关API
```python
# RAG 路由中的同步调用
response = knowledgebase_service.list_knowledge_bases(...)

# 优化方案
response = await asyncio.to_thread(
    knowledgebase_service.list_knowledge_bases,
    current_user.ali_uid, current_user.wy_id, max_results, next_token
)
```

#### 7. PPT相关API
```python
# PPT 路由中的同步调用
response = ppt_service.get_ppt_auth_code(ali_uid=current_user.ali_uid)

# 优化方案
response = await asyncio.to_thread(
    ppt_service.get_ppt_auth_code,
    ali_uid=current_user.ali_uid
)
```

### ✅ 已正确异步化

#### 1. `/agents/list` - 正确
```python
@router.get("/agents/list")
async def list_agents():
    client = WaiyInfraClient()
    response = await client.list_apps_async()  # ✅ 真正异步
```

#### 2. `/status.taobao` - 正确
```python
@router.get("/status.taobao")
async def status_taobao():
    return "success"  # ✅ 简单操作，异步声明合理
```

## 🛠️ 实施步骤

### 第一阶段：核心会话API
1. ✅ `/sessions/send` - 已完成
2. 🔄 `/sessions/query` - 进行中
3. 🔄 `/sessions/list` - 进行中

### 第二阶段：文件相关API
1. 🔄 `/files/presigned-upload`
2. 🔄 `/files/list`
3. 🔄 `/files/download-urls`

### 第三阶段：其他业务API
1. 🔄 知识库相关API
2. 🔄 PPT相关API
3. 🔄 用户设置API

## 📊 性能预期

### 优化前后对比
| API | 优化前 | 优化后 | 预期提升 |
|-----|--------|--------|----------|
| `/sessions/send` | 混合模式 | 纯异步 | 30-50% |
| `/sessions/query` | 混合模式 | 纯异步 | 20-40% |
| `/files/upload` | 混合模式 | 纯异步 | 40-60% |

### 并发处理能力
- **优化前**: 受线程池限制，约100-200并发
- **优化后**: 事件循环处理，可达1000+并发

## 🧪 测试验证

### 性能测试
```bash
# 运行异步性能测试
python tests/test_async_performance.py
```

### 功能测试
```bash
# 运行API功能测试
pytest tests/test_api_async.py -v
```

## ⚠️ 注意事项

### 1. 数据库操作
- 使用 `asyncio.to_thread()` 包装同步数据库操作
- 考虑后续升级到异步数据库驱动

### 2. 文件操作
- 大文件操作使用 `asyncio.to_thread()`
- 考虑使用 `aiofiles` 进行真正的异步文件操作

### 3. 外部服务调用
- 网络请求优先使用异步客户端
- 同步客户端用 `asyncio.to_thread()` 包装

### 4. 错误处理
- 异步操作的异常处理保持一致
- 确保资源正确释放

## 📈 监控指标

### 关键指标
1. **响应时间**: 平均响应时间 < 500ms
2. **并发处理**: 支持 > 500 并发请求
3. **错误率**: < 1%
4. **资源使用**: CPU使用率 < 70%

### 监控工具
- 使用 `PerformanceMiddleware` 监控请求时间
- 使用 `asyncio` 内置工具监控事件循环
- 使用应用日志监控错误率
