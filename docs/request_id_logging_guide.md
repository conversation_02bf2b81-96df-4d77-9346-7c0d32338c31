# Request ID 日志追踪指南

## 概述

本服务已经实现了完整的 `request_id` 日志追踪功能，每条日志都会包含对应的请求ID，方便追踪请求的完整生命周期。

## 功能特性

### 1. 自动 Request ID 管理
- 支持从多个来源获取 `request_id`
- 自动设置到日志上下文中
- 支持嵌套请求上下文

### 2. 多来源支持
按优先级顺序：
1. **HTTP 请求头**：`X-Request-ID`、`X-Request-Id`、`x-request-id`
2. **查询参数**：`request_id`
3. **当前上下文**：已设置的 `request_id`
4. **自动生成**：UUID4 格式

### 3. 日志格式
```
2025-08-02 18:07:45 | INFO | req-001 | module:function:line - 日志消息
```

## 使用方法

### 1. 在 FastAPI 路由中使用

```python
from fastapi import Depends
from src.presentation.api.dependencies.api_common_utils import get_request_id_dependency

@router.post("/api/example")
async def example_endpoint(
    request_id: str = Depends(get_request_id_dependency)
):
    # request_id 会自动设置到日志上下文中
    logger.info("处理请求开始")  # 日志会包含 request_id
    
    # 业务逻辑...
    
    logger.info("处理请求完成")  # 日志会包含相同的 request_id
    return {"request_id": request_id}
```

### 2. 在服务层中使用

```python
from src.shared.logging.logger import RequestContext, logger

def some_service_method(request_id: str):
    with RequestContext(request_id):
        logger.info("服务方法开始执行")
        
        # 调用其他方法，日志会自动包含 request_id
        other_method()
        
        logger.info("服务方法执行完成")

def other_method():
    # 不需要传递 request_id，会自动从上下文获取
    logger.debug("执行其他方法")
```

### 3. 异步任务中使用

```python
import asyncio
from src.shared.logging.logger import RequestContext, logger

async def async_task(request_id: str):
    with RequestContext(request_id):
        logger.info("异步任务开始")
        await some_async_operation()
        logger.info("异步任务完成")

# 在路由中创建异步任务
asyncio.create_task(async_task(request_id))
```

## 客户端集成

### 1. 发送 Request ID

客户端可以通过以下方式发送 `request_id`：

**方式1：HTTP 请求头（推荐）**
```bash
curl -H "X-Request-ID: my-custom-request-123" \
     http://localhost:8000/api/sessions/send
```

**方式2：查询参数**
```bash
curl "http://localhost:8000/api/sessions/send?request_id=my-custom-request-123"
```

### 2. 前端 JavaScript 示例

```javascript
// 生成 request_id
const requestId = `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

// 发送请求
fetch('/api/sessions/send', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId
    },
    body: JSON.stringify({
        prompt: 'Hello',
        agent_id: 'agent123'
    })
});
```

## 日志追踪示例

### 完整请求追踪
```
2025-08-02 18:07:45 | INFO | req-abc123 | session_routes:send_message:85 - 立即返回会话ID: sess_456
2025-08-02 18:07:45 | INFO | req-abc123 | session_service:send_message:234 - 开始发送消息: session_id=sess_456
2025-08-02 18:07:46 | DEBUG | req-abc123 | memory_sdk:create_message:67 - 创建消息记录
2025-08-02 18:07:47 | INFO | req-abc123 | agent_service:invoke:123 - 调用智能体成功
2025-08-02 18:07:47 | INFO | req-abc123 | session_service:send_message:245 - 消息发送完成: round_id=round_789
```

### 异步任务追踪
```
2025-08-02 18:07:45 | INFO | req-abc123 | session_routes:send_message:95 - 接口响应完成
2025-08-02 18:07:45 | INFO | req-abc123 | session_routes:_send_message_async:45 - 开始异步发送消息
2025-08-02 18:07:47 | INFO | req-abc123 | session_routes:_send_message_async:58 - 异步消息发送成功
```

## 最佳实践

### 1. Request ID 格式建议
- 使用有意义的前缀：`req-`, `api-`, `task-`
- 包含时间戳：便于排序和查找
- 包含随机部分：避免冲突
- 示例：`req-20250802-180745-abc123`

### 2. 日志记录建议
```python
# ✅ 好的做法
logger.info(f"处理用户请求: user_id={user_id}")
logger.error(f"数据库连接失败: {str(e)}")

# ❌ 避免的做法
print(f"Debug info: {data}")  # 不会包含 request_id
```

### 3. 错误处理
```python
try:
    # 业务逻辑
    result = some_operation()
    logger.info("操作成功完成")
except Exception as e:
    logger.error(f"操作失败: {str(e)}")
    raise
```

## 故障排查

### 1. 查找特定请求的所有日志
```bash
grep "req-abc123" /path/to/logs/app.log
```

### 2. 分析请求处理时间
```bash
grep "req-abc123" /path/to/logs/app.log | head -1  # 请求开始
grep "req-abc123" /path/to/logs/app.log | tail -1  # 请求结束
```

### 3. 统计请求频率
```bash
grep -o "req-[^|]*" /path/to/logs/app.log | sort | uniq -c | sort -nr
```

## 注意事项

1. **性能影响**：Request ID 追踪对性能影响极小
2. **内存使用**：使用 `contextvars` 实现，内存开销很小
3. **线程安全**：完全线程安全，支持并发请求
4. **异步支持**：完全支持异步操作和任务

## 配置选项

当前实现使用默认配置，如需自定义可以修改：

- `src/shared/logging/logger.py`：日志格式和上下文管理
- `src/presentation/api/dependencies/api_common_utils.py`：Request ID 获取逻辑
- `src/presentation/api/middlewares/request_id_middleware.py`：中间件配置
