<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Service - AI对话系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #1a1a1a;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 
                0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
            position: relative;
            z-index: 1;
        }

        .main-content {
            display: flex;
            height: 75vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 380px;
            background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
            border-right: 1px solid rgba(0, 0, 0, 0.06);
            padding: 30px;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 1px;
            height: 100%;
            background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
        }

        .sidebar h3 {
            margin-bottom: 20px;
            color: #1e293b;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .sidebar h3::before {
            content: '⚙️';
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
            color: #1f2937;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .file-upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 16px;
            padding: 30px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: #f8f9fa;
        }

        .file-upload-area:hover {
            border-color: #667eea;
            background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .file-upload-area.dragover {
            border-color: #667eea;
            background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
            transform: scale(1.02);
        }

        .file-upload-area.uploading {
            border-color: #f59e0b;
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .file-upload-area .upload-icon {
            font-size: 32px;
            margin-bottom: 12px;
            color: #667eea;
            display: block;
        }

        .file-upload-area .upload-text {
            color: #374151;
            font-size: 16px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .file-upload-area .upload-hint {
            color: #6b7280;
            font-size: 13px;
            line-height: 1.4;
        }

        .file-list {
            max-height: 250px;
            overflow-y: auto;
            margin-top: 15px;
            padding-right: 5px;
        }

        .file-list::-webkit-scrollbar {
            width: 6px;
        }

        .file-list::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .file-list::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .file-list::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            margin-bottom: 8px;
            font-size: 13px;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .file-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #cbd5e1;
        }

        .file-item .file-info {
            flex: 1;
            min-width: 0;
        }

        .file-item .file-name {
            font-weight: 600;
            color: #1e293b;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 4px;
        }

        .file-item .file-meta {
            color: #64748b;
            font-size: 11px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .file-item .file-status {
            margin-left: 12px;
            font-size: 11px;
            padding: 4px 8px;
            border-radius: 6px;
            white-space: nowrap;
            font-weight: 500;
        }

        .file-item .file-status.uploading {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
        }

        .file-item .file-status.success {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
        }

        .file-item .file-status.error {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #991b1b;
        }

        .file-item .remove-btn {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border: none;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 11px;
            cursor: pointer;
            margin-left: 8px;
        }

        .file-item .remove-btn:hover {
            background: #c82333;
        }

        .settings-info {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            color: #6c757d;
        }

        .settings-info div {
            margin-bottom: 5px;
        }

        /* 聊天区域样式 */
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message-user { text-align: right; }
        .message-assistant { text-align: left; }
        .message-system { text-align: center; }

        .message-bubble {
            display: inline-block;
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .message-user .message-bubble {
            background: #4a90e2;
            color: white;
        }

        .message-assistant .message-bubble {
            background: white;
            color: #212529;
            border: 1px solid #dee2e6;
        }

        .message-assistant .message-bubble.artifact {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid #0ea5e9;
            border-left: 4px solid #0ea5e9;
            position: relative;
        }

        .message-assistant .message-bubble.artifact::before {
            content: '🎨';
            position: absolute;
            top: -8px;
            right: -8px;
            background: #0ea5e9;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .message-system .message-bubble {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message-meta {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        .message-resources {
            margin-top: 8px;
            font-size: 12px;
        }

        .resource-item {
            display: inline-block;
            background: rgba(255, 255, 255, 0.3);
            padding: 2px 8px;
            border-radius: 12px;
            margin-right: 5px;
            margin-bottom: 3px;
        }

        .input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .input-group input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ced4da;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
        }

        .input-group input:focus {
            border-color: #4a90e2;
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
        }

        .input-group button {
            padding: 12px 24px;
            background: #4a90e2;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.2s;
        }

        .input-group button:hover {
            background: #357abd;
        }

        .input-group button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: none;
            color: #6c757d;
            font-style: italic;
            padding: 10px 20px;
        }

        .typing-indicator.show {
            display: block;
        }

        .status-bar {
            padding: 10px 20px;
            background: #e9ecef;
            border-top: 1px solid #dee2e6;
            font-size: 12px;
            color: #6c757d;
        }

        .welcome-message {
            text-align: center;
            color: #6c757d;
            padding: 40px;
        }

        .welcome-message h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .btn-clear {
            background: #dc3545;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 10px;
        }

        .btn-clear:hover {
            background: #c82333;
        }

        /* 隐藏的文件输入 */
        #fileInput {
            display: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
                height: auto;
            }
            
            .sidebar {
                width: 100%;
                max-height: 300px;
            }
            
            .messages {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Alpha Service</h1>
            <p>AI对话系统 - 支持文件上传和多轮对话</p>
        </div>
        
        <div class="main-content">
            <div class="sidebar">
                <h3>⚙️ 对话设置</h3>

                <div class="form-group">
                    <label for="agentId">Agent ID</label>
                    <select id="agentId">
                        <option value="alpha">alpha</option>
                        <option value="deeper">deeper</option>
                        <option value="deep-research" selected>deep-research</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="sessionId">会话ID (可选)</label>
                    <input type="text" id="sessionId" placeholder="留空将创建新会话">
                </div>

                <h3 style="margin-top: 25px;">🔐 认证配置</h3>

                <div class="form-group">
                    <label for="loginToken">Login Token</label>
                    <input type="text" id="loginToken" value="test_token_12345" placeholder="输入登录令牌">
                </div>

                <div class="form-group">
                    <label for="loginSessionId">Login Session ID</label>
                    <input type="text" id="loginSessionId" value="test_session_12345" placeholder="输入登录会话ID">
                </div>

                <div class="form-group">
                    <label for="regionId">Region ID</label>
                    <input type="text" id="regionId" value="cn-hangzhou" placeholder="输入区域ID">
                </div>
                
                <div class="form-group">
                    <label>📎 文件上传</label>
                    <div class="file-upload-area" id="fileUploadArea">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">点击或拖拽文件到此处</div>
                        <div class="upload-hint">支持多种文件格式，最大9MB</div>
                    </div>
                    <input type="file" id="fileInput" multiple accept="*/*">
                </div>
                
                <div class="file-list" id="fileList">
                    <!-- 文件列表将在这里动态生成 -->
                </div>
                
                <button class="btn-clear" onclick="clearFiles()">清空文件</button>
                
                <div class="settings-info">
                    <div><strong>当前会话:</strong> <span id="currentSession">无</span></div>
                    <div><strong>已上传文件:</strong> <span id="fileCount">0</span> 个</div>
                    <div><strong>连接状态:</strong> <span id="connectionStatus">未连接</span></div>
                </div>
            </div>
            
            <div class="chat-area">
                <div class="messages" id="messages">
                    <div class="welcome-message">
                        <h3>🌟 欢迎使用Alpha Service</h3>
                        <p>配置Agent ID，可选择上传文件，然后开始对话</p>
                    </div>
                </div>
                
                <div class="typing-indicator" id="typingIndicator">
                    🤖 AI正在思考...
                </div>
                
                <div class="input-area">
                    <div class="input-group">
                        <input type="text" id="messageInput" placeholder="输入你的消息..." 
                               onkeypress="if(event.key==='Enter') sendMessage()">
                        <button id="sendBtn" onclick="sendMessage()">发送</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="status-bar">
            <span>🚀 Alpha Service v2.0 | 支持文件上传</span>
            <span style="float: right;" id="currentTime"></span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSessionId = null;
        let currentRoundId = null;
        let messageCount = 0;
        let eventSource = null;
        let isRoundCompleted = false;
        let lastMessageId = null;
        let receivedMessageIds = new Set();
        let uploadedFiles = new Map(); // 存储已上传的文件信息

        // 获取认证参数
        function getAuthParams() {
            return {
                loginToken: document.getElementById('loginToken').value.trim() || 'test_token_12345',
                loginSessionId: document.getElementById('loginSessionId').value.trim() || 'test_session_12345',
                regionId: document.getElementById('regionId').value.trim() || 'cn-hangzhou'
            };
        }

        // 生成随机的 auth_code
        function generateAuthCode() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let result = '';
            for (let i = 0; i < 16; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        // 获取认证头部
        function getAuthHeaders() {
            const authParams = getAuthParams();
            return {
                'X-Login-Token': authParams.loginToken,
                'X-Login-Session-Id': authParams.loginSessionId,
                'X-Region-Id': authParams.regionId
            };
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
            setupFileUpload();
        });

        // 设置文件上传功能
        function setupFileUpload() {
            const fileUploadArea = document.getElementById('fileUploadArea');
            const fileInput = document.getElementById('fileInput');
            
            // 点击上传区域触发文件选择
            fileUploadArea.addEventListener('click', () => {
                fileInput.click();
            });
            
            // 文件选择变化事件
            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });
            
            // 拖拽事件
            fileUploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileUploadArea.classList.add('dragover');
            });
            
            fileUploadArea.addEventListener('dragleave', () => {
                fileUploadArea.classList.remove('dragover');
            });
            
            fileUploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                fileUploadArea.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
            });
        }

        // 处理选择的文件
        function handleFiles(files) {
            Array.from(files).forEach(file => {
                uploadFile(file);
            });
        }

        // 上传文件
        async function uploadFile(file) {
            // 检查文件大小 (9MB限制)
            const maxSize = 9 * 1024 * 1024;
            if (file.size > maxSize) {
                addMessage('system', `❌ 文件 "${file.name}" 超过9MB限制`);
                return;
            }

            const fileId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            
            // 添加到文件列表UI
            addFileToList(fileId, file, 'uploading');
            
            try {
                // 1. 获取预签名上传URL
                const uploadUrlResponse = await fetch('/api/files/get-upload-url', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify({
                        ...getAuthParams(),
                        file_info: {
                            FileName: file.name,
                            FileSize: file.size,
                            FileType: file.type || 'application/octet-stream'
                        },
                        file_type: 'sessionFile'
                    })
                });

                if (!uploadUrlResponse.ok) {
                    const errorData = await uploadUrlResponse.json();
                    throw new Error(errorData.detail || `获取上传URL失败: ${uploadUrlResponse.status}`);
                }

                const uploadData = await uploadUrlResponse.json();

                // 检查API响应格式
                if (!uploadData.success || !uploadData.data) {
                    throw new Error(uploadData.message || '获取上传URL失败');
                }

                const { file_id, upload_url } = uploadData.data;

                updateFileStatus(fileId, 'uploading', '上传中...');


                // 2. 上传文件到OSS（设置Content-Length头部）
                const uploadResponse = await fetch(upload_url, {
                    method: 'PUT',
                    headers: {
                        'Content-Length': file.size.toString(),
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: file
                });

                if (!uploadResponse.ok) {
                    throw new Error(`文件上传失败: ${uploadResponse.status}`);
                }

                const etag = uploadResponse.headers.get('ETag');

                // 3. 确认上传
                const confirmResponse = await fetch('/api/files/confirm-upload', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify({
                        ...getAuthParams(),
                        file_id: file_id,
                        etag: etag
                    })
                });

                if (!confirmResponse.ok) {
                    const errorData = await confirmResponse.json();
                    throw new Error(errorData.message || errorData.detail || `确认上传失败: ${confirmResponse.status}`);
                }

                const confirmData = await confirmResponse.json();
                if (!confirmData.success) {
                    throw new Error(confirmData.message || '确认上传失败');
                }

                // 4. 上传成功，保存文件信息
                uploadedFiles.set(fileId, {
                    file_id: file_id,
                    name: file.name,
                    size: file.size,
                    type: file.type
                });

                updateFileStatus(fileId, 'success', '上传成功');
                updateFileCount();
                
                addMessage('system', `✅ 文件 "${file.name}" 上传成功`);

            } catch (error) {
                console.error('文件上传失败:', error);
                updateFileStatus(fileId, 'error', error.message);
                addMessage('system', `❌ 文件 "${file.name}" 上传失败: ${error.message}`);
            }
        }

        // 添加文件到列表
        function addFileToList(fileId, file, status) {
            const fileList = document.getElementById('fileList');
            
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.id = `file-${fileId}`;
            
            const fileSize = formatFileSize(file.size);
            
            fileItem.innerHTML = `
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-meta">${fileSize} • ${file.type || 'unknown'}</div>
                </div>
                <div class="file-status ${status}">准备中...</div>
                <button class="remove-btn" onclick="removeFile('${fileId}')">×</button>
            `;
            
            fileList.appendChild(fileItem);
        }

        // 更新文件状态
        function updateFileStatus(fileId, status, message) {
            const fileItem = document.getElementById(`file-${fileId}`);
            if (fileItem) {
                const statusElement = fileItem.querySelector('.file-status');
                statusElement.className = `file-status ${status}`;
                statusElement.textContent = message;
            }
        }

        // 移除文件
        function removeFile(fileId) {
            const fileItem = document.getElementById(`file-${fileId}`);
            if (fileItem) {
                fileItem.remove();
                uploadedFiles.delete(fileId);
                updateFileCount();
            }
        }

        // 清空所有文件
        function clearFiles() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            uploadedFiles.clear();
            updateFileCount();
            addMessage('system', '🗑️ 已清空所有文件');
        }

        // 更新文件计数
        function updateFileCount() {
            const fileCount = document.getElementById('fileCount');
            fileCount.textContent = uploadedFiles.size;
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 更新时间显示
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleTimeString();
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const agentId = document.getElementById('agentId').value.trim();
            const sessionId = document.getElementById('sessionId').value.trim();
            
            const message = input.value.trim();
            if (!message) return;
            
            if (!agentId) {
                alert('请输入Agent ID');
                return;
            }

            try {
                sendBtn.disabled = true;
                sendBtn.textContent = '发送中...';
                
                // 准备资源列表
                const resources = Array.from(uploadedFiles.values()).map(file => ({
                    Type: 'file',
                    ResourceId: file.file_id
                }));

                // 立即显示用户消息
                addMessage('user', message, resources);
                input.value = '';
                
                // 发送到后端
                const requestBody = {
                    ...getAuthParams(),
                    session_id: sessionId || currentSessionId,
                    prompt: message,
                    agent_id: agentId,
                    auth_code: generateAuthCode()  // 添加随机生成的 auth_code
                };

                if (resources.length > 0) {
                    requestBody.resources = resources;
                }

                const response = await fetch('/api/sessions/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(requestBody)
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
                
                const result = await response.json();
                
                currentSessionId = result.data.session_id;
                currentRoundId = result.data.round_id;
                isRoundCompleted = false;
                lastMessageId = null;
                receivedMessageIds.clear();
                
                document.getElementById('currentSession').textContent = currentSessionId ? 
                    currentSessionId.substring(0, 12) + '...' : '无';
                
                // 建立SSE连接接收流式消息
                connectSSE();
                
            } catch (error) {
                console.error('发送消息失败:', error);
                addMessage('system', `❌ 发送失败: ${error.message}`);
            } finally {
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';
            }
        }

        // 建立SSE连接
        function connectSSE() {
            if (eventSource) {
                eventSource.close();
            }

            // 获取认证参数
            const authParams = getAuthParams();

            // 构建包含认证参数的URL
            let url = `/api/sessions/stream?session_id=${currentSessionId}`;
            url += `&loginToken=${encodeURIComponent(authParams.loginToken)}`;
            url += `&loginSessionId=${encodeURIComponent(authParams.loginSessionId)}`;
            url += `&regionId=${encodeURIComponent(authParams.regionId)}`;

            console.log('SSE连接URL:', url);

            eventSource = new EventSource(url);
            
            document.getElementById('connectionStatus').textContent = '连接中...';
            document.getElementById('typingIndicator').classList.add('show');
            
            eventSource.onopen = function() {
                document.getElementById('connectionStatus').textContent = '已连接';
                console.log('SSE连接已建立');
            };
            
            eventSource.onmessage = function(event) {
                console.log('收到SSE原始消息:', event.data);

                // 直接展示原始的 event.data 内容，不进行 JSON 解析
                const rawData = event.data;

                // 简单的消息去重（基于原始数据内容）
                if (receivedMessageIds.has(rawData)) {
                    console.log('跳过重复消息');
                    return;
                }

                receivedMessageIds.add(rawData);

                // 直接显示原始数据
                addMessage('assistant', rawData, null, 'RAW_DATA');

                // 简单检查是否包含 DONE 来判断是否结束
                if (rawData.includes('"type":"DONE"') || rawData.includes('"type": "DONE"')) {
                    document.getElementById('typingIndicator').classList.remove('show');
                    console.log('检测到DONE消息，Round完成');

                    isRoundCompleted = true;

                    // 主动关闭EventSource
                    if (eventSource) {
                        eventSource.close();
                        eventSource = null;
                        document.getElementById('connectionStatus').textContent = 'Round完成';
                    }
                }
            };
            
            eventSource.onerror = function(error) {
                console.error('SSE连接错误:', error);
                document.getElementById('connectionStatus').textContent = '连接断开';
                document.getElementById('typingIndicator').classList.remove('show');
                
                // 如果Round未完成，自动重试
                if (!isRoundCompleted) {
                    setTimeout(() => {
                        if (currentSessionId && currentRoundId && !eventSource) {
                            console.log('尝试重新连接SSE...');
                            addMessage('system', '🔄 连接断开，正在重试...');
                            connectSSE();
                        }
                    }, 2000);
                }
            };
        }

        // 添加消息到界面
        function addMessage(role, content, resources = null, type = '') {
            const messagesDiv = document.getElementById('messages');

            // 清空欢迎信息
            if (messagesDiv.innerHTML.includes('欢迎使用Alpha Service')) {
                messagesDiv.innerHTML = '';
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message message-${role}`;

            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble';
            bubbleDiv.textContent = content;

            const metaDiv = document.createElement('div');
            metaDiv.className = 'message-meta';

            // 使用当前时间
            let metaText = new Date().toLocaleTimeString();
            if (type) metaText += ` [${type}]`;
            metaDiv.textContent = metaText;

            messageDiv.appendChild(bubbleDiv);
            messageDiv.appendChild(metaDiv);

            // 添加资源信息
            if (resources && resources.length > 0) {
                const resourcesDiv = document.createElement('div');
                resourcesDiv.className = 'message-resources';

                resources.forEach(resource => {
                    const resourceSpan = document.createElement('span');
                    resourceSpan.className = 'resource-item';
                    const fileName = getFileNameById(resource.ResourceId);
                    resourceSpan.textContent = `📎 ${fileName}`;
                    resourcesDiv.appendChild(resourceSpan);
                });

                messageDiv.appendChild(resourcesDiv);
            }

            messagesDiv.appendChild(messageDiv);

            // 滚动到底部
            messagesDiv.scrollTop = messagesDiv.scrollHeight;

            messageCount++;
        }

        // 根据文件ID获取文件名
        function getFileNameById(fileId) {
            for (const [key, file] of uploadedFiles) {
                if (file.file_id === fileId) {
                    return file.name;
                }
            }
            return 'Unknown File';
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(event) {
            if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
                sendMessage();
            }
        });

        // 页面关闭时清理
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>