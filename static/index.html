<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Service - AI对话系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4a90e2, #357abd);
            color: white;
            padding: 20px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .main-content {
            display: flex;
            height: 70vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 350px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .sidebar h3 {
            margin-bottom: 15px;
            color: #495057;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
            font-size: 14px;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4a90e2;
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
        }

        .file-upload-area {
            border: 2px dashed #ced4da;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: #f8f9fa;
        }

        .file-upload-area:hover {
            border-color: #4a90e2;
            background: #e3f2fd;
        }

        .file-upload-area.dragover {
            border-color: #4a90e2;
            background: #e3f2fd;
        }

        .file-upload-area.uploading {
            border-color: #ffc107;
            background: #fff3cd;
        }

        .file-upload-area .upload-icon {
            font-size: 24px;
            margin-bottom: 10px;
            color: #6c757d;
        }

        .file-upload-area .upload-text {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .file-upload-area .upload-hint {
            color: #6c757d;
            font-size: 12px;
        }

        .file-list {
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin-bottom: 5px;
            font-size: 13px;
        }

        .file-item .file-info {
            flex: 1;
            min-width: 0;
        }

        .file-item .file-name {
            font-weight: 500;
            color: #212529;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 2px;
        }

        .file-item .file-meta {
            color: #6c757d;
            font-size: 11px;
        }

        .file-item .file-status {
            margin-left: 10px;
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 3px;
            white-space: nowrap;
        }

        .file-item .file-status.uploading {
            background: #fff3cd;
            color: #856404;
        }

        .file-item .file-status.success {
            background: #d4edda;
            color: #155724;
        }

        .file-item .file-status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .file-item .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 11px;
            cursor: pointer;
            margin-left: 8px;
        }

        .file-item .remove-btn:hover {
            background: #c82333;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: #4a90e2;
            transition: width 0.3s;
        }

        .settings-info {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            color: #6c757d;
        }

        .settings-info div {
            margin-bottom: 5px;
        }

        /* 聊天区域样式 */
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message-user { text-align: right; }
        .message-assistant { text-align: left; }
        .message-system { text-align: center; }

        .message-bubble {
            display: inline-block;
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .message-user .message-bubble {
            background: #4a90e2;
            color: white;
        }

        .message-assistant .message-bubble {
            background: white;
            color: #212529;
            border: 1px solid #dee2e6;
        }

        .message-system .message-bubble {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message-meta {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        .message-resources {
            margin-top: 8px;
            font-size: 12px;
        }

        .resource-item {
            display: inline-block;
            background: rgba(255, 255, 255, 0.3);
            padding: 2px 8px;
            border-radius: 12px;
            margin-right: 5px;
            margin-bottom: 3px;
        }

        .input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .input-group input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ced4da;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
        }

        .input-group input:focus {
            border-color: #4a90e2;
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
        }

        .input-group button {
            padding: 12px 24px;
            background: #4a90e2;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.2s;
        }

        .input-group button:hover {
            background: #357abd;
        }

        .input-group button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: none;
            color: #6c757d;
            font-style: italic;
            padding: 10px 20px;
        }

        .typing-indicator.show {
            display: block;
        }

        .status-bar {
            padding: 10px 20px;
            background: #e9ecef;
            border-top: 1px solid #dee2e6;
            font-size: 12px;
            color: #6c757d;
        }

        .welcome-message {
            text-align: center;
            color: #6c757d;
            padding: 40px;
        }

        .welcome-message h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .btn-clear {
            background: #dc3545;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 10px;
        }

        .btn-clear:hover {
            background: #c82333;
        }

        /* 隐藏的文件输入 */
        #fileInput {
            display: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
                height: auto;
            }
            
            .sidebar {
                width: 100%;
                max-height: 300px;
            }
            
            .messages {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Alpha Service</h1>
            <p>AI对话系统 - 支持文件上传和多轮对话</p>
        </div>
        
        <div class="main-content">
            <div class="sidebar">
                <h3>⚙️ 对话设置</h3>
                
                <div class="form-group">
                    <label for="agentId">Agent ID</label>
                    <input type="text" id="agentId" value="deep-research" placeholder="输入Agent ID">
                </div>
                
                <div class="form-group">
                    <label for="sessionId">会话ID (可选)</label>
                    <input type="text" id="sessionId" placeholder="留空将创建新会话">
                </div>
                
                <div class="form-group">
                    <label>📎 文件上传</label>
                    <div class="file-upload-area" id="fileUploadArea">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">点击或拖拽文件到此处</div>
                        <div class="upload-hint">支持多种文件格式，最大9MB</div>
                    </div>
                    <input type="file" id="fileInput" multiple accept="*/*">
                </div>
                
                <div class="file-list" id="fileList">
                    <!-- 文件列表将在这里动态生成 -->
                </div>
                
                <button class="btn-clear" onclick="clearFiles()">清空文件</button>
                
                <div class="settings-info">
                    <div><strong>当前会话:</strong> <span id="currentSession">无</span></div>
                    <div><strong>已上传文件:</strong> <span id="fileCount">0</span> 个</div>
                    <div><strong>连接状态:</strong> <span id="connectionStatus">未连接</span></div>
                </div>
            </div>
            
            <div class="chat-area">
                <div class="messages" id="messages">
                    <div class="welcome-message">
                        <h3>🌟 欢迎使用Alpha Service</h3>
                        <p>配置Agent ID，可选择上传文件，然后开始对话</p>
                    </div>
                </div>
                
                <div class="typing-indicator" id="typingIndicator">
                    🤖 AI正在思考...
                </div>
                
                <div class="input-area">
                    <div class="input-group">
                        <input type="text" id="messageInput" placeholder="输入你的消息..." 
                               onkeypress="if(event.key==='Enter') sendMessage()">
                        <button id="sendBtn" onclick="sendMessage()">发送</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="status-bar">
            <span>🚀 Alpha Service v2.0 | 支持文件上传</span>
            <span style="float: right;" id="currentTime"></span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSessionId = null;
        let currentRoundId = null;
        let messageCount = 0;
        let eventSource = null;
        let isRoundCompleted = false;
        let lastMessageId = null;
        let receivedMessageIds = new Set();
        let uploadedFiles = new Map(); // 存储已上传的文件信息

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
            setupFileUpload();
        });

        // 设置文件上传功能
        function setupFileUpload() {
            const fileUploadArea = document.getElementById('fileUploadArea');
            const fileInput = document.getElementById('fileInput');
            
            // 点击上传区域触发文件选择
            fileUploadArea.addEventListener('click', () => {
                fileInput.click();
            });
            
            // 文件选择变化事件
            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });
            
            // 拖拽事件
            fileUploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileUploadArea.classList.add('dragover');
            });
            
            fileUploadArea.addEventListener('dragleave', () => {
                fileUploadArea.classList.remove('dragover');
            });
            
            fileUploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                fileUploadArea.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
            });
        }

        // 处理选择的文件
        function handleFiles(files) {
            Array.from(files).forEach(file => {
                uploadFile(file);
            });
        }

        // 上传文件
        async function uploadFile(file) {
            // 检查文件大小 (9MB限制)
            const maxSize = 9 * 1024 * 1024;
            if (file.size > maxSize) {
                addMessage('system', `❌ 文件 "${file.name}" 超过9MB限制`);
                return;
            }

            const fileId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            
            // 添加到文件列表UI
            addFileToList(fileId, file, 'uploading');
            
            try {
                // 1. 获取预签名上传URL
                const uploadUrlResponse = await fetch('/api/files/get-upload-url', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        file_info: {
                            FileName: file.name,
                            FileSize: file.size,
                            FileType: file.type || 'application/octet-stream'
                        },
                        file_type: 'session_file'
                    })
                });

                if (!uploadUrlResponse.ok) {
                    const errorData = await uploadUrlResponse.json();
                    throw new Error(errorData.detail || `获取上传URL失败: ${uploadUrlResponse.status}`);
                }

                const uploadData = await uploadUrlResponse.json();
                const { file_id, upload_url, headers } = uploadData;

                updateFileStatus(fileId, 'uploading', '上传中...');

                // 2. 上传文件到OSS（处理CORS）
                const uploadResponse = await fetch(upload_url, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': file.type || 'application/octet-stream',
                        'Origin': window.location.origin,
                        ...headers
                    },
                    mode: 'cors',
                    body: file
                });

                if (!uploadResponse.ok) {
                    throw new Error(`文件上传失败: ${uploadResponse.status}`);
                }

                const etag = uploadResponse.headers.get('ETag');

                // 3. 确认上传
                const confirmResponse = await fetch('/api/files/confirm-upload', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        file_id: file_id,
                        etag: etag
                    })
                });

                if (!confirmResponse.ok) {
                    const errorData = await confirmResponse.json();
                    throw new Error(errorData.detail || `确认上传失败: ${confirmResponse.status}`);
                }

                // 4. 上传成功，保存文件信息
                uploadedFiles.set(fileId, {
                    file_id: file_id,
                    name: file.name,
                    size: file.size,
                    type: file.type
                });

                updateFileStatus(fileId, 'success', '上传成功');
                updateFileCount();
                
                addMessage('system', `✅ 文件 "${file.name}" 上传成功`);

            } catch (error) {
                console.error('文件上传失败:', error);
                updateFileStatus(fileId, 'error', error.message);
                addMessage('system', `❌ 文件 "${file.name}" 上传失败: ${error.message}`);
            }
        }

        // 添加文件到列表
        function addFileToList(fileId, file, status) {
            const fileList = document.getElementById('fileList');
            
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.id = `file-${fileId}`;
            
            const fileSize = formatFileSize(file.size);
            
            fileItem.innerHTML = `
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-meta">${fileSize} • ${file.type || 'unknown'}</div>
                </div>
                <div class="file-status ${status}">准备中...</div>
                <button class="remove-btn" onclick="removeFile('${fileId}')">×</button>
            `;
            
            fileList.appendChild(fileItem);
        }

        // 更新文件状态
        function updateFileStatus(fileId, status, message) {
            const fileItem = document.getElementById(`file-${fileId}`);
            if (fileItem) {
                const statusElement = fileItem.querySelector('.file-status');
                statusElement.className = `file-status ${status}`;
                statusElement.textContent = message;
            }
        }

        // 移除文件
        function removeFile(fileId) {
            const fileItem = document.getElementById(`file-${fileId}`);
            if (fileItem) {
                fileItem.remove();
                uploadedFiles.delete(fileId);
                updateFileCount();
            }
        }

        // 清空所有文件
        function clearFiles() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            uploadedFiles.clear();
            updateFileCount();
            addMessage('system', '🗑️ 已清空所有文件');
        }

        // 更新文件计数
        function updateFileCount() {
            const fileCount = document.getElementById('fileCount');
            fileCount.textContent = uploadedFiles.size;
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 更新时间显示
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleTimeString();
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const agentId = document.getElementById('agentId').value.trim();
            const sessionId = document.getElementById('sessionId').value.trim();
            
            const message = input.value.trim();
            if (!message) return;
            
            if (!agentId) {
                alert('请输入Agent ID');
                return;
            }

            try {
                sendBtn.disabled = true;
                sendBtn.textContent = '发送中...';
                
                // 准备资源列表
                const resources = Array.from(uploadedFiles.values()).map(file => ({
                    Type: 'file',
                    ResourceId: file.file_id
                }));

                // 立即显示用户消息
                addMessage('user', message, resources);
                input.value = '';
                
                // 发送到后端
                const requestBody = {
                    session_id: sessionId || currentSessionId,
                    prompt: message,
                    agent_id: agentId
                };

                if (resources.length > 0) {
                    requestBody.resources = resources;
                }

                const response = await fetch('/api/sessions/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
                
                const result = await response.json();
                
                currentSessionId = result.session_id;
                currentRoundId = result.round_id;
                isRoundCompleted = false;
                lastMessageId = null;
                receivedMessageIds.clear();
                
                document.getElementById('currentSession').textContent = currentSessionId ? 
                    currentSessionId.substring(0, 12) + '...' : '无';
                
                // 建立SSE连接接收流式消息
                connectSSE();
                
            } catch (error) {
                console.error('发送消息失败:', error);
                addMessage('system', `❌ 发送失败: ${error.message}`);
            } finally {
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';
            }
        }

        // 建立SSE连接
        function connectSSE() {
            if (eventSource) {
                eventSource.close();
            }
            
            let url = `/api/sessions/stream?session_id=${currentSessionId}`;
            if (lastMessageId) {
                url += `&last_message_id=${lastMessageId}`;
            }
            
            eventSource = new EventSource(url);
            
            document.getElementById('connectionStatus').textContent = '连接中...';
            document.getElementById('typingIndicator').classList.add('show');
            
            eventSource.onopen = function() {
                document.getElementById('connectionStatus').textContent = '已连接';
                console.log('SSE连接已建立');
            };
            
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    
                    if (data.eventType === 'message') {
                        const message = data.data;
                        
                        // 检查消息去重
                        if (message.messageId && receivedMessageIds.has(message.messageId)) {
                            console.log(`跳过重复消息: ${message.messageId}`);
                            return;
                        }
                        
                        // 记录消息ID
                        if (message.messageId) {
                            receivedMessageIds.add(message.messageId);
                            lastMessageId = message.messageId;
                        }
                        
                        // 处理不同类型的消息
                        let content = '';
                        let displayType = '';
                        
                        if (message.role === 'assistant') {
                            content = message.content;
                            displayType = '🤖 AI回复';
                        } else if (message.role === 'tool') {
                            content = `🔧 工具: ${message.content}`;
                            displayType = '🔧 工具';
                        } else if (message.role === 'system') {
                            content = message.content;
                            displayType = '🔧 系统';
                        } else {
                            content = message.content;
                            displayType = message.role;
                        }
                        
                        if (content) {
                            addMessage('assistant', content, null, displayType, message.messageId);
                        }
                        
                    } else if (data.eventType === 'done') {
                        document.getElementById('typingIndicator').classList.remove('show');
                        console.log('Round完成:', data.data);
                        
                        isRoundCompleted = true;
                        
                        // 主动关闭EventSource
                        if (eventSource) {
                            eventSource.close();
                            eventSource = null;
                            document.getElementById('connectionStatus').textContent = 'Round完成';
                        }
                        
                    } else if (data.eventType === 'error') {
                        console.error('SSE错误事件:', data.data);
                        addMessage('system', `❌ 连接错误: ${data.data.error || '未知错误'}`);
                    }
                    
                } catch (error) {
                    console.error('解析SSE消息失败:', error);
                    addMessage('system', `⚠️ 消息解析失败: ${error.message}`);
                }
            };
            
            eventSource.onerror = function(error) {
                console.error('SSE连接错误:', error);
                document.getElementById('connectionStatus').textContent = '连接断开';
                document.getElementById('typingIndicator').classList.remove('show');
                
                // 如果Round未完成，自动重试
                if (!isRoundCompleted) {
                    setTimeout(() => {
                        if (currentSessionId && currentRoundId && !eventSource) {
                            console.log('尝试重新连接SSE...');
                            addMessage('system', '🔄 连接断开，正在重试...');
                            connectSSE();
                        }
                    }, 2000);
                }
            };
        }

        // 添加消息到界面
        function addMessage(role, content, resources = null, type = '', messageId = null) {
            const messagesDiv = document.getElementById('messages');
            
            // 清空欢迎信息
            if (messagesDiv.innerHTML.includes('欢迎使用Alpha Service')) {
                messagesDiv.innerHTML = '';
            }
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `message message-${role}`;
            
            if (messageId) {
                messageDiv.setAttribute('data-message-id', messageId);
            }
            
            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble';
            bubbleDiv.textContent = content;
            
            const metaDiv = document.createElement('div');
            metaDiv.className = 'message-meta';
            let metaText = new Date().toLocaleTimeString();
            if (type) metaText += ` [${type}]`;
            if (messageId) metaText += ` ID:${messageId.substring(0, 8)}...`;
            metaDiv.textContent = metaText;
            
            messageDiv.appendChild(bubbleDiv);
            messageDiv.appendChild(metaDiv);
            
            // 添加资源信息
            if (resources && resources.length > 0) {
                const resourcesDiv = document.createElement('div');
                resourcesDiv.className = 'message-resources';
                
                resources.forEach(resource => {
                    const resourceSpan = document.createElement('span');
                    resourceSpan.className = 'resource-item';
                    const fileName = getFileNameById(resource.ResourceId);
                    resourceSpan.textContent = `📎 ${fileName}`;
                    resourcesDiv.appendChild(resourceSpan);
                });
                
                messageDiv.appendChild(resourcesDiv);
            }
            
            messagesDiv.appendChild(messageDiv);
            
            // 滚动到底部
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            
            messageCount++;
        }

        // 根据文件ID获取文件名
        function getFileNameById(fileId) {
            for (const [key, file] of uploadedFiles) {
                if (file.file_id === fileId) {
                    return file.name;
                }
            }
            return 'Unknown File';
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(event) {
            if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
                sendMessage();
            }
        });

        // 页面关闭时清理
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>