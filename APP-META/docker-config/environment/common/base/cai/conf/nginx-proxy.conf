# proxy conf
#include                     user.conf;
user admin;
worker_rlimit_nofile        100000;

error_log                   "logs/nginx_error.log" warn;
pid                         /home/<USER>/cai/logs/tengine-proxy.pid;

events {
    use                     epoll;
    worker_connections      20480;
}


http {
    include                 /home/<USER>/cai/conf/mime.types;
    default_type            application/octet-stream;

    root                    htdocs;

    sendfile                on;
    tcp_nopush              on;

    server_tokens           off;

    keepalive_timeout       0;

    client_header_timeout   1m;
    send_timeout            1m;
    client_max_body_size    50m;
    client_body_temp_path   /home/<USER>/cai/data/client_body;

    index                   index.html index.htm;

    log_format              proxyformat    "$remote_addr $http_x_readtime [$time_local] \"$request_method http://$host$request_uri\" $status $body_bytes_sent \"$http_referer\" \"$upstream_addr\" \"$http_user_agent\" \"$cookie_unb\" \"$cookie_cookie2\" \"$http_eagleeye_traceid\"";

    access_log              "logs/nginx_access.log" proxyformat;
    log_not_found           off;

    gzip                    on;
    gzip_http_version       1.0;
    gzip_comp_level         6;
    gzip_min_length         1024;
    gzip_proxied            any;
    gzip_vary               on;
    gzip_disable            msie6;
    gzip_buffers            96 8k;
    gzip_types              text/xml text/plain text/css application/javascript application/x-javascript application/rss+xml application/json;

    proxy_set_header        Host $host;
    proxy_set_header        X-Real-IP $remote_addr;
    proxy_set_header        Web-Server-Type nginx;
    proxy_set_header        WL-Proxy-Client-IP $remote_addr;
    proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header        EagleEye-TraceId $http_eagleeye_traceid;
    proxy_redirect          off;
    proxy_buffers           128 8k;
    proxy_temp_path         data/proxy;
    proxy_intercept_errors  off;


    variables_hash_max_size     1024;
    variables_hash_bucket_size  64;

    server {
        listen              80;
        server_name         www.taobao.com;

        # for daily-traceid: use user_ip in eagleeye traceid
        set $eaddr $remote_addr;
        if ($http_x_forwarded_for != "") {
               set $eaddr $http_x_forwarded_for;
        }
        if ($http_x_real_ip != "") {
               set $eaddr $http_x_real_ip;
        }


        location / {
            proxy_pass   http://127.0.0.1:8000;

            # if you want to enable cell logic, you must change your proxy_pass conf to following
            #
            # proxy_pass $ups;
        }

        location /status.taobao {
            alias /home/<USER>/app.online;
        }
    }

    server {
        listen              80;
        server_name         status.taobao.com;


        location            = /nginx_status {
            stub_status     on;
        }
    }

    include /home/<USER>/cai/apps/*.conf;
}