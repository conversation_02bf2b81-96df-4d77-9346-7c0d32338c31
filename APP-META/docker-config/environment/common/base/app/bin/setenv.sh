# set ${APP_NAME}, if empty $(basename "${APP_HOME}") will be used.
NGINX_HOME=$ADMIN_HOME/cai

export LANG=zh_CN.UTF-8
export NLS_LANG=AMERICAN_AMERICA.ZHS16GBK
export LD_LIBRARY_PATH=/opt/taobao/oracle/lib:/opt/taobao/lib:$LD_LIBRARY_PATH
export MAX_START_TIMEOUT_SECONDS=120
export MAX_START_VIPSERVER_TIMEOUT_SECONDS=15
export MAX_STOP_TIMEOUT_SECONDS=15
export MAX_STOP_VIPSERVER_TIMEOUT_SECONDS=15
#export CPU_COUNT="$(grep -c 'cpu[0-9][0-9]*' /proc/stat)"
#export CPU_COUNT="$(grep -c 'cpu[0-9][0-9]*' /proc/stat)"
ulimit -c unlimited

# if set to "1", skip start nginx.
test -z "$NGINX_SKIP" && NGINX_SKIP=0

# set port for checking status.taobao file. Comment it if no need.
STATUS_PORT=80

# env check and calculate
#
if [ -z "$APP_NAME" ]; then
        APP_NAME=$(basename "${APP_HOME}")
fi
if [ -z "$NGINX_HOME" ]; then
        NGINX_HOME=$ADMIN_HOME/cai
fi

STATUSROOT_HOME="${NGINX_HOME}/htdocs"
NGINXCTL=$NGINX_HOME/bin/nginxctl
# 暂时不用nginx，tengine对ubuntu场景支持不好
NGINX_SKIP=0

source $APP_HOME/custom/bin/setenv.sh
source $APP_HOME/custom/bin/start.sh
source $APP_HOME/custom/bin/stop.sh
source $APP_HOME/custom/bin/health.sh