# 项目结构
```tree
├── APP-META (构建用到的文件)
│    └── docker-config (构建镜像时的工作目录)
│        ├── Dockerfile_testing
│        └── environment
│            └── common
│                ├── base (要使用aone启动应用，必须包含的脚本/配置)
│                │   ├── app
│                │   │   └── bin
│                │   │       ├── appctl.sh
│                │   │       ├── health.sh
│                │   │       ├── setenv.sh
│                │   │       ├── start.sh
│                │   │       └── stop.sh
│                │   └── cai
│                │       ├── bin
│                │       │   └── nginxctl
│                │       └── conf
│                │           └── nginx-proxy.conf
│                └── custom (项目自定义的脚本/配置)
│                    └── app
│                        └── bin
│                            ├── health.sh
│                            ├── setenv.sh
│                            ├── start.sh
│                            └── stop.sh
├── wuying-alpha-service.release (构建文件)
├── conf (配置文件)
│    ├── circus.ini
│    └── requirements.txt
├── src (项目代码)
│    └── main.py
└── 启动必看 - 不看部署必挂.md
```
# 重要的事情说三遍

## 定制启动脚本请写到APP-META/.../common/custom目录下!!!
## 定制启动脚本请写到APP-META/.../common/custom目录下!!!
## 定制启动脚本请写到APP-META/.../common/custom目录下!!!

APP-META/.../common/custom目录中存储了健康检查(health.sh)、环境变量(setenv.sh)、启动(start.sh)、停止(stop.sh)等脚本，这些脚本会在容器启动时被执行。如果项目有特殊需求，可以在custom目录下修改对应的脚本，不要直接修改base目录下的脚本，否则会导致项目无法正常启动。


# 本地启动
