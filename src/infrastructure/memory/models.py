"""
Memory SDK 数据结构定义
"""
from dataclasses import dataclass
from typing import List, Optional, Any, Dict


@dataclass
class PaginationInfo:
    """分页信息"""
    page: int
    page_size: int
    total: int
    total_pages: int
    has_prev: bool
    has_next: bool


@dataclass
class MessageInfo:
    """消息信息"""
    role: str
    content: str
    timestamp: str
    duration: Optional[float] = None
    message_id: Optional[str] = None


@dataclass
class RoundInfo:
    """轮次信息"""
    round_id: int
    messages: List[MessageInfo]


@dataclass
class SessionMessagesResult:
    """会话消息查询结果"""
    session_id: str
    rounds: List[RoundInfo]
    total_rounds: int
    pagination: PaginationInfo


@dataclass
class RoundMessagesResult:
    """轮次消息查询结果"""
    session_id: str
    round_id: int
    messages: List[MessageInfo]
    has_more: bool


@dataclass
class MessageData:
    """通用消息数据结构，用于在系统内部传递消息信息"""
    message_id: Optional[str] = None
    role: str = ""
    content: str = ""
    timestamp: str = ""
    trace_id: Optional[str] = None
    app_id: Optional[str] = None
    duration: Optional[float] = None
    tool_calls: Optional[Any] = None
    tool_call_id: Optional[str] = None
    tool_name: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {}
        for field_name, field_value in self.__dict__.items():
            if field_value is not None:
                result[field_name] = field_value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MessageData':
        """从字典创建MessageData对象"""
        return cls(**data)
    
    @classmethod
    def from_message(cls, message: 'Message') -> 'MessageData':
        """从waiy_memory的Message对象创建MessageData"""
        from datetime import datetime
        return cls(
            message_id=message.message_id,
            role=message.role.value if hasattr(message.role, 'value') else str(message.role),
            content=message.content,
            timestamp=message.timestamp.isoformat() if message.timestamp else datetime.now().isoformat(),
            trace_id=message.trace_id,
            app_id=message.app_id,
            duration=message.duration,
            tool_calls=message.tool_calls,
            tool_call_id=message.tool_call_id,
            tool_name=message.tool_name
        ) 