"""
SQLAlchemy 知识库数据库模型
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, BigInteger, String, DateTime, Text, Index, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

# 创建基础模型类
Base = declarative_base()


class KnowledgeBaseModel(Base):
    """知识库表模型"""
    __tablename__ = 'knowledge_bases'

    # 主键和基础字段
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    kb_id = Column(String(255), nullable=False, comment='知识库唯一标识')
    name = Column(String(255), nullable=False, comment='知识库名称')
    description = Column(Text, nullable=True, comment='知识库描述')
    
    # 所有者字段
    owner_ali_uid = Column(BigInteger, nullable=False, comment='所有者阿里UID')
    owner_wy_id = Column(String(255), nullable=False, comment='所有者无影ID')
    
    # 状态字段
    is_deleted = Column(BigInteger, nullable=False, default=0, comment='是否删除，0表示未删除')
    
    # 时间字段
    gmt_created = Column(DateTime, nullable=True, default=func.current_timestamp(), comment='创建时间')
    gmt_modified = Column(DateTime, nullable=True, default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='修改时间')

    # 索引和约束定义
    __table_args__ = (
        UniqueConstraint('kb_id', name='uk_kb_id'),
        UniqueConstraint('owner_ali_uid', 'owner_wy_id', 'name', 'is_deleted', name='uk_aliuid_wyid_name_isdeleted'),
        Index('idx_owner_ali_uid', 'owner_ali_uid'),
        Index('idx_owner_wy_id', 'owner_wy_id'),
        Index('idx_is_deleted', 'is_deleted'),
        Index('idx_gmt_created', 'gmt_created'),
        Index('idx_gmt_modified', 'gmt_modified'),
    )
    
    def __repr__(self):
        return f"<KnowledgeBaseModel(kb_id='{self.kb_id}', name='{self.name}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'kb_id': self.kb_id,
            'name': self.name,
            'description': self.description,
            'owner_ali_uid': self.owner_ali_uid,
            'owner_wy_id': self.owner_wy_id,
            'is_deleted': self.is_deleted,
            'gmt_created': self.gmt_created.isoformat() if self.gmt_created is not None else None,
            'gmt_modified': self.gmt_modified.isoformat() if self.gmt_modified is not None else None,
        }
    
    def is_active(self) -> bool:
        """检查知识库是否有效（未删除）"""
        return bool(self.is_deleted == 0) 

class KbDocumentModel(Base):
    """知识库文档表模型"""
    __tablename__ = 'kb_documents'

    # 主键和基础字段
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    doc_id = Column(String(255), nullable=True, comment='文档唯一标识')
    oss_bucket = Column(String(255), nullable=False, comment='OSS存储桶名称')
    oss_object_name = Column(String(500), nullable=False, comment='OSS对象名称')
    file_title = Column(String(500), nullable=False, comment='文档标题')
    
    # 文件信息字段
    file_size = Column(Integer, nullable=False, comment='文件大小')
    
    # 状态和会话字段
    status = Column(String(128), nullable=False, comment='文件解析状态：processing/success/failed')
    session_id = Column(String(128), nullable=True, comment='会话ID，非必填')
    file_id = Column(String(256), nullable=False, comment='文件ID')
    
    # 时间字段
    gmt_created = Column(DateTime, nullable=False, default=func.current_timestamp(), comment='创建时间')
    gmt_modified = Column(DateTime, nullable=False, default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='修改时间')

    # 索引和约束定义
    __table_args__ = (
        UniqueConstraint('doc_id', name='uk_doc_id'),
        Index('idx_doc_id', 'doc_id'),
        Index('idx_oss_bucket', 'oss_bucket'),
        Index('idx_file_title', 'file_title'),
        Index('idx_status', 'status'),
        Index('idx_session_id', 'session_id'),
        Index('idx_file_id', 'file_id'),
        Index('idx_gmt_created', 'gmt_created'),
        Index('idx_gmt_modified', 'gmt_modified'),
    )
    
    def __repr__(self):
        return f"<KbDocumentModel(doc_id='{self.doc_id}', title='{self.file_title}', status='{self.status}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'doc_id': self.doc_id,
            'oss_bucket': self.oss_bucket,
            'oss_object_name': self.oss_object_name,
            'file_title': self.file_title,
            'file_size': self.file_size,
            'status': self.status,
            'session_id': self.session_id,
            'file_id': self.file_id,
            'gmt_created': self.gmt_created.isoformat() if self.gmt_created is not None else None,
            'gmt_modified': self.gmt_modified.isoformat() if self.gmt_modified is not None else None,
        }
    
    def get_file_size_mb(self) -> float:
        """获取文件大小（MB）"""
        return self.file_size / (1024 * 1024) if self.file_size else 0.0
    
    def get_file_size_kb(self) -> float:
        """获取文件大小（KB）"""
        return self.file_size / 1024 if self.file_size else 0.0 


class KbDocumentRelationModel(Base):
    """知识库文档关联表模型"""
    __tablename__ = 'kb_document_relations'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    kb_id = Column(String(255), nullable=False, comment='知识库ID')
    file_id = Column(String(255), nullable=False, comment='文档ID')
    gmt_created = Column(DateTime, nullable=True, default=func.current_timestamp(), comment='创建时间')
    gmt_modified = Column(DateTime, nullable=True, default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='修改时间')
    is_deleted = Column(BigInteger, nullable=False, comment='是否删除，0表示未删除')
    session_id = Column(String(255), nullable=True, comment='冗余字段，用于方便查询会话文件')

    __table_args__ = (
        UniqueConstraint('kb_id', 'file_id', 'is_deleted', name='uk_kbid_docid_isdeleted'),
        Index('idx_kb_id', 'kb_id'),
        Index('idx_file_id', 'file_id'),
        Index('idx_is_deleted', 'is_deleted'),
        Index('idx_session_id', 'session_id'),
        Index('idx_gmt_created', 'gmt_created'),
        Index('idx_gmt_modified', 'gmt_modified'),
    )

    def __repr__(self):
        return f"<KbDocumentRelationModel(kb_id='{self.kb_id}', file_id='{self.file_id}', is_deleted={self.is_deleted})>"

    def to_dict(self) -> dict:
        return {
            'id': self.id,
            'kb_id': self.kb_id,
            'file_id': self.file_id,
            'session_id': self.session_id,
            'gmt_created': self.gmt_created.isoformat() if self.gmt_created else None,
            'gmt_modified': self.gmt_modified.isoformat() if self.gmt_modified else None,
            'is_deleted': self.is_deleted,
        } 


class KbSessionModel(Base):
    """知识库会话表模型"""
    __tablename__ = 'kb_sessions'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    kb_id = Column(String(255), nullable=True, comment='知识库ID')
    session_id = Column(String(256), nullable=True, comment='会话ID')
    is_deleted = Column(BigInteger, nullable=False, comment='0 表示使用中，非0表示删除')
    message_id_list = Column(Text, nullable=True, comment='消息ID列表，JSON数组')
    gmt_created = Column(DateTime, nullable=True, default=func.current_timestamp(), comment='创建时间')
    gmt_modified = Column(DateTime, nullable=True, default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='修改时间')
    snippet_id = Column(String(256), nullable=True, comment='rag映射ID')
    snippet_status = Column(String(256), nullable=False, comment='snippet 状态：processing/success/failed')
    session_status = Column(String(256), nullable=False, comment='会话状态：processing/success/failed')

    __table_args__ = (
        UniqueConstraint('kb_id', 'session_id', 'is_deleted', name='uk_kbid_sessionid_isdeleted'),
        Index('idx_kb_id', 'kb_id'),
        Index('idx_session_id', 'session_id'),
        Index('idx_is_deleted', 'is_deleted'),
        Index('idx_gmt_created', 'gmt_created'),
        Index('idx_gmt_modified', 'gmt_modified'),
    )

    def __repr__(self):
        return f"<KbSessionModel(kb_id='{self.kb_id}', session_id='{self.session_id}', is_deleted={self.is_deleted})>"

    def to_dict(self) -> dict:
        return {
            'id': self.id,
            'kb_id': self.kb_id,
            'session_id': self.session_id,
            'is_deleted': self.is_deleted,
            'message_id_list': self.message_id_list,
            'gmt_created': self.gmt_created.isoformat() if self.gmt_created else None,
            'gmt_modified': self.gmt_modified.isoformat() if self.gmt_modified else None,
            'snippet_id': self.snippet_id,
            'snippet_status': self.snippet_status,
            'session_status': self.session_status,
        }

    def is_active(self) -> bool:
        """检查会话是否有效（未删除）"""
        return bool(self.is_deleted == 0) 


class KbOperationLogModel(Base):
    """知识库操作日志表模型"""
    __tablename__ = 'kb_operation_logs'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    kb_id = Column(String(255), nullable=False, comment='知识库ID')
    wy_id = Column(String(255), nullable=False, comment='操作者无影ID')
    ali_uid = Column(BigInteger, nullable=False, comment='操作者阿里UID')
    operation_type = Column(String(100), nullable=False, comment='操作类型：create/delete')
    target_type = Column(String(100), nullable=False, comment='目标类型：document/session')
    target_id = Column(String(255), nullable=False, comment='目标ID：doc_id/session_id')
    gmt_created = Column(DateTime, nullable=False, default=func.current_timestamp(), comment='创建时间')
    gmt_modified = Column(DateTime, nullable=False, default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='修改时间')
    status = Column(String(256), nullable=False, comment='状态：processing/success/failed')

    __table_args__ = (
        Index('idx_aliuid_wyid_kbid', 'ali_uid', 'wy_id', 'kb_id'),
    )

    def to_dict(self) -> dict:
        return {
            'id': self.id,
            'kb_id': self.kb_id,
            'wy_id': self.wy_id,
            'ali_uid': self.ali_uid,
            'operation_type': self.operation_type,
            'target_type': self.target_type,
            'target_id': self.target_id,
            'gmt_created': self.gmt_created.isoformat() if self.gmt_created else None,
            'gmt_modified': self.gmt_modified.isoformat() if self.gmt_modified else None,
            'status': self.status,
        } 