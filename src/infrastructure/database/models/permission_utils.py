# -*- coding: utf-8 -*-
"""
权限工具类
提供便捷的权限操作方法
"""
from typing import List, Set, Union
from .auth_models import PermissionType

# 权限参数类型：可以是枚举或字符串
PermissionParam = Union[PermissionType, str]


def normalize_permission(permission: PermissionParam) -> str:
    """将权限参数标准化为字符串"""
    if isinstance(permission, PermissionType):
        return permission.value
    return permission


class PermissionUtils:
    """权限工具类"""
    
    @staticmethod
    def get_owner_permissions() -> List[str]:
        """获取资源所有者的所有权限"""
        return PermissionType.get_owner_permissions()
    
    @staticmethod
    def get_public_permissions() -> List[str]:
        """获取公开资源的权限"""
        return PermissionType.get_public_permissions()
    
    @staticmethod
    def get_read_only_permissions() -> List[str]:
        """获取只读权限"""
        return [PermissionType.READ.value]
    
    @staticmethod
    def get_read_write_permissions() -> List[str]:
        """获取读写权限"""
        return [PermissionType.READ.value, PermissionType.WRITE.value]
    
    @staticmethod
    def get_basic_permissions() -> List[str]:
        """获取基础权限（读、写、删除）"""
        return [PermissionType.READ.value, PermissionType.WRITE.value, PermissionType.DELETE.value]
    
    @staticmethod
    def has_permission(user_permissions: List[str], required_permission: PermissionParam) -> bool:
        """检查用户是否拥有指定权限"""
        permission_str = normalize_permission(required_permission)
        return permission_str in user_permissions

    @staticmethod
    def has_any_permission(user_permissions: List[str], required_permissions: List[PermissionParam]) -> bool:
        """检查用户是否拥有任意一个指定权限"""
        permission_strs = [normalize_permission(perm) for perm in required_permissions]
        return any(perm in user_permissions for perm in permission_strs)

    @staticmethod
    def has_all_permissions(user_permissions: List[str], required_permissions: List[PermissionParam]) -> bool:
        """检查用户是否拥有所有指定权限"""
        permission_strs = [normalize_permission(perm) for perm in required_permissions]
        return all(perm in user_permissions for perm in permission_strs)
    
    @staticmethod
    def merge_permissions(*permission_lists: List[str]) -> List[str]:
        """合并多个权限列表，去重"""
        merged: Set[str] = set()
        for perm_list in permission_lists:
            merged.update(perm_list)
        return list(merged)
    
    @staticmethod
    def is_admin_permission(permission: PermissionParam) -> bool:
        """检查是否为管理员权限"""
        permission_str = normalize_permission(permission)
        return permission_str == PermissionType.ADMIN.value

    @staticmethod
    def is_read_permission(permission: PermissionParam) -> bool:
        """检查是否为读取权限"""
        permission_str = normalize_permission(permission)
        return permission_str == PermissionType.READ.value

    @staticmethod
    def is_write_permission(permission: PermissionParam) -> bool:
        """检查是否为写入权限"""
        permission_str = normalize_permission(permission)
        return permission_str == PermissionType.WRITE.value

    @staticmethod
    def is_delete_permission(permission: PermissionParam) -> bool:
        """检查是否为删除权限"""
        permission_str = normalize_permission(permission)
        return permission_str == PermissionType.DELETE.value

    @staticmethod
    def is_share_permission(permission: PermissionParam) -> bool:
        """检查是否为分享权限"""
        permission_str = normalize_permission(permission)
        return permission_str == PermissionType.SHARE.value
    
    @staticmethod
    def validate_permissions(permissions: List[str]) -> bool:
        """验证权限列表是否有效"""
        return all(PermissionType.is_valid_permission(perm) for perm in permissions)
    
    @staticmethod
    def filter_valid_permissions(permissions: List[str]) -> List[str]:
        """过滤出有效的权限"""
        return [perm for perm in permissions if PermissionType.is_valid_permission(perm)]
    
    @staticmethod
    def get_permission_display_names(permissions: List[str]) -> List[str]:
        """获取权限的显示名称列表"""
        return [PermissionType.get_display_name(perm) for perm in permissions]
    
    @staticmethod
    def format_permissions_for_display(permissions: List[str]) -> str:
        """格式化权限列表用于显示"""
        display_names = PermissionUtils.get_permission_display_names(permissions)
        return "、".join(display_names)


# 便捷的权限常量
class Permissions:
    """权限常量类"""
    
    # 单个权限
    READ = PermissionType.READ.value
    WRITE = PermissionType.WRITE.value
    DELETE = PermissionType.DELETE.value
    SHARE = PermissionType.SHARE.value
    ADMIN = PermissionType.ADMIN.value
    
    # 权限组合
    OWNER = PermissionType.get_owner_permissions()
    PUBLIC = PermissionType.get_public_permissions()
    READ_ONLY = [READ]
    READ_WRITE = [READ, WRITE]
    BASIC = [READ, WRITE, DELETE]
    
    @classmethod
    def all(cls) -> List[str]:
        """获取所有权限"""
        return PermissionType.get_all_permissions()


# 权限检查装饰器的便捷函数
def require_permission(permission: PermissionParam):
    """要求特定权限的装饰器工厂"""
    def decorator(func):
        from functools import wraps

        @wraps(func)
        def wrapper(*args, **kwargs):
            # 这里可以添加权限检查逻辑
            # 实际实现应该与auth_service集成
            permission_str = normalize_permission(permission)
            # TODO: 实际的权限检查逻辑
            return func(*args, **kwargs)
        return wrapper
    return decorator


def require_read_permission():
    """要求读取权限"""
    return require_permission(Permissions.READ)


def require_write_permission():
    """要求写入权限"""
    return require_permission(Permissions.WRITE)


def require_delete_permission():
    """要求删除权限"""
    return require_permission(Permissions.DELETE)


def require_share_permission():
    """要求分享权限"""
    return require_permission(Permissions.SHARE)


def require_admin_permission():
    """要求管理权限"""
    return require_permission(Permissions.ADMIN)
