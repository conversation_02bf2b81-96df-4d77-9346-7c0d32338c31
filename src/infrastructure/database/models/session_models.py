"""
SQLAlchemy Session数据库模型 - 简化版
只包含Session表的定义
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, BigInteger, String, DateTime, JSON, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

# 创建基础模型类
Base = declarative_base()


class SessionModel(Base):
    """Session表模型"""
    __tablename__ = 'alpha_sessions'

    # 主键和基础字段
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='自增主键')
    session_id = Column(String(64), nullable=False, comment='会话ID')
    ali_uid = Column(BigInteger, nullable=False, comment='阿里云用户ID')
    agent_id = Column(String(64), nullable=False, comment='Agent ID')

    # 业务字段
    title = Column(String(255), default='', comment='会话标题')
    status = Column(String(32), nullable=False, default='CREATE', comment='会话状态')

    # 时间字段
    gmt_create = Column(DateTime, nullable=True, default=func.current_timestamp(), comment='创建时间')
    gmt_modified = Column(DateTime, nullable=True, default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='修改时间')

    # 扩展字段
    meta_data = Column('metadata', JSON, nullable=True, comment='元数据JSON')
    wy_id = Column(String(255), nullable=True, comment='无影用户id')

    # 索引定义
    __table_args__ = (
        Index('idx_agent_id', 'agent_id'),
        Index('idx_ali_uid', 'ali_uid'),
        Index('idx_gmt_create', 'gmt_create'),
        Index('idx_session_id', 'session_id'),
        Index('idx_status', 'status'),
    )
    
    def __repr__(self):
        return f"<SessionModel(session_id='{self.session_id}', status='{self.status}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'session_id': self.session_id,
            'ali_uid': self.ali_uid,
            'agent_id': self.agent_id,
            'title': self.title,
            'status': self.status,
            'gmt_create': self.gmt_create.isoformat() if self.gmt_create else None,
            'gmt_modified': self.gmt_modified.isoformat() if self.gmt_modified else None,
            'metadata': self.meta_data or {},
            'wy_id': self.wy_id
        }
    
    def start_processing(self) -> bool:
        """开始处理 - 更新状态为ACTIVE"""
        from datetime import datetime
        old_status = self.status
        self.status = 'ACTIVE'
        self.gmt_modified = datetime.now()
        
        from loguru import logger
        logger.info(f"[SessionModel] 开始处理Session: {self.session_id} {old_status} -> {self.status}")
        return True