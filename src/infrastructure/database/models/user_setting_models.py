"""
SQLAlchemy UserSetting数据库模型
用户设置表的定义
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, BigInteger, String, DateTime, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

# 创建基础模型类
Base = declarative_base()


class UserSettingModel(Base):
    """用户设置表模型"""
    __tablename__ = 'alpha_user_setting'

    # 主键和基础字段
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键')
    ali_uid = Column(BigInteger, nullable=False, comment='阿里uid')
    wy_id = Column(String(255), nullable=False, comment='无影id')
    
    # 业务字段
    desktop_id = Column(String(255), nullable=True, comment='优先使用的桌面id')
    model = Column(String(255), nullable=True, comment='优先使用的模型')
    
    # 时间字段
    gmt_create = Column(DateTime, nullable=False, default=func.current_timestamp(), comment='创建时间')
    gmt_modified = Column(DateTime, nullable=False, default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='修改时间')

    # 索引定义
    __table_args__ = (
        Index('idx_ali_uid', 'ali_uid'),
        Index('idx_wy_id', 'wy_id'),
        Index('idx_gmt_create', 'gmt_create'),
    )
    
    def __repr__(self):
        return f"<UserSettingModel(ali_uid={self.ali_uid}, wy_id='{self.wy_id}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'ali_uid': self.ali_uid,
            'wy_id': self.wy_id,
            'desktop_id': self.desktop_id,
            'model': self.model,
            'gmt_create': self.gmt_create.isoformat() if self.gmt_create else None,
            'gmt_modified': self.gmt_modified.isoformat() if self.gmt_modified else None,
        }