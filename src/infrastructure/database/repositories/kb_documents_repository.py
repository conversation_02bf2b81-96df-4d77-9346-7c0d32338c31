"""
知识库文档数据库服务层
提供知识库文档的CRUD操作
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session as SQLSession
from sqlalchemy.exc import IntegrityError
from sqlalchemy import UnaryExpression, and_, or_
from loguru import logger

from ..models.knowledgebase_models import KbDocumentModel
from ..connection import db_manager


class KbDocumentsRepository:
    """知识库文档数据库仓库"""

    def __init__(self):
        self.db_manager = db_manager

    def create_kb_document(
        self,
        doc_id: str,
        oss_bucket: str,
        oss_object_name: str,
        file_title: str,
        file_size: int,
        status: str,
        file_id: str,
        session_id: Optional[str] = None,
    ) -> dict:
        """创建知识库文档，返回 dict"""
        try:
            assert doc_id, "doc_id is required"
            assert oss_bucket, "oss_bucket is required"
            assert oss_object_name, "oss_object_name is required"
            assert file_title, "file_title is required"
            assert file_size >= 0, "file_size must be non-negative"
            assert status, "status is required"
            assert file_id, "file_id is required"

            kb_document = KbDocumentModel(
                doc_id=doc_id,
                oss_bucket=oss_bucket,
                oss_object_name=oss_object_name,
                file_title=file_title,
                file_size=file_size,
                status=status,
                file_id=file_id,
                session_id=session_id,
            )
            with self.db_manager.get_session() as db_session:
                db_session.add(kb_document)
                db_session.flush()  # 获取自增ID
                logger.info(
                    f"[KbDocumentsDB] 创建文档: {doc_id} (ID: {kb_document.id})"
                )
                # 立即转 dict，避免 session 关闭后对象失效
                return kb_document.to_dict()
        except IntegrityError as e:
            logger.error(f"[KbDocumentsDB] 文档已存在: {doc_id}")
            raise ValueError(f"文档ID已存在: {doc_id}")
        except Exception as e:
            logger.error(f"[KbDocumentsDB] 创建文档失败: {e}")
            raise

    def get_kb_document_by_id(self, file_id: str) -> Optional[KbDocumentModel]:
        """根据文档ID获取文档"""
        try:
            with self.db_manager.get_session() as db_session:
                kb_document = (
                    db_session.query(KbDocumentModel)
                    .filter(KbDocumentModel.file_id == file_id)
                    .first()
                )

                # 只有当session不为None时才进行refresh和expunge操作
                if kb_document is not None:
                    db_session.refresh(kb_document)
                    db_session.expunge(kb_document)
                return kb_document

        except Exception as e:
            logger.error(f"[KbDocumentsDB] 获取文档失败: {e}")
            raise

    def list_kb_documents(
        self,
        doc_id: Optional[str] = None,
        oss_bucket: Optional[str] = None,
        file_title: Optional[str] = None,
        status: Optional[str] = None,
        session_id: Optional[str] = None,
        file_id: Optional[str] = None,
        file_id_list: Optional[List[str]] = None,
        less_than_equal_id: Optional[int] = None,
        less_than_equal_gmt_create: Optional[datetime] = None,
        limit: Optional[int] = None,
        order_pairs: Optional[list[UnaryExpression]] = None,
    ) -> List[KbDocumentModel]:
        """分页搜索知识库文档"""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(KbDocumentModel)

                if doc_id:
                    query = query.filter(KbDocumentModel.doc_id == doc_id)
                if oss_bucket:
                    query = query.filter(KbDocumentModel.oss_bucket == oss_bucket)
                if file_title:
                    query = query.filter(
                        KbDocumentModel.file_title.like(f"%{file_title}%")
                    )
                if status:
                    query = query.filter(KbDocumentModel.status == status)
                if session_id:
                    query = query.filter(KbDocumentModel.session_id == session_id)
                if file_id:
                    query = query.filter(KbDocumentModel.file_id == file_id)
                if file_id_list:
                    query = query.filter(KbDocumentModel.file_id.in_(file_id_list))
                if less_than_equal_gmt_create:
                    query = query.filter(
                        KbDocumentModel.gmt_created <= less_than_equal_gmt_create
                    )
                if less_than_equal_id:
                    query = query.filter(KbDocumentModel.id <= less_than_equal_id)
                if order_pairs:
                    for order_pair in order_pairs:
                        query = query.order_by(order_pair)
                if limit is not None:
                    query = query.limit(limit)
                results = query.all()

                # 确保所有延迟加载的属性都被加载
                for obj in results:
                    db_session.refresh(obj)

                # 从 session 中分离所有对象，避免 session 结束后对象游离
                detached_results = []
                for obj in results:
                    db_session.expunge(obj)
                    detached_results.append(obj)

                return detached_results

        except Exception as e:
            logger.error(f"[KbDocumentsDB] 分页搜索文档失败: {e}")
            raise

    def update_kb_document(
        self,
        doc_id: str,
        file_title: Optional[str] = None,
        oss_bucket: Optional[str] = None,
        oss_object_name: Optional[str] = None,
        file_size: Optional[int] = None,
        status: Optional[str] = None,
        session_id: Optional[str] = None,
        file_id: Optional[str] = None,
    ) -> Optional[KbDocumentModel]:
        """更新知识库文档"""
        try:
            with self.db_manager.get_session() as db_session:
                kb_document = (
                    db_session.query(KbDocumentModel)
                    .filter(KbDocumentModel.doc_id == doc_id)
                    .first()
                )

                if not kb_document:
                    logger.warning(f"[KbDocumentsDB] 文档不存在: {doc_id}")
                    return None

                # 更新字段
                if file_title is not None:
                    kb_document.file_title = file_title
                if oss_bucket is not None:
                    kb_document.oss_bucket = oss_bucket
                if oss_object_name is not None:
                    kb_document.oss_object_name = oss_object_name
                if file_size is not None:
                    kb_document.file_size = file_size
                if status is not None:
                    kb_document.status = status
                if session_id is not None:
                    kb_document.session_id = session_id
                if file_id is not None:
                    kb_document.file_id = file_id

                # 更新时间会自动更新
                db_session.commit()
                logger.info(f"[KbDocumentsDB] 更新文档成功: {doc_id}")

                return kb_document

        except Exception as e:
            logger.error(f"[KbDocumentsDB] 更新文档失败: {e}")
            raise

    def count_kb_documents(
        self,
        doc_id: Optional[str] = None,
        oss_bucket: Optional[str] = None,
        file_title: Optional[str] = None,
        status: Optional[str] = None,
        session_id: Optional[str] = None,
        file_id: Optional[str] = None,
    ) -> int:
        """统计知识库文档数量"""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(KbDocumentModel)

                if doc_id:
                    query = query.filter(KbDocumentModel.doc_id == doc_id)
                if oss_bucket:
                    query = query.filter(KbDocumentModel.oss_bucket == oss_bucket)
                if file_title:
                    query = query.filter(
                        KbDocumentModel.file_title.like(f"%{file_title}%")
                    )
                if status:
                    query = query.filter(KbDocumentModel.status == status)
                if session_id:
                    query = query.filter(KbDocumentModel.session_id == session_id)
                if file_id:
                    query = query.filter(KbDocumentModel.file_id == file_id)

                return query.count()

        except Exception as e:
            logger.error(f"[KbDocumentsDB] 统计文档数量失败: {e}")
            raise

    def count_documents_by_kb(self, kb_id: str) -> int:
        """统计某知识库下的文档数量（通过关联表）"""
        try:
            with self.db_manager.get_session() as db_session:
                # 通过关联表统计文档数量
                from ..models.knowledgebase_models import KbDocumentRelationModel

                count = (
                    db_session.query(KbDocumentRelationModel)
                    .filter(
                        and_(
                            KbDocumentRelationModel.kb_id == kb_id,
                            KbDocumentRelationModel.session_id.is_(None),
                            KbDocumentRelationModel.is_deleted == 0,
                        )
                    )
                    .count()
                )
                return count
        except Exception as e:
            logger.error(f"[KbDocumentsDB] 统计知识库文档数量失败: {e}")
            raise

    def batch_count_documents_by_kb_ids(self, kb_ids: List[str]) -> Dict[str, int]:
        """批量统计多个知识库下的文档数量"""
        try:
            if not kb_ids:
                return {}

            with self.db_manager.get_session() as db_session:
                from ..models.knowledgebase_models import KbDocumentRelationModel
                from sqlalchemy import func

                # 使用GROUP BY批量统计
                results = (
                    db_session.query(
                        KbDocumentRelationModel.kb_id,
                        func.count(KbDocumentRelationModel.id).label("count"),
                    )
                    .filter(
                        and_(
                            KbDocumentRelationModel.kb_id.in_(kb_ids),
                            KbDocumentRelationModel.session_id.is_(None),
                            KbDocumentRelationModel.is_deleted == 0,
                        )
                    )
                    .group_by(KbDocumentRelationModel.kb_id)
                    .all()
                )

                # 转换为字典格式
                count_dict = {kb_id: 0 for kb_id in kb_ids}  # 初始化所有kb_id为0
                for kb_id, count in results:
                    count_dict[kb_id] = count

                return count_dict
        except Exception as e:
            logger.error(f"[KbDocumentsDB] 批量统计知识库文档数量失败: {e}")
            raise

    def batch_sum_file_size_by_session_ids(
        self, kb_id: str, session_ids: List[str]
    ) -> Dict[str, int]:
        """批量统计指定知识库下多个会话的文件大小总和"""
        try:
            if not session_ids:
                return {}

            with self.db_manager.get_session() as db_session:
                from ..models.knowledgebase_models import KbDocumentRelationModel
                from sqlalchemy import func

                # 通过关联表和文档表JOIN，统计指定知识库下每个会话的文件大小总和
                results = (
                    db_session.query(
                        KbDocumentRelationModel.session_id,
                        func.sum(KbDocumentModel.file_size).label("total_size"),
                    )
                    .join(
                        KbDocumentModel,
                        and_(
                            KbDocumentRelationModel.file_id == KbDocumentModel.file_id,
                            KbDocumentRelationModel.is_deleted == 0,
                        ),
                    )
                    .filter(
                        and_(
                            KbDocumentRelationModel.kb_id == kb_id,
                            KbDocumentRelationModel.session_id.in_(session_ids),
                        )
                    )
                    .group_by(KbDocumentRelationModel.session_id)
                    .all()
                )

                # 转换为字典格式
                size_dict = {
                    session_id: 0 for session_id in session_ids
                }  # 初始化所有session_id为0
                for session_id, total_size in results:
                    if total_size is not None:
                        size_dict[session_id] = int(total_size)

                return size_dict
        except Exception as e:
            logger.error(f"[KbDocumentsDB] 批量统计会话文件大小失败: {e}")
            raise

    def batch_insert_documents(
        self, documents: List[KbDocumentModel]
    ) -> List[KbDocumentModel]:
        """批量插入知识库文档"""
        try:
            with self.db_manager.get_session() as db_session:
                kb_documents = []
                for kb_document in documents:
                    kb_documents.append(kb_document)

                db_session.add_all(kb_documents)
                db_session.flush()

                # 确保所有延迟加载的属性都被加载
                for obj in kb_documents:
                    db_session.refresh(obj)

                # 从 session 中分离所有对象，避免 session 结束后对象游离
                detached_results = []
                for obj in kb_documents:
                    db_session.expunge(obj)
                    detached_results.append(obj)

                logger.info(f"[KbDocumentsDB] 批量插入文档成功: {len(documents)} 个")
                return detached_results

        except Exception as e:
            logger.error(f"[KbDocumentsDB] 批量插入文档失败: {e}")
            raise


# 创建全局实例
kb_documents_repository = KbDocumentsRepository()
