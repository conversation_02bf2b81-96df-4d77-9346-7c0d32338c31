# -*- coding: utf-8 -*-
"""
文件数据库服务
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import UnaryExpression, and_, or_, desc, asc
from loguru import logger

from ..connection import db_manager
from ..models.file_models import AlphaFile, FileType, UploadStatus


class FileRepository:
    """文件数据仓库 - 只负责数据库CRUD操作"""
    
    @staticmethod
    def _not_deleted_condition():
        """生成未删除的查询条件（兼容老数据）"""
        return or_(AlphaFile.is_delete == 0, AlphaFile.is_delete.is_(None))
    
    def create_file(
        self,
        title: str,
        oss_bucket: str,
        oss_object_name: str,
        file_type: str,
        session_id: Optional[str] = None,
        ali_uid: Optional[int] = None,
        wy_id: Optional[str] = None,
        artifact_id: Optional[str] = None,
        file_size: Optional[int] = None,
        content_type: Optional[str] = None,
        content: Optional[str] = None,
        upload_status: Optional[str] = None
    ) -> AlphaFile:
        """
        创建文件记录

        Args:
            title: 文件名
            oss_bucket: OSS存储桶
            oss_object_name: OSS对象名称
            file_type: 文件类型
            session_id: 会话ID
            ali_uid: 阿里云用户ID
            wy_id: wy用户ID
            artifact_id: 制品ID，如果未提供则自动生成UUID
            file_size: 文件大小
            content_type: 文件MIME类型
            content: 文件内容

        Returns:
            AlphaFile: 创建的文件对象
        """
        try:
            # 如果没有提供artifact_id，则生成一个带前缀的UUID
            if artifact_id is None:
                import uuid
                artifact_id = f"artifact-{str(uuid.uuid4()).replace('-', '')}"
            
            db = db_manager.get_session_sync()
            try:
                # 检查artifact_id是否已存在
                if artifact_id:
                    existing_file = db.query(AlphaFile).filter(AlphaFile.artifact_id == artifact_id).first()
                    if existing_file:
                        raise ValueError(f"artifact_id已存在: {artifact_id}")
                
                file_obj = AlphaFile(
                    title=title,
                    oss_bucket=oss_bucket,
                    oss_object_name=oss_object_name,
                    type=file_type,
                    session_id=session_id,
                    ali_uid=ali_uid,
                    wy_id=wy_id,
                    artifact_id=artifact_id,
                    file_size=file_size,
                    content_type=content_type,
                    content=content,
                    upload_status=upload_status,
                    is_delete=0  # 默认设置为未删除
                )

                db.add(file_obj)
                db.commit()
                db.refresh(file_obj)

                logger.info(f"[FileDB] 创建文件记录成功: id={file_obj.id}, title={title}")
                return file_obj
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"[FileDB] 创建文件记录失败: {e}")
            raise

    def get_file_by_id(self, file_id: int) -> Optional[AlphaFile]:
        """
        根据ID获取文件（只返回未删除的文件）
        
        Args:
            file_id: 文件ID
            
        Returns:
            AlphaFile: 文件对象，如果不存在或已删除则返回None
        """
        try:
            db = db_manager.get_session_sync()
            try:
                file_obj = db.query(AlphaFile).filter(
                    AlphaFile.id == file_id,
                    self._not_deleted_condition()  # 只查询未删除的文件（兼容老数据）
                ).first()
                return file_obj
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"[FileDB] 获取文件失败: file_id={file_id}, error={e}")
            return None
    
    def get_files_by_ids(self, file_ids: List[str]) -> List[AlphaFile]:
        """
        根据文件ID列表获取文件（只返回未删除的文件）
        
        Args:
            file_ids: 文件ID列表
            
        Returns:
            List[AlphaFile]: 文件列表
        """
        try:
            db = db_manager.get_session_sync()
            try:
                files = db.query(AlphaFile).filter(
                    AlphaFile.id.in_(file_ids),
                    self._not_deleted_condition()  # 只查询未删除的文件（兼容老数据）
                ).all()

                logger.info(f"[FileDB] 根据ID获取文件列表成功: file_ids={file_ids}, found={len(files)}")
                return files
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"[FileDB] 根据ID获取文件列表失败: file_ids={file_ids}, error={e}")
            return []

    def get_files_by_artifact_ids(self, artifact_ids: List[str]) -> List[AlphaFile]:
        """
        根据制品ID列表获取文件（只返回未删除的文件）
        
        Args:
            artifact_ids: 制品ID列表
            
        Returns:
            List[AlphaFile]: 文件列表
        """
        try:
            db = db_manager.get_session_sync()
            try:
                files = db.query(AlphaFile).filter(
                    AlphaFile.artifact_id.in_(artifact_ids),
                    AlphaFile.artifact_id.isnot(None),
                    self._not_deleted_condition()  # 只查询未删除的文件（兼容老数据）
                ).all()

                logger.info(f"[FileDB] 根据制品ID获取文件列表成功: artifact_ids={artifact_ids}, found={len(files)}")
                return files
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"[FileDB] 根据制品ID获取文件列表失败: artifact_ids={artifact_ids}, error={e}")
            return []

    def get_file_by_artifact_id(self, artifact_id: str) -> Optional[AlphaFile]:
        """
        根据制品ID获取单个文件（只返回未删除的文件）
        
        Args:
            artifact_id: 制品ID
            
        Returns:
            Optional[AlphaFile]: 文件对象，如果不存在或已删除则返回None
        """
        try:
            db = db_manager.get_session_sync()
            try:
                file_obj = db.query(AlphaFile).filter(
                    AlphaFile.artifact_id == artifact_id,
                    AlphaFile.artifact_id.isnot(None),
                    self._not_deleted_condition()  # 只查询未删除的文件（兼容老数据）
                ).first()

                if file_obj:
                    logger.info(f"[FileDB] 根据制品ID获取文件成功: artifact_id={artifact_id}, file_id={file_obj.id}")
                else:
                    logger.warning(f"[FileDB] 根据制品ID未找到文件: artifact_id={artifact_id}")
                
                return file_obj
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"[FileDB] 根据制品ID获取文件失败: artifact_id={artifact_id}, error={e}")
            return None
     
    def list_files_by_session(
        self,
        ali_uid: int,
        wy_id: str,
        session_id: str,
        file_types: Optional[List[str]] = None,
        less_than_equal_id: Optional[int] = None,
        less_than_equal_gmt_create: Optional[datetime] = None,
        limit: int = 100,
        order_pairs: Optional[list[UnaryExpression]] = None,
    ) -> List[AlphaFile]:
        """
        根据会话ID获取文件列表（只返回未删除的文件）
        
        Args:
            session_id: 会话ID
            file_types: 文件类型过滤列表，可以传多个类型
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[AlphaFile]: 文件列表
        """
        try:
            db = db_manager.get_session_sync()
            try:
                query = db.query(AlphaFile).filter(
                    AlphaFile.session_id == session_id,
                    AlphaFile.ali_uid == ali_uid,
                    AlphaFile.wy_id == wy_id,
                    self._not_deleted_condition()  # 只查询未删除的文件（兼容老数据）
                )

                if file_types:
                    query = query.filter(AlphaFile.type.in_(file_types))

                if less_than_equal_gmt_create:
                    query = query.filter(
                        AlphaFile.gmt_created >= less_than_equal_gmt_create
                    )
                if less_than_equal_id:
                    query = query.filter(AlphaFile.id >= less_than_equal_id)
                if order_pairs:
                    for order_pair in order_pairs:
                        query = query.order_by(order_pair)
                if limit is not None:
                    query = query.limit(limit)

                # files = query.order_by(desc(AlphaFile.gmt_created)).offset(offset).limit(limit).all()
                files = query.all()

                logger.info(f"[FileDB] 获取会话文件列表成功: session_id={session_id}, types={file_types}, count={len(files)}")
                return files
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"[FileDB] 获取会话文件列表失败: session_id={session_id}, error={e}")
            return []

    def update_file_progress(
        self,
        file_id: int,
        progress: int,
        status: Optional[str] = None,
        error_message: Optional[str] = None,
        file_size: Optional[int] = None
    ) -> bool:
        """
        更新文件上传进度（只更新未删除的文件）

        Args:
            file_id: 文件ID
            progress: 上传进度（0-100）
            status: 新状态
            error_message: 错误信息
            file_size: 文件大小

        Returns:
            bool: 是否更新成功
        """
        try:
            db = db_manager.get_session_sync()
            try:
                file_obj = db.query(AlphaFile).filter(
                    AlphaFile.id == file_id,
                    AlphaFile.is_delete == 0  # 只更新未删除的文件
                ).first()
                if not file_obj:
                    logger.warning(f"[FileDB] 文件不存在或已删除: file_id={file_id}")
                    return False

                file_obj.upload_progress = progress
                if status:
                    file_obj.upload_status = status
                if error_message:
                    file_obj.error_message = error_message
                if file_size is not None:
                    file_obj.file_size = file_size
                file_obj.gmt_modified = datetime.utcnow()

                db.commit()
                logger.info(f"[FileDB] 更新文件进度成功: file_id={file_id}, progress={progress}%")
                return True
            finally:
                db.close()

        except Exception as e:
            logger.error(f"[FileDB] 更新文件进度失败: file_id={file_id}, error={e}")
            return False
    
    def update_file_status(
        self,
        file_id: int,
        status: str,
        error_message: Optional[str] = None,
        file_size: Optional[int] = None
    ) -> bool:
        """
        更新文件状态（只更新未删除的文件）
        
        Args:
            file_id: 文件ID
            status: 新状态
            error_message: 错误信息
            file_size: 文件大小
            
        Returns:
            bool: 是否更新成功
        """
        try:
            db = db_manager.get_session_sync()
            try:
                file_obj = db.query(AlphaFile).filter(
                    AlphaFile.id == file_id,
                    AlphaFile.is_delete == 0  # 只更新未删除的文件
                ).first()
                if not file_obj:
                    logger.warning(f"[FileDB] 文件不存在或已删除: file_id={file_id}")
                    return False
                
                file_obj.upload_status = status
                if error_message:
                    file_obj.error_message = error_message
                if file_size is not None:
                    file_obj.file_size = file_size
                file_obj.gmt_modified = datetime.utcnow()
                
                db.commit()
                logger.info(f"[FileDB] 更新文件状态成功: file_id={file_id}, status={status}")
                return True
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"[FileDB] 更新文件状态失败: file_id={file_id}, error={e}")
            return False

    def update_file_content(
        self,
        file_id: int,
        content: str
    ) -> bool:
        """
        更新文件内容（只更新未删除的文件）

        Args:
            file_id: 文件ID
            content: 文件内容

        Returns:
            bool: 是否更新成功
        """
        try:
            db = db_manager.get_session_sync()
            try:
                file_obj = db.query(AlphaFile).filter(
                    AlphaFile.id == file_id,
                    AlphaFile.is_delete == 0  # 只更新未删除的文件
                ).first()
                if not file_obj:
                    logger.warning(f"[FileDB] 文件不存在或已删除: file_id={file_id}")
                    return False

                file_obj.content = content
                file_obj.gmt_modified = datetime.utcnow()

                db.commit()
                logger.info(f"[FileDB] 更新文件内容成功: file_id={file_id}")
                return True
            finally:
                db.close()

        except Exception as e:
            logger.error(f"[FileDB] 更新文件内容失败: file_id={file_id}, error={e}")
            return False
    
    def update_file_name(self, file_id: int, new_file_name: str) -> bool:
        """
        更新文件名（只更新未删除的文件）

        Args:
            file_id: 文件ID
            new_file_name: 新文件名

        Returns:
            bool: 是否更新成功
        """
        try:
            db = db_manager.get_session_sync()
            try:
                file_obj = db.query(AlphaFile).filter(
                    AlphaFile.id == file_id,
                    self._not_deleted_condition()  # 只更新未删除的文件（兼容老数据）
                ).first()
                if not file_obj:
                    logger.warning(f"[FileDB] 文件不存在或已删除: file_id={file_id}")
                    return False

                old_file_name = file_obj.title
                file_obj.title = new_file_name
                file_obj.gmt_modified = datetime.utcnow()

                db.commit()
                logger.info(f"[FileDB] 更新文件名成功: file_id={file_id}, old_name={old_file_name}, new_name={new_file_name}")
                return True
            finally:
                db.close()

        except Exception as e:
            logger.error(f"[FileDB] 更新文件名失败: file_id={file_id}, error={e}")
            return False

    def mark_file_analyzing(self, file_id: int) -> bool:
        """
        标记文件为分析中状态（只标记未删除的文件）

        Args:
            file_id: 文件ID

        Returns:
            bool: 是否更新成功
        """
        try:
            db = db_manager.get_session_sync()
            try:
                file_obj = db.query(AlphaFile).filter(
                    AlphaFile.id == file_id,
                    AlphaFile.is_delete == 0  # 只标记未删除的文件
                ).first()
                if not file_obj:
                    logger.warning(f"[FileDB] 文件不存在或已删除: file_id={file_id}")
                    return False

                file_obj.upload_status = UploadStatus.ANALYZING.value
                file_obj.upload_progress = 100
                file_obj.gmt_modified = datetime.utcnow()

                db.commit()
                logger.info(f"[FileDB] 标记文件为分析中成功: file_id={file_id}")
                return True
            finally:
                db.close()

        except Exception as e:
            logger.error(f"[FileDB] 标记文件为分析中失败: file_id={file_id}, error={e}")
            return False

    def mark_file_analysis_completed(self, file_id: int) -> bool:
        """
        标记文件分析完成（只标记未删除的文件）

        Args:
            file_id: 文件ID

        Returns:
            bool: 是否更新成功
        """
        try:
            db = db_manager.get_session_sync()
            try:
                file_obj = db.query(AlphaFile).filter(
                    AlphaFile.id == file_id,
                    AlphaFile.is_delete == 0  # 只标记未删除的文件
                ).first()
                if not file_obj:
                    logger.warning(f"[FileDB] 文件不存在或已删除: file_id={file_id}")
                    return False

                file_obj.upload_status = UploadStatus.COMPLETED.value
                file_obj.gmt_modified = datetime.utcnow()

                db.commit()
                logger.info(f"[FileDB] 标记文件分析完成: file_id={file_id}")
                return True
            finally:
                db.close()

        except Exception as e:
            logger.error(f"[FileDB] 标记文件分析完成失败: file_id={file_id}, error={e}")
            return False

    def update_file_doc_id(self, file_id: int, doc_id: str) -> bool:
        """
        更新文件的RAG文档ID（只更新未删除的文件）

        Args:
            file_id: 文件ID
            doc_id: RAG解析返回的文档ID

        Returns:
            bool: 是否更新成功
        """
        try:
            db = db_manager.get_session_sync()
            try:
                file_obj = db.query(AlphaFile).filter(
                    AlphaFile.id == file_id,
                    AlphaFile.is_delete == 0  # 只更新未删除的文件
                ).first()
                if not file_obj:
                    logger.warning(f"[FileDB] 文件不存在或已删除: file_id={file_id}")
                    return False

                file_obj.doc_id = doc_id
                file_obj.gmt_modified = datetime.utcnow()

                db.commit()
                logger.info(f"[FileDB] 更新文件doc_id成功: file_id={file_id}, doc_id={doc_id}")
                return True
            finally:
                db.close()

        except Exception as e:
            logger.error(f"[FileDB] 更新文件doc_id失败: file_id={file_id}, error={e}")
            return False

    def mark_file_completed(self, file_id: int, file_size: Optional[int] = None) -> bool:
        """
        标记文件上传完成（只标记未删除的文件）

        Args:
            file_id: 文件ID
            file_size: 文件大小

        Returns:
            bool: 是否更新成功
        """
        try:
            db = db_manager.get_session_sync()
            try:
                file_obj = db.query(AlphaFile).filter(
                    AlphaFile.id == file_id,
                    AlphaFile.is_delete == 0  # 只标记未删除的文件
                ).first()
                if not file_obj:
                    logger.warning(f"[FileDB] 文件不存在或已删除: file_id={file_id}")
                    return False

                file_obj.mark_completed(file_size)
                db.commit()

                logger.info(f"[FileDB] 标记文件完成成功: file_id={file_id}")
                return True
            finally:
                db.close()

        except Exception as e:
            logger.error(f"[FileDB] 标记文件完成失败: file_id={file_id}, error={e}")
            return False

    def mark_file_failed(self, file_id: int, error_message: str) -> bool:
        """
        标记文件上传失败（只标记未删除的文件）

        Args:
            file_id: 文件ID
            error_message: 错误信息

        Returns:
            bool: 是否更新成功
        """
        try:
            db = db_manager.get_session_sync()
            try:
                file_obj = db.query(AlphaFile).filter(
                    AlphaFile.id == file_id,
                    AlphaFile.is_delete == 0  # 只标记未删除的文件
                ).first()
                if not file_obj:
                    logger.warning(f"[FileDB] 文件不存在或已删除: file_id={file_id}")
                    return False

                file_obj.mark_failed(error_message)
                db.commit()

                logger.info(f"[FileDB] 标记文件失败成功: file_id={file_id}")
                return True
            finally:
                db.close()

        except Exception as e:
            logger.error(f"[FileDB] 标记文件失败失败: file_id={file_id}, error={e}")
            return False
    
    def delete_file(self, file_id: int) -> bool:
        """
        软删除文件记录（标记为已删除，不实际删除数据）
        
        Args:
            file_id: 文件ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            db = db_manager.get_session_sync()
            try:
                file_obj = db.query(AlphaFile).filter(
                    AlphaFile.id == file_id,
                    self._not_deleted_condition()  # 只查询未删除的文件（兼容老数据）
                ).first()
                if not file_obj:
                    logger.warning(f"[FileDB] 文件不存在或已删除: file_id={file_id}")
                    return False
                
                # 软删除：标记 is_delete = 1
                file_obj.is_delete = 1
                file_obj.gmt_modified = datetime.utcnow()
                db.commit()
                
                logger.info(f"[FileDB] 软删除文件记录成功: file_id={file_id}")
                return True
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"[FileDB] 软删除文件记录失败: file_id={file_id}, error={e}")
            return False
    
    def restore_file(self, file_id: int) -> bool:
        """
        恢复软删除的文件记录
        
        Args:
            file_id: 文件ID
            
        Returns:
            bool: 是否恢复成功
        """
        try:
            db = db_manager.get_session_sync()
            try:
                file_obj = db.query(AlphaFile).filter(
                    AlphaFile.id == file_id,
                    AlphaFile.is_delete == 1  # 只查询已删除的文件
                ).first()
                if not file_obj:
                    logger.warning(f"[FileDB] 文件不存在或未删除: file_id={file_id}")
                    return False
                
                # 恢复文件：标记 is_delete = 0
                file_obj.is_delete = 0
                file_obj.gmt_modified = datetime.utcnow()
                db.commit()
                
                logger.info(f"[FileDB] 恢复文件记录成功: file_id={file_id}")
                return True
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"[FileDB] 恢复文件记录失败: file_id={file_id}, error={e}")
            return False
    
    def get_file_statistics(self, session_id: Optional[str] = None) -> Dict[str, Any]:
        """
        获取文件统计信息（只统计未删除的文件）
        
        Args:
            session_id: 会话ID，如果提供则只统计该会话的文件
            
        Returns:
            Dict: 统计信息
        """
        try:
            db = db_manager.get_session_sync()
            try:
                query = db.query(AlphaFile).filter(AlphaFile.is_delete == 0)  # 只统计未删除的文件
                
                if session_id:
                    query = query.filter(AlphaFile.session_id == session_id)
                
                total_files = query.count()
                
                # 按类型统计
                type_stats = {}
                for file_type in [FileType.RESULT_ARTIFACT.value, FileType.PROCESS_ARTIFACT.value, FileType.SESSION_FILE.value]:
                    count = query.filter(AlphaFile.type == file_type).count()
                    type_stats[file_type] = count
                
                # 按状态统计
                status_stats = {}
                for status in ["uploading", "completed", "failed"]:
                    count = query.filter(AlphaFile.upload_status == status).count()
                    status_stats[status] = count
                
                return {
                    "total_files": total_files,
                    "type_distribution": type_stats,
                    "status_distribution": status_stats
                }
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"[FileDB] 获取文件统计失败: error={e}")
            return {}


# 创建全局实例
file_repository = FileRepository()
