"""
知识库会话数据库服务层
提供知识库会话的CRUD操作
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from domain.models.enums import KbState
from sqlalchemy.orm import Session as SQLSession
from sqlalchemy.exc import IntegrityError
from sqlalchemy import and_, or_, UnaryExpression
from loguru import logger

from ..models.knowledgebase_models import KbSessionModel
from ..connection import db_manager


class KbSessionRepository:
    """知识库会话数据库仓库"""

    def __init__(self):
        self.db_manager = db_manager

    def create_session(
        self,
        kb_id: str,
        session_id: str,
        message_id_list: Optional[str] = None,
        snippet_id: Optional[str] = None,
        snippet_status: str = KbState.PROCESSING.value,
        session_status: str = KbState.PROCESSING.value,
    ) -> KbSessionModel:
        """创建知识库会话"""
        try:
            assert kb_id, "kb_id is required"
            assert session_id, "session_id is required"
            session = KbSessionModel(
                kb_id=kb_id,
                session_id=session_id,
                is_deleted=0,  # 0 表示使用中
                message_id_list=message_id_list,
                snippet_id=snippet_id,
                snippet_status=snippet_status,
                session_status=session_status,
            )
            with self.db_manager.get_session() as db_session:
                db_session.add(session)
                db_session.flush()
                logger.info(
                    f"[KbSessionDB] 创建会话: kb_id={kb_id}, session_id={session_id}"
                )
                # 只有当session不为None时才进行refresh和expunge操作
                if session is not None:
                    db_session.refresh(session)
                    db_session.expunge(session)
                return session
        except IntegrityError as e:
            logger.error(
                f"[KbSessionDB] 会话已存在: kb_id={kb_id}, session_id={session_id}"
            )
            raise ValueError(f"会话已存在: kb_id={kb_id}, session_id={session_id}")
        except Exception as e:
            logger.error(f"[KbSessionDB] 创建会话失败: {e}")
            raise

    def get_session(self, kb_id: str, session_id: str) -> Optional[KbSessionModel]:
        """根据kb_id和session_id获取会话"""
        try:
            with self.db_manager.get_session() as db_session:
                session = (
                    db_session.query(KbSessionModel)
                    .filter(
                        and_(
                            KbSessionModel.kb_id == kb_id,
                            KbSessionModel.session_id == session_id,
                            KbSessionModel.is_deleted == 0,  # 0 表示使用中
                        )
                    )
                    .first()
                )

                # 只有当session不为None时才进行refresh和expunge操作
                if session is not None:
                    db_session.refresh(session)
                    db_session.expunge(session)
                return session
        except Exception as e:
            logger.error(f"[KbSessionDB] 获取会话失败: {e}")
            raise

    def list_sessions(
        self,
        kb_id: Optional[str] = None,
        session_id_list: Optional[List[str]] = None,
        snippet_status: Optional[str] = None,
        not_snippet_status: Optional[str] = None,
        session_status: Optional[str] = None,
        less_than_equal_id: Optional[int] = None,
        less_than_equal_gmt_create: Optional[datetime] = None,
        limit: Optional[int] = None,
        order_pairs: Optional[list[UnaryExpression]] = None,
    ) -> List[KbSessionModel]:
        """获取某知识库下所有会话，支持分页"""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(KbSessionModel).filter(
                    and_(
                        KbSessionModel.is_deleted == 0,  # 0 表示使用中
                    )
                )
                if kb_id:
                    query = query.filter(KbSessionModel.kb_id == kb_id)
                if snippet_status:
                    query = query.filter(KbSessionModel.snippet_status == snippet_status)
                if not_snippet_status:
                    query = query.filter(KbSessionModel.snippet_status != not_snippet_status)
                if session_status:
                    query = query.filter(KbSessionModel.session_status == session_status)
                if session_id_list:
                    query = query.filter(KbSessionModel.session_id.in_(session_id_list))
                if less_than_equal_id:
                    query = query.filter(KbSessionModel.id <= less_than_equal_id)
                if less_than_equal_gmt_create:
                    query = query.filter(
                        KbSessionModel.gmt_created <= less_than_equal_gmt_create
                    )
                if order_pairs:
                    query = query.order_by(*order_pairs)
                if limit:
                    query = query.limit(limit)
                results = query.all()

                # 确保所有延迟加载的属性都被加载
                for obj in results:
                    db_session.refresh(obj)

                # 从 session 中分离所有对象，避免 session 结束后对象游离
                detached_results = []
                for obj in results:
                    db_session.expunge(obj)
                    detached_results.append(obj)

                return detached_results
        except Exception as e:
            logger.error(f"[KbSessionDB] 获取知识库会话失败: {e}")
            raise

    def update_session_selective(
        self,
        kb_id: str,
        session_id: str,
        message_id_list: Optional[str] = None,
        snippet_id: Optional[str] = None,
        snippet_status: Optional[str] = None,
        session_status: Optional[str] = None,
    ) -> Optional[KbSessionModel]:
        """更新知识库会话"""
        try:
            with self.db_manager.get_session() as db_session:
                session = (
                    db_session.query(KbSessionModel)
                    .filter(
                        and_(
                            KbSessionModel.kb_id == kb_id,
                            KbSessionModel.session_id == session_id,
                            KbSessionModel.is_deleted == 0,  # 0 表示使用中
                        )
                    )
                    .first()
                )
                if not session:
                    logger.warning(
                        f"[KbSessionDB] 会话不存在: kb_id={kb_id}, session_id={session_id}"
                    )
                    return None
                if message_id_list is not None:
                    session.message_id_list = message_id_list
                if snippet_id is not None:
                    session.snippet_id = snippet_id
                if snippet_status is not None:
                    session.snippet_status = snippet_status
                if session_status is not None:
                    session.session_status = session_status
                session.gmt_modified = datetime.now()
                logger.info(
                    f"[KbSessionDB] 更新会话: kb_id={kb_id}, session_id={session_id}"
                )
                # 只有当session不为None时才进行refresh和expunge操作
                if session is not None:
                    db_session.refresh(session)
                    db_session.expunge(session)
                return session
        except IntegrityError as e:
            logger.error(f"[KbSessionDB] 更新会话失败，唯一约束冲突: {e}")
            raise ValueError("会话唯一约束冲突")
        except Exception as e:
            logger.error(f"[KbSessionDB] 更新会话失败: {e}")
            raise

    def update_session_by_id(self, session: KbSessionModel) -> Optional[KbSessionModel]:
        """根据session中的id主键更新对象"""
        try:
            assert session.id is not None, "session.id is required"
            
            with self.db_manager.get_session() as db_session:
                # 根据主键ID查询要更新的会话
                existing_session = db_session.query(KbSessionModel).filter(
                    KbSessionModel.id == session.id
                ).first()
                
                if not existing_session:
                    logger.warning(f"[KbSessionDB] 会话不存在: id={session.id}")
                    return None
                
                # 更新字段（除了主键id和创建时间）
                existing_session.kb_id = session.kb_id
                existing_session.session_id = session.session_id
                existing_session.is_deleted = session.is_deleted
                existing_session.message_id_list = session.message_id_list
                existing_session.snippet_id = session.snippet_id
                existing_session.snippet_status = session.snippet_status
                existing_session.session_status = session.session_status
                
                # 更新修改时间
                existing_session.gmt_modified = datetime.now()
                
                # 提交事务
                db_session.commit()
                
                # 刷新对象并分离
                db_session.refresh(existing_session)
                db_session.expunge(existing_session)
                
                logger.info(f"[KbSessionDB] 更新会话成功: id={session.id}")
                return existing_session
                
        except IntegrityError as e:
            logger.error(f"[KbSessionDB] 更新会话失败，唯一约束冲突: {e}")
            raise ValueError("会话唯一约束冲突")
        except Exception as e:
            logger.error(f"[KbSessionDB] 更新会话失败: {e}")
            raise

    def soft_delete_session(self, kb_id: str, session_id: str) -> bool:
        """软删除知识库会话（单个会话）"""
        return self.soft_delete_sessions(kb_id=kb_id, session_ids=[session_id]) > 0

    def soft_delete_sessions(
        self, 
        kb_id: str, 
        session_ids: Optional[List[str]] = None
    ) -> int:
        """软删除知识库会话（支持批量删除）"""
        try:
            with self.db_manager.get_session() as db_session:
                # 构建查询条件
                conditions = [
                    KbSessionModel.kb_id == kb_id,
                    KbSessionModel.is_deleted == 0,  # 0 表示使用中
                ]
                
                # 如果指定了 session_ids，添加会话ID条件
                if session_ids:
                    conditions.append(KbSessionModel.session_id.in_(session_ids))
                
                # 查询要删除的会话
                sessions = (
                    db_session.query(KbSessionModel)
                    .filter(and_(*conditions))
                    .all()
                )
                
                if not sessions:
                    logger.warning(
                        f"[KbSessionDB] 没有找到要删除的会话: kb_id={kb_id}, "
                        f"session_ids={session_ids}"
                    )
                    return 0
                
                # 批量软删除
                current_time = datetime.now()
                for session in sessions:
                    session.is_deleted = session.id  # 非0表示删除
                    session.gmt_modified = current_time
                
                # 提交事务
                db_session.commit()
                
                logger.info(
                    f"[KbSessionDB] 批量软删除会话成功: kb_id={kb_id}, "
                    f"删除数量={len(sessions)}, session_ids={session_ids}"
                )
                return len(sessions)
                
        except Exception as e:
            logger.error(f"[KbSessionDB] 批量软删除会话失败: {e}")
            raise

    def count_sessions(
        self, kb_id: str, session_id_list: Optional[List[str]] = None
    ) -> int:
        """统计某知识库下的会话数量"""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(KbSessionModel).filter(
                    and_(
                        KbSessionModel.kb_id == kb_id,
                        KbSessionModel.is_deleted == 0,  # 0 表示使用中
                    )
                )

                # 只有当 session_id_list 不为空时才添加过滤条件
                if session_id_list:
                    query = query.filter(KbSessionModel.session_id.in_(session_id_list))

                return query.count()
        except Exception as e:
            logger.error(f"[KbSessionDB] 统计会话数量失败: {e}")
            raise

    def batch_count_sessions_by_kb_ids(self, kb_ids: List[str]) -> Dict[str, int]:
        """批量统计多个知识库下的会话数量"""
        try:
            if not kb_ids:
                return {}

            with self.db_manager.get_session() as db_session:
                from sqlalchemy import func

                # 使用GROUP BY批量统计
                results = (
                    db_session.query(
                        KbSessionModel.kb_id,
                        func.count(KbSessionModel.id).label("count"),
                    )
                    .filter(
                        and_(
                            KbSessionModel.kb_id.in_(kb_ids),
                            KbSessionModel.is_deleted == 0,  # 0 表示使用中
                        )
                    )
                    .group_by(KbSessionModel.kb_id)
                    .all()
                )

                # 转换为字典格式
                count_dict = {kb_id: 0 for kb_id in kb_ids}  # 初始化所有kb_id为0
                for kb_id, count in results:
                    count_dict[kb_id] = count

                return count_dict
        except Exception as e:
            logger.error(f"[KbSessionDB] 批量统计知识库会话数量失败: {e}")
            raise


# 全局实例
kb_sessions_repository = KbSessionRepository()
