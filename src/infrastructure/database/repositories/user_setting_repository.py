"""
UserSetting数据库服务层
提供UserSetting的CRUD操作
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session as SQLSession
from sqlalchemy.exc import IntegrityError
from loguru import logger

from ..models.user_setting_models import UserSettingModel
from ..connection import db_manager


class UserSettingDatabaseService:
    """用户设置数据库服务"""
    
    def __init__(self):
        self.db_manager = db_manager
    
    def get_user_setting(
        self, 
        ali_uid: int, 
        wy_id: str
    ) -> Optional[UserSettingModel]:
        """根据ali_uid和wy_id获取用户设置"""
        try:
            with self.db_manager.get_session() as db_session:
                setting_model = db_session.query(UserSettingModel).filter(
                    UserSettingModel.ali_uid == ali_uid,
                    UserSettingModel.wy_id == wy_id
                ).first()
                
                if setting_model:
                    logger.info(f"[UserSettingDB] 获取用户设置: ali_uid={ali_uid}, wy_id={wy_id}")
                    
                    # 创建一个新的UserSettingModel实例，避免SQLAlchemy Session绑定问题
                    detached_model = UserSettingModel(
                        id=setting_model.id,
                        ali_uid=setting_model.ali_uid,
                        wy_id=setting_model.wy_id,
                        desktop_id=setting_model.desktop_id,
                        model=setting_model.model,
                        gmt_create=setting_model.gmt_create,
                        gmt_modified=setting_model.gmt_modified
                    )
                    return detached_model
                else:
                    logger.info(f"[UserSettingDB] 用户设置不存在: ali_uid={ali_uid}, wy_id={wy_id}")
                    return None
                
        except Exception as e:
            logger.error(f"[UserSettingDB] 获取用户设置失败: {e}")
            raise
    
    def create_user_setting(
        self,
        ali_uid: int,
        wy_id: str,
        desktop_id: Optional[str] = None,
        model: Optional[str] = None
    ) -> UserSettingModel:
        """创建新的用户设置"""
        try:
            # 创建UserSetting模型
            setting_model = UserSettingModel(
                ali_uid=ali_uid,
                wy_id=wy_id,
                desktop_id=desktop_id,
                model=model
            )
            
            # 保存到数据库
            with self.db_manager.get_session() as db_session:
                db_session.add(setting_model)
                db_session.flush()  # 获取自增ID
                
                logger.info(f"[UserSettingDB] 创建用户设置: ali_uid={ali_uid}, wy_id={wy_id} (ID: {setting_model.id})")
                
                # 创建一个新的UserSettingModel实例，避免SQLAlchemy Session绑定问题
                detached_model = UserSettingModel(
                    id=setting_model.id,
                    ali_uid=setting_model.ali_uid,
                    wy_id=setting_model.wy_id,
                    desktop_id=setting_model.desktop_id,
                    model=setting_model.model,
                    gmt_create=setting_model.gmt_create,
                    gmt_modified=setting_model.gmt_modified
                )
                return detached_model
                
        except IntegrityError as e:
            logger.error(f"[UserSettingDB] 用户设置已存在: ali_uid={ali_uid}, wy_id={wy_id}")
            raise ValueError(f"用户设置已存在: ali_uid={ali_uid}, wy_id={wy_id}")
        except Exception as e:
            logger.error(f"[UserSettingDB] 创建用户设置失败: {e}")
            raise
    
    def update_user_setting(
        self,
        ali_uid: int,
        wy_id: str,
        desktop_id: Optional[str] = None,
        model: Optional[str] = None
    ) -> bool:
        """更新用户设置"""
        try:
            with self.db_manager.get_session() as db_session:
                setting_model = db_session.query(UserSettingModel).filter(
                    UserSettingModel.ali_uid == ali_uid,
                    UserSettingModel.wy_id == wy_id
                ).first()
                
                if not setting_model:
                    logger.warning(f"[UserSettingDB] 用户设置不存在: ali_uid={ali_uid}, wy_id={wy_id}")
                    return False
                
                # 更新字段
                old_desktop_id = setting_model.desktop_id
                old_model = setting_model.model
                
                if desktop_id is not None:
                    setting_model.desktop_id = desktop_id
                if model is not None:
                    setting_model.model = model
                
                setting_model.gmt_modified = datetime.now()
                
                logger.info(f"[UserSettingDB] 更新用户设置: ali_uid={ali_uid}, wy_id={wy_id}, "
                          f"desktop_id: {old_desktop_id} -> {desktop_id}, model: {old_model} -> {model}")
                return True
                
        except Exception as e:
            logger.error(f"[UserSettingDB] 更新用户设置失败: {e}")
            raise
    
    def create_or_update_user_setting(
        self,
        ali_uid: int,
        wy_id: str,
        desktop_id: Optional[str] = None,
        model: Optional[str] = None
    ) -> UserSettingModel:
        """创建或更新用户设置（如果不存在则创建，存在则更新）"""
        try:
            # 先尝试获取现有设置
            existing_setting = self.get_user_setting(ali_uid, wy_id)
            
            if existing_setting:
                # 更新现有设置
                success = self.update_user_setting(ali_uid, wy_id, desktop_id, model)
                if success:
                    # 返回更新后的设置
                    return self.get_user_setting(ali_uid, wy_id)
                else:
                    raise ValueError("更新用户设置失败")
            else:
                # 创建新设置
                return self.create_user_setting(ali_uid, wy_id, desktop_id, model)
                
        except Exception as e:
            logger.error(f"[UserSettingDB] 创建或更新用户设置失败: {e}")
            raise
    
    def delete_user_setting(
        self, 
        ali_uid: int, 
        wy_id: str
    ) -> bool:
        """删除用户设置"""
        try:
            with self.db_manager.get_session() as db_session:
                setting_model = db_session.query(UserSettingModel).filter(
                    UserSettingModel.ali_uid == ali_uid,
                    UserSettingModel.wy_id == wy_id
                ).first()
                
                if not setting_model:
                    logger.warning(f"[UserSettingDB] 用户设置不存在: ali_uid={ali_uid}, wy_id={wy_id}")
                    return False
                
                db_session.delete(setting_model)
                logger.info(f"[UserSettingDB] 删除用户设置: ali_uid={ali_uid}, wy_id={wy_id}")
                return True
                
        except Exception as e:
            logger.error(f"[UserSettingDB] 删除用户设置失败: {e}")
            raise


# 全局UserSetting数据库服务实例
user_setting_db_service = UserSettingDatabaseService()