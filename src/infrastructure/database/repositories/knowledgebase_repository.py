"""
知识库数据库服务层
提供知识库的CRUD操作
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session as SQLSession
from sqlalchemy.exc import IntegrityError
from sqlalchemy import UnaryExpression, and_, or_
from loguru import logger

from ..models.knowledgebase_models import KnowledgeBaseModel
from ..connection import db_manager


class KnowledgeBaseRepository:
    """知识库数据库仓库"""

    def __init__(self):
        self.db_manager = db_manager

    def create_knowledge_base(
        self,
        kb_id: str,
        name: str,
        owner_ali_uid: int,
        owner_wy_id: str,
        description: Optional[str] = None,
    ) -> KnowledgeBaseModel:
        """创建知识库，返回 dict"""
        try:
            assert kb_id is not None, "kb_id is required"
            assert name is not None, "name is required"
            assert owner_ali_uid is not None, "owner_ali_uid is required"
            assert owner_wy_id is not None, "owner_wy_id is required"

            knowledge_base = KnowledgeBaseModel(
                kb_id=kb_id,
                name=name,
                description=description,
                owner_ali_uid=owner_ali_uid,
                owner_wy_id=owner_wy_id,
                is_deleted=0,
            )
            with self.db_manager.get_session() as db_session:
                db_session.add(knowledge_base)
                db_session.flush()  # 获取自增ID
                logger.info(
                    f"[KnowledgeBaseDB] 创建知识库: {kb_id} (ID: {knowledge_base.id})"
                )
                # 只有当session不为None时才进行refresh和expunge操作
                if knowledge_base is not None:
                    db_session.refresh(knowledge_base)
                    db_session.expunge(knowledge_base)
                return knowledge_base
        except IntegrityError as e:
            logger.error(f"[KnowledgeBaseDB] 知识库已存在: {kb_id}")
            raise ValueError(f"知识库ID已存在: {kb_id}")
        except Exception as e:
            logger.error(f"[KnowledgeBaseDB] 创建知识库失败: {e}")
            raise

    def get_knowledge_base_by_id(self, kb_id: str) -> Optional[KnowledgeBaseModel]:
        """根据知识库ID获取知识库"""
        try:
            with self.db_manager.get_session() as db_session:
                knowledge_base = (
                    db_session.query(KnowledgeBaseModel)
                    .filter(
                        and_(
                            KnowledgeBaseModel.kb_id == kb_id,
                            KnowledgeBaseModel.is_deleted == 0,
                        )
                    )
                    .first()
                )

                if knowledge_base is None:
                    return None

                # 确保所有延迟加载的属性都被加载
                db_session.refresh(knowledge_base)

                # 从 session 中分离对象，避免 session 结束后对象游离
                db_session.expunge(knowledge_base)

                return knowledge_base

        except Exception as e:
            logger.error(f"[KnowledgeBaseDB] 获取知识库失败: {e}")
            raise

    def list_knowledge_bases(
        self,
        owner_ali_uid: int,
        owner_wy_id: str,
        name: Optional[str] = None,
        kb_ids: Optional[List[str]] = None,
        less_than_equal_id: Optional[int] = None,
        less_than_equal_gmt_create: Optional[datetime] = None,
        limit: Optional[int] = None,
        order_pairs: Optional[list[UnaryExpression]] = None,
        is_all: Optional[bool] = False, # 是否查询所有知识库，包括已删除的
    ) -> List[KnowledgeBaseModel]:
        """分页搜索知识库"""
        try:
            assert owner_ali_uid is not None, "owner_ali_uid is required"
            assert owner_wy_id is not None, "owner_wy_id is required"

            with self.db_manager.get_session() as db_session:
                # 构建基础查询条件
                base_conditions = [
                    KnowledgeBaseModel.owner_ali_uid == owner_ali_uid,
                    KnowledgeBaseModel.owner_wy_id == owner_wy_id,
                ]
                
                # 如果 is_all 为 False，添加 is_deleted == 0 条件
                if not is_all:
                    base_conditions.append(KnowledgeBaseModel.is_deleted == 0)
                
                query = db_session.query(KnowledgeBaseModel).filter(and_(*base_conditions))

                if name:
                    query = query.filter(KnowledgeBaseModel.name == name)
                if kb_ids:
                    query = query.filter(KnowledgeBaseModel.kb_id.in_(kb_ids))
                if less_than_equal_gmt_create:
                    query = query.filter(
                        KnowledgeBaseModel.gmt_created <= less_than_equal_gmt_create
                    )
                if less_than_equal_id:
                    query = query.filter(KnowledgeBaseModel.id <= less_than_equal_id)
                if order_pairs:
                    for order_pair in order_pairs:
                        query = query.order_by(order_pair)
                if limit is not None:
                    query = query.limit(limit)

                # 获取查询结果
                results = query.all()

                # 确保所有延迟加载的属性都被加载
                for obj in results:
                    db_session.refresh(obj)

                # 从 session 中分离所有对象，避免 session 结束后对象游离
                detached_results = []
                for obj in results:
                    db_session.expunge(obj)
                    detached_results.append(obj)

                return detached_results

        except Exception as e:
            logger.error(f"[KnowledgeBaseDB] 分页搜索知识库失败: {e}")
            raise

    def update_knowledge_base(
        self,
        kb_id: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        is_update_selective: bool = True,
    ) -> None:
        """更新知识库"""
        try:
            with self.db_manager.get_session() as db_session:
                knowledge_base = (
                    db_session.query(KnowledgeBaseModel)
                    .filter(
                        and_(
                            KnowledgeBaseModel.kb_id == kb_id,
                            KnowledgeBaseModel.is_deleted == 0,
                        )
                    )
                    .first()
                )

                if not knowledge_base:
                    logger.warning(f"[KnowledgeBaseDB] 知识库不存在: {kb_id}")
                    return None

                # 更新字段
                if name is not None or not is_update_selective:
                    knowledge_base.name = name
                if description is not None or not is_update_selective:
                    knowledge_base.description = description

                knowledge_base.gmt_modified = datetime.now()

                with self.db_manager.get_session() as db_session:
                    db_session.commit()

                logger.info(f"[KnowledgeBaseDB] 更新知识库: {kb_id}")

        except IntegrityError as e:
            logger.error(f"[KnowledgeBaseDB] 更新知识库失败，名称冲突: {e}")
            raise ValueError("知识库名称已存在")
        except Exception as e:
            logger.error(f"[KnowledgeBaseDB] 更新知识库失败: {e}")
            raise

    def soft_delete_knowledge_base(self, kb_id: str) -> bool:
        """软删除知识库"""
        try:
            with self.db_manager.get_session() as db_session:
                knowledge_base = (
                    db_session.query(KnowledgeBaseModel)
                    .filter(
                        and_(
                            KnowledgeBaseModel.kb_id == kb_id,
                            KnowledgeBaseModel.is_deleted == 0,
                        )
                    )
                    .first()
                )

                if not knowledge_base:
                    logger.warning(f"[KnowledgeBaseDB] 知识库不存在: {kb_id}")
                    return False

                knowledge_base.is_deleted = knowledge_base.id
                knowledge_base.gmt_modified = datetime.now()

                with self.db_manager.get_session() as db_session:
                    db_session.commit()

                logger.info(f"[KnowledgeBaseDB] 软删除知识库: {kb_id}")
                return True

        except Exception as e:
            logger.error(f"[KnowledgeBaseDB] 软删除知识库失败: {e}")
            raise

    def count_knowledge_bases_by_owner(
        self, owner_ali_uid: int, owner_wy_id: str
    ) -> int:
        """统计用户的知识库数量"""
        try:
            assert owner_ali_uid is not None, "owner_ali_uid is required"
            assert owner_wy_id is not None, "owner_wy_id is required"
            with self.db_manager.get_session() as db_session:
                count = (
                    db_session.query(KnowledgeBaseModel)
                    .filter(
                        and_(
                            KnowledgeBaseModel.owner_ali_uid == owner_ali_uid,
                            KnowledgeBaseModel.owner_wy_id == owner_wy_id,
                            KnowledgeBaseModel.is_deleted == 0,
                        )
                    )
                    .count()
                )

                return count

        except Exception as e:
            logger.error(f"[KnowledgeBaseDB] 统计知识库数量失败: {e}")
            raise


# 全局知识库数据库服务实例
knowledgebase_repository = KnowledgeBaseRepository()
