"""
知识库操作日志数据库服务层
提供操作日志的插入和查询操作
"""

import datetime
from typing import Optional, List

from loguru import logger
from sqlalchemy import UnaryExpression, desc

from domain.models.enums import KbState
from ..connection import db_manager
from ..models.knowledgebase_models import KbOperationLogModel


class KbOperationLogsRepository:
    """知识库操作日志数据库仓库"""

    def __init__(self):
        self.db_manager = db_manager

    def insert_log(
        self,
        kb_id: str,
        wy_id: str,
        ali_uid: int,
        operation_type: str,
        target_type: str,
        target_id: str,
        status: str = KbState.PROCESSING.value,
    ) -> dict:
        """插入一条操作日志，返回 dict"""
        try:
            log = KbOperationLogModel(
                kb_id=kb_id,
                wy_id=wy_id,
                ali_uid=ali_uid,
                operation_type=operation_type,
                target_type=target_type,
                target_id=target_id,
                status=status,
            )
            with self.db_manager.get_session() as db_session:
                db_session.add(log)
                db_session.flush()
                logger.info(f"[KbOperationLogsDB] 插入操作日志: {log.id}")
                return log.to_dict()
        except Exception as e:
            logger.error(f"[KbOperationLogsDB] 插入操作日志失败: {e}")
            raise

    def batch_insert_logs(
        self,
        logs_data: List[dict],
    ) -> List[dict]:
        """批量插入操作日志，返回 dict 列表"""
        try:
            logs = []
            for log_data in logs_data:
                log = KbOperationLogModel(
                    kb_id=log_data["kb_id"],
                    wy_id=log_data["wy_id"],
                    ali_uid=log_data["ali_uid"],
                    operation_type=log_data["operation_type"],
                    target_type=log_data["target_type"],
                    target_id=log_data["target_id"],
                    status=log_data.get("status", KbState.PROCESSING.value),
                )
                logs.append(log)

            with self.db_manager.get_session() as db_session:
                db_session.add_all(logs)
                db_session.flush()
                
                # 获取插入的日志ID
                log_ids = [log.id for log in logs]
                logger.info(f"[KbOperationLogsDB] 批量插入操作日志成功: {log_ids}")
                
                # 返回日志字典列表
                return [log.to_dict() for log in logs]
        except Exception as e:
            logger.error(f"[KbOperationLogsDB] 批量插入操作日志失败: {e}")
            raise

    def list_logs(
        self,
        kb_id: Optional[str] = None,
        wy_id: Optional[str] = None,
        ali_uid: Optional[int] = None,
        target_id: Optional[str] = None,
        operation_type: Optional[str] = None,
        target_type: Optional[str] = None,
        less_than_equal_id: Optional[int] = None,
        less_than_equal_gmt_create: Optional[datetime] = None,
        limit: Optional[int] = None,
        order_pairs: Optional[list[UnaryExpression]] = None,
    ) -> List[KbOperationLogModel]:
        """按条件分页查询操作日志，返回 dict 列表"""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(KbOperationLogModel)
                if kb_id:
                    query = query.filter(KbOperationLogModel.kb_id == kb_id)
                if wy_id:
                    query = query.filter(KbOperationLogModel.wy_id == wy_id)
                if ali_uid:
                    query = query.filter(KbOperationLogModel.ali_uid == ali_uid)
                if target_id:
                    query = query.filter(KbOperationLogModel.target_id == target_id)
                if operation_type:
                    query = query.filter(
                        KbOperationLogModel.operation_type == operation_type
                    )
                if target_type:
                    query = query.filter(KbOperationLogModel.target_type == target_type)
                if less_than_equal_id:
                    query = query.filter(KbOperationLogModel.id <= less_than_equal_id)
                if less_than_equal_gmt_create:
                    query = query.filter(
                        KbOperationLogModel.gmt_created <= less_than_equal_gmt_create
                    )
                if order_pairs:
                    for order_pair in order_pairs:
                        query = query.order_by(order_pair)
                else:
                    query = query.order_by(desc(KbOperationLogModel.gmt_created))
                if limit:
                    query = query.limit(limit)
                results = query.all()

                # 确保所有延迟加载的属性都被加载
                for obj in results:
                    db_session.refresh(obj)

                # 从 session 中分离所有对象，避免 session 结束后对象游离
                detached_results = []
                for obj in results:
                    db_session.expunge(obj)
                    detached_results.append(obj)
                return detached_results

        except Exception as e:
            logger.error(f"[KbOperationLogsDB] 查询操作日志失败: {e}")
            raise

    def count_logs(
        self,
        kb_id: Optional[str] = None,
        wy_id: Optional[str] = None,
        ali_uid: Optional[int] = None,
        target_id: Optional[str] = None,
        operation_type: Optional[str] = None,
        target_type: Optional[str] = None,
    ) -> int:
        """统计操作日志数量"""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(KbOperationLogModel)
                if kb_id:
                    query = query.filter(KbOperationLogModel.kb_id == kb_id)
                if wy_id:
                    query = query.filter(KbOperationLogModel.wy_id == wy_id)
                if ali_uid:
                    query = query.filter(KbOperationLogModel.ali_uid == ali_uid)
                if target_id:
                    query = query.filter(KbOperationLogModel.target_id == target_id)
                if operation_type:
                    query = query.filter(
                        KbOperationLogModel.operation_type == operation_type
                    )
                if target_type:
                    query = query.filter(KbOperationLogModel.target_type == target_type)
                return query.count()
        except Exception as e:
            logger.error(f"[KbOperationLogsDB] 统计操作日志数量失败: {e}")
            raise


# 全局操作日志数据库服务实例
kb_operation_logs_repository = KbOperationLogsRepository()
