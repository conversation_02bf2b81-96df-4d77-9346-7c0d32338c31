#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鉴权数据仓库
"""
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import sessionmaker, joinedload
from sqlalchemy import and_, or_
from loguru import logger
from datetime import datetime

from ..connection import db_manager
from ..models.auth_models import (
    Team, TeamMember, Resource, Role, Permission,
    RolePermission, UserRole, ResourcePermission, Share, ShareAccessLog
)


class AuthRepository:
    """鉴权数据仓库"""
    
    def __init__(self):
        self.session_factory = sessionmaker()
    
    # ==================== 用户标识管理 ====================
    # 注意：不再维护用户表，直接使用 ali_uid + wy_id 作为用户标识
    
    # ==================== 资源管理 ====================
    
    def create_resource(
        self,
        resource_type: str,
        resource_id: str,
        resource_name: str,
        owner_ali_uid: Optional[int] = None,
        owner_wy_id: Optional[str] = None,
        team_owner_id: Optional[int] = None,
        is_public: bool = False
    ) -> Resource:
        """创建资源记录"""
        try:
            session = db_manager.get_session_sync()
            try:
                resource = Resource(
                    resource_type=resource_type,
                    resource_id=resource_id,
                    resource_name=resource_name,
                    owner_ali_uid=owner_ali_uid,
                    owner_wy_id=owner_wy_id,
                    team_owner_id=team_owner_id,
                    is_public=is_public
                )

                session.add(resource)
                session.commit()
                session.refresh(resource)

                logger.info(f"[AuthRepo] 创建资源成功: id={resource.id}, type={resource_type}, resource_id={resource_id}")
                return resource
            finally:
                session.close()

        except Exception as e:
            logger.error(f"[AuthRepo] 创建资源失败: type={resource_type}, resource_id={resource_id}, error={e}")
            raise
    
    def get_resource(self, resource_type: str, resource_id: str) -> Optional[Resource]:
        """获取资源"""
        try:
            session = db_manager.get_session_sync()
            try:
                resource = session.query(Resource).filter(
                    and_(
                        Resource.resource_type == resource_type,
                        Resource.resource_id == resource_id,
                        Resource.is_deleted == False
                    )
                ).first()
                return resource
            finally:
                session.close()
        except Exception as e:
            logger.error(f"[AuthRepo] 获取资源失败: type={resource_type}, resource_id={resource_id}, error={e}")
            return None

    def get_resource_include_deleted(self, resource_type: str, resource_id: str) -> Optional[Resource]:
        """获取资源（包括已删除的资源）"""
        try:
            session = db_manager.get_session_sync()
            try:
                resource = session.query(Resource).filter(
                    and_(
                        Resource.resource_type == resource_type,
                        Resource.resource_id == resource_id
                    )
                ).first()
                return resource
            finally:
                session.close()
        except Exception as e:
            logger.error(f"[AuthRepo] 获取资源失败（包括已删除）: type={resource_type}, resource_id={resource_id}, error={e}")
            return None

    def delete_resource(self, resource_type: str, resource_id: str) -> bool:
        """删除资源（软删除）"""
        try:
            session = db_manager.get_session_sync()
            try:
                resource = session.query(Resource).filter(
                    and_(
                        Resource.resource_type == resource_type,
                        Resource.resource_id == resource_id,
                        Resource.is_deleted == False
                    )
                ).first()

                if not resource:
                    logger.warning(f"[AuthRepo] 资源不存在或已删除: type={resource_type}, resource_id={resource_id}")
                    return False

                # 软删除资源
                resource.is_deleted = True
                resource.gmt_modified = datetime.utcnow()

                session.commit()

                logger.info(f"[AuthRepo] 资源删除成功: id={resource.id}, type={resource_type}, resource_id={resource_id}")
                return True

            finally:
                session.close()

        except Exception as e:
            logger.error(f"[AuthRepo] 删除资源失败: type={resource_type}, resource_id={resource_id}, error={e}")
            return False

    def restore_resource(
        self,
        resource_type: str,
        resource_id: str,
        resource_name: str,
        owner_ali_uid: Optional[int] = None,
        owner_wy_id: Optional[str] = None,
        team_owner_id: Optional[int] = None,
        is_public: bool = False
    ) -> bool:
        """恢复已删除的资源"""
        try:
            session = db_manager.get_session_sync()
            try:
                resource = session.query(Resource).filter(
                    and_(
                        Resource.resource_type == resource_type,
                        Resource.resource_id == resource_id,
                        Resource.is_deleted == True
                    )
                ).first()

                if not resource:
                    logger.warning(f"[AuthRepo] 未找到已删除的资源: type={resource_type}, resource_id={resource_id}")
                    return False

                # 恢复资源并更新信息
                resource.is_deleted = False
                resource.resource_name = resource_name
                resource.owner_ali_uid = owner_ali_uid
                resource.owner_wy_id = owner_wy_id
                resource.team_owner_id = team_owner_id
                resource.is_public = is_public
                resource.gmt_modified = datetime.utcnow()

                session.commit()

                logger.info(f"[AuthRepo] 资源恢复成功: id={resource.id}, type={resource_type}, resource_id={resource_id}")
                return True

            finally:
                session.close()

        except Exception as e:
            logger.error(f"[AuthRepo] 恢复资源失败: type={resource_type}, resource_id={resource_id}, error={e}")
            return False

    def cleanup_resource_permissions(self, resource_id: int) -> bool:
        """清理资源相关的权限记录（软删除）"""
        try:
            session = db_manager.get_session_sync()
            try:
                # 软删除资源相关的权限记录
                updated_count = session.query(ResourcePermission).filter(
                    and_(
                        ResourcePermission.resource_id == resource_id,
                        ResourcePermission.is_deleted == False
                    )
                ).update({
                    'is_deleted': True,
                    'gmt_modified': datetime.utcnow()
                })

                session.commit()

                logger.info(f"[AuthRepo] 清理资源权限成功: resource_id={resource_id}, updated_count={updated_count}")
                return True

            finally:
                session.close()

        except Exception as e:
            logger.error(f"[AuthRepo] 清理资源权限失败: resource_id={resource_id}, error={e}")
            return False

    def cleanup_resource_shares(self, resource_id: int) -> bool:
        """清理资源相关的分享记录（软删除）"""
        try:
            session = db_manager.get_session_sync()
            try:
                # 将资源相关的分享记录设为非活跃状态（软删除）
                updated_count = session.query(Share).filter(
                    and_(
                        Share.resource_id == resource_id,
                        Share.is_active == True
                    )
                ).update({
                    'is_active': False,
                    'gmt_modified': datetime.utcnow()
                })

                session.commit()

                logger.info(f"[AuthRepo] 清理资源分享成功: resource_id={resource_id}, updated_count={updated_count}")
                return True

            finally:
                session.close()

        except Exception as e:
            logger.error(f"[AuthRepo] 清理资源分享失败: resource_id={resource_id}, error={e}")
            return False

    def restore_resource_permissions(self, resource_id: int) -> bool:
        """恢复资源相关的权限记录（可选操作）"""
        try:
            session = db_manager.get_session_sync()
            try:
                # 恢复资源相关的权限记录
                updated_count = session.query(ResourcePermission).filter(
                    and_(
                        ResourcePermission.resource_id == resource_id,
                        ResourcePermission.is_deleted == True
                    )
                ).update({
                    'is_deleted': False,
                    'gmt_modified': datetime.utcnow()
                })

                session.commit()

                logger.info(f"[AuthRepo] 恢复资源权限成功: resource_id={resource_id}, updated_count={updated_count}")
                return True

            finally:
                session.close()

        except Exception as e:
            logger.error(f"[AuthRepo] 恢复资源权限失败: resource_id={resource_id}, error={e}")
            return False

    def restore_resource_shares(self, resource_id: int) -> bool:
        """恢复资源相关的分享记录（可选操作）"""
        try:
            session = db_manager.get_session_sync()
            try:
                # 恢复资源相关的分享记录（只恢复未过期的）
                updated_count = session.query(Share).filter(
                    and_(
                        Share.resource_id == resource_id,
                        Share.is_active == False,
                        or_(
                            Share.expires_at.is_(None),
                            Share.expires_at > datetime.utcnow()
                        )
                    )
                ).update({
                    'is_active': True,
                    'gmt_modified': datetime.utcnow()
                })

                session.commit()

                logger.info(f"[AuthRepo] 恢复资源分享成功: resource_id={resource_id}, updated_count={updated_count}")
                return True

            finally:
                session.close()

        except Exception as e:
            logger.error(f"[AuthRepo] 恢复资源分享失败: resource_id={resource_id}, error={e}")
            return False
    
    def get_user_resources(
        self,
        ali_uid: int,
        wy_id: str,
        resource_type: Optional[str] = None,
        include_team_resources: bool = True
    ) -> List[Resource]:
        """获取用户可访问的资源列表"""
        try:
            session = db_manager.get_session_sync()
            try:
                query = session.query(Resource).filter(Resource.is_deleted == False)
                
                # 构建条件：个人拥有的资源 OR 公开资源 OR 有权限的资源
                conditions = [
                    and_(Resource.owner_ali_uid == ali_uid, Resource.owner_wy_id == wy_id),
                    Resource.is_public == True
                ]

                # 如果包含团队资源
                if include_team_resources:
                    # 获取用户所在的团队
                    user_teams = session.query(TeamMember.team_id).filter(
                        and_(
                            TeamMember.member_ali_uid == ali_uid,
                            TeamMember.member_wy_id == wy_id,
                            TeamMember.is_active == True
                        )
                    ).subquery()

                    conditions.append(Resource.team_owner_id.in_(user_teams))
                
                query = query.filter(or_(*conditions))
                
                # 资源类型过滤
                if resource_type:
                    query = query.filter(Resource.resource_type == resource_type)
                
                resources = query.all()
                return resources
            finally:
                session.close()

        except Exception as e:
            logger.error(f"[AuthRepo] 获取用户资源失败: ali_uid={ali_uid}, wy_id={wy_id}, error={e}")
            return []
    
    # ==================== 权限管理 ====================
    
    def get_user_permissions(self, ali_uid: int, wy_id: str, resource_id: int) -> List[str]:
        """获取用户对特定资源的权限列表"""
        try:
            session = db_manager.get_session_sync()
            try:
                # 1. 通过角色获得的权限（暂时跳过，因为当前系统主要使用资源级权限）
                role_permissions = []
                
                # 2. 直接授予的资源权限
                resource_permissions = session.query(Permission.name).join(
                    ResourcePermission, Permission.id == ResourcePermission.permission_id
                ).filter(
                    and_(
                        ResourcePermission.resource_id == resource_id,
                        ResourcePermission.user_ali_uid == ali_uid,
                        ResourcePermission.user_wy_id == wy_id,
                        ResourcePermission.is_deleted == False,
                        or_(
                            ResourcePermission.expires_at.is_(None),
                            ResourcePermission.expires_at > datetime.utcnow()
                        )
                    )
                ).all()
                
                # 3. 通过团队获得的权限
                team_permissions = session.query(Permission.name).join(
                    ResourcePermission, Permission.id == ResourcePermission.permission_id
                ).join(
                    TeamMember, ResourcePermission.team_id == TeamMember.team_id
                ).filter(
                    and_(
                        ResourcePermission.resource_id == resource_id,
                        ResourcePermission.is_deleted == False,
                        TeamMember.member_ali_uid == ali_uid,
                        TeamMember.member_wy_id == wy_id,
                        TeamMember.is_active == True,
                        or_(
                            ResourcePermission.expires_at.is_(None),
                            ResourcePermission.expires_at > datetime.utcnow()
                        )
                    )
                ).all()
                
                # 合并所有权限
                all_permissions = set()
                for perm_tuple in role_permissions + resource_permissions + team_permissions:
                    all_permissions.add(perm_tuple[0])
                
                return list(all_permissions)
            finally:
                session.close()

        except Exception as e:
            logger.error(f"[AuthRepo] 获取用户权限失败: user_id={user_id}, resource_id={resource_id}, error={e}")
            return []
    
    def check_resource_ownership(self, ali_uid: int, wy_id: str, resource_id: int) -> bool:
        """检查用户是否拥有资源"""
        try:
            session = db_manager.get_session_sync()
            try:
                # 检查个人所有权
                personal_owned = session.query(Resource).filter(
                    and_(
                        Resource.id == resource_id,
                        Resource.owner_ali_uid == ali_uid,
                        Resource.owner_wy_id == wy_id,
                        Resource.is_deleted == False
                    )
                ).first()

                if personal_owned:
                    return True

                # 检查团队所有权
                team_owned = session.query(Resource).join(
                    TeamMember, Resource.team_owner_id == TeamMember.team_id
                ).filter(
                    and_(
                        Resource.id == resource_id,
                        TeamMember.member_ali_uid == ali_uid,
                        TeamMember.member_wy_id == wy_id,
                        TeamMember.is_active == True,
                        TeamMember.role.in_(['owner', 'admin']),  # 只有团队所有者和管理员算作拥有者
                        Resource.is_deleted == False
                    )
                ).first()

                return team_owned is not None
            finally:
                session.close()

        except Exception as e:
            logger.error(f"[AuthRepo] 检查资源所有权失败: ali_uid={ali_uid}, wy_id={wy_id}, resource_id={resource_id}, error={e}")
            return False
    
    # ==================== 团队管理 ====================
    
    def create_team(self, name: str, creator_ali_uid: int, creator_wy_id: str, description: Optional[str] = None) -> Team:
        """创建团队"""
        try:
            session = db_manager.get_session_sync()
            try:
                team = Team(
                    name=name,
                    creator_ali_uid=creator_ali_uid,
                    creator_wy_id=creator_wy_id,
                    description=description
                )

                session.add(team)
                session.flush()  # 获取team.id

                # 添加创建者为团队所有者
                team_member = TeamMember(
                    team_id=team.id,
                    member_ali_uid=creator_ali_uid,
                    member_wy_id=creator_wy_id,
                    role='owner'
                )
                session.add(team_member)

                session.commit()
                session.refresh(team)

                logger.info(f"[AuthRepo] 创建团队成功: id={team.id}, name={name}, creator_ali_uid={creator_ali_uid}, creator_wy_id={creator_wy_id}")
                return team
            finally:
                session.close()

        except Exception as e:
            logger.error(f"[AuthRepo] 创建团队失败: name={name}, creator_id={creator_id}, error={e}")
            raise
    
    def get_user_teams(self, ali_uid: int, wy_id: str) -> List[Team]:
        """获取用户所在的团队"""
        try:
            session = db_manager.get_session_sync()
            try:
                teams = session.query(Team).join(
                    TeamMember, Team.id == TeamMember.team_id
                ).filter(
                    and_(
                        TeamMember.member_ali_uid == ali_uid,
                        TeamMember.member_wy_id == wy_id,
                        TeamMember.is_active == True,
                        Team.is_active == True,
                        Team.is_deleted == False
                    )
                ).all()

                return teams
            finally:
                session.close()

        except Exception as e:
            logger.error(f"[AuthRepo] 获取用户团队失败: ali_uid={ali_uid}, wy_id={wy_id}, error={e}")
            return []
    
    # ==================== 分享管理 ====================
    
    def create_share(
        self,
        resource_id: int,
        creator_ali_uid: int,
        creator_wy_id: str,
        share_code: str,
        share_type: str,
        password: Optional[str] = None,
        allowed_permissions: Optional[List[str]] = None,
        max_access_count: Optional[int] = None,
        expires_at: Optional[datetime] = None
    ) -> Share:
        """创建分享"""
        try:
            session = db_manager.get_session_sync()
            try:
                share = Share(
                    resource_id=resource_id,
                    creator_ali_uid=creator_ali_uid,
                    creator_wy_id=creator_wy_id,
                    share_code=share_code,
                    share_type=share_type,
                    password=password,
                    allowed_permissions=','.join(allowed_permissions) if allowed_permissions else None,
                    max_access_count=max_access_count,
                    expires_at=expires_at
                )
                
                session.add(share)
                session.commit()
                session.refresh(share)
                
                logger.info(f"[AuthRepo] 创建分享成功: id={share.id}, code={share_code}")
                return share
            finally:
                session.close()

        except Exception as e:
            logger.error(f"[AuthRepo] 创建分享失败: resource_id={resource_id}, error={e}")
            raise
    
    def get_share_by_code(self, share_code: str) -> Optional[Share]:
        """根据分享码获取分享"""
        try:
            session = db_manager.get_session_sync()
            try:
                share = session.query(Share).options(
                    joinedload(Share.resource),
                    joinedload(Share.creator)
                ).filter(
                    and_(
                        Share.share_code == share_code,
                        Share.is_active == True,
                        or_(
                            Share.expires_at.is_(None),
                            Share.expires_at > datetime.utcnow()
                        )
                    )
                ).first()
                
                return share
            finally:
                session.close()

        except Exception as e:
            logger.error(f"[AuthRepo] 获取分享失败: share_code={share_code}, error={e}")
            return None


# 创建全局实例
auth_repository = AuthRepository()
