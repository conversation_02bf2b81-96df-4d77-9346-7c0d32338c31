"""
Saga 事务管理器
专门处理分布式事务的补偿操作
"""

import functools
from typing import Optional, Callable, Any, Dict, List
from contextlib import contextmanager
from loguru import logger

from .transaction import transactional, TransactionalError


class SagaTransactionManager:
    """Saga 事务管理器"""
    
    def __init__(self):
        self.compensation_actions: List[Dict[str, Any]] = []
    
    def add_compensation_action(self, action: Callable, *args, **kwargs):
        """添加补偿操作"""
        self.compensation_actions.append({
            'action': action,
            'args': args,
            'kwargs': kwargs
        })
    
    def execute_compensations(self):
        """执行所有补偿操作"""
        for compensation in reversed(self.compensation_actions):  # 反向执行
            try:
                action = compensation['action']
                args = compensation['args']
                kwargs = compensation['kwargs']
                
                logger.info(f"执行补偿操作: {action.__name__}")
                action(*args, **kwargs)
                logger.info(f"补偿操作成功: {action.__name__}")
                
            except Exception as e:
                logger.error(f"补偿操作失败: {action.__name__}, 错误: {e}")
                # 补偿操作失败，记录告警但继续执行其他补偿操作
    
    def clear_compensations(self):
        """清空补偿操作列表"""
        self.compensation_actions.clear()


def saga_transactional(
    rollback_on: Optional[type] = None,
    commit_on: Optional[type] = None,
    read_only: bool = False
):
    """
    Saga 事务装饰器
    结合本地事务和分布式补偿操作
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 创建 Saga 事务管理器
            saga_manager = SagaTransactionManager()
            
            # 将 saga_manager 添加到 kwargs 中，供函数内部使用
            kwargs['_saga_manager'] = saga_manager
            
            try:
                # 使用普通事务装饰器包装函数
                @transactional(rollback_on=rollback_on, commit_on=commit_on, read_only=read_only)
                def inner_func(*inner_args, **inner_kwargs):
                    return func(*inner_args, **inner_kwargs)
                
                # 执行函数
                result = inner_func(*args, **kwargs)
                
                # 如果成功，清空补偿操作
                saga_manager.clear_compensations()
                
                return result
                
            except Exception as e:
                # 执行补偿操作
                logger.error(f"Saga 事务失败，执行补偿操作: {func.__name__}")
                saga_manager.execute_compensations()
                
                # 重新抛出异常
                raise
                
        return wrapper
    return decorator


@contextmanager
def saga_transaction_context(
    rollback_on: Optional[type] = None,
    commit_on: Optional[type] = None,
    read_only: bool = False
):
    """
    Saga 事务上下文管理器
    """
    saga_manager = SagaTransactionManager()
    
    try:
        yield saga_manager
        
        # 如果成功，清空补偿操作
        saga_manager.clear_compensations()
        
    except Exception as e:
        # 执行补偿操作
        logger.error(f"Saga 事务上下文失败，执行补偿操作")
        saga_manager.execute_compensations()
        
        # 重新抛出异常
        raise


# 便捷函数
def add_compensation_action(action: Callable, *args, **kwargs):
    """
    添加补偿操作的便捷函数
    需要在 saga_transactional 装饰的函数内部使用
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*func_args, **func_kwargs):
            # 获取 saga_manager
            saga_manager = func_kwargs.get('_saga_manager')
            if saga_manager:
                saga_manager.add_compensation_action(action, *args, **kwargs)
            
            return func(*func_args, **func_kwargs)
        return wrapper
    return decorator 