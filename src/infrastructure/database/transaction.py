"""
数据库事务装饰器
提供类似Java @Transactional的功能
"""

import functools
from typing import Optional, Callable, Any
from contextlib import contextmanager
from loguru import logger

from .connection import db_manager


class TransactionalError(Exception):
    """事务异常"""
    pass


def transactional(
    rollback_on: Optional[type] = None,
    commit_on: Optional[type] = None,
    read_only: bool = False,
    saga_compensation: Optional[Callable] = None
):
    """
    事务装饰器，类似Java的@Transactional
    
    Args:
        rollback_on: 指定异常类型时回滚事务
        commit_on: 指定异常类型时提交事务
        read_only: 是否为只读事务
        saga_compensation: Saga模式的补偿操作函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取数据库会话
            session = db_manager.get_session_sync()
            
            try:
                # 执行被装饰的函数
                result = func(*args, **kwargs)
                
                # 如果不是只读事务，提交事务
                if not read_only:
                    session.commit()
                    logger.debug(f"事务提交成功: {func.__name__}")
                
                return result
                
            except Exception as e:
                # 回滚事务
                session.rollback()
                logger.error(f"事务回滚: {func.__name__}, 错误: {e}")
                
                # 执行 Saga 补偿操作
                if saga_compensation:
                    try:
                        logger.info(f"执行 Saga 补偿操作: {func.__name__}")
                        saga_compensation(*args, **kwargs)
                        logger.info(f"Saga 补偿操作成功: {func.__name__}")
                    except Exception as compensation_error:
                        logger.error(f"Saga 补偿操作失败: {func.__name__}, 错误: {compensation_error}")
                        # 补偿操作失败，记录告警但继续抛出原始异常
                
                # 根据配置决定是否重新抛出异常
                if rollback_on and isinstance(e, rollback_on):
                    raise TransactionalError(f"事务回滚异常: {e}") from e
                elif commit_on and isinstance(e, commit_on):
                    # 对于指定异常，尝试提交事务
                    try:
                        session.commit()
                        logger.info(f"指定异常提交事务: {func.__name__}")
                    except Exception as commit_error:
                        session.rollback()
                        logger.error(f"提交事务失败，回滚: {commit_error}")
                        raise TransactionalError(f"提交事务失败: {commit_error}") from commit_error
                else:
                    # 默认重新抛出原始异常
                    raise
                    
            finally:
                # 关闭会话
                session.close()
                
        return wrapper
    return decorator


@contextmanager
def transaction_context(
    rollback_on: Optional[type] = None,
    commit_on: Optional[type] = None,
    read_only: bool = False
):
    """
    事务上下文管理器
    
    Args:
        rollback_on: 指定异常类型时回滚事务
        commit_on: 指定异常类型时提交事务
        read_only: 是否为只读事务
    """
    session = db_manager.get_session_sync()
    
    try:
        yield session
        
        # 如果不是只读事务，提交事务
        if not read_only:
            session.commit()
            logger.debug("事务上下文提交成功")
            
    except Exception as e:
        # 回滚事务
        session.rollback()
        logger.error(f"事务上下文回滚，错误: {e}")
        
        # 根据配置决定是否重新抛出异常
        if rollback_on and isinstance(e, rollback_on):
            raise TransactionalError(f"事务回滚异常: {e}") from e
        elif commit_on and isinstance(e, commit_on):
            # 对于指定异常，尝试提交事务
            try:
                session.commit()
                logger.info("指定异常提交事务")
            except Exception as commit_error:
                session.rollback()
                logger.error(f"提交事务失败，回滚: {commit_error}")
                raise TransactionalError(f"提交事务失败: {commit_error}") from commit_error
        else:
            # 默认重新抛出原始异常
            raise
            
    finally:
        # 关闭会话
        session.close()


def require_transaction(func: Callable) -> Callable:
    """
    要求事务的装饰器
    确保函数在事务中执行
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 检查是否已经在事务中
        # 这里可以通过检查当前线程的session状态来判断
        # 简化实现，直接使用事务装饰器
        return transactional()(func)(*args, **kwargs)
    return wrapper 