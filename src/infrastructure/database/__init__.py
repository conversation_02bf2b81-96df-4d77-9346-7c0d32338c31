"""
数据库服务 - Database Services

包含数据库连接、数据模型和仓库服务
"""

# 数据库连接
from .connection import db_manager, get_db_session

# 导入仓库服务
from .repositories import (
    session_db_service,
    file_repository,
    auth_repository
)

# 导入数据模型
from .models import (
    SessionModel,
    AlphaFile, FileType, UploadStatus,
    Team, TeamMember, Resource, Role, Permission,
    RolePermission, UserRole, ResourcePermission, Share, ShareAccessLog
)

__all__ = [
    # 连接管理
    "db_manager",
    "get_db_session",

    # 仓库服务
    "session_db_service",
    "file_repository",
    "auth_repository",

    # 数据模型
    "SessionModel",
    "AlphaFile", "FileType", "UploadStatus",
    "Team", "TeamMember", "Resource", "Role", "Permission",
    "RolePermission", "UserRole", "ResourcePermission", "Share", "ShareAccessLog",
]