"""
SQLAlchemy数据库模型
定义Session、Round、Message三个核心表
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum as SQLEnum, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Session as SQLSession
from sqlalchemy.sql import func

from ...domain.models.enums import SessionStatus, MessageType

# 创建基础模型类
Base = declarative_base()


class SessionModel(Base):
    """Session表模型"""
    __tablename__ = 'sessions'
    
    # 主键和基础字段
    id = Column(Integer, primary_key=True, autoincrement=True, comment='自增主键')
    session_id = Column(String(64), unique=True, nullable=False, index=True, comment='会话ID')
    ali_uid = Column(String(64), nullable=False, index=True, comment='阿里云用户ID')
    agent_id = Column(String(64), nullable=False, index=True, comment='Agent ID')
    
    # 业务字段
    title = Column(String(255), default='', comment='会话标题')
    status = Column(SQLEnum(SessionStatus), default=SessionStatus.CREATE, nullable=False, index=True, comment='会话状态')
    
    # 时间字段
    gmt_create = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    gmt_modified = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='修改时间')
    
    # 扩展字段
    metadata = Column(JSON, default=dict, comment='元数据JSON')
    
    # 关系定义
    rounds = relationship("RoundModel", back_populates="session", cascade="all, delete-orphan", lazy="select")
    
    def __repr__(self):
        return f"<SessionModel(session_id='{self.session_id}', status='{self.status}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'session_id': self.session_id,
            'ali_uid': self.ali_uid,
            'agent_id': self.agent_id,
            'title': self.title,
            'status': self.status.name if self.status else None,
            'gmt_create': self.gmt_create.isoformat() if self.gmt_create else None,
            'gmt_modified': self.gmt_modified.isoformat() if self.gmt_modified else None,
            'metadata': self.metadata or {}
        }


class RoundModel(Base):
    """Round表模型"""
    __tablename__ = 'rounds'
    
    # 主键和外键
    id = Column(Integer, primary_key=True, autoincrement=True, comment='自增主键')
    round_id = Column(Integer, nullable=False, comment='轮次ID（在Session内递增）')
    session_id = Column(String(64), ForeignKey('sessions.session_id', ondelete='CASCADE'), 
                       nullable=False, index=True, comment='关联的会话ID')
    
    # 业务字段
    user_prompt = Column(Text, nullable=False, comment='用户输入的提示词')
    status = Column(SQLEnum(RoundStatus), default=RoundStatus.PROCESSING, nullable=False, index=True, comment='轮次状态')
    
    # 时间字段
    start_time = Column(DateTime, default=func.now(), nullable=False, comment='开始时间')
    end_time = Column(DateTime, nullable=True, comment='结束时间')
    
    # 扩展字段
    usage_info = Column(JSON, default=dict, comment='使用量信息JSON')
    metadata = Column(JSON, default=dict, comment='元数据JSON')
    
    # 关系定义
    session = relationship("SessionModel", back_populates="rounds")
    messages = relationship("MessageModel", back_populates="round", cascade="all, delete-orphan", lazy="select")
    
    # 复合索引
    __table_args__ = (
        {'comment': 'Round轮次表'},
    )
    
    def __repr__(self):
        return f"<RoundModel(session_id='{self.session_id}', round_id={self.round_id}, status='{self.status}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'round_id': self.round_id,
            'session_id': self.session_id,
            'user_prompt': self.user_prompt,
            'status': self.status.name if self.status else None,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'usage_info': self.usage_info or {},
            'metadata': self.metadata or {}
        }


class MessageModel(Base):
    """Message表模型"""
    __tablename__ = 'messages'
    
    # 主键和外键
    id = Column(Integer, primary_key=True, autoincrement=True, comment='自增主键')
    message_id = Column(String(64), unique=True, nullable=False, index=True, comment='消息ID')
    round_id = Column(Integer, nullable=False, comment='关联的轮次ID')
    session_id = Column(String(64), ForeignKey('sessions.session_id', ondelete='CASCADE'), 
                       nullable=False, index=True, comment='关联的会话ID')
    
    # 业务字段
    content = Column(Text, nullable=False, comment='消息内容')
    message_type = Column(SQLEnum(MessageType), nullable=False, index=True, comment='消息类型')
    
    # 时间字段
    timestamp = Column(DateTime, default=func.now(), nullable=False, comment='消息时间戳')
    
    # 扩展字段
    metadata = Column(JSON, default=dict, comment='元数据JSON')
    
    # 关系定义（通过session_id关联，因为round表可能被删除）
    session = relationship("SessionModel")
    round = relationship("RoundModel", back_populates="messages", 
                        primaryjoin="and_(MessageModel.session_id == RoundModel.session_id, "
                                   "MessageModel.round_id == RoundModel.round_id)")
    
    # 复合索引
    __table_args__ = (
        {'comment': 'Message消息表'},
    )
    
    def __repr__(self):
        return f"<MessageModel(message_id='{self.message_id}', type='{self.message_type}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'message_id': self.message_id,
            'round_id': self.round_id,
            'session_id': self.session_id,
            'content': self.content,
            'message_type': self.message_type.name if self.message_type else None,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'metadata': self.metadata or {}
        }


# 创建数据库表的SQL语句（用于参考）
CREATE_TABLES_SQL = """
-- Sessions表
CREATE TABLE IF NOT EXISTS sessions (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    session_id VARCHAR(64) NOT NULL UNIQUE COMMENT '会话ID',
    ali_uid VARCHAR(64) NOT NULL COMMENT '阿里云用户ID',
    agent_id VARCHAR(64) NOT NULL COMMENT 'Agent ID',
    title VARCHAR(255) DEFAULT '' COMMENT '会话标题',
    status ENUM('CREATE', 'ACTIVE', 'PROCESSING', 'DISCONNECTED', 'CLOSED') 
           DEFAULT 'CREATE' NOT NULL COMMENT '会话状态',
    gmt_create TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    gmt_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    metadata JSON COMMENT '元数据JSON',
    
    INDEX idx_session_id (session_id),
    INDEX idx_ali_uid (ali_uid),
    INDEX idx_agent_id (agent_id),
    INDEX idx_status (status),
    INDEX idx_gmt_create (gmt_create)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Session会话表';

-- Rounds表
CREATE TABLE IF NOT EXISTS rounds (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    round_id INT NOT NULL COMMENT '轮次ID（在Session内递增）',
    session_id VARCHAR(64) NOT NULL COMMENT '关联的会话ID',
    user_prompt TEXT NOT NULL COMMENT '用户输入的提示词',
    status ENUM('PROCESSING', 'COMPLETED', 'FAILED') 
           DEFAULT 'PROCESSING' NOT NULL COMMENT '轮次状态',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    usage_info JSON COMMENT '使用量信息JSON',
    metadata JSON COMMENT '元数据JSON',
    
    INDEX idx_session_id (session_id),
    INDEX idx_round_id (round_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    UNIQUE KEY uk_session_round (session_id, round_id),
    
    FOREIGN KEY (session_id) REFERENCES sessions(session_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Round轮次表';

-- Messages表
CREATE TABLE IF NOT EXISTS messages (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    message_id VARCHAR(64) NOT NULL UNIQUE COMMENT '消息ID',
    round_id INT NOT NULL COMMENT '关联的轮次ID',
    session_id VARCHAR(64) NOT NULL COMMENT '关联的会话ID',
    content TEXT NOT NULL COMMENT '消息内容',
    message_type ENUM('USER_INPUT', 'LLM_OUTPUT', 'TITLE', 'FOLLOW_UP') 
                 NOT NULL COMMENT '消息类型',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '消息时间戳',
    metadata JSON COMMENT '元数据JSON',
    
    INDEX idx_message_id (message_id),
    INDEX idx_session_id (session_id),
    INDEX idx_round_id (round_id),
    INDEX idx_message_type (message_type),
    INDEX idx_timestamp (timestamp),
    INDEX idx_session_round (session_id, round_id),
    
    FOREIGN KEY (session_id) REFERENCES sessions(session_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Message消息表';
""" 