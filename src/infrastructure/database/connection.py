"""
SQLAlchemy数据库连接管理器
提供ORM会话管理和数据库操作
"""

from contextlib import contextmanager
from typing import Optional, Generator, Any
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session as SQLSession
from sqlalchemy.pool import QueuePool
from loguru import logger

from .models.session_models import Base, SessionModel
from src.shared.config.environments import env_manager


class DatabaseManager:
    """SQLAlchemy数据库管理器"""
    
    def __init__(self):
        self.engine: Optional[Any] = None
        self.SessionLocal: Optional[sessionmaker] = None
        self._initialized = False
    
    def initialize(self):
        """初始化数据库连接"""
        if self._initialized:
            return
        
        try:
            # 获取数据库配置
            config = env_manager.get_config()
            
            # 处理密码解密
            password = config.db_mysql_password
            # if password and password.startswith("enc:"):
                # try:
                    # from ...shared.config.key_center_config import kc
                    # password = kc.kc_decrypt(password[4:])  # 去掉enc:前缀
                # except Exception as e:
                    # logger.warning(f"密码解密失败，使用原始密码: {e}")
                    # password = config.db_mysql_password
            
            database_url = (
                f"mysql+pymysql://{config.db_mysql_user}:{password}@"
                f"{config.db_mysql_host}:{config.db_mysql_port}/{config.db_mysql_name}"
                f"?charset=utf8mb4"
            )
            logger.info(f"数据库连接URL: mysql+pymysql://{config.db_mysql_user}:***@{config.db_mysql_host}:{config.db_mysql_port}/{config.db_mysql_name}")
            
            # 创建引擎
            self.engine = create_engine(
                database_url,
                poolclass=QueuePool,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=env_manager.is_development(),  # 开发环境打印SQL
                connect_args={
                    "connect_timeout": 10,
                    "read_timeout": 30,
                    "write_timeout": 30,
                }
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # 测试连接
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                if result.fetchone()[0] != 1:
                    raise Exception("数据库连接测试失败")
            
            logger.info(f"数据库连接初始化成功: {config.db_mysql_host}:{config.db_mysql_port}")
            self._initialized = True
            
        except Exception as e:
            logger.error(f"数据库连接初始化失败: {e}")
            raise
    
    def create_tables(self):
        """创建数据库表"""
        try:
            if not self._initialized:
                self.initialize()
            
            # 使用SQLAlchemy创建表
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据库表创建/更新完成")
            
        except Exception as e:
            logger.error(f"创建数据库表失败: {e}")
            raise
    
    @contextmanager
    def get_session(self) -> Generator[SQLSession, None, None]:
        """获取数据库会话上下文管理器"""
        if not self._initialized:
            self.initialize()
        
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            session.close()
    
    def get_session_sync(self) -> SQLSession:
        """获取同步数据库会话（需要手动管理）"""
        if not self._initialized:
            self.initialize()
        
        return self.SessionLocal()
    
    def close(self):
        """关闭数据库连接"""
        try:
            if self.engine:
                self.engine.dispose()
                logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {e}")
        finally:
            self._initialized = False
            self.engine = None
            self.SessionLocal = None
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self._initialized:
                return False
            
            with self.get_session() as session:
                result = session.execute(text("SELECT 1")).fetchone()
                return result[0] == 1
                
        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")
            return False
    
    def get_connection_info(self) -> dict:
        """获取连接信息"""
        if not self._initialized:
            return {"status": "未初始化"}
        
        config = env_manager.get_config()
        return {
            "status": "已连接",
            "host": config.db_mysql_host,
            "port": config.db_mysql_port,
            "database": config.db_mysql_name,
            "user": config.db_mysql_user,
            "pool_size": self.engine.pool.size() if self.engine else 0,
            "checked_out": self.engine.pool.checkedout() if self.engine else 0,
        }


# 全局数据库管理器实例
db_manager = DatabaseManager()


# 兼容性函数（保持原有接口）
def get_db_session():
    """获取数据库会话（FastAPI依赖注入用）"""
    session = db_manager.get_session_sync()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close() 