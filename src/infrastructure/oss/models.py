"""
OSS相关数据模型
"""
from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from datetime import datetime

@dataclass
class OSSConfig:
    """OSS配置"""
    region: str
    bucket_name: str
    endpoint: Optional[str] = None
    enable_crc: bool = True
    enable_md5: bool = True
    max_retry_count: int = 3
    # 无密钥认证相关配置
    ram_role_arn: Optional[str] = None
    region_id: Optional[str] = None
    app_group: Optional[str] = None
    akless_env: Optional[str] = None

    def __post_init__(self):
        if not self.endpoint:
            self.endpoint = f"https://oss-{self.region}.aliyuncs.com"

@dataclass
class UploadResult:
    """上传结果"""
    success: bool
    key: str
    url: str
    etag: Optional[str] = None
    size: Optional[int] = None
    content_type: Optional[str] = None
    error_message: Optional[str] = None
    upload_time: Optional[datetime] = None
    
    def __post_init__(self):
        if self.upload_time is None:
            self.upload_time = datetime.now()

@dataclass
class DownloadResult:
    """下载结果"""
    success: bool
    key: str
    content: Optional[bytes] = None
    local_path: Optional[str] = None
    size: Optional[int] = None
    content_type: Optional[str] = None
    last_modified: Optional[datetime] = None
    error_message: Optional[str] = None
    download_time: Optional[datetime] = None
    
    def __post_init__(self):
        if self.download_time is None:
            self.download_time = datetime.now()

@dataclass
class ObjectInfo:
    """对象信息"""
    key: str
    size: int
    last_modified: datetime
    etag: str
    content_type: str
    storage_class: str
    is_directory: bool = False
    
@dataclass
class ListResult:
    """列举结果"""
    success: bool
    objects: List[ObjectInfo]
    next_marker: Optional[str] = None
    is_truncated: bool = False
    prefix: Optional[str] = None
    delimiter: Optional[str] = None
    max_keys: int = 1000
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if self.objects is None:
            self.objects = []

@dataclass
class DeleteResult:
    """删除结果"""
    success: bool
    key: str
    error_message: Optional[str] = None
    delete_time: Optional[datetime] = None
    
    def __post_init__(self):
        if self.delete_time is None:
            self.delete_time = datetime.now()

@dataclass
class BucketInfo:
    """Bucket信息"""
    name: str
    creation_date: datetime
    location: str
    storage_class: str
    acl: str
    versioning: str 