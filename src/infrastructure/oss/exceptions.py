"""
OSS相关异常类
"""

class OSSClientError(Exception):
    """OSS客户端异常"""
    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code

class OSSServiceError(Exception):
    """OSS服务异常"""
    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code

class OSSFileNotFoundError(OSSServiceError):
    """OSS文件不存在异常"""
    pass

class OSSUploadError(OSSServiceError):
    """OSS上传异常"""
    pass

class OSSDownloadError(OSSServiceError):
    """OSS下载异常"""
    pass

class OSSPermissionError(OSSServiceError):
    """OSS权限异常"""
    pass 