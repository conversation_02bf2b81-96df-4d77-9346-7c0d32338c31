"""
阿里云OSS服务封装 - 提供基础的OSS操作功能
"""
import os
from loguru import logger
import mimetypes
from typing import Optional, List, Dict, Any, Union, BinaryIO
from datetime import datetime
from pathlib import Path
import oss2

from .client import OSSClient
from .models import (
    OSSConfig, UploadResult, DownloadResult, ListResult, 
    DeleteResult, ObjectInfo, BucketInfo
)
from .exceptions import (
    OSSServiceError, OSSFileNotFoundError, OSSUploadError, 
    OSSDownloadError, OSSPermissionError
)

# 使用 loguru logger

class OSSService:
    """阿里云OSS服务封装"""
    
    def __init__(self, config: Optional[OSSConfig] = None):
        """
        初始化OSS服务

        Args:
            config: OSS配置，如果为None则从项目配置系统自动获取
        """
        self.oss_client = OSSClient(config)
        self.config = self.oss_client.get_config()
        self.bucket = self.oss_client.get_bucket()

        # 初始化RAG专用的OSS客户端
        self.rag_oss_client = None
        self.rag_bucket = None
        self._initialize_rag_client()

    def _initialize_rag_client(self):
        """初始化RAG专用的OSS客户端"""
        try:
            # 创建RAG服务的OSS配置
            rag_config = self._create_rag_oss_config()
            if rag_config:
                self.rag_oss_client = OSSClient(rag_config)
                self.rag_bucket = self.rag_oss_client.get_bucket()
                logger.info(f"RAG OSS客户端初始化成功: bucket={rag_config.bucket_name}")
            else:
                logger.warning("RAG OSS配置创建失败，将无法访问RAG服务的文件")
        except Exception as e:
            logger.error(f"RAG OSS客户端初始化失败: {e}")
            self.rag_oss_client = None
            self.rag_bucket = None

    def _create_rag_oss_config(self) -> Optional[OSSConfig]:
        """创建RAG服务的OSS配置（使用无密钥认证）"""
        try:
            # 导入项目配置系统
            try:
                from ...shared.config.environments import env_manager
            except ImportError:
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
                from shared.config.environments import env_manager

            config = env_manager.get_config()

            # 从配置文件读取RAG OSS配置
            bucket_name = config.rag_oss_bucket_name
            region = getattr(config, 'rag_oss_region', 'cn-hangzhou')
            endpoint = getattr(config, 'rag_oss_endpoint', 'https://oss-cn-hangzhou.aliyuncs.com')

            if not bucket_name:
                logger.error(f"缺少RAG OSS配置: bucket_name={bool(bucket_name)}")
                return None

            # RAG服务的OSS配置（使用无密钥认证）
            rag_config = OSSConfig(
                region=region,
                bucket_name=bucket_name,
                endpoint=endpoint,
                ram_role_arn=config.ram_role_arn,
                region_id=config.region_id,
                app_group=config.app_group,
                akless_env=config.akless_env
            )
            logger.info(f"RAG OSS配置：{rag_config}")
            logger.info(f"RAG OSS配置创建成功（无密钥认证）: bucket={rag_config.bucket_name}, region={region}, ram_role_arn={config.ram_role_arn}")
            return rag_config

        except Exception as e:
            logger.error(f"创建RAG OSS配置失败: {e}")
            return None

    def upload_file(self,
                   local_path: str, 
                   key: str, 
                   bucket_name: Optional[str] = None,
                   content_type: Optional[str] = None,
                   metadata: Optional[Dict[str, str]] = None) -> UploadResult:
        """
        上传文件到OSS
        
        Args:
            local_path: 本地文件路径
            key: OSS对象键名
            bucket_name: Bucket名称，默认使用配置中的bucket
            content_type: 文件MIME类型，自动检测
            metadata: 自定义元数据
            
        Returns:
            UploadResult: 上传结果
        """
        try:
            # 检查本地文件是否存在
            if not os.path.exists(local_path):
                raise OSSUploadError(f"本地文件不存在: {local_path}")
            
            # 自动检测文件类型
            if not content_type:
                content_type, _ = mimetypes.guess_type(local_path)
                if not content_type:
                    content_type = 'application/octet-stream'
            
            # 获取文件大小
            file_size = os.path.getsize(local_path)
            
            # 构建请求头
            headers = {}
            if content_type:
                headers['Content-Type'] = content_type
            if metadata:
                # 添加用户自定义元数据
                for k, v in metadata.items():
                    if not k.startswith('x-oss-meta-'):
                        k = f'x-oss-meta-{k}'
                    headers[k] = v
            
            # 使用put_object_from_file上传文件
            result = self.bucket.put_object_from_file(key, local_path, headers=headers)
            
            # 构建访问URL
            url = f"{self.config.endpoint}/{self.config.bucket_name}/{key}"
            
            upload_result = UploadResult(
                success=True,
                key=key,
                url=url,
                etag=result.etag,
                size=file_size,
                content_type=content_type
            )
            
            logger.info(f"文件上传成功: {key} ({file_size} bytes)")
            return upload_result
            
        except Exception as e:
            logger.error(f"文件上传失败: {e}")
            return UploadResult(
                success=False,
                key=key,
                url="",
                error_message=str(e)
            )
    
    def upload_bytes(self,
                    data: bytes,
                    key: str,
                    bucket_name: Optional[str] = None,
                    content_type: Optional[str] = None,
                    metadata: Optional[Dict[str, str]] = None,
                    progress_callback: Optional[callable] = None) -> UploadResult:
        """
        上传字节数据到OSS

        Args:
            data: 要上传的字节数据
            key: OSS对象键名
            bucket_name: Bucket名称
            content_type: 文件MIME类型
            metadata: 自定义元数据
            progress_callback: 进度回调函数，接收 (consumed_bytes, total_bytes) 参数

        Returns:
            UploadResult: 上传结果
        """
        try:
            # 默认内容类型
            if not content_type:
                content_type = 'application/octet-stream'
            
            # 构建请求头
            headers = {}
            if content_type:
                headers['Content-Type'] = content_type
            if metadata:
                # 添加用户自定义元数据
                for k, v in metadata.items():
                    if not k.startswith('x-oss-meta-'):
                        k = f'x-oss-meta-{k}'
                    headers[k] = v
            
            # 使用put_object上传数据，支持进度回调
            from io import BytesIO
            data_stream = BytesIO(data)

            # 如果有进度回调，使用流式上传
            if progress_callback:
                result = self.bucket.put_object(key, data_stream, headers=headers, progress_callback=progress_callback)
            else:
                result = self.bucket.put_object(key, data, headers=headers)
            
            # 构建访问URL
            url = f"{self.config.endpoint}/{self.config.bucket_name}/{key}"
            
            upload_result = UploadResult(
                success=True,
                key=key,
                url=url,
                etag=result.etag,
                size=len(data),
                content_type=content_type
            )
            
            logger.info(f"字节数据上传成功: {key} ({len(data)} bytes)")
            return upload_result
            
        except Exception as e:
            logger.error(f"字节数据上传失败: {e}")
            return UploadResult(
                success=False,
                key=key,
                url="",
                error_message=str(e)
            )
    
    def download_file(self, 
                     key: str, 
                     local_path: str,
                     bucket_name: Optional[str] = None) -> DownloadResult:
        """
        从OSS下载文件
        
        Args:
            key: OSS对象键名
            local_path: 本地保存路径
            bucket_name: Bucket名称
            
        Returns:
            DownloadResult: 下载结果
        """
        try:
            # 创建本地目录
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            # 使用get_object_to_file下载文件
            result = self.bucket.get_object_to_file(key, local_path)
            
            # 获取文件大小
            file_size = os.path.getsize(local_path)
            
            download_result = DownloadResult(
                success=True,
                key=key,
                local_path=local_path,
                size=file_size,
                content_type=result.content_type,
                last_modified=result.last_modified
            )
            
            logger.info(f"文件下载成功: {key} -> {local_path}")
            return download_result
            
        except oss2.exceptions.NoSuchKey:
            logger.error(f"文件不存在: {key}")
            return DownloadResult(
                success=False,
                key=key,
                error_message=f"文件不存在: {key}"
            )
        except Exception as e:
            logger.error(f"文件下载失败: {e}")
            return DownloadResult(
                success=False,
                key=key,
                error_message=str(e)
            )
    
    def download_bytes(self, 
                      key: str, 
                      bucket_name: Optional[str] = None) -> DownloadResult:
        """
        从OSS下载文件内容到内存
        
        Args:
            key: OSS对象键名
            bucket_name: Bucket名称
            
        Returns:
            DownloadResult: 下载结果
        """
        try:
            # 使用get_object下载文件内容
            result = self.bucket.get_object(key)
            
            # 读取所有内容到内存
            content = result.read()
            
            download_result = DownloadResult(
                success=True,
                key=key,
                content=content,
                size=len(content),
                content_type=result.content_type,
                last_modified=result.last_modified
            )
            
            logger.info(f"文件内容下载成功: {key} ({len(content)} bytes)")
            return download_result
            
        except oss2.exceptions.NoSuchKey:
            logger.error(f"文件不存在: {key}")
            return DownloadResult(
                success=False,
                key=key,
                error_message=f"文件不存在: {key}"
            )
        except Exception as e:
            logger.error(f"文件内容下载失败: {e}")
            return DownloadResult(
                success=False,
                key=key,
                error_message=str(e)
            )
    
    def list_objects(self, 
                    prefix: Optional[str] = None,
                    delimiter: Optional[str] = None,
                    max_keys: int = 1000,
                    marker: Optional[str] = None,
                    bucket_name: Optional[str] = None) -> ListResult:
        """
        列举OSS对象
        
        Args:
            prefix: 对象键前缀
            delimiter: 分隔符
            max_keys: 最大返回数量
            marker: 分页标记
            bucket_name: Bucket名称
            
        Returns:
            ListResult: 列举结果
        """
        try:
            # 使用list_objects列举对象
            result = self.bucket.list_objects(
                prefix=prefix or '',
                delimiter=delimiter or '',
                marker=marker or '',
                max_keys=max_keys
            )
            
            # 转换对象信息
            objects = []
            if result.object_list:
                for obj in result.object_list:
                    objects.append(ObjectInfo(
                        key=obj.key,
                        size=obj.size,
                        last_modified=datetime.fromtimestamp(obj.last_modified),
                        etag=obj.etag,
                        content_type=obj.type or 'application/octet-stream',
                        storage_class=obj.storage_class or 'Standard'
                    ))
            
            # 处理目录（公共前缀）
            if result.prefix_list:
                for prefix_info in result.prefix_list:
                    objects.append(ObjectInfo(
                        key=prefix_info,
                        size=0,
                        last_modified=datetime.now(),
                        etag="",
                        content_type="application/x-directory",
                        storage_class="Standard",
                        is_directory=True
                    ))
            
            list_result = ListResult(
                success=True,
                objects=objects,
                next_marker=result.next_marker,
                is_truncated=result.is_truncated,
                prefix=prefix,
                delimiter=delimiter,
                max_keys=max_keys
            )
            
            logger.info(f"对象列举成功: {len(objects)}个对象")
            return list_result
            
        except Exception as e:
            logger.error(f"对象列举失败: {e}")
            return ListResult(
                success=False,
                objects=[],
                error_message=str(e)
            )
    
    def delete_object(self, 
                     key: str, 
                     bucket_name: Optional[str] = None) -> DeleteResult:
        """
        删除OSS对象
        
        Args:
            key: 对象键名
            bucket_name: Bucket名称
            
        Returns:
            DeleteResult: 删除结果
        """
        try:
            # 使用delete_object删除对象
            self.bucket.delete_object(key)
            
            result = DeleteResult(
                success=True,
                key=key
            )
            
            logger.info(f"对象删除成功: {key}")
            return result
            
        except Exception as e:
            logger.error(f"对象删除失败: {e}")
            return DeleteResult(
                success=False,
                key=key,
                error_message=str(e)
            )
    
    def delete_objects(self, 
                      keys: List[str], 
                      bucket_name: Optional[str] = None) -> List[DeleteResult]:
        """
        批量删除OSS对象
        
        Args:
            keys: 对象键名列表
            bucket_name: Bucket名称
            
        Returns:
            List[DeleteResult]: 删除结果列表
        """
        results = []
        
        try:
            # 使用batch_delete_objects批量删除
            result = self.bucket.batch_delete_objects(keys)
            
            # 处理删除结果
            deleted_keys = set(result.deleted_keys)
            
            for key in keys:
                if key in deleted_keys:
                    results.append(DeleteResult(
                        success=True,
                        key=key
                    ))
                else:
                    results.append(DeleteResult(
                        success=False,
                        key=key,
                        error_message="删除失败，原因未知"
                    ))
            
            logger.info(f"批量删除完成: {len(keys)}个对象")
            return results
            
        except Exception as e:
            logger.error(f"批量删除失败: {e}")
            # 返回所有对象的失败结果
            return [DeleteResult(
                success=False,
                key=key,
                error_message=str(e)
            ) for key in keys]
    
    def object_exists(self, 
                     key: str, 
                     bucket_name: Optional[str] = None) -> bool:
        """
        检查对象是否存在
        
        Args:
            key: 对象键名
            bucket_name: Bucket名称
            
        Returns:
            bool: 对象是否存在
        """
        try:
            # 使用head_object检查对象是否存在
            self.bucket.head_object(key)
            return True
            
        except oss2.exceptions.NoSuchKey:
            return False
        except Exception as e:
            logger.error(f"检查对象是否存在失败: {e}")
            return False
    
    def get_object_info(self, 
                       key: str, 
                       bucket_name: Optional[str] = None) -> Optional[ObjectInfo]:
        """
        获取对象信息
        
        Args:
            key: 对象键名
            bucket_name: Bucket名称
            
        Returns:
            Optional[ObjectInfo]: 对象信息，如果不存在返回None
        """
        try:
            # 使用head_object获取对象信息
            result = self.bucket.head_object(key)
            
            return ObjectInfo(
                key=key,
                size=result.content_length,
                last_modified=datetime.fromtimestamp(result.last_modified),
                etag=result.etag,
                content_type=result.content_type or 'application/octet-stream',
                storage_class=getattr(result, 'storage_class', 'Standard') or 'Standard'
            )
            
        except oss2.exceptions.NoSuchKey:
            return None
        except Exception as e:
            logger.error(f"获取对象信息失败: {e}")
            return None
        
    def generate_presigned_url_with_preview(self, 
                                         key: str, 
                                         expires_in: int = 3600,
                                         bucket_name: Optional[str] = None) -> Optional[str]:
        """
        生成预签名URL
        """
        try:
            # 构建在线预览的处理指令。
            doc_process = 'doc/preview,export_1,print_1/watermark,text_5YaF6YOo6LWE5paZ,size_30,t_60'

            # 使用sign_url生成预签名URL
            url = self.bucket.sign_url('GET', key, expires_in, params={'x-oss-process': doc_process}, slash_safe=True)
            
            logger.info(f"生成预签名预览URL成功: {key}")
            return url
            
        except Exception as e:
            logger.error(f"生成预签名预览URL失败: {e}")
            return None
    

    def generate_presigned_url(self, 
                              key: str, 
                              expires_in: int = 3600,
                              bucket_name: Optional[str] = None) -> Optional[str]:
        """
        生成预签名URL
        
        Args:
            key: 对象键名
            expires_in: 过期时间（秒）
            bucket_name: Bucket名称
            
        Returns:
            Optional[str]: 预签名URL，失败时返回None
        """
        try:
            # 使用sign_url生成预签名URL
            url = self.bucket.sign_url('GET', key, expires_in)
            
            logger.info(f"生成预签名URL成功: {key}")
            return url
            
        except Exception as e:
            logger.error(f"生成预签名URL失败: {e}")
            return None

    def generate_rag_presigned_url(self,
                                  key: str,
                                  expires_in: int = 3600,
                                  method: str = 'GET') -> Optional[str]:
        """
        为RAG服务的文件生成预签名URL

        Args:
            key: RAG服务中的对象键名
            expires_in: URL有效期（秒），默认1小时
            method: HTTP方法，默认GET

        Returns:
            str: 预签名URL，失败时返回None
        """
        if not self.rag_bucket:
            logger.error("RAG OSS客户端未初始化，无法生成预签名URL")
            return None

        try:
            # 使用RAG专用的bucket生成预签名URL
            url = self.rag_bucket.sign_url(method, key, expires_in)
            logger.info(f"RAG预签名URL生成成功: key={key}, expires_in={expires_in}")
            return url

        except Exception as e:
            logger.error(f"生成RAG预签名URL失败: key={key}, error={e}")
            return None

    def download_rag_file_content(self, key: str) -> Optional[str]:
        """
        下载RAG服务的文件内容

        Args:
            key: RAG服务中的对象键名

        Returns:
            str: 文件内容，失败时返回None
        """
        if not self.rag_bucket:
            logger.error("RAG OSS客户端未初始化，无法下载文件")
            return None

        try:
            # 直接使用RAG bucket下载文件内容
            result = self.rag_bucket.get_object(key)
            content = result.read().decode('utf-8')
            logger.info(f"RAG文件下载成功: key={key}, 内容长度={len(content)}")
            return content

        except Exception as e:
            logger.error(f"下载RAG文件失败: key={key}, error={e}")
            return None

    def generate_presigned_upload_url(
        self,
        object_key: str,
        expires: int = 900,
        content_type: Optional[str] = None,
        content_length: Optional[int] = None
    ) -> tuple[str, Dict[str, str]]:
        """
        生成预签名上传URL

        Args:
            object_key: OSS对象键
            expires: 过期时间（秒），默认15分钟
            content_type: 文件MIME类型
            content_length: 文件大小

        Returns:
            (预签名上传URL, 上传时需要的headers)
        """
        try:
            logger.info(f"[OSSService] 生成预签名上传URL: object_key={object_key}, expires={expires}")

            # 构建请求参数
            params = {}
            headers = {}

            # 根据文件扩展名自动设置 Content-Type
            if not content_type:
                # 从object_key提取文件扩展名并设置对应的Content-Type
                content_type = self._get_content_type_by_extension(object_key)
            
            headers['Content-Type'] = "application/x-www-form-urlencoded"

            # 设置Content-Length
            if content_length:
                headers['Content-Length'] = str(content_length)

            # 生成预签名URL
            url = self.oss_client.bucket.sign_url(
                'PUT',
                object_key,
                expires,
                headers=headers,
                params=params
            )

            logger.info(f"[OSSService] 预签名URL生成成功: {object_key}")
            return url, headers

        except Exception as e:
            logger.error(f"[OSSService] 生成预签名URL失败: object_key={object_key}, error={e}")
            raise OSSServiceError(f"生成预签名URL失败: {str(e)}")

    def _get_content_type_by_extension(self, filename: str) -> Optional[str]:
        """
        根据文件扩展名获取对应的Content-Type
        
        Args:
            filename: 文件名或路径
            
        Returns:
            Optional[str]: Content-Type，未匹配时返回None
        """
        if not filename:
            return None
            
        # 提取文件扩展名（转为小写）
        ext = Path(filename).suffix.lower().lstrip('.')
        
        # 定义扩展名到Content-Type的映射
        content_type_map = {
            # 文档格式
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'ppt': 'application/vnd.ms-powerpoint',
            'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'xlsm': 'application/vnd.ms-excel.sheet.macroEnabled.12',
            
            # 其他格式
            'md': 'text/x-markdown',
            'markdown': 'text/x-markdown',
            'html': 'text/html',
            'htm': 'text/html',
            'epub': 'application/epub+zip',
            'mobi': 'application/x-mobipocket-ebook',
            'rtf': 'application/rtf',
            'txt': 'text/plain',
            
            # 图片格式
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'bmp': 'image/bmp',
            'gif': 'image/gif',
            
            # 音视频格式
            'mp4': 'video/mp4',
            'mkv': 'video/x-matroska',
            'avi': 'video/x-msvideo',
            'mov': 'video/quicktime',
            'wmv': 'video/x-ms-wmv',
            'mp3': 'audio/mpeg',
            'wav': 'audio/wav',
            'aac': 'audio/aac',
        }
        
        return content_type_map.get(ext)

    def generate_presigned_download_url(
        self,
        object_key: str,
        expires: int = 3600,
        response_content_disposition: Optional[str] = None
    ) -> str:
        """
        生成预签名下载URL

        Args:
            object_key: OSS对象键
            expires: 过期时间（秒），默认1小时
            response_content_disposition: 响应头Content-Disposition

        Returns:
            预签名下载URL
        """
        try:
            logger.info(f"[OSSService] 生成预签名下载URL: object_key={object_key}, expires={expires}")

            params = {}
            if response_content_disposition:
                params['response-content-disposition'] = response_content_disposition

            # 生成预签名URL
            url = self.oss_client.bucket.sign_url(
                'GET',
                object_key,
                expires,
                params=params
            )

            logger.info(f"[OSSService] 预签名下载URL生成成功: {object_key}")
            return url

        except Exception as e:
            logger.error(f"[OSSService] 生成预签名下载URL失败: object_key={object_key}, error={e}")
            raise OSSServiceError(f"生成预签名下载URL失败: {str(e)}")

    def check_object_exists(self, object_key: str) -> bool:
        """
        检查对象是否存在

        Args:
            object_key: OSS对象键

        Returns:
            是否存在
        """
        try:
            return self.oss_client.bucket.object_exists(object_key)
        except Exception as e:
            logger.error(f"[OSSService] 检查对象存在性失败: object_key={object_key}, error={e}")
            return False

    def get_object_meta(self, object_key: str) -> Optional[Dict[str, Any]]:
        """
        获取对象元数据

        Args:
            object_key: OSS对象键

        Returns:
            对象元数据
        """
        try:
            result = self.oss_client.bucket.get_object_meta(object_key)
            return {
                'content_length': getattr(result, 'content_length', None),
                'content_type': getattr(result, 'content_type', None),
                'etag': getattr(result, 'etag', None),
                'last_modified': getattr(result, 'last_modified', None)
            }
        except Exception as e:
            logger.error(f"[OSSService] 获取对象元数据失败: object_key={object_key}, error={e}")
            return None

    def copy_object(self,source_key: str,
                    source_bucket: Optional[str] = None,
                    dest_key: Optional[str] = None) -> bool:
        """
        复制OSS对象到当前bucket
        
        Args:
            source_key: 源对象键名
            dest_key: 目标对象键名（与dest_key_template二选一）
            source_bucket: 源Bucket名称，默认使用当前bucket

        Returns:
            bool: 复制是否成功
        """
        try:
            # 确定源bucket名称
            source_bucket_name = source_bucket or self.config.bucket_name
            
            # 执行复制操作到当前bucket
            self.bucket.copy_object(
                source_bucket_name,
                source_key,
                dest_key
            )
            
            logger.info(f"文件复制成功: {source_bucket_name}/{source_key} -> {dest_key}")
            return True
            
        except Exception as e:
            logger.error(f"文件复制失败: {e}")
            return False

    def close(self):
        """关闭客户端"""
        self.oss_client.close()
        if self.rag_oss_client:
            self.rag_oss_client.close()


# 创建全局实例
oss_service = OSSService()