"""
阿里云OSS客户端封装
"""
import os
from loguru import logger
import threading
from typing import Optional, Dict, Any
import oss2
from oss2.credentials import EnvironmentVariableCredentialsProvider

from aliyunaklesscredprovider.core import AklessCredproviderFactory
from aliyunaklesscredprovider.core.config import CredentialProviderConfig

from .models import OSSConfig
from .exceptions import OSSClientError

# 使用 loguru logger

class OSSClient:
    """阿里云OSS客户端封装"""

    def __init__(self, config: Optional[OSSConfig] = None):
        """
        初始化OSS客户端

        Args:
            config: OSS配置，如果为None则从项目配置系统自动获取
        """
        self.config = config or self._create_config_from_env()
        self._auth = None
        self._auth_lock = threading.Lock()
        self.bucket = None
        self._initialize_client()
    
    def _create_config_from_env(self) -> OSSConfig:
        """从项目配置系统创建OSS配置"""
        try:
            # 导入项目配置系统
            try:
                from ...shared.config.environments import env_manager
            except ImportError:
                # 如果相对导入失败，使用绝对导入
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
                from shared.config.environments import env_manager

            config = env_manager.get_config()

            # 从配置文件中获取OSS相关配置
            region = getattr(config, 'oss_region', "cn-hangzhou")
            bucket_name = getattr(config, 'oss_bucket_name', "alpha-service-oss")
            endpoint = getattr(config, 'oss_endpoint', None)

            # 如果配置中没有endpoint，则根据region自动生成
            if not endpoint:
                endpoint = f"https://oss-{region}.aliyuncs.com"

            # 获取无密钥认证相关配置
            ram_role_arn = getattr(config, 'ram_role_arn', '')
            region_id = getattr(config, 'region_id', 'cn-hangzhou')
            app_group = getattr(config, 'app_group', '')
            akless_env = getattr(config, 'akless_env', '')

            # 创建OSS配置对象，这里不再需要access_key_id和access_key_secret
            # 因为我们将使用无密钥认证
            oss_config = OSSConfig(
                region=region,
                bucket_name=bucket_name,
                endpoint=endpoint
            )

            # 将无密钥认证配置存储到config对象中，供后续使用
            oss_config.ram_role_arn = ram_role_arn
            oss_config.region_id = region_id
            oss_config.app_group = app_group
            oss_config.akless_env = akless_env

            logger.info(f"OSS配置创建成功: region={region}, bucket={bucket_name}, 使用无密钥认证")
            return oss_config

        except Exception as e:
            logger.error(f"创建OSS配置失败: {e}")
            raise OSSClientError(f"创建OSS配置失败: {e}")

    def _get_auth(self):
        """
        获取 OSS 认证对象（线程安全）
        优先使用阿里云无密钥认证，失败时回退到AccessKey认证

        Returns:
            OSS 认证对象
        """
        if self._auth is None:
            with self._auth_lock:
                # 双重检查锁定模式
                if self._auth is None:
                    # 尝试无密钥认证
                    if self._try_akless_auth():
                        return self._auth

                    # 无密钥认证失败，输出详细的错误信息
                    self._log_akless_auth_failure()
                    raise OSSClientError("无密钥认证失败，请检查配置和环境")
        return self._auth

    def _try_akless_auth(self) -> bool:
        """
        尝试使用无密钥认证

        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始尝试无密钥认证...")

            # 检查是否有必要的无密钥认证配置
            missing_configs = []
            if not self.config.ram_role_arn:
                missing_configs.append("ram_role_arn")
            if not self.config.region_id:
                missing_configs.append("region_id")
            if not self.config.akless_env:
                missing_configs.append("akless_env")

            config = CredentialProviderConfig(
                ram_role_arn=self.config.ram_role_arn,
                region_id=self.config.region_id,
                app_name="wuying-alpha-service",
                app_group="",
                app_env=self.config.akless_env
            )
            credential_provider = AklessCredproviderFactory.get_opensdk_v2_credential_with_config(config)
            self._auth = oss2.ProviderAuthV2(credential_provider)
            logger.info(f"✅ 成功创建无密钥认证: ram_role_arn={self.config.ram_role_arn}")
            return True
        except Exception as e:
            logger.error(f"无密钥认证创建失败: {e}")
            logger.error(f"异常类型: {type(e).__name__}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

    def _log_akless_auth_failure(self):
        """
        记录无密钥认证失败的详细信息，用于问题诊断
        """
        logger.error("=" * 80)
        logger.error("🚨 无密钥认证失败诊断信息:")
        logger.error("=" * 80)

        # 1. 配置信息检查
        logger.error("📋 配置信息:")
        logger.error(f"  ram_role_arn: {self.config.ram_role_arn or '❌ 未配置'}")
        logger.error(f"  region_id: {self.config.region_id or '❌ 未配置'}")
        logger.error(f"  app_group: {self.config.app_group or '❌ 未配置'}")
        logger.error(f"  akless_env: {self.config.akless_env or '❌ 未配置'}")

        # 2. 环境变量检查
        import os
        logger.error("🌍 相关环境变量:")
        env_vars = [
            'ALIBABA_CLOUD_ACCESS_KEY_ID',
            'ALIBABA_CLOUD_ACCESS_KEY_SECRET',
            'ALIBABA_CLOUD_SECURITY_TOKEN',
            'ALIBABA_CLOUD_ROLE_ARN',
            'ALIBABA_CLOUD_ROLE_SESSION_NAME',
            'ALIBABA_CLOUD_REGION_ID',
            'ALIBABA_CLOUD_CREDENTIALS_FILE',
            'HOME',
            'AKLESS_ENV',
            'APP_GROUP'
        ]

        for var in env_vars:
            value = os.environ.get(var)
            if value:
                # 对敏感信息进行脱敏
                if 'KEY' in var or 'SECRET' in var or 'TOKEN' in var:
                    display_value = f"{value[:8]}***{value[-4:]}" if len(value) > 12 else "***"
                else:
                    display_value = value
                logger.error(f"  {var}: {display_value}")
            else:
                logger.error(f"  {var}: ❌ 未设置")

        # 3. 文件系统检查
        logger.error("📁 凭证文件检查:")
        credential_paths = [
            os.path.expanduser("~/.alibabacloud/credentials"),
            os.path.expanduser("~/.aliyun/config.json"),
            "/etc/credentials"
        ]

        for path in credential_paths:
            if os.path.exists(path):
                try:
                    stat = os.stat(path)
                    logger.error(f"  {path}: ✅ 存在 (大小: {stat.st_size} bytes)")
                except Exception as e:
                    logger.error(f"  {path}: ⚠️ 存在但无法读取 ({e})")
            else:
                logger.error(f"  {path}: ❌ 不存在")

        # 4. 网络和权限检查建议
        logger.error("🔧 排查建议:")
        logger.error("  1. 检查 RAM 角色是否存在且有正确的权限")
        logger.error("  2. 检查应用是否运行在支持无密钥认证的环境中")
        logger.error("  3. 检查网络连接是否正常，能否访问阿里云元数据服务")
        logger.error("  4. 检查 ECS 实例是否绑定了正确的 RAM 角色")
        logger.error("  5. 检查容器环境是否正确配置了服务账号")

        logger.error("=" * 80)

    def _try_access_key_auth(self) -> bool:
        """
        尝试使用AccessKey认证（回退方案）

        Returns:
            bool: 是否成功
        """
        try:
            # 从配置中获取AccessKey
            access_key_id, access_key_secret = self._get_access_key_from_config()

            if not access_key_id or not access_key_secret:
                logger.error("AccessKey认证配置不完整")
                return False

            self._auth = oss2.Auth(access_key_id, access_key_secret)
            logger.info("✅ 成功创建AccessKey认证（回退方案）")
            return True
        except Exception as e:
            logger.error(f"AccessKey认证失败: {e}")
            return False

    def _get_access_key_from_config(self):
        """
        从配置中获取AccessKey

        Returns:
            tuple: (access_key_id, access_key_secret)
        """
        try:
            # 导入项目配置系统
            try:
                from ...shared.config.environments import env_manager
            except ImportError:
                # 如果相对导入失败，使用绝对导入
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
                from shared.config.environments import env_manager

            config = env_manager.get_config()

            # 优先使用OSS专用的凭据，如果没有则回退到MQ凭据
            access_key_id = getattr(config, 'oss_access_key', None)
            access_key_secret = getattr(config, 'oss_secret_key', None)

            # 如果没有OSS专用凭据，使用MQ凭据
            if not access_key_id or not access_key_secret:
                logger.info("未找到OSS专用凭据，使用MQ凭据")
                access_key_id = config.mq_access_key
                access_key_secret = config.mq_secret_key
            else:
                logger.info("使用OSS专用凭据")

            return access_key_id, access_key_secret

        except Exception as e:
            logger.error(f"获取AccessKey配置失败: {e}")
            return None, None

    def _initialize_client(self):
        """初始化OSS客户端"""
        try:
            # 获取无密钥认证对象
            auth = self._get_auth()

            # 创建Bucket对象
            self.bucket = oss2.Bucket(
                auth,
                self.config.endpoint,
                self.config.bucket_name,
                region=self.config.region
            )

            logger.info(f"OSS客户端初始化成功: {self.config.endpoint}")

        except Exception as e:
            logger.error(f"初始化OSS客户端失败: {e}")
            raise OSSClientError(f"初始化OSS客户端失败: {e}")
    
    def get_bucket(self) -> oss2.Bucket:
        """获取OSS Bucket实例"""
        if not self.bucket:
            raise OSSClientError("OSS客户端未初始化")
        return self.bucket
    
    def get_config(self) -> OSSConfig:
        """获取OSS配置"""
        return self.config
    
    def is_initialized(self) -> bool:
        """检查客户端是否已初始化"""
        return self.bucket is not None
    
    def reinitialize(self):
        """重新初始化客户端"""
        self.bucket = None
        with self._auth_lock:
            self._auth = None
        self._initialize_client()
    
    def test_connection(self) -> bool:
        """测试OSS连接"""
        try:
            # 获取认证对象并尝试列举bucket来测试连接
            auth = self._get_auth()
            list(oss2.BucketIterator(oss2.Service(auth, self.config.endpoint)))
            logger.info("OSS连接测试成功")
            return True
        except Exception as e:
            logger.error(f"OSS连接测试失败: {e}")
            return False
    
    def create_bucket_if_not_exists(self, bucket_name: Optional[str] = None) -> bool:
        """创建Bucket（如果不存在）"""
        bucket_name = bucket_name or self.config.bucket_name
        
        try:
            # 检查bucket是否存在
            self.bucket.head_bucket()
            logger.info(f"Bucket已存在: {bucket_name}")
            return True
            
        except oss2.exceptions.NoSuchBucket:
            # Bucket不存在，创建它
            try:
                self.bucket.create_bucket(oss2.BUCKET_ACL_PRIVATE)
                logger.info(f"Bucket创建成功: {bucket_name}")
                return True
            except Exception as e:
                logger.error(f"创建Bucket失败: {e}")
                return False
        except Exception as e:
            logger.error(f"检查Bucket失败: {e}")
            return False
    
    def close(self):
        """关闭客户端"""
        if self.bucket:
            self.bucket = None
            with self._auth_lock:
                self._auth = None
            logger.info("OSS客户端已关闭")