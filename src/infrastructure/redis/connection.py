"""
Redis连接管理器
提供Redis连接池管理和连接操作
"""

from typing import Optional, Any
import redis
from redis.connection import ConnectionPool
from loguru import logger

from src.shared.config.environments import env_manager


class RedisManager:
    """Redis连接管理器"""
    
    def __init__(self):
        self.pool: Optional[ConnectionPool] = None
        self.client: Optional[redis.Redis] = None
        self._initialized = False
    
    def initialize(self):
        """初始化Redis连接"""
        if self._initialized:
            return
        
        try:
            # 获取Redis配置
            config = env_manager.get_config()
            
            # 检查Redis是否启用
            if not getattr(config, 'redis_enabled', True):
                logger.info("Redis未启用，跳过初始化")
                return
            
            # 处理密码（阿里云Redis格式：账号:密码）
            password = getattr(config, 'redis_password', '')

            # 处理密码解密
            # if password and password.startswith("enc:"):
                # try:
                    # from ...shared.config.key_center_config import kc
                    # password = kc.kc_decrypt(password[4:])  # 去掉enc:前缀
                # except Exception as e:
                    # logger.warning(f"Redis密码解密失败，使用原始密码: {e}")
                    # password = getattr(config, 'redis_password', '')

            # 创建连接池参数
            pool_kwargs = {
                'host': getattr(config, 'redis_host', 'localhost'),
                'port': getattr(config, 'redis_port', 6379),
                'db': getattr(config, 'redis_db', 0),
                'max_connections': getattr(config, 'redis_max_connections', 10),
                'socket_timeout': getattr(config, 'redis_socket_timeout', 5),
                'socket_connect_timeout': getattr(config, 'redis_socket_connect_timeout', 5),
                'decode_responses': True,  # 自动解码响应为字符串
                'retry_on_timeout': True,
                'health_check_interval': 30,
            }

            # 添加认证信息（阿里云Redis直接使用password参数传递账号:密码格式）
            if password:
                pool_kwargs['password'] = password
                if ':' in password:
                    logger.info("使用阿里云Redis认证格式: 账号:密码")
                else:
                    logger.info("使用密码认证")
            else:
                logger.info("未配置Redis密码，使用无密码连接")

            # 创建连接池
            self.pool = ConnectionPool(**pool_kwargs)
            
            # 创建Redis客户端
            self.client = redis.Redis(connection_pool=self.pool)
            
            # 测试连接
            self.client.ping()
            
            logger.info(f"Redis连接初始化成功: {config.redis_host}:{config.redis_port}")
            self._initialized = True
            
        except Exception as e:
            logger.error(f"Redis连接初始化失败: {e}")
            raise
    
    def get_client(self) -> redis.Redis:
        """获取Redis客户端"""
        if not self._initialized:
            self.initialize()
        
        if not self.client:
            raise RuntimeError("Redis客户端未初始化")
        
        return self.client
    
    def close(self):
        """关闭Redis连接"""
        try:
            if self.client:
                self.client.close()
                logger.info("Redis连接已关闭")
        except Exception as e:
            logger.error(f"关闭Redis连接失败: {e}")
        finally:
            self._initialized = False
            self.client = None
            self.pool = None
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self._initialized:
                return False
            
            if not self.client:
                return False
            
            # 执行ping命令测试连接
            result = self.client.ping()
            return result is True
                
        except Exception as e:
            logger.error(f"Redis健康检查失败: {e}")
            return False
    
    def get_connection_info(self) -> dict:
        """获取连接信息"""
        if not self._initialized:
            return {"status": "未初始化"}
        
        config = env_manager.get_config()
        info = {
            "status": "已连接" if self.client else "未连接",
            "host": getattr(config, 'redis_host', 'localhost'),
            "port": getattr(config, 'redis_port', 6379),
            "db": getattr(config, 'redis_db', 0),
        }
        
        # 获取连接池信息
        if self.pool:
            try:
                info.update({
                    "max_connections": getattr(self.pool, 'max_connections', 'unknown'),
                    "created_connections": getattr(self.pool, '_created_connections', 'unknown'),
                    "available_connections": getattr(self.pool, '_available_connections', 'unknown'),
                })
            except Exception as e:
                logger.debug(f"获取连接池详细信息失败: {e}")
                info["pool_status"] = "connected"
        
        return info


# 全局Redis管理器实例
redis_manager = RedisManager()
