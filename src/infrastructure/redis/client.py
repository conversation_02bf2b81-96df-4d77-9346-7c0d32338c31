"""
Redis客户端操作类
提供常用的Redis读写操作方法
"""

from typing import Optional, Any, Dict, List, Union
import json
from datetime import timedelta
from loguru import logger

from .connection import redis_manager


class RedisClient:
    """Redis客户端操作类"""
    
    def __init__(self):
        self._redis = None
    
    @property
    def redis(self):
        """获取Redis客户端实例"""
        if self._redis is None:
            self._redis = redis_manager.get_client()
        return self._redis
    
    # ==================== 字符串操作 ====================
    
    def set(self, key: str, value: Any, ex: Optional[Union[int, timedelta]] = None) -> bool:
        """
        设置键值对
        
        Args:
            key: 键名
            value: 值（会自动序列化为JSON）
            ex: 过期时间（秒或timedelta对象）
        
        Returns:
            bool: 操作是否成功
        """
        try:
            # 如果值不是字符串，则序列化为JSON
            if not isinstance(value, str):
                value = json.dumps(value, ensure_ascii=False)
            
            result = self.redis.set(key, value, ex=ex)
            # logger.debug(f"Redis SET: {key} = {value[:100]}...")  # 注释掉以提高性能
            return result
        except Exception as e:
            logger.error(f"Redis SET失败: {key}, error: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取键值
        
        Args:
            key: 键名
            default: 默认值
        
        Returns:
            Any: 键对应的值（会尝试JSON反序列化）
        """
        try:
            value = self.redis.get(key)
            if value is None:
                return default
            
            # 尝试JSON反序列化
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                # 如果不是JSON格式，直接返回字符串
                return value
                
        except Exception as e:
            logger.error(f"Redis GET失败: {key}, error: {e}")
            return default
    
    def delete(self, *keys: str) -> int:
        """
        删除键
        
        Args:
            *keys: 要删除的键名
        
        Returns:
            int: 删除的键数量
        """
        try:
            result = self.redis.delete(*keys)
            logger.debug(f"Redis DELETE: {keys}")
            return result
        except Exception as e:
            logger.error(f"Redis DELETE失败: {keys}, error: {e}")
            return 0
    
    def exists(self, key: str) -> bool:
        """
        检查键是否存在
        
        Args:
            key: 键名
        
        Returns:
            bool: 键是否存在
        """
        try:
            return bool(self.redis.exists(key))
        except Exception as e:
            logger.error(f"Redis EXISTS失败: {key}, error: {e}")
            return False
    
    def expire(self, key: str, time: Union[int, timedelta]) -> bool:
        """
        设置键的过期时间
        
        Args:
            key: 键名
            time: 过期时间（秒或timedelta对象）
        
        Returns:
            bool: 操作是否成功
        """
        try:
            result = self.redis.expire(key, time)
            logger.debug(f"Redis EXPIRE: {key}, time: {time}")
            return result
        except Exception as e:
            logger.error(f"Redis EXPIRE失败: {key}, error: {e}")
            return False
    
    def ttl(self, key: str) -> int:
        """
        获取键的剩余生存时间
        
        Args:
            key: 键名
        
        Returns:
            int: 剩余秒数，-1表示永不过期，-2表示键不存在
        """
        try:
            return self.redis.ttl(key)
        except Exception as e:
            logger.error(f"Redis TTL失败: {key}, error: {e}")
            return -2
    
    # ==================== 哈希操作 ====================
    
    def hset(self, name: str, mapping: Dict[str, Any]) -> int:
        """
        设置哈希字段
        
        Args:
            name: 哈希名
            mapping: 字段映射字典
        
        Returns:
            int: 新增的字段数量
        """
        try:
            # 序列化值
            serialized_mapping = {}
            for key, value in mapping.items():
                if not isinstance(value, str):
                    serialized_mapping[key] = json.dumps(value, ensure_ascii=False)
                else:
                    serialized_mapping[key] = value
            
            result = self.redis.hset(name, mapping=serialized_mapping)
            logger.debug(f"Redis HSET: {name}, fields: {list(mapping.keys())}")
            return result
        except Exception as e:
            logger.error(f"Redis HSET失败: {name}, error: {e}")
            return 0
    
    def hget(self, name: str, key: str, default: Any = None) -> Any:
        """
        获取哈希字段值
        
        Args:
            name: 哈希名
            key: 字段名
            default: 默认值
        
        Returns:
            Any: 字段值（会尝试JSON反序列化）
        """
        try:
            value = self.redis.hget(name, key)
            if value is None:
                return default
            
            # 尝试JSON反序列化
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
                
        except Exception as e:
            logger.error(f"Redis HGET失败: {name}.{key}, error: {e}")
            return default
    
    def hgetall(self, name: str) -> Dict[str, Any]:
        """
        获取哈希的所有字段和值
        
        Args:
            name: 哈希名
        
        Returns:
            Dict[str, Any]: 字段映射字典
        """
        try:
            data = self.redis.hgetall(name)
            result = {}
            
            for key, value in data.items():
                # 尝试JSON反序列化
                try:
                    result[key] = json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    result[key] = value
            
            return result
        except Exception as e:
            logger.error(f"Redis HGETALL失败: {name}, error: {e}")
            return {}
    
    def hdel(self, name: str, *keys: str) -> int:
        """
        删除哈希字段
        
        Args:
            name: 哈希名
            *keys: 要删除的字段名
        
        Returns:
            int: 删除的字段数量
        """
        try:
            result = self.redis.hdel(name, *keys)
            logger.debug(f"Redis HDEL: {name}, fields: {keys}")
            return result
        except Exception as e:
            logger.error(f"Redis HDEL失败: {name}, error: {e}")
            return 0
    
    # ==================== 列表操作 ====================
    
    def lpush(self, name: str, *values: Any) -> int:
        """
        从列表左侧推入元素
        
        Args:
            name: 列表名
            *values: 要推入的值
        
        Returns:
            int: 推入后列表的长度
        """
        try:
            # 序列化值
            serialized_values = []
            for value in values:
                if not isinstance(value, str):
                    serialized_values.append(json.dumps(value, ensure_ascii=False))
                else:
                    serialized_values.append(value)
            
            result = self.redis.lpush(name, *serialized_values)
            logger.debug(f"Redis LPUSH: {name}, count: {len(values)}")
            return result
        except Exception as e:
            logger.error(f"Redis LPUSH失败: {name}, error: {e}")
            return 0
    
    def rpush(self, name: str, *values: Any) -> int:
        """
        从列表右侧推入元素
        
        Args:
            name: 列表名
            *values: 要推入的值
        
        Returns:
            int: 推入后列表的长度
        """
        try:
            # 序列化值
            serialized_values = []
            for value in values:
                if not isinstance(value, str):
                    serialized_values.append(json.dumps(value, ensure_ascii=False))
                else:
                    serialized_values.append(value)
            
            result = self.redis.rpush(name, *serialized_values)
            logger.debug(f"Redis RPUSH: {name}, count: {len(values)}")
            return result
        except Exception as e:
            logger.error(f"Redis RPUSH失败: {name}, error: {e}")
            return 0
    
    def lpop(self, name: str, count: Optional[int] = None) -> Any:
        """
        从列表左侧弹出元素
        
        Args:
            name: 列表名
            count: 弹出的元素数量
        
        Returns:
            Any: 弹出的元素（会尝试JSON反序列化）
        """
        try:
            if count is None:
                value = self.redis.lpop(name)
                if value is None:
                    return None
                
                # 尝试JSON反序列化
                try:
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    return value
            else:
                values = self.redis.lpop(name, count)
                result = []
                for value in values:
                    try:
                        result.append(json.loads(value))
                    except (json.JSONDecodeError, TypeError):
                        result.append(value)
                return result
                
        except Exception as e:
            logger.error(f"Redis LPOP失败: {name}, error: {e}")
            return None if count is None else []
    
    def rpop(self, name: str, count: Optional[int] = None) -> Any:
        """
        从列表右侧弹出元素
        
        Args:
            name: 列表名
            count: 弹出的元素数量
        
        Returns:
            Any: 弹出的元素（会尝试JSON反序列化）
        """
        try:
            if count is None:
                value = self.redis.rpop(name)
                if value is None:
                    return None
                
                # 尝试JSON反序列化
                try:
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    return value
            else:
                values = self.redis.rpop(name, count)
                result = []
                for value in values:
                    try:
                        result.append(json.loads(value))
                    except (json.JSONDecodeError, TypeError):
                        result.append(value)
                return result
                
        except Exception as e:
            logger.error(f"Redis RPOP失败: {name}, error: {e}")
            return None if count is None else []
    
    def llen(self, name: str) -> int:
        """
        获取列表长度
        
        Args:
            name: 列表名
        
        Returns:
            int: 列表长度
        """
        try:
            return self.redis.llen(name)
        except Exception as e:
            logger.error(f"Redis LLEN失败: {name}, error: {e}")
            return 0
    
    # ==================== 集合操作 ====================
    
    def sadd(self, name: str, *values: Any) -> int:
        """
        向集合添加元素
        
        Args:
            name: 集合名
            *values: 要添加的值
        
        Returns:
            int: 新增的元素数量
        """
        try:
            # 序列化值
            serialized_values = []
            for value in values:
                if not isinstance(value, str):
                    serialized_values.append(json.dumps(value, ensure_ascii=False))
                else:
                    serialized_values.append(value)
            
            result = self.redis.sadd(name, *serialized_values)
            logger.debug(f"Redis SADD: {name}, count: {len(values)}")
            return result
        except Exception as e:
            logger.error(f"Redis SADD失败: {name}, error: {e}")
            return 0
    
    def srem(self, name: str, *values: Any) -> int:
        """
        从集合移除元素
        
        Args:
            name: 集合名
            *values: 要移除的值
        
        Returns:
            int: 移除的元素数量
        """
        try:
            # 序列化值
            serialized_values = []
            for value in values:
                if not isinstance(value, str):
                    serialized_values.append(json.dumps(value, ensure_ascii=False))
                else:
                    serialized_values.append(value)
            
            result = self.redis.srem(name, *serialized_values)
            logger.debug(f"Redis SREM: {name}, count: {len(values)}")
            return result
        except Exception as e:
            logger.error(f"Redis SREM失败: {name}, error: {e}")
            return 0
    
    def smembers(self, name: str) -> set:
        """
        获取集合的所有成员
        
        Args:
            name: 集合名
        
        Returns:
            set: 集合成员
        """
        try:
            values = self.redis.smembers(name)
            result = set()
            
            for value in values:
                # 尝试JSON反序列化
                try:
                    result.add(json.loads(value))
                except (json.JSONDecodeError, TypeError):
                    result.add(value)
            
            return result
        except Exception as e:
            logger.error(f"Redis SMEMBERS失败: {name}, error: {e}")
            return set()
    
    def scard(self, name: str) -> int:
        """
        获取集合大小
        
        Args:
            name: 集合名
        
        Returns:
            int: 集合大小
        """
        try:
            return self.redis.scard(name)
        except Exception as e:
            logger.error(f"Redis SCARD失败: {name}, error: {e}")
            return 0
    
    # ==================== 通用操作 ====================
    
    def keys(self, pattern: str = "*") -> List[str]:
        """
        获取匹配模式的键列表
        
        Args:
            pattern: 匹配模式
        
        Returns:
            List[str]: 键列表
        """
        try:
            return self.redis.keys(pattern)
        except Exception as e:
            logger.error(f"Redis KEYS失败: {pattern}, error: {e}")
            return []
    
    def flushdb(self) -> bool:
        """
        清空当前数据库
        
        Returns:
            bool: 操作是否成功
        """
        try:
            result = self.redis.flushdb()
            logger.warning("Redis FLUSHDB: 已清空当前数据库")
            return result
        except Exception as e:
            logger.error(f"Redis FLUSHDB失败: {e}")
            return False
    
    def ping(self) -> bool:
        """
        测试连接
        
        Returns:
            bool: 连接是否正常
        """
        try:
            result = self.redis.ping()
            return result is True
        except Exception as e:
            logger.error(f"Redis PING失败: {e}")
            return False
