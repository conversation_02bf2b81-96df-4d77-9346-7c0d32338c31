"""
早期日志拦截模块
在项目启动时就被导入，确保在第三方库配置日志之前就设置好拦截器
"""
import logging
import sys
from typing import Optional

class EarlyLoggingInterceptor:
    """早期日志拦截器"""
    
    def __init__(self):
        self.original_handlers = {}
        self.intercept_handler = None
        self._setup_intercept()
    
    def _setup_intercept(self):
        """设置拦截器"""
        # 创建一个简单的拦截处理器
        class SimpleInterceptHandler(logging.Handler):
            def emit(self, record):
                # 简单地将日志输出到stderr，避免重复
                # 这里我们暂时禁用所有第三方库的日志输出
                pass
        
        self.intercept_handler = SimpleInterceptHandler()
        
        # 立即设置到root logger
        root_logger = logging.getLogger()
        root_logger.handlers.clear()
        root_logger.addHandler(self.intercept_handler)
        root_logger.setLevel(logging.DEBUG)
        
        # 设置基础配置
        logging.basicConfig(
            handlers=[self.intercept_handler],
            level=logging.DEBUG,
            force=True
        )
    
    def restore_logging(self):
        """恢复日志系统（当loguru准备好时调用）"""
        if self.intercept_handler:
            root_logger = logging.getLogger()
            root_logger.removeHandler(self.intercept_handler)

# 创建全局拦截器实例
early_interceptor = EarlyLoggingInterceptor() 