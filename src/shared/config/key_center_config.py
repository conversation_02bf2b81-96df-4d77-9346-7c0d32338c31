import os
import traceback
from loguru import logger

from keycenter import kc_crypto_service as kcs
from keycenter import kc_key_sync as kks

# 使用keycenter进行密钥管理
# KC环境地址一览：https://yuque.antfin.com/mkf08w/cvdlr6/environment
# 日常使用域名:
DAILY_HOST = "https://daily-keycenter.alibaba.net/keycenter"
# 预发使用域名:
PRE_HOST = "http://pre-keycenter-service-internal.alibaba-inc.com/keycenter"
# 线上使用域名:
ONLINE_HOST = "http://keycenter-service.alibaba-inc.com/keycenter"
# 应用发放码：https://keycenter.alibaba.net/#/app/my
KC_APP_CODE = "567396056591492391061feae066cad7"  # daily
# 密钥名称：https://keycenter.alibaba.net/#/key/my
KEY_NAME = "wuying-alpha-service_aone_key"


class DecryptError(Exception):
    pass


class KeyCenterConfig:
    def __init__(self):
        # 优先从Dynaconf配置中读取，如果没有则使用环境变量或默认值
        try:
            from .environments import env_manager
            config = env_manager.get_config()
            self.server_host = config.kc_server or os.getenv("KC_SERVER", DAILY_HOST)
            self.publish_num = config.kc_app_code or os.getenv("KC_APP_CODE", KC_APP_CODE)
            self.key_name = config.kc_key_name or os.getenv("KC_KEY_NAME", KEY_NAME)
        except Exception:
            # 如果无法读取Dynaconf配置，回退到环境变量
            self.server_host = os.getenv("KC_SERVER", DAILY_HOST)
            self.publish_num = os.getenv("KC_APP_CODE", KC_APP_CODE)
            self.key_name = os.getenv("KC_KEY_NAME", KEY_NAME)


class KC:
    def __init__(self):
        self.cfg = KeyCenterConfig()
        self._init_keycenter()  # KC初始化
    
    def _init_keycenter(self):
        """初始化KeyCenter服务"""
        try:
            kks.initialize(self.cfg.server_host, self.cfg.publish_num)
            kcs.init_service(self.cfg.server_host, self.cfg.publish_num)
            logger.info(f"KC初始化已完成, keyname:{self.cfg.key_name}, server_host:{self.cfg.server_host}, publish_num:{self.cfg.publish_num}")
        except Exception as e:
            logger.warning(f"KeyCenter初始化失败: {e}")
            raise



    def kc_decrypt(self, encrypted) -> str:
        """
        KC解密方法

        Args:
            encrypted (str): 需要解密的密文

        Returns:
            str: 解密后的明文

        Raises:
            DecryptError: 解密失败时抛出
        """
        if not encrypted:
            logger.warning("收到空密文")
            return encrypted

        try:
            logger.debug(f"开始解密密文: {encrypted}")
            decrypted = kcs.decrypt(self.cfg.key_name, encrypted)
            if not decrypted:
                raise DecryptError("解密结果为空")

            logger.debug("解密成功")
            return decrypted

        except Exception as e:
            error_msg = f"解密失败,返回原值: encrypted={encrypted}, error={str(e)}"
            logger.error(error_msg)
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise DecryptError(error_msg) from e

    def kc_encrypt(self, plaintext):
        """
        KC加密
        plaintext: 待加密明文
        """
        return kcs.encrypt(self.cfg.key_name, plaintext)


kc = KC()