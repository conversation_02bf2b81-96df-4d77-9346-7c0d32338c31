import json
import os
from typing import Optional
from loguru import logger

DEFAULT_ENDPOINT = "http://jmenv.tbsite.net:8080/diamond-server/diamond"
DATA_ID = "alpha-service:application"
DEFAULT_GROUP = "DEFAULT_GROUP"

class DynamicConfig:
    """Diamond配置中心客户端"""
    def __init__(self):
        # 延迟导入以避免循环依赖
        from .environments import env_manager
        env_config = env_manager.get_config()
        
        self.endpoint = env_config.diamond_endpoint
        self.data_id = env_config.diamond_data_id
        self.config = None
        self.client = None
        self._init_client()

    def _init_client(self):
        """初始化Nacos客户端"""
        try:
            import nacos
            self.client = nacos.NacosClient(endpoint=self.endpoint, namespace="")
            self.get_config()
            print(f"Diamond配置中心连接成功: {self.endpoint}")
        except ImportError:
            print("警告: nacos-sdk-python未安装，配置中心功能不可用")
        except Exception as e:
            print(f"Diamond配置中心连接失败: {e}")

    def get_config(self) -> Optional[str]:
        """获取配置"""
        if not self.client:
            return None
        
        try:
            self.config = self.client.get_config(self.data_id, DEFAULT_GROUP)
            return self.config
        except Exception as e:
            print(f"获取配置失败: {e}")
            return None

    def get_config_by_key(self, key: str) -> Optional[str]:
        """根据key获取配置值"""
        if self.config is None:
            return None
        
        try:
            config_dict = json.loads(self.config)
            return config_dict.get(key)
        except json.JSONDecodeError:
            print("配置格式不是有效的JSON")
            return None
        except Exception as e:
            print(f"解析配置失败: {e}")
            return None

    def add_config_listener(self, callback):
        """添加配置监听器"""
        if not self.client:
            return
        
        try:
            self.client.add_config_listener(self.data_id, DEFAULT_GROUP, callback)
        except Exception as e:
            print(f"添加配置监听器失败: {e}")

# 全局实例
dynamicConfig = DynamicConfig() 