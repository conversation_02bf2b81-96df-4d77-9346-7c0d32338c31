"""
配置兼容层 - 为保持向后兼容而提供的简单接口
推荐直接使用: from .environments import env_manager
"""

from loguru import logger
from .environments import env_manager
from .diamond_config import dynamicConfig

class Settings:
    """简化的配置访问器 - 提供向后兼容性"""
    
    def __init__(self):
        self._config = env_manager.get_config()
    
    @property
    def environment(self) -> str:
        """当前环境"""
        return env_manager.current_env.value
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return env_manager.is_production()
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return env_manager.is_development()

    def get_environment_info(self) -> dict:
        """获取环境信息"""
        env_info = env_manager.get_environment_info()
        config = env_manager.get_config()
        
        return {
            **env_info,
            "api_debug": config.api_debug,
            "log_level": config.log_level,
            "database_name": config.db_mysql_name
        }

    def load_from_diamond(self):
        """从Diamond配置中心加载配置"""
        if dynamicConfig.config:
            try:
                logger.info("从Diamond配置中心加载配置成功")
            except Exception as e:
                logger.error(f"从Diamond配置中心加载配置失败: {e}")

# 创建全局配置实例 - 保持向后兼容
settings = Settings()

# 尝试从配置中心加载配置
settings.load_from_diamond() 

# 推荐的使用方式 - 直接导出 env_manager
__all__ = ['settings', 'env_manager'] 