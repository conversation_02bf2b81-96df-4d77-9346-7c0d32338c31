"""
阿里云无AK认证全局初始化模块
在应用启动时调用一次，为所有客户端提供无AK认证支持
"""
import os
from loguru import logger
from typing import Optional

# 全局初始化状态
_akless_initialized = False
_initialization_error = None


def init_akless_auth() -> bool:
    """
    初始化阿里云无AK认证环境
    
    Returns:
        bool: 初始化是否成功
    """
    global _akless_initialized, _initialization_error
    
    if _akless_initialized:
        logger.info("无AK认证已经初始化，跳过重复初始化")
        return True
    
    try:
        from aliyunaklesscredprovider.core import AklessCredproviderFactory
        from aliyunaklesscredprovider.core.config import CredentialProviderConfig
        
        # 从配置中读取参数
        try:
            from ..config.environments import env_manager
            config = env_manager.get_config()
            
            ram_role_arn = config.ram_role_arn
            region_id = config.region_id
            app_group = getattr(config, 'app_group', '')
            akless_env = getattr(config, 'akless_env', 'testing')
            
            if not ram_role_arn:
                raise ValueError("配置中缺少ram_role_arn，无法初始化无AK认证")
                
            logger.info(f"开始初始化无AK认证环境 - RAM角色: {ram_role_arn}, 环境: {env_manager.current_env.value}")
            
        except Exception as e:
            logger.error(f"读取无AK认证配置失败: {e}")
            _initialization_error = f"读取配置失败: {e}"
            return False
        
        # 使用与OSS客户端相同的参数进行初始化
        try:
            config_obj = CredentialProviderConfig(
                ram_role_arn=ram_role_arn,
                region_id=region_id,
                app_name="wuying-alpha-service",  # 参考OSS客户端的值
                app_group=app_group or "",        # 参考OSS客户端的值
                app_env=akless_env
            )
            
            # 初始化环境（这会设置全局状态）
            AklessCredproviderFactory.get_opensdk_v2_credential_with_config(config_obj)
            
            _akless_initialized = True
            logger.info("✅ 无AK认证环境初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"无AK认证环境初始化失败: {e}")
            _initialization_error = f"初始化失败: {e}"
            return False
            
    except ImportError as e:
        logger.error(f"无AK认证依赖包导入失败: {e}")
        _initialization_error = f"依赖包导入失败: {e}"
        return False


def get_akless_credential(ram_role_arn: str):
    """
    获取无AK凭证（前提是已经初始化过环境）
    
    Args:
        ram_role_arn: RAM角色ARN
        
    Returns:
        凭证对象
        
    Raises:
        RuntimeError: 如果环境未初始化或初始化失败
    """
    global _akless_initialized, _initialization_error
    
    if not _akless_initialized:
        if _initialization_error:
            raise RuntimeError(f"无AK认证环境初始化失败: {_initialization_error}")
        else:
            raise RuntimeError("无AK认证环境未初始化，请先调用 init_akless_auth()")
    
    try:
        from aliyunaklesscredprovider.core import AklessCredproviderFactory
        return AklessCredproviderFactory.get_opensdk_v2_credential(ram_role_arn)
    except Exception as e:
        logger.error(f"获取无AK凭证失败: {e}")
        raise RuntimeError(f"获取无AK凭证失败: {e}")


def is_akless_initialized() -> bool:
    """
    检查无AK认证是否已初始化
    
    Returns:
        bool: 是否已初始化
    """
    return _akless_initialized


def get_initialization_error() -> Optional[str]:
    """
    获取初始化错误信息
    
    Returns:
        Optional[str]: 错误信息，如果没有错误则返回None
    """
    return _initialization_error


def reset_akless_auth():
    """
    重置无AK认证状态（主要用于测试）
    """
    global _akless_initialized, _initialization_error
    _akless_initialized = False
    _initialization_error = None
    logger.info("无AK认证状态已重置")
