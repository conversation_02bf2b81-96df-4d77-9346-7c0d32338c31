"""
应用启动初始化模块
在应用启动时执行必要的初始化操作
"""
from loguru import logger
from .auth import init_akless_auth


def initialize_app():
    """
    应用启动时的初始化操作
    """
    logger.info("🚀 开始应用初始化...")
    
    # 初始化无AK认证环境
    logger.info("初始化阿里云无AK认证环境...")
    akless_success = init_akless_auth()
    
    if akless_success:
        logger.info("✅ 阿里云无AK认证环境初始化成功")
    else:
        logger.warning("⚠️ 阿里云无AK认证环境初始化失败，将回退到AccessKey认证")
    
    logger.info("🎉 应用初始化完成")
    return {
        "akless_auth": akless_success
    }
