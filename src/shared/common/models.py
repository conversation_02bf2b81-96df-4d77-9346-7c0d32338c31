from pydantic import BaseModel, Field
from typing import Optional, Any, Dict
from datetime import datetime

class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(True, description="请求是否成功")
    message: str = Field("操作成功", description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")

class ErrorResponse(BaseResponse):
    """错误响应模型"""
    success: bool = Field(False, description="请求失败")
    error_code: Optional[str] = Field(None, description="错误代码")
    error_detail: Optional[Dict[str, Any]] = Field(None, description="错误详情")

class PaginationModel(BaseModel):
    """分页模型"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=100, description="每页数量")
    total: Optional[int] = Field(None, description="总数量")
    total_pages: Optional[int] = Field(None, description="总页数")

class HealthStatus(BaseModel):
    """健康状态模型"""
    status: str = Field("healthy", description="服务状态")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    version: str = Field("0.1.0", description="服务版本")
    uptime: Optional[float] = Field(None, description="运行时间(秒)")
    dependencies: Optional[Dict[str, str]] = Field(None, description="依赖服务状态") 