"""
API模型
请求和响应的数据模型定义
"""

from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum




class QueryHistoryResponse(BaseModel):
    """查询历史响应"""
    session_id: str
    ali_uid: str
    agent_id: str
    title: str
    status: str
    gmt_create: datetime
    gmt_modified: datetime
    total_rounds: int
    rounds: List[Dict[str, Any]]  # 序列化后的Round数据


class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    status: str
    timestamp: str
    sessions: Dict[str, int]
    version: str


class CleanupResponse(BaseModel):
    """清理响应"""
    message: str
    cleaned_count: int
    remaining_count: int


class ExecutionEnvironmentRequest(BaseModel):
    """获取执行环境请求"""
    login_token: str = Field(..., description="登录令牌")


class ExecutionEnvironmentResponse(BaseModel):
    """获取执行环境响应"""
    environments: List[Dict[str, str]]
    total: int


class RenameSessionRequest(BaseModel):
    """重命名会话请求"""
    session_id: str = Field(..., description="会话ID")
    new_title: str = Field(..., description="新的会话标题")


class RenameSessionResponse(BaseModel):
    """重命名会话响应"""
    session_id: str = Field(..., description="会话ID")
    new_title: str = Field(..., description="新的会话标题")


class DeleteSessionRequest(BaseModel):
    """删除会话请求"""
    session_id: str = Field(..., description="会话ID")


class DeleteSessionResponse(BaseModel):
    """删除会话响应"""
    session_id: str = Field(..., description="已删除的会话ID")


class ArtifactFile(BaseModel):
    """AI制品文件信息"""
    file_name: str = Field(..., description="文件名")
    file_type: str = Field(..., description="文件类型")
    file_size: int = Field(..., description="文件大小（字节）")
    file_size_formatted: str = Field(..., description="格式化文件大小")
    created_time: datetime = Field(..., description="创建时间")
    download_url: str = Field(..., description="下载URL")
    preview_url: Optional[str] = Field(None, description="预览URL")


class SessionArtifactsResponse(BaseModel):
    """查询AI制品响应"""
    session_id: str = Field(..., description="会话ID")
    result_files: List[ArtifactFile] = Field(..., description="结果制品文件列表")
    process_files: List[ArtifactFile] = Field(..., description="过程制品文件列表")
    total_result_files: int = Field(..., description="结果制品文件总数")
    total_process_files: int = Field(..., description="过程制品文件总数")


# ==================== 会话相关模型 ====================

class SessionInfo(BaseModel):
    """会话信息"""
    sessionId: str = Field(..., description="会话ID")
    title: str = Field(..., description="会话标题")
    agentId: str = Field(..., description="智能体ID")
    aliUid: int = Field(..., description="阿里云用户ID")
    wyId: Optional[str] = Field(None, description="无影用户ID")
    status: str = Field(..., description="会话状态")
    gmtCreate: str = Field(..., description="创建时间")
    gmtModified: str = Field(..., description="修改时间")
    totalRounds: int = Field(..., description="总对话轮数")
    lastUserPrompt: str = Field(..., description="最后一次用户输入")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class SessionListData(BaseModel):
    """会话列表数据"""
    sessions: List[SessionInfo] = Field(..., description="会话列表")
    total: int = Field(..., description="总会话数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")
    has_more: bool = Field(..., description="是否还有更多数据")


class SessionListResponse(BaseModel):
    """会话列表响应"""
    code: int = Field(..., description="响应码")
    msg: str = Field(..., description="响应消息")
    data: SessionListData = Field(..., description="响应数据")


# ==================== 会话操作相关模型 ====================

class SessionRenameRequest(BaseModel):
    """重命名会话请求"""
    session_id: str = Field(..., description="会话ID")
    new_title: str = Field(..., description="新的会话标题", min_length=1, max_length=200)


class SessionRenameData(BaseModel):
    """重命名会话响应数据"""
    session_id: str = Field(..., description="会话ID")
    new_title: str = Field(..., description="新的会话标题")


class SessionRenameResponse(BaseModel):
    """重命名会话响应"""
    code: int = Field(..., description="响应码")
    msg: str = Field(..., description="响应消息")
    data: SessionRenameData = Field(..., description="响应数据")


class SessionDeleteRequest(BaseModel):
    """删除会话请求"""
    session_id: str = Field(..., description="会话ID")


class SessionDeleteData(BaseModel):
    """删除会话响应数据"""
    session_id: str = Field(..., description="已删除的会话ID")


class SessionDeleteResponse(BaseModel):
    """删除会话响应"""
    code: int = Field(..., description="响应码")
    msg: str = Field(..., description="响应消息")
    data: SessionDeleteData = Field(..., description="响应数据")


# ==================== 枚举定义 ====================

class ResourceType(str, Enum):
    """资源类型枚举"""
    FILE = "file"
    KNOWLEDGE_BASE = "knowledge_base"
    SESSION = "session"


# ==================== 预签名上传相关模型 ====================

class FileInfo(BaseModel):
    """文件信息"""
    file_name: str = Field(..., alias="FileName", description="文件名")
    file_size: int = Field(..., alias="FileSize", description="文件大小（字节）")
    file_type: str = Field(..., alias="FileType", description="文件类型/扩展名")

    class Config:
        allow_population_by_field_name = True


class PresignedUploadRequest(BaseModel):
    """预签名上传请求"""
    session_id: Optional[str] = Field(None, description="会话ID，为空则创建新会话")
    file_info: FileInfo = Field(..., description="文件信息")
    file_type: Optional[str] = Field("sessionFile", description="文件类型标识")
    agent_id: Optional[str] = Field(None, description="AgentId")


class PresignedUploadResponse(BaseModel):
    """预签名上传响应"""
    file_id: str = Field(..., description="文件ID")
    session_id: str = Field(..., description="会话ID")
    upload_url: str = Field(..., description="预签名上传URL")
    headers: Optional[Dict[str, str]] = Field(None, description="上传时需要的额外头部")


class ConfirmUploadRequest(BaseModel):
    """确认上传完成请求"""
    file_id: str = Field(..., description="文件ID")
    etag: Optional[str] = Field(None, description="文件ETag（可选）")


class ConfirmUploadResponse(BaseModel):
    """确认上传完成响应"""
    file_id: str = Field(..., description="文件ID")
    status: str = Field(..., description="文件状态")
    message: str = Field(..., description="响应消息")


# ==================== 用户设置相关模型 ====================

class UpdateUserSettingRequest(BaseModel):
    """更新用户设置请求"""
    desktop_id: Optional[str] = Field(None, description="优先使用的桌面ID")
    model: Optional[str] = Field(None, description="优先使用的模型")


class CurrentSetting(BaseModel):
    """当前用户设置"""
    desktop_id: Optional[str] = Field(None, description="优先使用的桌面ID")
    model: Optional[str] = Field(None, description="优先使用的模型")


class UserSettingData(BaseModel):
    """用户设置数据"""
    current_setting: CurrentSetting = Field(..., description="当前用户设置")
    available_environments: List[Dict[str, str]] = Field(..., description="可用环境列表")
    available_models: List[str] = Field(..., description="可用模型列表")
    version: str = Field(..., description="版本信息")


class UserSettingResponse(BaseModel):
    """用户设置响应"""
    code: int = Field(..., description="响应码")
    msg: str = Field(..., description="响应消息")
    data: UserSettingData = Field(..., description="响应数据")


# ==================== 会话相关模型 ====================

class SessionResource(BaseModel):
    """会话资源"""
    model_config = ConfigDict(populate_by_name=True)

    type: ResourceType = Field(..., alias="Type", description="资源类型：file或knowledge_base")
    resource_id: str = Field(..., alias="ResourceId", description="资源ID：文件ID或知识库ID")


class SendMessageRequest(BaseModel):
    """发送消息请求"""
    model_config = ConfigDict(populate_by_name=True)

    session_id: Optional[str] = Field(None, alias="SessionId", description="会话ID，为空则创建新会话")
    prompt: str = Field(..., alias="Prompt", description="用户请求内容")
    agent_id: str = Field(..., alias="AgentId", description="Agent ID")
    desktop_id: Optional[str] = Field(None, alias="DesktopId", description="桌面ID")
    auth_code: Optional[str] = Field(None, alias="AuthCode", description="AuthCode")
    resources: Optional[List[SessionResource]] = Field(None, alias="Resources", description="对话资源列表")


class SendMessageResponseData(BaseModel):
    """发送消息响应数据"""
    model_config = ConfigDict(populate_by_name=True)

    session_id: str = Field(..., alias="SessionId", description="会话ID")
    round_id: str = Field(..., alias="RoundId", description="轮次ID")


class SendMessageResponse(BaseModel):
    """发送消息响应"""
    model_config = ConfigDict(populate_by_name=True)

    code: int = Field(..., alias="Code", description="状态码，200成功")
    msg: str = Field(..., alias="Msg", description="消息")
    data: Optional[SendMessageResponseData] = Field(None, alias="Data", description="响应数据")


# ==================== 消息反馈相关模型 ====================

class MessageFeedbackRequest(BaseModel):
    """消息反馈请求"""
    model_config = ConfigDict(populate_by_name=True)

    session_id: str = Field(..., alias="SessionId", description="会话ID")
    message_id: str = Field(..., alias="MessageId", description="消息ID")
    feedback: Optional[str] = Field(None, alias="Feedback", description="反馈信息: like, dislike, 或 null 表示取消反馈")


class MessageFeedbackResponseData(BaseModel):
    """消息反馈响应数据"""
    model_config = ConfigDict(populate_by_name=True)

    session_id: str = Field(..., alias="SessionId", description="会话ID")
    message_id: str = Field(..., alias="MessageId", description="消息ID")
    feedback: Optional[str] = Field(None, alias="Feedback", description="反馈信息")
    success: bool = Field(..., alias="Success", description="是否设置成功")


class MessageFeedbackResponse(BaseModel):
    """消息反馈响应"""
    model_config = ConfigDict(populate_by_name=True)

    code: int = Field(..., alias="Code", description="状态码，200成功")
    msg: str = Field(..., alias="Msg", description="消息")
    data: Optional[MessageFeedbackResponseData] = Field(None, alias="Data", description="响应数据")


