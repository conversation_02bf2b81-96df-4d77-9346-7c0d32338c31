# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from alibabacloud_tea_openapi.client import Client as OpenApiClient 
from alibabacloud_tea_openapi import utils_models as open_api_util_models 
from darabonba.core import <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON> 
from alibabacloud_tea_openapi.utils import Utils 
from alibabacloud_wuyingaiinner20250718 import models as main_models 
from darabonba.runtime import RuntimeOptions 
from typing import Dict


"""
"""
class Client(OpenApiClient):

    def __init__(
        self,
        config: open_api_util_models.Config,
    ):
        super().__init__(config)
        self._endpoint_rule = ''
        self.check_config(config)
        self._endpoint = self.get_endpoint('wuyingaiinner', self._region_id, self._endpoint_rule, self._network, self._suffix, self._endpoint_map, self._endpoint)

    def get_endpoint(
        self,
        product_id: str,
        region_id: str,
        endpoint_rule: str,
        network: str,
        suffix: str,
        endpoint_map: Dict[str, str],
        endpoint: str,
    ) -> str:
        if not DaraCore.is_null(endpoint):
            return endpoint
        if not DaraCore.is_null(endpoint_map) and not DaraCore.is_null(endpoint_map.get(region_id)):
            return endpoint_map.get(region_id)
        return Utils.get_endpoint_rules(product_id, region_id, endpoint_rule, network, suffix)

    def verify_login_token_with_options(
        self,
        request: main_models.VerifyLoginTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.VerifyLoginTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.region_id):
            query['RegionId'] = request.region_id
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'VerifyLoginToken',
            version = '2025-07-18',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.VerifyLoginTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def verify_login_token_with_options_async(
        self,
        request: main_models.VerifyLoginTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.VerifyLoginTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.region_id):
            query['RegionId'] = request.region_id
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'VerifyLoginToken',
            version = '2025-07-18',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.VerifyLoginTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def verify_login_token(
        self,
        request: main_models.VerifyLoginTokenRequest,
    ) -> main_models.VerifyLoginTokenResponse:
        runtime = RuntimeOptions()
        return self.verify_login_token_with_options(request, runtime)

    async def verify_login_token_async(
        self,
        request: main_models.VerifyLoginTokenRequest,
    ) -> main_models.VerifyLoginTokenResponse:
        runtime = RuntimeOptions()
        return await self.verify_login_token_with_options_async(request, runtime)
