# AppStreamInnerClient - AppStream内部服务客户端

`AppStreamInnerClient` 是对阿里云 `appstream-center-inner-20211212` SDK 的封装，提供简化的接口来访问 AppStream 内部服务，专门用于获取 STS Token。

## 功能特性

- ✅ **简化的接口**: 提供易于使用的 `GetStsToken` 方法封装
- ✅ **同步和异步支持**: 支持同步和异步调用方式
- ✅ **单例模式**: 支持全局单例实例管理
- ✅ **完整的错误处理**: 提供详细的异常信息和日志记录
- ✅ **类型提示**: 完整的 TypeScript 风格类型注解
- ✅ **配置灵活**: 支持自定义端点和超时设置

## 安装和导入

```python
from src.popclients.appstream_inner_client import (
    AppStreamInnerClient,
    AppStreamInnerClientError,
    get_appstream_inner_client
)
```

## 基本使用

### 1. 创建客户端实例

```python
from src.popclients.appstream_inner_client import AppStreamInnerClient

# 创建客户端
client = AppStreamInnerClient(
    access_key_id="your_access_key_id",
    access_key_secret="your_access_key_secret",
    endpoint="appstream-center-inner.aliyuncs.com"  # 可选，有默认值
)
```

### 2. 获取 STS Token（同步）

```python
try:
    response = client.get_sts_token(
        end_user_id="user_001",
        account_type="SIMPLE",
        auto_create_user=True,
        external_user_id="external_001"
    )
    
    if response.body and response.body.token:
        token_info = response.body.token
        print(f"STS Token: {token_info.sts_token}")
        print(f"Session ID: {token_info.session_id}")
        print(f"End User ID: {token_info.end_user_id}")
        print(f"Ali UID: {token_info.ali_uid}")
        print(f"WY ID: {token_info.wy_id}")
    
except AppStreamInnerClientError as e:
    print(f"获取STS Token失败: {e}")
```

### 3. 获取 STS Token（异步）

```python
import asyncio

async def get_token_async():
    try:
        response = await client.get_sts_token_async(
            end_user_id="user_001",
            account_type="SIMPLE",
            auto_create_user=True,
            external_user_id="external_001"
        )
        
        if response.body and response.body.token:
            token_info = response.body.token
            print(f"异步获取STS Token成功: {token_info.sts_token}")
        
    except AppStreamInnerClientError as e:
        print(f"异步获取STS Token失败: {e}")

# 运行异步函数
asyncio.run(get_token_async())
```

## 单例模式使用

```python
from src.popclients.appstream_inner_client import get_appstream_inner_client

# 首次调用需要提供凭证
client = get_appstream_inner_client(
    access_key_id="your_access_key_id",
    access_key_secret="your_access_key_secret"
)

# 后续调用直接获取同一个实例
client2 = get_appstream_inner_client()
assert client is client2  # True
```

## GetStsToken 方法参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `account_type` | `str` | 否 | 账户类型，如 "SIMPLE" |
| `auto_create_user` | `bool` | 否 | 是否自动创建用户 |
| `end_user_id` | `str` | 否 | 终端用户ID |
| `external_user_id` | `str` | 否 | 外部用户ID |
| `policy` | `str` | 否 | 策略 |
| `user_ali_uid` | `int` | 否 | 用户阿里云UID |

## 响应结构

```python
# GetStsTokenResponse 结构
response.body.request_id      # 请求ID
response.body.token.ali_uid   # 阿里云UID
response.body.token.end_user_id    # 终端用户ID
response.body.token.session_id     # 会话ID
response.body.token.sts_token      # STS Token
response.body.token.wy_id          # WY ID
```

## 高级用法

### 服务封装示例

```python
class AppStreamTokenService:
    def __init__(self, access_key_id: str, access_key_secret: str):
        self.client = AppStreamInnerClient(
            access_key_id=access_key_id,
            access_key_secret=access_key_secret
        )
    
    def get_user_token(self, user_id: str) -> dict:
        """获取用户Token的简化方法"""
        response = self.client.get_sts_token(
            end_user_id=user_id,
            account_type="SIMPLE",
            auto_create_user=True
        )
        
        if response.body and response.body.token:
            return {
                "sts_token": response.body.token.sts_token,
                "session_id": response.body.token.session_id,
                "user_id": response.body.token.end_user_id
            }
        return {}

# 使用服务
token_service = AppStreamTokenService("key", "secret")
token_info = token_service.get_user_token("user_001")
```

### 配置自定义超时

```python
client = AppStreamInnerClient(
    access_key_id="your_key",
    access_key_secret="your_secret",
    connect_timeout=10000,  # 10秒连接超时
    read_timeout=30000      # 30秒读取超时
)
```

## 错误处理

```python
from src.popclients.appstream_inner_client import AppStreamInnerClientError

try:
    response = client.get_sts_token(end_user_id="user_001")
except AppStreamInnerClientError as e:
    # 客户端特定错误
    print(f"AppStream客户端错误: {e}")
except Exception as e:
    # 其他异常
    print(f"未知错误: {e}")
```

## 日志记录

客户端使用 `loguru` 进行日志记录：

```python
# 日志级别说明
# DEBUG: 详细的请求/响应信息
# INFO: 客户端初始化和基本操作信息
# ERROR: 错误和异常信息
# SUCCESS: 操作成功信息
```

## 客户端信息

```python
# 获取客户端配置信息
client_info = client.get_client_info()
print(client_info)
# 输出:
# {
#     'access_key_id': 'your_key',
#     'endpoint': 'appstream-center-inner.aliyuncs.com',
#     'connect_timeout': 5000,
#     'read_timeout': 10000
# }
```

## 与 PcInsideClient 的对比

| 特性 | AppStreamInnerClient | PcInsideClient |
|------|---------------------|----------------|
| **主要用途** | 获取 STS Token | 桌面管理 |
| **核心方法** | `get_sts_token()` | `describe_desktops()` |
| **SDK版本** | appstream-center-inner-20211212 | wuying-pc-inside-20221123 |
| **默认端点** | appstream-center-inner.aliyuncs.com | wuying-pc-inside.aliyuncs.com |

## 测试

运行测试以验证客户端功能：

```bash
python test_appstream_inner_client.py
```

## 注意事项

1. **凭证安全**: 请妥善保管访问密钥，不要在代码中硬编码
2. **网络连接**: 确保网络可以访问阿里云服务端点
3. **错误处理**: 建议在生产环境中实现完整的错误处理逻辑
4. **日志级别**: 生产环境建议调整日志级别，避免敏感信息泄露

## 示例代码

完整的使用示例请参考：
- `src/popclients/appstream_inner_client_example.py`
- `test_appstream_inner_client.py`
