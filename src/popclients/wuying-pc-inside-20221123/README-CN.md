[English](README.md) | 简体中文

![](https://aliyunsdk-pages.alicdn.com/icons/AlibabaCloud.svg)

## Alibaba Cloud wuying-pc-inside SDK for Python

## 要求

- Python >= 3.7

## 安装

- **使用 pip 安装(推荐)**

如未安装 `pip`, 请先至pip官网 [pip user guide](https://pip.pypa.io/en/stable/installing/ "pip User Guide") 安装pip .

```bash
# 安装 alibabacloud_wuying_pc_inside20221123
pip install alibabacloud_wuying_pc_inside20221123
```

## 问题

[提交 Issue](https://github.com/aliyun/alibabacloud-python-sdk/issues/new)，不符合指南的问题可能会立即关闭。

## 使用说明

[快速使用](https://github.com/aliyun/alibabacloud-python-sdk/blob/master/docs/0-Usage-CN.md#%E5%BF%AB%E9%80%9F%E4%BD%BF%E7%94%A8)

## 发行说明

每个版本的详细更改记录在[发行说明](https://github.com/aliyun/alibabacloud-python-sdk/blob/master/wuying-pc-inside-20221123/ChangeLog.md)中。

## 相关

- [最新源码](https://github.com/aliyun/alibabacloud-python-sdk/)

## 许可证

[Apache-2.0](http://www.apache.org/licenses/LICENSE-2.0)

Copyright (c) 2009-present, Alibaba Cloud All rights reserved.
