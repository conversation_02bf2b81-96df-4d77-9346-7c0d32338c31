# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class ActivateResourceShrinkRequest(DaraModel):
    def __init__(
        self,
        create_by: str = None,
        desktop_name: str = None,
        desktop_type: str = None,
        expire_time: int = None,
        input_end_user_ids_shrink: str = None,
        input_instance_ids_shrink: str = None,
        order_id: str = None,
        resource_owner_ali_uid: str = None,
        resource_owner_team_id: str = None,
        user_city: str = None,
        user_end_user_id: str = None,
    ):
        self.create_by = create_by
        self.desktop_name = desktop_name
        self.desktop_type = desktop_type
        self.expire_time = expire_time
        self.input_end_user_ids_shrink = input_end_user_ids_shrink
        # This parameter is required.
        self.input_instance_ids_shrink = input_instance_ids_shrink
        # This parameter is required.
        self.order_id = order_id
        self.resource_owner_ali_uid = resource_owner_ali_uid
        self.resource_owner_team_id = resource_owner_team_id
        self.user_city = user_city
        self.user_end_user_id = user_end_user_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.create_by is not None:
            result['CreateBy'] = self.create_by

        if self.desktop_name is not None:
            result['DesktopName'] = self.desktop_name

        if self.desktop_type is not None:
            result['DesktopType'] = self.desktop_type

        if self.expire_time is not None:
            result['ExpireTime'] = self.expire_time

        if self.input_end_user_ids_shrink is not None:
            result['InputEndUserIds'] = self.input_end_user_ids_shrink

        if self.input_instance_ids_shrink is not None:
            result['InputInstanceIds'] = self.input_instance_ids_shrink

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.resource_owner_ali_uid is not None:
            result['ResourceOwnerAliUid'] = self.resource_owner_ali_uid

        if self.resource_owner_team_id is not None:
            result['ResourceOwnerTeamId'] = self.resource_owner_team_id

        if self.user_city is not None:
            result['UserCity'] = self.user_city

        if self.user_end_user_id is not None:
            result['UserEndUserId'] = self.user_end_user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CreateBy') is not None:
            self.create_by = m.get('CreateBy')

        if m.get('DesktopName') is not None:
            self.desktop_name = m.get('DesktopName')

        if m.get('DesktopType') is not None:
            self.desktop_type = m.get('DesktopType')

        if m.get('ExpireTime') is not None:
            self.expire_time = m.get('ExpireTime')

        if m.get('InputEndUserIds') is not None:
            self.input_end_user_ids_shrink = m.get('InputEndUserIds')

        if m.get('InputInstanceIds') is not None:
            self.input_instance_ids_shrink = m.get('InputInstanceIds')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('ResourceOwnerAliUid') is not None:
            self.resource_owner_ali_uid = m.get('ResourceOwnerAliUid')

        if m.get('ResourceOwnerTeamId') is not None:
            self.resource_owner_team_id = m.get('ResourceOwnerTeamId')

        if m.get('UserCity') is not None:
            self.user_city = m.get('UserCity')

        if m.get('UserEndUserId') is not None:
            self.user_end_user_id = m.get('UserEndUserId')

        return self

