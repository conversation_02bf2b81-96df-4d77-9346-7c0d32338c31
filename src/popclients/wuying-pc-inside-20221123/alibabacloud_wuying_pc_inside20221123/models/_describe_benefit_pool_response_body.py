# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeBenefitPoolResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeBenefitPoolResponseBodyData = None,
        err_code: str = None,
        err_msg: str = None,
        request_id: str = None,
    ):
        self.code = code
        self.data = data
        self.err_code = err_code
        self.err_msg = err_msg
        self.request_id = request_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['code'] = self.code

        if self.data is not None:
            result['data'] = self.data.to_map()

        if self.err_code is not None:
            result['errCode'] = self.err_code

        if self.err_msg is not None:
            result['errMsg'] = self.err_msg

        if self.request_id is not None:
            result['requestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('code') is not None:
            self.code = m.get('code')

        if m.get('data') is not None:
            temp_model = main_models.DescribeBenefitPoolResponseBodyData()
            self.data = temp_model.from_map(m.get('data'))

        if m.get('errCode') is not None:
            self.err_code = m.get('errCode')

        if m.get('errMsg') is not None:
            self.err_msg = m.get('errMsg')

        if m.get('requestId') is not None:
            self.request_id = m.get('requestId')

        return self

class DescribeBenefitPoolResponseBodyData(DaraModel):
    def __init__(
        self,
        benefit_infos: List[main_models.DescribeBenefitPoolResponseBodyDataBenefitInfos] = None,
        name: str = None,
        pool_id: str = None,
        status: str = None,
    ):
        self.benefit_infos = benefit_infos
        self.name = name
        self.pool_id = pool_id
        self.status = status

    def validate(self):
        if self.benefit_infos:
            for v1 in self.benefit_infos:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['benefitInfos'] = []
        if self.benefit_infos is not None:
            for k1 in self.benefit_infos:
                result['benefitInfos'].append(k1.to_map() if k1 else None)

        if self.name is not None:
            result['name'] = self.name

        if self.pool_id is not None:
            result['poolId'] = self.pool_id

        if self.status is not None:
            result['status'] = self.status

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.benefit_infos = []
        if m.get('benefitInfos') is not None:
            for k1 in m.get('benefitInfos'):
                temp_model = main_models.DescribeBenefitPoolResponseBodyDataBenefitInfos()
                self.benefit_infos.append(temp_model.from_map(k1))

        if m.get('name') is not None:
            self.name = m.get('name')

        if m.get('poolId') is not None:
            self.pool_id = m.get('poolId')

        if m.get('status') is not None:
            self.status = m.get('status')

        return self

class DescribeBenefitPoolResponseBodyDataBenefitInfos(DaraModel):
    def __init__(
        self,
        available_amount: int = None,
        benefit_id: str = None,
        benefit_type: str = None,
        locked_amount: int = None,
        total_amount: int = None,
    ):
        self.available_amount = available_amount
        self.benefit_id = benefit_id
        self.benefit_type = benefit_type
        self.locked_amount = locked_amount
        self.total_amount = total_amount

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.available_amount is not None:
            result['availableAmount'] = self.available_amount

        if self.benefit_id is not None:
            result['benefitId'] = self.benefit_id

        if self.benefit_type is not None:
            result['benefitType'] = self.benefit_type

        if self.locked_amount is not None:
            result['lockedAmount'] = self.locked_amount

        if self.total_amount is not None:
            result['totalAmount'] = self.total_amount

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('availableAmount') is not None:
            self.available_amount = m.get('availableAmount')

        if m.get('benefitId') is not None:
            self.benefit_id = m.get('benefitId')

        if m.get('benefitType') is not None:
            self.benefit_type = m.get('benefitType')

        if m.get('lockedAmount') is not None:
            self.locked_amount = m.get('lockedAmount')

        if m.get('totalAmount') is not None:
            self.total_amount = m.get('totalAmount')

        return self

