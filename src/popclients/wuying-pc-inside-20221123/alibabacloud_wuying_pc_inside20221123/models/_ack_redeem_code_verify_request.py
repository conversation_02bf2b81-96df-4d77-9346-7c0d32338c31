# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class AckRedeemCodeVerifyRequest(DaraModel):
    def __init__(
        self,
        action: str = None,
        activity_code: str = None,
        activity_id: str = None,
        ali_uid: int = None,
        commit_order_id: str = None,
        current_step_id: str = None,
        login_token: str = None,
        redeem_code: str = None,
        session_id: str = None,
        user_union_id: str = None,
    ):
        self.action = action
        self.activity_code = activity_code
        self.activity_id = activity_id
        self.ali_uid = ali_uid
        self.commit_order_id = commit_order_id
        self.current_step_id = current_step_id
        self.login_token = login_token
        self.redeem_code = redeem_code
        self.session_id = session_id
        self.user_union_id = user_union_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.action is not None:
            result['action'] = self.action

        if self.activity_code is not None:
            result['activityCode'] = self.activity_code

        if self.activity_id is not None:
            result['activityId'] = self.activity_id

        if self.ali_uid is not None:
            result['aliUid'] = self.ali_uid

        if self.commit_order_id is not None:
            result['commitOrderId'] = self.commit_order_id

        if self.current_step_id is not None:
            result['currentStepId'] = self.current_step_id

        if self.login_token is not None:
            result['loginToken'] = self.login_token

        if self.redeem_code is not None:
            result['redeemCode'] = self.redeem_code

        if self.session_id is not None:
            result['sessionId'] = self.session_id

        if self.user_union_id is not None:
            result['userUnionId'] = self.user_union_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('action') is not None:
            self.action = m.get('action')

        if m.get('activityCode') is not None:
            self.activity_code = m.get('activityCode')

        if m.get('activityId') is not None:
            self.activity_id = m.get('activityId')

        if m.get('aliUid') is not None:
            self.ali_uid = m.get('aliUid')

        if m.get('commitOrderId') is not None:
            self.commit_order_id = m.get('commitOrderId')

        if m.get('currentStepId') is not None:
            self.current_step_id = m.get('currentStepId')

        if m.get('loginToken') is not None:
            self.login_token = m.get('loginToken')

        if m.get('redeemCode') is not None:
            self.redeem_code = m.get('redeemCode')

        if m.get('sessionId') is not None:
            self.session_id = m.get('sessionId')

        if m.get('userUnionId') is not None:
            self.user_union_id = m.get('userUnionId')

        return self

