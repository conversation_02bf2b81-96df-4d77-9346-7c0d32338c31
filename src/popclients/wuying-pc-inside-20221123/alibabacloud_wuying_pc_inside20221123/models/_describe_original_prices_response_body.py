# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeOriginalPricesResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        message: str = None,
        price_response: main_models.DescribeOriginalPricesResponseBodyPriceResponse = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.message = message
        self.price_response = price_response
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.price_response:
            self.price_response.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.message is not None:
            result['Message'] = self.message

        if self.price_response is not None:
            result['PriceResponse'] = self.price_response.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('PriceResponse') is not None:
            temp_model = main_models.DescribeOriginalPricesResponseBodyPriceResponse()
            self.price_response = temp_model.from_map(m.get('PriceResponse'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeOriginalPricesResponseBodyPriceResponse(DaraModel):
    def __init__(
        self,
        price_detail_list: List[main_models.DescribeOriginalPricesResponseBodyPriceResponsePriceDetailList] = None,
        summary_price: main_models.DescribeOriginalPricesResponseBodyPriceResponseSummaryPrice = None,
    ):
        self.price_detail_list = price_detail_list
        self.summary_price = summary_price

    def validate(self):
        if self.price_detail_list:
            for v1 in self.price_detail_list:
                 if v1:
                    v1.validate()
        if self.summary_price:
            self.summary_price.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['PriceDetailList'] = []
        if self.price_detail_list is not None:
            for k1 in self.price_detail_list:
                result['PriceDetailList'].append(k1.to_map() if k1 else None)

        if self.summary_price is not None:
            result['SummaryPrice'] = self.summary_price.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.price_detail_list = []
        if m.get('PriceDetailList') is not None:
            for k1 in m.get('PriceDetailList'):
                temp_model = main_models.DescribeOriginalPricesResponseBodyPriceResponsePriceDetailList()
                self.price_detail_list.append(temp_model.from_map(k1))

        if m.get('SummaryPrice') is not None:
            temp_model = main_models.DescribeOriginalPricesResponseBodyPriceResponseSummaryPrice()
            self.summary_price = temp_model.from_map(m.get('SummaryPrice'))

        return self

class DescribeOriginalPricesResponseBodyPriceResponseSummaryPrice(DaraModel):
    def __init__(
        self,
        currency: str = None,
        original_price: float = None,
    ):
        self.currency = currency
        self.original_price = original_price

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.currency is not None:
            result['Currency'] = self.currency

        if self.original_price is not None:
            result['OriginalPrice'] = self.original_price

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Currency') is not None:
            self.currency = m.get('Currency')

        if m.get('OriginalPrice') is not None:
            self.original_price = m.get('OriginalPrice')

        return self

class DescribeOriginalPricesResponseBodyPriceResponsePriceDetailList(DaraModel):
    def __init__(
        self,
        price: main_models.DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListPrice = None,
        product_and_sku_detail: main_models.DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetail = None,
    ):
        self.price = price
        self.product_and_sku_detail = product_and_sku_detail

    def validate(self):
        if self.price:
            self.price.validate()
        if self.product_and_sku_detail:
            self.product_and_sku_detail.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.price is not None:
            result['Price'] = self.price.to_map()

        if self.product_and_sku_detail is not None:
            result['ProductAndSkuDetail'] = self.product_and_sku_detail.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Price') is not None:
            temp_model = main_models.DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListPrice()
            self.price = temp_model.from_map(m.get('Price'))

        if m.get('ProductAndSkuDetail') is not None:
            temp_model = main_models.DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetail()
            self.product_and_sku_detail = temp_model.from_map(m.get('ProductAndSkuDetail'))

        return self

class DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetail(DaraModel):
    def __init__(
        self,
        product: main_models.DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProduct = None,
        product_sku: main_models.DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProductSku = None,
    ):
        self.product = product
        self.product_sku = product_sku

    def validate(self):
        if self.product:
            self.product.validate()
        if self.product_sku:
            self.product_sku.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.product is not None:
            result['Product'] = self.product.to_map()

        if self.product_sku is not None:
            result['ProductSku'] = self.product_sku.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Product') is not None:
            temp_model = main_models.DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProduct()
            self.product = temp_model.from_map(m.get('Product'))

        if m.get('ProductSku') is not None:
            temp_model = main_models.DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProductSku()
            self.product_sku = temp_model.from_map(m.get('ProductSku'))

        return self

class DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProductSku(DaraModel):
    def __init__(
        self,
        attribute: str = None,
        sku_code: str = None,
        sku_desc: str = None,
        sku_name: str = None,
    ):
        self.attribute = attribute
        self.sku_code = sku_code
        self.sku_desc = sku_desc
        self.sku_name = sku_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.attribute is not None:
            result['Attribute'] = self.attribute

        if self.sku_code is not None:
            result['SkuCode'] = self.sku_code

        if self.sku_desc is not None:
            result['SkuDesc'] = self.sku_desc

        if self.sku_name is not None:
            result['SkuName'] = self.sku_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Attribute') is not None:
            self.attribute = m.get('Attribute')

        if m.get('SkuCode') is not None:
            self.sku_code = m.get('SkuCode')

        if m.get('SkuDesc') is not None:
            self.sku_desc = m.get('SkuDesc')

        if m.get('SkuName') is not None:
            self.sku_name = m.get('SkuName')

        return self

class DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProduct(DaraModel):
    def __init__(
        self,
        enable_discount_coupon: bool = None,
        is_first_buy: bool = None,
        is_subscribe: bool = None,
        product_code: str = None,
        product_desc: str = None,
        product_name: str = None,
    ):
        self.enable_discount_coupon = enable_discount_coupon
        self.is_first_buy = is_first_buy
        self.is_subscribe = is_subscribe
        self.product_code = product_code
        self.product_desc = product_desc
        self.product_name = product_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.enable_discount_coupon is not None:
            result['EnableDiscountCoupon'] = self.enable_discount_coupon

        if self.is_first_buy is not None:
            result['IsFirstBuy'] = self.is_first_buy

        if self.is_subscribe is not None:
            result['IsSubscribe'] = self.is_subscribe

        if self.product_code is not None:
            result['ProductCode'] = self.product_code

        if self.product_desc is not None:
            result['ProductDesc'] = self.product_desc

        if self.product_name is not None:
            result['ProductName'] = self.product_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('EnableDiscountCoupon') is not None:
            self.enable_discount_coupon = m.get('EnableDiscountCoupon')

        if m.get('IsFirstBuy') is not None:
            self.is_first_buy = m.get('IsFirstBuy')

        if m.get('IsSubscribe') is not None:
            self.is_subscribe = m.get('IsSubscribe')

        if m.get('ProductCode') is not None:
            self.product_code = m.get('ProductCode')

        if m.get('ProductDesc') is not None:
            self.product_desc = m.get('ProductDesc')

        if m.get('ProductName') is not None:
            self.product_name = m.get('ProductName')

        return self

class DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListPrice(DaraModel):
    def __init__(
        self,
        currency: str = None,
        original_price: float = None,
    ):
        self.currency = currency
        self.original_price = original_price

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.currency is not None:
            result['Currency'] = self.currency

        if self.original_price is not None:
            result['OriginalPrice'] = self.original_price

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Currency') is not None:
            self.currency = m.get('Currency')

        if m.get('OriginalPrice') is not None:
            self.original_price = m.get('OriginalPrice')

        return self

