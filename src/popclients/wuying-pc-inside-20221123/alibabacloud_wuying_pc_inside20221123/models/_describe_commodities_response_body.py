# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeCommoditiesResponseBody(DaraModel):
    def __init__(
        self,
        data: main_models.DescribeCommoditiesResponseBodyData = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.data = data
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Data') is not None:
            temp_model = main_models.DescribeCommoditiesResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeCommoditiesResponseBodyData(DaraModel):
    def __init__(
        self,
        commodity_item_1: List[main_models.DescribeCommoditiesResponseBodyDataCommodityItem1] = None,
        commodity_item_2: List[main_models.DescribeCommoditiesResponseBodyDataCommodityItem2] = None,
    ):
        self.commodity_item_1 = commodity_item_1
        self.commodity_item_2 = commodity_item_2

    def validate(self):
        if self.commodity_item_1:
            for v1 in self.commodity_item_1:
                 if v1:
                    v1.validate()
        if self.commodity_item_2:
            for v1 in self.commodity_item_2:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['CommodityItem1'] = []
        if self.commodity_item_1 is not None:
            for k1 in self.commodity_item_1:
                result['CommodityItem1'].append(k1.to_map() if k1 else None)

        result['CommodityItem2'] = []
        if self.commodity_item_2 is not None:
            for k1 in self.commodity_item_2:
                result['CommodityItem2'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.commodity_item_1 = []
        if m.get('CommodityItem1') is not None:
            for k1 in m.get('CommodityItem1'):
                temp_model = main_models.DescribeCommoditiesResponseBodyDataCommodityItem1()
                self.commodity_item_1.append(temp_model.from_map(k1))

        self.commodity_item_2 = []
        if m.get('CommodityItem2') is not None:
            for k1 in m.get('CommodityItem2'):
                temp_model = main_models.DescribeCommoditiesResponseBodyDataCommodityItem2()
                self.commodity_item_2.append(temp_model.from_map(k1))

        return self

class DescribeCommoditiesResponseBodyDataCommodityItem2(DaraModel):
    def __init__(
        self,
        code: str = None,
        duration_time: int = None,
        name: str = None,
        period: int = None,
        period_unit: str = None,
    ):
        self.code = code
        self.duration_time = duration_time
        self.name = name
        self.period = period
        self.period_unit = period_unit

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.duration_time is not None:
            result['DurationTime'] = self.duration_time

        if self.name is not None:
            result['Name'] = self.name

        if self.period is not None:
            result['Period'] = self.period

        if self.period_unit is not None:
            result['PeriodUnit'] = self.period_unit

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('DurationTime') is not None:
            self.duration_time = m.get('DurationTime')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('Period') is not None:
            self.period = m.get('Period')

        if m.get('PeriodUnit') is not None:
            self.period_unit = m.get('PeriodUnit')

        return self

class DescribeCommoditiesResponseBodyDataCommodityItem1(DaraModel):
    def __init__(
        self,
        bandwidth: int = None,
        cpu_core_count: int = None,
        gpu_count: int = None,
        memory_size: int = None,
        os_type: str = None,
        spec_code: str = None,
        spec_desc: str = None,
        spec_name: str = None,
        system_disk_size: int = None,
        user_disk_size: int = None,
    ):
        self.bandwidth = bandwidth
        self.cpu_core_count = cpu_core_count
        self.gpu_count = gpu_count
        self.memory_size = memory_size
        self.os_type = os_type
        self.spec_code = spec_code
        self.spec_desc = spec_desc
        self.spec_name = spec_name
        self.system_disk_size = system_disk_size
        self.user_disk_size = user_disk_size

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.bandwidth is not None:
            result['Bandwidth'] = self.bandwidth

        if self.cpu_core_count is not None:
            result['CpuCoreCount'] = self.cpu_core_count

        if self.gpu_count is not None:
            result['GpuCount'] = self.gpu_count

        if self.memory_size is not None:
            result['MemorySize'] = self.memory_size

        if self.os_type is not None:
            result['OsType'] = self.os_type

        if self.spec_code is not None:
            result['SpecCode'] = self.spec_code

        if self.spec_desc is not None:
            result['SpecDesc'] = self.spec_desc

        if self.spec_name is not None:
            result['SpecName'] = self.spec_name

        if self.system_disk_size is not None:
            result['SystemDiskSize'] = self.system_disk_size

        if self.user_disk_size is not None:
            result['UserDiskSize'] = self.user_disk_size

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Bandwidth') is not None:
            self.bandwidth = m.get('Bandwidth')

        if m.get('CpuCoreCount') is not None:
            self.cpu_core_count = m.get('CpuCoreCount')

        if m.get('GpuCount') is not None:
            self.gpu_count = m.get('GpuCount')

        if m.get('MemorySize') is not None:
            self.memory_size = m.get('MemorySize')

        if m.get('OsType') is not None:
            self.os_type = m.get('OsType')

        if m.get('SpecCode') is not None:
            self.spec_code = m.get('SpecCode')

        if m.get('SpecDesc') is not None:
            self.spec_desc = m.get('SpecDesc')

        if m.get('SpecName') is not None:
            self.spec_name = m.get('SpecName')

        if m.get('SystemDiskSize') is not None:
            self.system_disk_size = m.get('SystemDiskSize')

        if m.get('UserDiskSize') is not None:
            self.user_disk_size = m.get('UserDiskSize')

        return self

