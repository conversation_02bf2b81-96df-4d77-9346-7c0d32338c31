# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeCouponOperateSumRequest(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        begin_from: str = None,
        end_to: str = None,
        status: str = None,
    ):
        self.ali_uid = ali_uid
        self.begin_from = begin_from
        self.end_to = end_to
        self.status = status

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['aliUid'] = self.ali_uid

        if self.begin_from is not None:
            result['beginFrom'] = self.begin_from

        if self.end_to is not None:
            result['endTo'] = self.end_to

        if self.status is not None:
            result['status'] = self.status

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('aliUid') is not None:
            self.ali_uid = m.get('aliUid')

        if m.get('beginFrom') is not None:
            self.begin_from = m.get('beginFrom')

        if m.get('endTo') is not None:
            self.end_to = m.get('endTo')

        if m.get('status') is not None:
            self.status = m.get('status')

        return self

