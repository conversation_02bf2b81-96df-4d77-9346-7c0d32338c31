# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class SendLauncherMessageRequest(DaraModel):
    def __init__(
        self,
        is_contain_coin: bool = None,
        is_contain_desktop: bool = None,
        message_template_id: str = None,
        user_ids: str = None,
    ):
        self.is_contain_coin = is_contain_coin
        self.is_contain_desktop = is_contain_desktop
        self.message_template_id = message_template_id
        self.user_ids = user_ids

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.is_contain_coin is not None:
            result['IsContainCoin'] = self.is_contain_coin

        if self.is_contain_desktop is not None:
            result['IsContainDesktop'] = self.is_contain_desktop

        if self.message_template_id is not None:
            result['MessageTemplateId'] = self.message_template_id

        if self.user_ids is not None:
            result['UserIds'] = self.user_ids

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('IsContainCoin') is not None:
            self.is_contain_coin = m.get('IsContainCoin')

        if m.get('IsContainDesktop') is not None:
            self.is_contain_desktop = m.get('IsContainDesktop')

        if m.get('MessageTemplateId') is not None:
            self.message_template_id = m.get('MessageTemplateId')

        if m.get('UserIds') is not None:
            self.user_ids = m.get('UserIds')

        return self

