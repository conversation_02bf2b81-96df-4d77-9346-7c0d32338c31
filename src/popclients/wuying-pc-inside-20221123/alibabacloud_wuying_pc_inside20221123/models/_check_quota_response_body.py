# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 


class CheckQuotaResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        message: str = None,
        quota_info: main_models.CheckQuotaResponseBodyQuotaInfo = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.message = message
        self.quota_info = quota_info
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.quota_info:
            self.quota_info.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.message is not None:
            result['Message'] = self.message

        if self.quota_info is not None:
            result['QuotaInfo'] = self.quota_info.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('QuotaInfo') is not None:
            temp_model = main_models.CheckQuotaResponseBodyQuotaInfo()
            self.quota_info = temp_model.from_map(m.get('QuotaInfo'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class CheckQuotaResponseBodyQuotaInfo(DaraModel):
    def __init__(
        self,
        is_over_resource_quota: bool = None,
        total_resource_quota: int = None,
    ):
        self.is_over_resource_quota = is_over_resource_quota
        self.total_resource_quota = total_resource_quota

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.is_over_resource_quota is not None:
            result['IsOverResourceQuota'] = self.is_over_resource_quota

        if self.total_resource_quota is not None:
            result['TotalResourceQuota'] = self.total_resource_quota

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('IsOverResourceQuota') is not None:
            self.is_over_resource_quota = m.get('IsOverResourceQuota')

        if m.get('TotalResourceQuota') is not None:
            self.total_resource_quota = m.get('TotalResourceQuota')

        return self

