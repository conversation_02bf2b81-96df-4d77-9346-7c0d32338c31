# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribePersonalUsersOwnedBenefitsInnerResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribePersonalUsersOwnedBenefitsInnerResponseBodyData = None,
        err_code: str = None,
        err_msg: str = None,
        request_id: str = None,
    ):
        self.code = code
        self.data = data
        self.err_code = err_code
        self.err_msg = err_msg
        self.request_id = request_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['code'] = self.code

        if self.data is not None:
            result['data'] = self.data.to_map()

        if self.err_code is not None:
            result['errCode'] = self.err_code

        if self.err_msg is not None:
            result['errMsg'] = self.err_msg

        if self.request_id is not None:
            result['requestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('code') is not None:
            self.code = m.get('code')

        if m.get('data') is not None:
            temp_model = main_models.DescribePersonalUsersOwnedBenefitsInnerResponseBodyData()
            self.data = temp_model.from_map(m.get('data'))

        if m.get('errCode') is not None:
            self.err_code = m.get('errCode')

        if m.get('errMsg') is not None:
            self.err_msg = m.get('errMsg')

        if m.get('requestId') is not None:
            self.request_id = m.get('requestId')

        return self

class DescribePersonalUsersOwnedBenefitsInnerResponseBodyData(DaraModel):
    def __init__(
        self,
        benefit_list: List[main_models.DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitList] = None,
        history_activity_list: List[main_models.DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataHistoryActivityList] = None,
        history_benefit_list: List[main_models.DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataHistoryBenefitList] = None,
    ):
        self.benefit_list = benefit_list
        self.history_activity_list = history_activity_list
        self.history_benefit_list = history_benefit_list

    def validate(self):
        if self.benefit_list:
            for v1 in self.benefit_list:
                 if v1:
                    v1.validate()
        if self.history_activity_list:
            for v1 in self.history_activity_list:
                 if v1:
                    v1.validate()
        if self.history_benefit_list:
            for v1 in self.history_benefit_list:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['benefitList'] = []
        if self.benefit_list is not None:
            for k1 in self.benefit_list:
                result['benefitList'].append(k1.to_map() if k1 else None)

        result['historyActivityList'] = []
        if self.history_activity_list is not None:
            for k1 in self.history_activity_list:
                result['historyActivityList'].append(k1.to_map() if k1 else None)

        result['historyBenefitList'] = []
        if self.history_benefit_list is not None:
            for k1 in self.history_benefit_list:
                result['historyBenefitList'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.benefit_list = []
        if m.get('benefitList') is not None:
            for k1 in m.get('benefitList'):
                temp_model = main_models.DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitList()
                self.benefit_list.append(temp_model.from_map(k1))

        self.history_activity_list = []
        if m.get('historyActivityList') is not None:
            for k1 in m.get('historyActivityList'):
                temp_model = main_models.DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataHistoryActivityList()
                self.history_activity_list.append(temp_model.from_map(k1))

        self.history_benefit_list = []
        if m.get('historyBenefitList') is not None:
            for k1 in m.get('historyBenefitList'):
                temp_model = main_models.DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataHistoryBenefitList()
                self.history_benefit_list.append(temp_model.from_map(k1))

        return self

class DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataHistoryBenefitList(DaraModel):
    def __init__(
        self,
        benefit_type: str = None,
        benefit_type_desc: str = None,
    ):
        self.benefit_type = benefit_type
        self.benefit_type_desc = benefit_type_desc

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.benefit_type is not None:
            result['benefitType'] = self.benefit_type

        if self.benefit_type_desc is not None:
            result['benefitTypeDesc'] = self.benefit_type_desc

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('benefitType') is not None:
            self.benefit_type = m.get('benefitType')

        if m.get('benefitTypeDesc') is not None:
            self.benefit_type_desc = m.get('benefitTypeDesc')

        return self

class DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataHistoryActivityList(DaraModel):
    def __init__(
        self,
        customer_activity_attend_history_count: int = None,
        customer_activity_id: str = None,
        customer_activity_name: str = None,
    ):
        self.customer_activity_attend_history_count = customer_activity_attend_history_count
        self.customer_activity_id = customer_activity_id
        self.customer_activity_name = customer_activity_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.customer_activity_attend_history_count is not None:
            result['customerActivityAttendHistoryCount'] = self.customer_activity_attend_history_count

        if self.customer_activity_id is not None:
            result['customerActivityId'] = self.customer_activity_id

        if self.customer_activity_name is not None:
            result['customerActivityName'] = self.customer_activity_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('customerActivityAttendHistoryCount') is not None:
            self.customer_activity_attend_history_count = m.get('customerActivityAttendHistoryCount')

        if m.get('customerActivityId') is not None:
            self.customer_activity_id = m.get('customerActivityId')

        if m.get('customerActivityName') is not None:
            self.customer_activity_name = m.get('customerActivityName')

        return self

class DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitList(DaraModel):
    def __init__(
        self,
        benefit_info: main_models.DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitListBenefitInfo = None,
        customer_activity_info: main_models.DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitListCustomerActivityInfo = None,
        description: str = None,
        expired_time: str = None,
        hyperlink: str = None,
        name: str = None,
        notes: str = None,
        status: str = None,
    ):
        self.benefit_info = benefit_info
        self.customer_activity_info = customer_activity_info
        self.description = description
        self.expired_time = expired_time
        self.hyperlink = hyperlink
        self.name = name
        self.notes = notes
        self.status = status

    def validate(self):
        if self.benefit_info:
            self.benefit_info.validate()
        if self.customer_activity_info:
            self.customer_activity_info.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.benefit_info is not None:
            result['benefitInfo'] = self.benefit_info.to_map()

        if self.customer_activity_info is not None:
            result['customerActivityInfo'] = self.customer_activity_info.to_map()

        if self.description is not None:
            result['description'] = self.description

        if self.expired_time is not None:
            result['expiredTime'] = self.expired_time

        if self.hyperlink is not None:
            result['hyperlink'] = self.hyperlink

        if self.name is not None:
            result['name'] = self.name

        if self.notes is not None:
            result['notes'] = self.notes

        if self.status is not None:
            result['status'] = self.status

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('benefitInfo') is not None:
            temp_model = main_models.DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitListBenefitInfo()
            self.benefit_info = temp_model.from_map(m.get('benefitInfo'))

        if m.get('customerActivityInfo') is not None:
            temp_model = main_models.DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitListCustomerActivityInfo()
            self.customer_activity_info = temp_model.from_map(m.get('customerActivityInfo'))

        if m.get('description') is not None:
            self.description = m.get('description')

        if m.get('expiredTime') is not None:
            self.expired_time = m.get('expiredTime')

        if m.get('hyperlink') is not None:
            self.hyperlink = m.get('hyperlink')

        if m.get('name') is not None:
            self.name = m.get('name')

        if m.get('notes') is not None:
            self.notes = m.get('notes')

        if m.get('status') is not None:
            self.status = m.get('status')

        return self

class DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitListCustomerActivityInfo(DaraModel):
    def __init__(
        self,
        customer_activity_id: str = None,
        customer_activity_name: str = None,
    ):
        self.customer_activity_id = customer_activity_id
        self.customer_activity_name = customer_activity_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.customer_activity_id is not None:
            result['customerActivityId'] = self.customer_activity_id

        if self.customer_activity_name is not None:
            result['customerActivityName'] = self.customer_activity_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('customerActivityId') is not None:
            self.customer_activity_id = m.get('customerActivityId')

        if m.get('customerActivityName') is not None:
            self.customer_activity_name = m.get('customerActivityName')

        return self

class DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitListBenefitInfo(DaraModel):
    def __init__(
        self,
        benefit_condition_desc: str = None,
        benefit_desc: str = None,
        benefit_type: str = None,
        benefit_type_desc: str = None,
    ):
        self.benefit_condition_desc = benefit_condition_desc
        self.benefit_desc = benefit_desc
        self.benefit_type = benefit_type
        self.benefit_type_desc = benefit_type_desc

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.benefit_condition_desc is not None:
            result['benefitConditionDesc'] = self.benefit_condition_desc

        if self.benefit_desc is not None:
            result['benefitDesc'] = self.benefit_desc

        if self.benefit_type is not None:
            result['benefitType'] = self.benefit_type

        if self.benefit_type_desc is not None:
            result['benefitTypeDesc'] = self.benefit_type_desc

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('benefitConditionDesc') is not None:
            self.benefit_condition_desc = m.get('benefitConditionDesc')

        if m.get('benefitDesc') is not None:
            self.benefit_desc = m.get('benefitDesc')

        if m.get('benefitType') is not None:
            self.benefit_type = m.get('benefitType')

        if m.get('benefitTypeDesc') is not None:
            self.benefit_type_desc = m.get('benefitTypeDesc')

        return self

