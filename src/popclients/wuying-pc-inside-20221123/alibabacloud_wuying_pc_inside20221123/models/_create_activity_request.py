# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateActivityRequest(DaraModel):
    def __init__(
        self,
        available_hours: int = None,
        benefit_id: str = None,
        benefit_quantity: int = None,
        desktop_spec_code: str = None,
        expire: str = None,
        expire_days: int = None,
        name: str = None,
        need_real_name_certify: bool = None,
        pool_id: str = None,
        scene: str = None,
        status: str = None,
    ):
        self.available_hours = available_hours
        self.benefit_id = benefit_id
        self.benefit_quantity = benefit_quantity
        self.desktop_spec_code = desktop_spec_code
        self.expire = expire
        self.expire_days = expire_days
        self.name = name
        self.need_real_name_certify = need_real_name_certify
        self.pool_id = pool_id
        self.scene = scene
        self.status = status

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.available_hours is not None:
            result['availableHours'] = self.available_hours

        if self.benefit_id is not None:
            result['benefitId'] = self.benefit_id

        if self.benefit_quantity is not None:
            result['benefitQuantity'] = self.benefit_quantity

        if self.desktop_spec_code is not None:
            result['desktopSpecCode'] = self.desktop_spec_code

        if self.expire is not None:
            result['expire'] = self.expire

        if self.expire_days is not None:
            result['expireDays'] = self.expire_days

        if self.name is not None:
            result['name'] = self.name

        if self.need_real_name_certify is not None:
            result['needRealNameCertify'] = self.need_real_name_certify

        if self.pool_id is not None:
            result['poolId'] = self.pool_id

        if self.scene is not None:
            result['scene'] = self.scene

        if self.status is not None:
            result['status'] = self.status

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('availableHours') is not None:
            self.available_hours = m.get('availableHours')

        if m.get('benefitId') is not None:
            self.benefit_id = m.get('benefitId')

        if m.get('benefitQuantity') is not None:
            self.benefit_quantity = m.get('benefitQuantity')

        if m.get('desktopSpecCode') is not None:
            self.desktop_spec_code = m.get('desktopSpecCode')

        if m.get('expire') is not None:
            self.expire = m.get('expire')

        if m.get('expireDays') is not None:
            self.expire_days = m.get('expireDays')

        if m.get('name') is not None:
            self.name = m.get('name')

        if m.get('needRealNameCertify') is not None:
            self.need_real_name_certify = m.get('needRealNameCertify')

        if m.get('poolId') is not None:
            self.pool_id = m.get('poolId')

        if m.get('scene') is not None:
            self.scene = m.get('scene')

        if m.get('status') is not None:
            self.status = m.get('status')

        return self

