# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import <PERSON><PERSON><PERSON><PERSON><PERSON> 
from typing import List


class InnerDescribeDesktopLocalInfoRequest(DaraModel):
    def __init__(
        self,
        desktop_ids: List[str] = None,
        desktop_label: str = None,
        desktop_type: str = None,
        end_user_ids: List[str] = None,
        resource_ids: List[str] = None,
    ):
        self.desktop_ids = desktop_ids
        self.desktop_label = desktop_label
        self.desktop_type = desktop_type
        self.end_user_ids = end_user_ids
        self.resource_ids = resource_ids

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.desktop_ids is not None:
            result['DesktopIds'] = self.desktop_ids

        if self.desktop_label is not None:
            result['DesktopLabel'] = self.desktop_label

        if self.desktop_type is not None:
            result['DesktopType'] = self.desktop_type

        if self.end_user_ids is not None:
            result['EndUserIds'] = self.end_user_ids

        if self.resource_ids is not None:
            result['ResourceIds'] = self.resource_ids

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DesktopIds') is not None:
            self.desktop_ids = m.get('DesktopIds')

        if m.get('DesktopLabel') is not None:
            self.desktop_label = m.get('DesktopLabel')

        if m.get('DesktopType') is not None:
            self.desktop_type = m.get('DesktopType')

        if m.get('EndUserIds') is not None:
            self.end_user_ids = m.get('EndUserIds')

        if m.get('ResourceIds') is not None:
            self.resource_ids = m.get('ResourceIds')

        return self

