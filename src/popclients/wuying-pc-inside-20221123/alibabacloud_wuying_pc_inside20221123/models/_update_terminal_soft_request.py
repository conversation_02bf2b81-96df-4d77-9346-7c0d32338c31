# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class UpdateTerminalSoftRequest(DaraModel):
    def __init__(
        self,
        sn: str = None,
        update_action: str = None,
    ):
        self.sn = sn
        self.update_action = update_action

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.sn is not None:
            result['Sn'] = self.sn

        if self.update_action is not None:
            result['UpdateAction'] = self.update_action

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Sn') is not None:
            self.sn = m.get('Sn')

        if m.get('UpdateAction') is not None:
            self.update_action = m.get('UpdateAction')

        return self

