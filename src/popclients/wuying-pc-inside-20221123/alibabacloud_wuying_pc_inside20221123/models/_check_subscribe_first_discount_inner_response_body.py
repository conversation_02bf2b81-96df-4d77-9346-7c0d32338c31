# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class CheckSubscribeFirstDiscountInnerResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.CheckSubscribeFirstDiscountInnerResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.CheckSubscribeFirstDiscountInnerResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class CheckSubscribeFirstDiscountInnerResponseBodyData(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        is_first_buy: bool = None,
        package_bundles: List[main_models.CheckSubscribeFirstDiscountInnerResponseBodyDataPackageBundles] = None,
    ):
        self.ali_uid = ali_uid
        self.is_first_buy = is_first_buy
        self.package_bundles = package_bundles

    def validate(self):
        if self.package_bundles:
            for v1 in self.package_bundles:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.is_first_buy is not None:
            result['IsFirstBuy'] = self.is_first_buy

        result['PackageBundles'] = []
        if self.package_bundles is not None:
            for k1 in self.package_bundles:
                result['PackageBundles'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('IsFirstBuy') is not None:
            self.is_first_buy = m.get('IsFirstBuy')

        self.package_bundles = []
        if m.get('PackageBundles') is not None:
            for k1 in m.get('PackageBundles'):
                temp_model = main_models.CheckSubscribeFirstDiscountInnerResponseBodyDataPackageBundles()
                self.package_bundles.append(temp_model.from_map(k1))

        return self

class CheckSubscribeFirstDiscountInnerResponseBodyDataPackageBundles(DaraModel):
    def __init__(
        self,
        bundle_id: str = None,
        instance_id: str = None,
        is_first_buy: bool = None,
        sku_code: str = None,
    ):
        self.bundle_id = bundle_id
        self.instance_id = instance_id
        self.is_first_buy = is_first_buy
        self.sku_code = sku_code

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.bundle_id is not None:
            result['BundleId'] = self.bundle_id

        if self.instance_id is not None:
            result['InstanceId'] = self.instance_id

        if self.is_first_buy is not None:
            result['IsFirstBuy'] = self.is_first_buy

        if self.sku_code is not None:
            result['SkuCode'] = self.sku_code

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('BundleId') is not None:
            self.bundle_id = m.get('BundleId')

        if m.get('InstanceId') is not None:
            self.instance_id = m.get('InstanceId')

        if m.get('IsFirstBuy') is not None:
            self.is_first_buy = m.get('IsFirstBuy')

        if m.get('SkuCode') is not None:
            self.sku_code = m.get('SkuCode')

        return self

