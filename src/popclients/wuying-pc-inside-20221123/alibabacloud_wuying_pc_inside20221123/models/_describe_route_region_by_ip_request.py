# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeRouteRegionByIpRequest(DaraModel):
    def __init__(
        self,
        user_ip: str = None,
    ):
        self.user_ip = user_ip

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.user_ip is not None:
            result['UserIp'] = self.user_ip

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('UserIp') is not None:
            self.user_ip = m.get('UserIp')

        return self

