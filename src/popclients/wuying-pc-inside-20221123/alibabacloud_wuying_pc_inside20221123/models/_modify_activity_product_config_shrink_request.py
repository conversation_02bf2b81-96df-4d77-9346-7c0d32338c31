# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class ModifyActivityProductConfigShrinkRequest(DaraModel):
    def __init__(
        self,
        config_code: str = None,
        new_activity_config_info_shrink: str = None,
    ):
        # This parameter is required.
        self.config_code = config_code
        self.new_activity_config_info_shrink = new_activity_config_info_shrink

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.config_code is not None:
            result['ConfigCode'] = self.config_code

        if self.new_activity_config_info_shrink is not None:
            result['NewActivityConfigInfo'] = self.new_activity_config_info_shrink

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ConfigCode') is not None:
            self.config_code = m.get('ConfigCode')

        if m.get('NewActivityConfigInfo') is not None:
            self.new_activity_config_info_shrink = m.get('NewActivityConfigInfo')

        return self

