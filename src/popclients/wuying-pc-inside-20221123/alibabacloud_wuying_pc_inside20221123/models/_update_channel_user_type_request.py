# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class UpdateChannelUserTypeRequest(DaraModel):
    def __init__(
        self,
        channel_id: str = None,
        pay_type: str = None,
        tenant_id: str = None,
        type: str = None,
        user_id: str = None,
    ):
        self.channel_id = channel_id
        self.pay_type = pay_type
        self.tenant_id = tenant_id
        self.type = type
        self.user_id = user_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.channel_id is not None:
            result['ChannelId'] = self.channel_id

        if self.pay_type is not None:
            result['PayType'] = self.pay_type

        if self.tenant_id is not None:
            result['TenantId'] = self.tenant_id

        if self.type is not None:
            result['Type'] = self.type

        if self.user_id is not None:
            result['UserId'] = self.user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ChannelId') is not None:
            self.channel_id = m.get('ChannelId')

        if m.get('PayType') is not None:
            self.pay_type = m.get('PayType')

        if m.get('TenantId') is not None:
            self.tenant_id = m.get('TenantId')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        return self

