# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class VerifyHardwareCouponRequest(DaraModel):
    def __init__(
        self,
        coupon_id: str = None,
        serial_num: str = None,
    ):
        self.coupon_id = coupon_id
        self.serial_num = serial_num

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.coupon_id is not None:
            result['couponId'] = self.coupon_id

        if self.serial_num is not None:
            result['serialNum'] = self.serial_num

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('couponId') is not None:
            self.coupon_id = m.get('couponId')

        if m.get('serialNum') is not None:
            self.serial_num = m.get('serialNum')

        return self

