# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class RollbackChannelTransferDesktopsRequest(DaraModel):
    def __init__(
        self,
        center_desktop_ids: str = None,
        order_id: str = None,
        package_instance_ids: str = None,
        transfer_action: str = None,
    ):
        self.center_desktop_ids = center_desktop_ids
        self.order_id = order_id
        self.package_instance_ids = package_instance_ids
        self.transfer_action = transfer_action

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.center_desktop_ids is not None:
            result['CenterDesktopIds'] = self.center_desktop_ids

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.package_instance_ids is not None:
            result['PackageInstanceIds'] = self.package_instance_ids

        if self.transfer_action is not None:
            result['TransferAction'] = self.transfer_action

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CenterDesktopIds') is not None:
            self.center_desktop_ids = m.get('CenterDesktopIds')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('PackageInstanceIds') is not None:
            self.package_instance_ids = m.get('PackageInstanceIds')

        if m.get('TransferAction') is not None:
            self.transfer_action = m.get('TransferAction')

        return self

