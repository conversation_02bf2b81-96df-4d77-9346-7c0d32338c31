# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class ActiveRedeemCouponRequest(DaraModel):
    def __init__(
        self,
        coupon_id: str = None,
        end_user_id: str = None,
        login_ali_uid: int = None,
        template_id: str = None,
    ):
        # This parameter is required.
        self.coupon_id = coupon_id
        # This parameter is required.
        self.end_user_id = end_user_id
        # This parameter is required.
        self.login_ali_uid = login_ali_uid
        # This parameter is required.
        self.template_id = template_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.coupon_id is not None:
            result['CouponId'] = self.coupon_id

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.login_ali_uid is not None:
            result['LoginAliUid'] = self.login_ali_uid

        if self.template_id is not None:
            result['TemplateId'] = self.template_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CouponId') is not None:
            self.coupon_id = m.get('CouponId')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('LoginAliUid') is not None:
            self.login_ali_uid = m.get('LoginAliUid')

        if m.get('TemplateId') is not None:
            self.template_id = m.get('TemplateId')

        return self

