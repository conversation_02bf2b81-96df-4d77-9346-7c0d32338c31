# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from typing import List


class DescribePackageOrdersRequest(DaraModel):
    def __init__(
        self,
        current_page: int = None,
        end_create_time: int = None,
        end_payment_time: int = None,
        login_ali_uid: int = None,
        order_id_list_json: List[str] = None,
        order_status_list_json: List[str] = None,
        order_type_list_json: List[str] = None,
        page_size: int = None,
        product_codes_json: List[str] = None,
        product_type_list_json: List[str] = None,
        start_create_time: int = None,
        start_payment_time: int = None,
    ):
        # This parameter is required.
        self.current_page = current_page
        self.end_create_time = end_create_time
        self.end_payment_time = end_payment_time
        # This parameter is required.
        self.login_ali_uid = login_ali_uid
        self.order_id_list_json = order_id_list_json
        self.order_status_list_json = order_status_list_json
        self.order_type_list_json = order_type_list_json
        # This parameter is required.
        self.page_size = page_size
        self.product_codes_json = product_codes_json
        self.product_type_list_json = product_type_list_json
        self.start_create_time = start_create_time
        self.start_payment_time = start_payment_time

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.current_page is not None:
            result['CurrentPage'] = self.current_page

        if self.end_create_time is not None:
            result['EndCreateTime'] = self.end_create_time

        if self.end_payment_time is not None:
            result['EndPaymentTime'] = self.end_payment_time

        if self.login_ali_uid is not None:
            result['LoginAliUid'] = self.login_ali_uid

        if self.order_id_list_json is not None:
            result['OrderIdListJson'] = self.order_id_list_json

        if self.order_status_list_json is not None:
            result['OrderStatusListJson'] = self.order_status_list_json

        if self.order_type_list_json is not None:
            result['OrderTypeListJson'] = self.order_type_list_json

        if self.page_size is not None:
            result['PageSize'] = self.page_size

        if self.product_codes_json is not None:
            result['ProductCodesJson'] = self.product_codes_json

        if self.product_type_list_json is not None:
            result['ProductTypeListJson'] = self.product_type_list_json

        if self.start_create_time is not None:
            result['StartCreateTime'] = self.start_create_time

        if self.start_payment_time is not None:
            result['StartPaymentTime'] = self.start_payment_time

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CurrentPage') is not None:
            self.current_page = m.get('CurrentPage')

        if m.get('EndCreateTime') is not None:
            self.end_create_time = m.get('EndCreateTime')

        if m.get('EndPaymentTime') is not None:
            self.end_payment_time = m.get('EndPaymentTime')

        if m.get('LoginAliUid') is not None:
            self.login_ali_uid = m.get('LoginAliUid')

        if m.get('OrderIdListJson') is not None:
            self.order_id_list_json = m.get('OrderIdListJson')

        if m.get('OrderStatusListJson') is not None:
            self.order_status_list_json = m.get('OrderStatusListJson')

        if m.get('OrderTypeListJson') is not None:
            self.order_type_list_json = m.get('OrderTypeListJson')

        if m.get('PageSize') is not None:
            self.page_size = m.get('PageSize')

        if m.get('ProductCodesJson') is not None:
            self.product_codes_json = m.get('ProductCodesJson')

        if m.get('ProductTypeListJson') is not None:
            self.product_type_list_json = m.get('ProductTypeListJson')

        if m.get('StartCreateTime') is not None:
            self.start_create_time = m.get('StartCreateTime')

        if m.get('StartPaymentTime') is not None:
            self.start_payment_time = m.get('StartPaymentTime')

        return self

