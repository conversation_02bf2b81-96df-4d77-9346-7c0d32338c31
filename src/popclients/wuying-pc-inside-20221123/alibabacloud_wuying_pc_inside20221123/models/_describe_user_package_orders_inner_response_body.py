# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeUserPackageOrdersInnerResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.DescribeUserPackageOrdersInnerResponseBodyData] = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.DescribeUserPackageOrdersInnerResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeUserPackageOrdersInnerResponseBodyData(DaraModel):
    def __init__(
        self,
        amount: int = None,
        creator_ali_uid: str = None,
        creator_end_user_id: int = None,
        gmt_canceled: str = None,
        gmt_create: str = None,
        gmt_paid: str = None,
        instance_id: str = None,
        is_first_buy: bool = None,
        is_subscribe: bool = None,
        order_id: str = None,
        order_status: str = None,
        order_type: str = None,
        product_code: str = None,
        product_info: str = None,
        product_sku_code: str = None,
        product_sku_info: str = None,
        product_type: str = None,
        trade_price: str = None,
    ):
        self.amount = amount
        self.creator_ali_uid = creator_ali_uid
        self.creator_end_user_id = creator_end_user_id
        # Use the UTC time format: yyyy-MM-ddTHH:mm:ssZ
        self.gmt_canceled = gmt_canceled
        # Use the UTC time format: yyyy-MM-ddTHH:mm:ssZ
        self.gmt_create = gmt_create
        # Use the UTC time format: yyyy-MM-ddTHH:mm:ssZ
        self.gmt_paid = gmt_paid
        self.instance_id = instance_id
        self.is_first_buy = is_first_buy
        self.is_subscribe = is_subscribe
        self.order_id = order_id
        self.order_status = order_status
        self.order_type = order_type
        self.product_code = product_code
        self.product_info = product_info
        self.product_sku_code = product_sku_code
        self.product_sku_info = product_sku_info
        self.product_type = product_type
        self.trade_price = trade_price

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.amount is not None:
            result['Amount'] = self.amount

        if self.creator_ali_uid is not None:
            result['CreatorAliUid'] = self.creator_ali_uid

        if self.creator_end_user_id is not None:
            result['CreatorEndUserId'] = self.creator_end_user_id

        if self.gmt_canceled is not None:
            result['GmtCanceled'] = self.gmt_canceled

        if self.gmt_create is not None:
            result['GmtCreate'] = self.gmt_create

        if self.gmt_paid is not None:
            result['GmtPaid'] = self.gmt_paid

        if self.instance_id is not None:
            result['InstanceId'] = self.instance_id

        if self.is_first_buy is not None:
            result['IsFirstBuy'] = self.is_first_buy

        if self.is_subscribe is not None:
            result['IsSubscribe'] = self.is_subscribe

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.order_status is not None:
            result['OrderStatus'] = self.order_status

        if self.order_type is not None:
            result['OrderType'] = self.order_type

        if self.product_code is not None:
            result['ProductCode'] = self.product_code

        if self.product_info is not None:
            result['ProductInfo'] = self.product_info

        if self.product_sku_code is not None:
            result['ProductSkuCode'] = self.product_sku_code

        if self.product_sku_info is not None:
            result['ProductSkuInfo'] = self.product_sku_info

        if self.product_type is not None:
            result['ProductType'] = self.product_type

        if self.trade_price is not None:
            result['TradePrice'] = self.trade_price

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Amount') is not None:
            self.amount = m.get('Amount')

        if m.get('CreatorAliUid') is not None:
            self.creator_ali_uid = m.get('CreatorAliUid')

        if m.get('CreatorEndUserId') is not None:
            self.creator_end_user_id = m.get('CreatorEndUserId')

        if m.get('GmtCanceled') is not None:
            self.gmt_canceled = m.get('GmtCanceled')

        if m.get('GmtCreate') is not None:
            self.gmt_create = m.get('GmtCreate')

        if m.get('GmtPaid') is not None:
            self.gmt_paid = m.get('GmtPaid')

        if m.get('InstanceId') is not None:
            self.instance_id = m.get('InstanceId')

        if m.get('IsFirstBuy') is not None:
            self.is_first_buy = m.get('IsFirstBuy')

        if m.get('IsSubscribe') is not None:
            self.is_subscribe = m.get('IsSubscribe')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('OrderStatus') is not None:
            self.order_status = m.get('OrderStatus')

        if m.get('OrderType') is not None:
            self.order_type = m.get('OrderType')

        if m.get('ProductCode') is not None:
            self.product_code = m.get('ProductCode')

        if m.get('ProductInfo') is not None:
            self.product_info = m.get('ProductInfo')

        if m.get('ProductSkuCode') is not None:
            self.product_sku_code = m.get('ProductSkuCode')

        if m.get('ProductSkuInfo') is not None:
            self.product_sku_info = m.get('ProductSkuInfo')

        if m.get('ProductType') is not None:
            self.product_type = m.get('ProductType')

        if m.get('TradePrice') is not None:
            self.trade_price = m.get('TradePrice')

        return self

