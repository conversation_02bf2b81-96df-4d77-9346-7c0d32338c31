# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeRedeemCodeInfoInnerResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeRedeemCodeInfoInnerResponseBodyData = None,
        request_id: str = None,
    ):
        self.code = code
        self.data = data
        self.request_id = request_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.request_id is not None:
            result['requestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeRedeemCodeInfoInnerResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('requestId') is not None:
            self.request_id = m.get('requestId')

        return self

class DescribeRedeemCodeInfoInnerResponseBodyData(DaraModel):
    def __init__(
        self,
        benefit_info: main_models.DescribeRedeemCodeInfoInnerResponseBodyDataBenefitInfo = None,
        channel_id: str = None,
        coupon_content: str = None,
        current_step_id: str = None,
        customer_activity_id: str = None,
        extra_coupon_info: main_models.DescribeRedeemCodeInfoInnerResponseBodyDataExtraCouponInfo = None,
        redeem_code: str = None,
        status: str = None,
        type: str = None,
        verify_step_info: main_models.DescribeRedeemCodeInfoInnerResponseBodyDataVerifyStepInfo = None,
        verify_step_total: int = None,
    ):
        self.benefit_info = benefit_info
        self.channel_id = channel_id
        self.coupon_content = coupon_content
        self.current_step_id = current_step_id
        self.customer_activity_id = customer_activity_id
        self.extra_coupon_info = extra_coupon_info
        self.redeem_code = redeem_code
        self.status = status
        self.type = type
        self.verify_step_info = verify_step_info
        self.verify_step_total = verify_step_total

    def validate(self):
        if self.benefit_info:
            self.benefit_info.validate()
        if self.extra_coupon_info:
            self.extra_coupon_info.validate()
        if self.verify_step_info:
            self.verify_step_info.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.benefit_info is not None:
            result['BenefitInfo'] = self.benefit_info.to_map()

        if self.channel_id is not None:
            result['ChannelId'] = self.channel_id

        if self.coupon_content is not None:
            result['CouponContent'] = self.coupon_content

        if self.current_step_id is not None:
            result['CurrentStepId'] = self.current_step_id

        if self.customer_activity_id is not None:
            result['CustomerActivityId'] = self.customer_activity_id

        if self.extra_coupon_info is not None:
            result['ExtraCouponInfo'] = self.extra_coupon_info.to_map()

        if self.redeem_code is not None:
            result['RedeemCode'] = self.redeem_code

        if self.status is not None:
            result['Status'] = self.status

        if self.type is not None:
            result['Type'] = self.type

        if self.verify_step_info is not None:
            result['VerifyStepInfo'] = self.verify_step_info.to_map()

        if self.verify_step_total is not None:
            result['VerifyStepTotal'] = self.verify_step_total

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('BenefitInfo') is not None:
            temp_model = main_models.DescribeRedeemCodeInfoInnerResponseBodyDataBenefitInfo()
            self.benefit_info = temp_model.from_map(m.get('BenefitInfo'))

        if m.get('ChannelId') is not None:
            self.channel_id = m.get('ChannelId')

        if m.get('CouponContent') is not None:
            self.coupon_content = m.get('CouponContent')

        if m.get('CurrentStepId') is not None:
            self.current_step_id = m.get('CurrentStepId')

        if m.get('CustomerActivityId') is not None:
            self.customer_activity_id = m.get('CustomerActivityId')

        if m.get('ExtraCouponInfo') is not None:
            temp_model = main_models.DescribeRedeemCodeInfoInnerResponseBodyDataExtraCouponInfo()
            self.extra_coupon_info = temp_model.from_map(m.get('ExtraCouponInfo'))

        if m.get('RedeemCode') is not None:
            self.redeem_code = m.get('RedeemCode')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        if m.get('VerifyStepInfo') is not None:
            temp_model = main_models.DescribeRedeemCodeInfoInnerResponseBodyDataVerifyStepInfo()
            self.verify_step_info = temp_model.from_map(m.get('VerifyStepInfo'))

        if m.get('VerifyStepTotal') is not None:
            self.verify_step_total = m.get('VerifyStepTotal')

        return self

class DescribeRedeemCodeInfoInnerResponseBodyDataVerifyStepInfo(DaraModel):
    def __init__(
        self,
        current_step: int = None,
        total_step: int = None,
        verify_ack_order_id: List[str] = None,
        verify_status: str = None,
    ):
        self.current_step = current_step
        self.total_step = total_step
        self.verify_ack_order_id = verify_ack_order_id
        self.verify_status = verify_status

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.current_step is not None:
            result['CurrentStep'] = self.current_step

        if self.total_step is not None:
            result['TotalStep'] = self.total_step

        if self.verify_ack_order_id is not None:
            result['VerifyAckOrderId'] = self.verify_ack_order_id

        if self.verify_status is not None:
            result['VerifyStatus'] = self.verify_status

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CurrentStep') is not None:
            self.current_step = m.get('CurrentStep')

        if m.get('TotalStep') is not None:
            self.total_step = m.get('TotalStep')

        if m.get('VerifyAckOrderId') is not None:
            self.verify_ack_order_id = m.get('VerifyAckOrderId')

        if m.get('VerifyStatus') is not None:
            self.verify_status = m.get('VerifyStatus')

        return self

class DescribeRedeemCodeInfoInnerResponseBodyDataExtraCouponInfo(DaraModel):
    def __init__(
        self,
        bandwidth: str = None,
        cpu_core_count: str = None,
        labels: List[str] = None,
        memory_size: str = None,
        name: str = None,
        os_type: str = None,
        shopping_mall_pics: str = None,
        spec_code: str = None,
        system_disk_size: str = None,
        user_disk_size: str = None,
    ):
        self.bandwidth = bandwidth
        self.cpu_core_count = cpu_core_count
        self.labels = labels
        self.memory_size = memory_size
        self.name = name
        self.os_type = os_type
        self.shopping_mall_pics = shopping_mall_pics
        self.spec_code = spec_code
        self.system_disk_size = system_disk_size
        self.user_disk_size = user_disk_size

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.bandwidth is not None:
            result['Bandwidth'] = self.bandwidth

        if self.cpu_core_count is not None:
            result['CpuCoreCount'] = self.cpu_core_count

        if self.labels is not None:
            result['Labels'] = self.labels

        if self.memory_size is not None:
            result['MemorySize'] = self.memory_size

        if self.name is not None:
            result['Name'] = self.name

        if self.os_type is not None:
            result['OsType'] = self.os_type

        if self.shopping_mall_pics is not None:
            result['ShoppingMallPics'] = self.shopping_mall_pics

        if self.spec_code is not None:
            result['SpecCode'] = self.spec_code

        if self.system_disk_size is not None:
            result['SystemDiskSize'] = self.system_disk_size

        if self.user_disk_size is not None:
            result['UserDiskSize'] = self.user_disk_size

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Bandwidth') is not None:
            self.bandwidth = m.get('Bandwidth')

        if m.get('CpuCoreCount') is not None:
            self.cpu_core_count = m.get('CpuCoreCount')

        if m.get('Labels') is not None:
            self.labels = m.get('Labels')

        if m.get('MemorySize') is not None:
            self.memory_size = m.get('MemorySize')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('OsType') is not None:
            self.os_type = m.get('OsType')

        if m.get('ShoppingMallPics') is not None:
            self.shopping_mall_pics = m.get('ShoppingMallPics')

        if m.get('SpecCode') is not None:
            self.spec_code = m.get('SpecCode')

        if m.get('SystemDiskSize') is not None:
            self.system_disk_size = m.get('SystemDiskSize')

        if m.get('UserDiskSize') is not None:
            self.user_disk_size = m.get('UserDiskSize')

        return self

class DescribeRedeemCodeInfoInnerResponseBodyDataBenefitInfo(DaraModel):
    def __init__(
        self,
        content: str = None,
        type: str = None,
    ):
        self.content = content
        self.type = type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.content is not None:
            result['Content'] = self.content

        if self.type is not None:
            result['Type'] = self.type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Content') is not None:
            self.content = m.get('Content')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        return self

