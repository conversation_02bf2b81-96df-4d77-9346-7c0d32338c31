# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeRedeemCodeInfoInnerRequest(DaraModel):
    def __init__(
        self,
        end_user_id: str = None,
        redeem_code: str = None,
    ):
        self.end_user_id = end_user_id
        self.redeem_code = redeem_code

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.redeem_code is not None:
            result['RedeemCode'] = self.redeem_code

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('RedeemCode') is not None:
            self.redeem_code = m.get('RedeemCode')

        return self

