# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeInitChannelUserInfoResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeInitChannelUserInfoResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeInitChannelUserInfoResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeInitChannelUserInfoResponseBodyData(DaraModel):
    def __init__(
        self,
        activation_time: str = None,
        brand: str = None,
        channel_id: str = None,
        channel_ids: List[str] = None,
        id: int = None,
        model: str = None,
        pay_type: str = None,
        phone_number: str = None,
        user_id: str = None,
    ):
        self.activation_time = activation_time
        self.brand = brand
        self.channel_id = channel_id
        self.channel_ids = channel_ids
        self.id = id
        self.model = model
        self.pay_type = pay_type
        self.phone_number = phone_number
        self.user_id = user_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activation_time is not None:
            result['ActivationTime'] = self.activation_time

        if self.brand is not None:
            result['Brand'] = self.brand

        if self.channel_id is not None:
            result['ChannelId'] = self.channel_id

        if self.channel_ids is not None:
            result['ChannelIds'] = self.channel_ids

        if self.id is not None:
            result['Id'] = self.id

        if self.model is not None:
            result['Model'] = self.model

        if self.pay_type is not None:
            result['PayType'] = self.pay_type

        if self.phone_number is not None:
            result['PhoneNumber'] = self.phone_number

        if self.user_id is not None:
            result['UserId'] = self.user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivationTime') is not None:
            self.activation_time = m.get('ActivationTime')

        if m.get('Brand') is not None:
            self.brand = m.get('Brand')

        if m.get('ChannelId') is not None:
            self.channel_id = m.get('ChannelId')

        if m.get('ChannelIds') is not None:
            self.channel_ids = m.get('ChannelIds')

        if m.get('Id') is not None:
            self.id = m.get('Id')

        if m.get('Model') is not None:
            self.model = m.get('Model')

        if m.get('PayType') is not None:
            self.pay_type = m.get('PayType')

        if m.get('PhoneNumber') is not None:
            self.phone_number = m.get('PhoneNumber')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        return self

