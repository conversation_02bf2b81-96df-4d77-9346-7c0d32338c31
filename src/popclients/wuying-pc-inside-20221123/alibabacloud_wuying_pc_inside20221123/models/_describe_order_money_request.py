# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeOrderMoneyRequest(DaraModel):
    def __init__(
        self,
        amount: int = None,
        commodity_code: str = None,
        package_code: str = None,
        parent_id: int = None,
        spec_code: str = None,
    ):
        self.amount = amount
        self.commodity_code = commodity_code
        self.package_code = package_code
        self.parent_id = parent_id
        self.spec_code = spec_code

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.amount is not None:
            result['Amount'] = self.amount

        if self.commodity_code is not None:
            result['CommodityCode'] = self.commodity_code

        if self.package_code is not None:
            result['PackageCode'] = self.package_code

        if self.parent_id is not None:
            result['ParentId'] = self.parent_id

        if self.spec_code is not None:
            result['SpecCode'] = self.spec_code

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Amount') is not None:
            self.amount = m.get('Amount')

        if m.get('CommodityCode') is not None:
            self.commodity_code = m.get('CommodityCode')

        if m.get('PackageCode') is not None:
            self.package_code = m.get('PackageCode')

        if m.get('ParentId') is not None:
            self.parent_id = m.get('ParentId')

        if m.get('SpecCode') is not None:
            self.spec_code = m.get('SpecCode')

        return self

