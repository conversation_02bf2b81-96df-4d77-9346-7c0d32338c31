# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateUserApplyInfoRequest(DaraModel):
    def __init__(
        self,
        email: str = None,
        user_union_id: str = None,
    ):
        self.email = email
        self.user_union_id = user_union_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.email is not None:
            result['email'] = self.email

        if self.user_union_id is not None:
            result['userUnionId'] = self.user_union_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('email') is not None:
            self.email = m.get('email')

        if m.get('userUnionId') is not None:
            self.user_union_id = m.get('userUnionId')

        return self

