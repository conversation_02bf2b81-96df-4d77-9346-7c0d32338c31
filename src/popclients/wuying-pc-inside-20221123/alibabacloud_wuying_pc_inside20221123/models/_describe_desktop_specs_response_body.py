# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeDesktopSpecsResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.DescribeDesktopSpecsResponseBodyData] = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.DescribeDesktopSpecsResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeDesktopSpecsResponseBodyData(DaraModel):
    def __init__(
        self,
        bandwidth: str = None,
        charge_type: str = None,
        cpu_core_count: str = None,
        desc: str = None,
        desktop_templates: List[main_models.DescribeDesktopSpecsResponseBodyDataDesktopTemplates] = None,
        gpu_count: str = None,
        gpu_spec: str = None,
        id: int = None,
        label: str = None,
        labels: List[str] = None,
        memory_size: str = None,
        name: str = None,
        os_type: str = None,
        period_unit: str = None,
        pics: str = None,
        resource_type: str = None,
        shopping_mall_pics: str = None,
        sort: str = None,
        spec_code: str = None,
        spec_group: str = None,
        system_disk_size: str = None,
        user_disk_size: str = None,
    ):
        self.bandwidth = bandwidth
        self.charge_type = charge_type
        self.cpu_core_count = cpu_core_count
        self.desc = desc
        self.desktop_templates = desktop_templates
        self.gpu_count = gpu_count
        self.gpu_spec = gpu_spec
        self.id = id
        self.label = label
        self.labels = labels
        self.memory_size = memory_size
        self.name = name
        self.os_type = os_type
        self.period_unit = period_unit
        self.pics = pics
        self.resource_type = resource_type
        self.shopping_mall_pics = shopping_mall_pics
        self.sort = sort
        self.spec_code = spec_code
        self.spec_group = spec_group
        self.system_disk_size = system_disk_size
        self.user_disk_size = user_disk_size

    def validate(self):
        if self.desktop_templates:
            for v1 in self.desktop_templates:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.bandwidth is not None:
            result['Bandwidth'] = self.bandwidth

        if self.charge_type is not None:
            result['ChargeType'] = self.charge_type

        if self.cpu_core_count is not None:
            result['CpuCoreCount'] = self.cpu_core_count

        if self.desc is not None:
            result['Desc'] = self.desc

        result['DesktopTemplates'] = []
        if self.desktop_templates is not None:
            for k1 in self.desktop_templates:
                result['DesktopTemplates'].append(k1.to_map() if k1 else None)

        if self.gpu_count is not None:
            result['GpuCount'] = self.gpu_count

        if self.gpu_spec is not None:
            result['GpuSpec'] = self.gpu_spec

        if self.id is not None:
            result['Id'] = self.id

        if self.label is not None:
            result['Label'] = self.label

        if self.labels is not None:
            result['Labels'] = self.labels

        if self.memory_size is not None:
            result['MemorySize'] = self.memory_size

        if self.name is not None:
            result['Name'] = self.name

        if self.os_type is not None:
            result['OsType'] = self.os_type

        if self.period_unit is not None:
            result['PeriodUnit'] = self.period_unit

        if self.pics is not None:
            result['Pics'] = self.pics

        if self.resource_type is not None:
            result['ResourceType'] = self.resource_type

        if self.shopping_mall_pics is not None:
            result['ShoppingMallPics'] = self.shopping_mall_pics

        if self.sort is not None:
            result['Sort'] = self.sort

        if self.spec_code is not None:
            result['SpecCode'] = self.spec_code

        if self.spec_group is not None:
            result['SpecGroup'] = self.spec_group

        if self.system_disk_size is not None:
            result['SystemDiskSize'] = self.system_disk_size

        if self.user_disk_size is not None:
            result['UserDiskSize'] = self.user_disk_size

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Bandwidth') is not None:
            self.bandwidth = m.get('Bandwidth')

        if m.get('ChargeType') is not None:
            self.charge_type = m.get('ChargeType')

        if m.get('CpuCoreCount') is not None:
            self.cpu_core_count = m.get('CpuCoreCount')

        if m.get('Desc') is not None:
            self.desc = m.get('Desc')

        self.desktop_templates = []
        if m.get('DesktopTemplates') is not None:
            for k1 in m.get('DesktopTemplates'):
                temp_model = main_models.DescribeDesktopSpecsResponseBodyDataDesktopTemplates()
                self.desktop_templates.append(temp_model.from_map(k1))

        if m.get('GpuCount') is not None:
            self.gpu_count = m.get('GpuCount')

        if m.get('GpuSpec') is not None:
            self.gpu_spec = m.get('GpuSpec')

        if m.get('Id') is not None:
            self.id = m.get('Id')

        if m.get('Label') is not None:
            self.label = m.get('Label')

        if m.get('Labels') is not None:
            self.labels = m.get('Labels')

        if m.get('MemorySize') is not None:
            self.memory_size = m.get('MemorySize')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('OsType') is not None:
            self.os_type = m.get('OsType')

        if m.get('PeriodUnit') is not None:
            self.period_unit = m.get('PeriodUnit')

        if m.get('Pics') is not None:
            self.pics = m.get('Pics')

        if m.get('ResourceType') is not None:
            self.resource_type = m.get('ResourceType')

        if m.get('ShoppingMallPics') is not None:
            self.shopping_mall_pics = m.get('ShoppingMallPics')

        if m.get('Sort') is not None:
            self.sort = m.get('Sort')

        if m.get('SpecCode') is not None:
            self.spec_code = m.get('SpecCode')

        if m.get('SpecGroup') is not None:
            self.spec_group = m.get('SpecGroup')

        if m.get('SystemDiskSize') is not None:
            self.system_disk_size = m.get('SystemDiskSize')

        if m.get('UserDiskSize') is not None:
            self.user_disk_size = m.get('UserDiskSize')

        return self

class DescribeDesktopSpecsResponseBodyDataDesktopTemplates(DaraModel):
    def __init__(
        self,
        id: int = None,
        name: str = None,
        price_id: str = None,
        region_id: str = None,
        region_name: str = None,
    ):
        self.id = id
        self.name = name
        self.price_id = price_id
        self.region_id = region_id
        self.region_name = region_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.id is not None:
            result['Id'] = self.id

        if self.name is not None:
            result['Name'] = self.name

        if self.price_id is not None:
            result['PriceId'] = self.price_id

        if self.region_id is not None:
            result['RegionId'] = self.region_id

        if self.region_name is not None:
            result['RegionName'] = self.region_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Id') is not None:
            self.id = m.get('Id')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('PriceId') is not None:
            self.price_id = m.get('PriceId')

        if m.get('RegionId') is not None:
            self.region_id = m.get('RegionId')

        if m.get('RegionName') is not None:
            self.region_name = m.get('RegionName')

        return self

