# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class UpdateCheckinActivityConfigInnerRequest(DaraModel):
    def __init__(
        self,
        activity: str = None,
        auth_code: str = None,
        config: str = None,
        ext: str = None,
    ):
        self.activity = activity
        self.auth_code = auth_code
        self.config = config
        self.ext = ext

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity is not None:
            result['Activity'] = self.activity

        if self.auth_code is not None:
            result['AuthCode'] = self.auth_code

        if self.config is not None:
            result['Config'] = self.config

        if self.ext is not None:
            result['Ext'] = self.ext

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Activity') is not None:
            self.activity = m.get('Activity')

        if m.get('AuthCode') is not None:
            self.auth_code = m.get('AuthCode')

        if m.get('Config') is not None:
            self.config = m.get('Config')

        if m.get('Ext') is not None:
            self.ext = m.get('Ext')

        return self

