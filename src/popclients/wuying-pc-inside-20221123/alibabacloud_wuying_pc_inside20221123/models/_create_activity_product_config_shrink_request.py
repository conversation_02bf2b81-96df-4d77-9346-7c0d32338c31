# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateActivityProductConfigShrinkRequest(DaraModel):
    def __init__(
        self,
        activity_product_config_shrink: str = None,
    ):
        # This parameter is required.
        self.activity_product_config_shrink = activity_product_config_shrink

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity_product_config_shrink is not None:
            result['ActivityProductConfig'] = self.activity_product_config_shrink

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivityProductConfig') is not None:
            self.activity_product_config_shrink = m.get('ActivityProductConfig')

        return self

