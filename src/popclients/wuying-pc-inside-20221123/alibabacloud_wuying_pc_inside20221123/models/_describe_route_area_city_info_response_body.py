# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 


class DescribeRouteAreaCityInfoResponseBody(DaraModel):
    def __init__(
        self,
        data: main_models.DescribeRouteAreaCityInfoResponseBodyData = None,
        err_code: str = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.data = data
        self.err_code = err_code
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.err_code is not None:
            result['ErrCode'] = self.err_code

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Data') is not None:
            temp_model = main_models.DescribeRouteAreaCityInfoResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('ErrCode') is not None:
            self.err_code = m.get('ErrCode')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeRouteAreaCityInfoResponseBodyData(DaraModel):
    def __init__(
        self,
        city: str = None,
        city_code: str = None,
        country: str = None,
        formatted_address: str = None,
        level: str = None,
        province: str = None,
        region: str = None,
    ):
        self.city = city
        self.city_code = city_code
        self.country = country
        self.formatted_address = formatted_address
        self.level = level
        self.province = province
        self.region = region

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.city is not None:
            result['City'] = self.city

        if self.city_code is not None:
            result['CityCode'] = self.city_code

        if self.country is not None:
            result['Country'] = self.country

        if self.formatted_address is not None:
            result['FormattedAddress'] = self.formatted_address

        if self.level is not None:
            result['Level'] = self.level

        if self.province is not None:
            result['Province'] = self.province

        if self.region is not None:
            result['Region'] = self.region

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('City') is not None:
            self.city = m.get('City')

        if m.get('CityCode') is not None:
            self.city_code = m.get('CityCode')

        if m.get('Country') is not None:
            self.country = m.get('Country')

        if m.get('FormattedAddress') is not None:
            self.formatted_address = m.get('FormattedAddress')

        if m.get('Level') is not None:
            self.level = m.get('Level')

        if m.get('Province') is not None:
            self.province = m.get('Province')

        if m.get('Region') is not None:
            self.region = m.get('Region')

        return self

