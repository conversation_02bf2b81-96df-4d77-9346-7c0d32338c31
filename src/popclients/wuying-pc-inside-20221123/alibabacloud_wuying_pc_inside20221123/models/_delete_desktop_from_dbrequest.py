# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DeleteDesktopFromDBRequest(DaraModel):
    def __init__(
        self,
        desktop_id: str = None,
        desktop_owner_ali_uid: int = None,
        request_id: str = None,
    ):
        self.desktop_id = desktop_id
        self.desktop_owner_ali_uid = desktop_owner_ali_uid
        self.request_id = request_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.desktop_owner_ali_uid is not None:
            result['DesktopOwnerAliUid'] = self.desktop_owner_ali_uid

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('DesktopOwnerAliUid') is not None:
            self.desktop_owner_ali_uid = m.get('DesktopOwnerAliUid')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

