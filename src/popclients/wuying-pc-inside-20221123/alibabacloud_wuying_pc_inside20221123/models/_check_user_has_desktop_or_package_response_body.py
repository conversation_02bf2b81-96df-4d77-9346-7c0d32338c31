# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 


class CheckUserHasDesktopOrPackageResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
        user_resource_ownership: main_models.CheckUserHasDesktopOrPackageResponseBodyUserResourceOwnership = None,
    ):
        self.code = code
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id
        self.user_resource_ownership = user_resource_ownership

    def validate(self):
        if self.user_resource_ownership:
            self.user_resource_ownership.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        if self.user_resource_ownership is not None:
            result['UserResourceOwnership'] = self.user_resource_ownership.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        if m.get('UserResourceOwnership') is not None:
            temp_model = main_models.CheckUserHasDesktopOrPackageResponseBodyUserResourceOwnership()
            self.user_resource_ownership = temp_model.from_map(m.get('UserResourceOwnership'))

        return self

class CheckUserHasDesktopOrPackageResponseBodyUserResourceOwnership(DaraModel):
    def __init__(
        self,
        has_desktop_or_package: bool = None,
    ):
        self.has_desktop_or_package = has_desktop_or_package

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.has_desktop_or_package is not None:
            result['HasDesktopOrPackage'] = self.has_desktop_or_package

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('HasDesktopOrPackage') is not None:
            self.has_desktop_or_package = m.get('HasDesktopOrPackage')

        return self

