# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeMultiplePricesShrinkRequest(DaraModel):
    def __init__(
        self,
        activity_id: str = None,
        amount: str = None,
        code_type: str = None,
        enable_discount_coupon: bool = None,
        end_user_id: str = None,
        event_code: str = None,
        login_ali_uid: int = None,
        lx_promotion_id: str = None,
        order_type: str = None,
        package_code: str = None,
        product_list_json_shrink: str = None,
        product_type: str = None,
        promotion_id: str = None,
        resource_id: str = None,
    ):
        self.activity_id = activity_id
        self.amount = amount
        self.code_type = code_type
        self.enable_discount_coupon = enable_discount_coupon
        # This parameter is required.
        self.end_user_id = end_user_id
        self.event_code = event_code
        # This parameter is required.
        self.login_ali_uid = login_ali_uid
        self.lx_promotion_id = lx_promotion_id
        self.order_type = order_type
        self.package_code = package_code
        # This parameter is required.
        self.product_list_json_shrink = product_list_json_shrink
        self.product_type = product_type
        self.promotion_id = promotion_id
        self.resource_id = resource_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity_id is not None:
            result['ActivityId'] = self.activity_id

        if self.amount is not None:
            result['Amount'] = self.amount

        if self.code_type is not None:
            result['CodeType'] = self.code_type

        if self.enable_discount_coupon is not None:
            result['EnableDiscountCoupon'] = self.enable_discount_coupon

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.event_code is not None:
            result['EventCode'] = self.event_code

        if self.login_ali_uid is not None:
            result['LoginAliUid'] = self.login_ali_uid

        if self.lx_promotion_id is not None:
            result['LxPromotionId'] = self.lx_promotion_id

        if self.order_type is not None:
            result['OrderType'] = self.order_type

        if self.package_code is not None:
            result['PackageCode'] = self.package_code

        if self.product_list_json_shrink is not None:
            result['ProductListJson'] = self.product_list_json_shrink

        if self.product_type is not None:
            result['ProductType'] = self.product_type

        if self.promotion_id is not None:
            result['PromotionId'] = self.promotion_id

        if self.resource_id is not None:
            result['ResourceId'] = self.resource_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivityId') is not None:
            self.activity_id = m.get('ActivityId')

        if m.get('Amount') is not None:
            self.amount = m.get('Amount')

        if m.get('CodeType') is not None:
            self.code_type = m.get('CodeType')

        if m.get('EnableDiscountCoupon') is not None:
            self.enable_discount_coupon = m.get('EnableDiscountCoupon')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('EventCode') is not None:
            self.event_code = m.get('EventCode')

        if m.get('LoginAliUid') is not None:
            self.login_ali_uid = m.get('LoginAliUid')

        if m.get('LxPromotionId') is not None:
            self.lx_promotion_id = m.get('LxPromotionId')

        if m.get('OrderType') is not None:
            self.order_type = m.get('OrderType')

        if m.get('PackageCode') is not None:
            self.package_code = m.get('PackageCode')

        if m.get('ProductListJson') is not None:
            self.product_list_json_shrink = m.get('ProductListJson')

        if m.get('ProductType') is not None:
            self.product_type = m.get('ProductType')

        if m.get('PromotionId') is not None:
            self.promotion_id = m.get('PromotionId')

        if m.get('ResourceId') is not None:
            self.resource_id = m.get('ResourceId')

        return self

