# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeBundleRequest(DaraModel):
    def __init__(
        self,
        bundle_id_condition: str = None,
        bundle_type: str = None,
    ):
        self.bundle_id_condition = bundle_id_condition
        self.bundle_type = bundle_type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.bundle_id_condition is not None:
            result['BundleIdCondition'] = self.bundle_id_condition

        if self.bundle_type is not None:
            result['BundleType'] = self.bundle_type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('BundleIdCondition') is not None:
            self.bundle_id_condition = m.get('BundleIdCondition')

        if m.get('BundleType') is not None:
            self.bundle_type = m.get('BundleType')

        return self

