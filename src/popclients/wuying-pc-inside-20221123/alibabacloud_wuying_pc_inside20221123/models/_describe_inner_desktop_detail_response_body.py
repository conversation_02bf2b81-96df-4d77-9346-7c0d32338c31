# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeInnerDesktopDetailResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeInnerDesktopDetailResponseBodyData = None,
        http_status_code: int = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.http_status_code = http_status_code
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.http_status_code is not None:
            result['HttpStatusCode'] = self.http_status_code

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeInnerDesktopDetailResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('HttpStatusCode') is not None:
            self.http_status_code = m.get('HttpStatusCode')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeInnerDesktopDetailResponseBodyData(DaraModel):
    def __init__(
        self,
        assignment_status: str = None,
        biz_status: str = None,
        change_type: str = None,
        charge_label: str = None,
        charge_name: str = None,
        collection_channel_id: str = None,
        connection_status: str = None,
        cpu: int = None,
        create_user_id: str = None,
        creation_time: str = None,
        desktop_id: str = None,
        desktop_name: str = None,
        desktop_spec: main_models.DescribeInnerDesktopDetailResponseBodyDataDesktopSpec = None,
        desktop_status: str = None,
        disks: List[main_models.DescribeInnerDesktopDetailResponseBodyDataDisks] = None,
        expire_time: str = None,
        init_channel_id: str = None,
        last_channel_id: str = None,
        management_flag: str = None,
        memory: int = None,
        package_duration: List[main_models.DescribeInnerDesktopDetailResponseBodyDataPackageDuration] = None,
        progress: str = None,
        region_id: str = None,
        region_name: str = None,
        sessions: List[main_models.DescribeInnerDesktopDetailResponseBodyDataSessions] = None,
        start_time: str = None,
        user_id: str = None,
    ):
        self.assignment_status = assignment_status
        self.biz_status = biz_status
        self.change_type = change_type
        self.charge_label = charge_label
        self.charge_name = charge_name
        self.collection_channel_id = collection_channel_id
        self.connection_status = connection_status
        self.cpu = cpu
        self.create_user_id = create_user_id
        self.creation_time = creation_time
        self.desktop_id = desktop_id
        self.desktop_name = desktop_name
        self.desktop_spec = desktop_spec
        self.desktop_status = desktop_status
        self.disks = disks
        self.expire_time = expire_time
        self.init_channel_id = init_channel_id
        self.last_channel_id = last_channel_id
        self.management_flag = management_flag
        self.memory = memory
        self.package_duration = package_duration
        self.progress = progress
        self.region_id = region_id
        self.region_name = region_name
        self.sessions = sessions
        self.start_time = start_time
        self.user_id = user_id

    def validate(self):
        if self.desktop_spec:
            self.desktop_spec.validate()
        if self.disks:
            for v1 in self.disks:
                 if v1:
                    v1.validate()
        if self.package_duration:
            for v1 in self.package_duration:
                 if v1:
                    v1.validate()
        if self.sessions:
            for v1 in self.sessions:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.assignment_status is not None:
            result['AssignmentStatus'] = self.assignment_status

        if self.biz_status is not None:
            result['BizStatus'] = self.biz_status

        if self.change_type is not None:
            result['ChangeType'] = self.change_type

        if self.charge_label is not None:
            result['ChargeLabel'] = self.charge_label

        if self.charge_name is not None:
            result['ChargeName'] = self.charge_name

        if self.collection_channel_id is not None:
            result['CollectionChannelId'] = self.collection_channel_id

        if self.connection_status is not None:
            result['ConnectionStatus'] = self.connection_status

        if self.cpu is not None:
            result['Cpu'] = self.cpu

        if self.create_user_id is not None:
            result['CreateUserId'] = self.create_user_id

        if self.creation_time is not None:
            result['CreationTime'] = self.creation_time

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.desktop_name is not None:
            result['DesktopName'] = self.desktop_name

        if self.desktop_spec is not None:
            result['DesktopSpec'] = self.desktop_spec.to_map()

        if self.desktop_status is not None:
            result['DesktopStatus'] = self.desktop_status

        result['Disks'] = []
        if self.disks is not None:
            for k1 in self.disks:
                result['Disks'].append(k1.to_map() if k1 else None)

        if self.expire_time is not None:
            result['ExpireTime'] = self.expire_time

        if self.init_channel_id is not None:
            result['InitChannelId'] = self.init_channel_id

        if self.last_channel_id is not None:
            result['LastChannelId'] = self.last_channel_id

        if self.management_flag is not None:
            result['ManagementFlag'] = self.management_flag

        if self.memory is not None:
            result['Memory'] = self.memory

        result['PackageDuration'] = []
        if self.package_duration is not None:
            for k1 in self.package_duration:
                result['PackageDuration'].append(k1.to_map() if k1 else None)

        if self.progress is not None:
            result['Progress'] = self.progress

        if self.region_id is not None:
            result['RegionId'] = self.region_id

        if self.region_name is not None:
            result['RegionName'] = self.region_name

        result['Sessions'] = []
        if self.sessions is not None:
            for k1 in self.sessions:
                result['Sessions'].append(k1.to_map() if k1 else None)

        if self.start_time is not None:
            result['StartTime'] = self.start_time

        if self.user_id is not None:
            result['UserId'] = self.user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AssignmentStatus') is not None:
            self.assignment_status = m.get('AssignmentStatus')

        if m.get('BizStatus') is not None:
            self.biz_status = m.get('BizStatus')

        if m.get('ChangeType') is not None:
            self.change_type = m.get('ChangeType')

        if m.get('ChargeLabel') is not None:
            self.charge_label = m.get('ChargeLabel')

        if m.get('ChargeName') is not None:
            self.charge_name = m.get('ChargeName')

        if m.get('CollectionChannelId') is not None:
            self.collection_channel_id = m.get('CollectionChannelId')

        if m.get('ConnectionStatus') is not None:
            self.connection_status = m.get('ConnectionStatus')

        if m.get('Cpu') is not None:
            self.cpu = m.get('Cpu')

        if m.get('CreateUserId') is not None:
            self.create_user_id = m.get('CreateUserId')

        if m.get('CreationTime') is not None:
            self.creation_time = m.get('CreationTime')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('DesktopName') is not None:
            self.desktop_name = m.get('DesktopName')

        if m.get('DesktopSpec') is not None:
            temp_model = main_models.DescribeInnerDesktopDetailResponseBodyDataDesktopSpec()
            self.desktop_spec = temp_model.from_map(m.get('DesktopSpec'))

        if m.get('DesktopStatus') is not None:
            self.desktop_status = m.get('DesktopStatus')

        self.disks = []
        if m.get('Disks') is not None:
            for k1 in m.get('Disks'):
                temp_model = main_models.DescribeInnerDesktopDetailResponseBodyDataDisks()
                self.disks.append(temp_model.from_map(k1))

        if m.get('ExpireTime') is not None:
            self.expire_time = m.get('ExpireTime')

        if m.get('InitChannelId') is not None:
            self.init_channel_id = m.get('InitChannelId')

        if m.get('LastChannelId') is not None:
            self.last_channel_id = m.get('LastChannelId')

        if m.get('ManagementFlag') is not None:
            self.management_flag = m.get('ManagementFlag')

        if m.get('Memory') is not None:
            self.memory = m.get('Memory')

        self.package_duration = []
        if m.get('PackageDuration') is not None:
            for k1 in m.get('PackageDuration'):
                temp_model = main_models.DescribeInnerDesktopDetailResponseBodyDataPackageDuration()
                self.package_duration.append(temp_model.from_map(k1))

        if m.get('Progress') is not None:
            self.progress = m.get('Progress')

        if m.get('RegionId') is not None:
            self.region_id = m.get('RegionId')

        if m.get('RegionName') is not None:
            self.region_name = m.get('RegionName')

        self.sessions = []
        if m.get('Sessions') is not None:
            for k1 in m.get('Sessions'):
                temp_model = main_models.DescribeInnerDesktopDetailResponseBodyDataSessions()
                self.sessions.append(temp_model.from_map(k1))

        if m.get('StartTime') is not None:
            self.start_time = m.get('StartTime')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        return self

class DescribeInnerDesktopDetailResponseBodyDataSessions(DaraModel):
    def __init__(
        self,
        establishment_time: str = None,
    ):
        self.establishment_time = establishment_time

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.establishment_time is not None:
            result['EstablishmentTime'] = self.establishment_time

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('EstablishmentTime') is not None:
            self.establishment_time = m.get('EstablishmentTime')

        return self

class DescribeInnerDesktopDetailResponseBodyDataPackageDuration(DaraModel):
    def __init__(
        self,
        package_create_time: str = None,
        package_expire_time: str = None,
        package_id: str = None,
        remain_time: int = None,
        status: str = None,
        total_time: int = None,
    ):
        self.package_create_time = package_create_time
        self.package_expire_time = package_expire_time
        self.package_id = package_id
        self.remain_time = remain_time
        self.status = status
        self.total_time = total_time

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.package_create_time is not None:
            result['PackageCreateTime'] = self.package_create_time

        if self.package_expire_time is not None:
            result['PackageExpireTime'] = self.package_expire_time

        if self.package_id is not None:
            result['PackageId'] = self.package_id

        if self.remain_time is not None:
            result['RemainTime'] = self.remain_time

        if self.status is not None:
            result['Status'] = self.status

        if self.total_time is not None:
            result['TotalTime'] = self.total_time

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('PackageCreateTime') is not None:
            self.package_create_time = m.get('PackageCreateTime')

        if m.get('PackageExpireTime') is not None:
            self.package_expire_time = m.get('PackageExpireTime')

        if m.get('PackageId') is not None:
            self.package_id = m.get('PackageId')

        if m.get('RemainTime') is not None:
            self.remain_time = m.get('RemainTime')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        if m.get('TotalTime') is not None:
            self.total_time = m.get('TotalTime')

        return self

class DescribeInnerDesktopDetailResponseBodyDataDisks(DaraModel):
    def __init__(
        self,
        disk_id: str = None,
        disk_size: int = None,
        disk_type: str = None,
    ):
        self.disk_id = disk_id
        self.disk_size = disk_size
        self.disk_type = disk_type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.disk_id is not None:
            result['DiskId'] = self.disk_id

        if self.disk_size is not None:
            result['DiskSize'] = self.disk_size

        if self.disk_type is not None:
            result['DiskType'] = self.disk_type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DiskId') is not None:
            self.disk_id = m.get('DiskId')

        if m.get('DiskSize') is not None:
            self.disk_size = m.get('DiskSize')

        if m.get('DiskType') is not None:
            self.disk_type = m.get('DiskType')

        return self

class DescribeInnerDesktopDetailResponseBodyDataDesktopSpec(DaraModel):
    def __init__(
        self,
        bandwidth: str = None,
        cpu_core_count: str = None,
        desc: str = None,
        gpu_count: str = None,
        gpu_spec: str = None,
        group: str = None,
        memory_size: str = None,
        name: str = None,
        os_type: str = None,
        price_id: str = None,
        spec_code: str = None,
        system_disk_size: str = None,
        user_disk_size: str = None,
    ):
        self.bandwidth = bandwidth
        self.cpu_core_count = cpu_core_count
        self.desc = desc
        self.gpu_count = gpu_count
        self.gpu_spec = gpu_spec
        self.group = group
        self.memory_size = memory_size
        self.name = name
        self.os_type = os_type
        self.price_id = price_id
        self.spec_code = spec_code
        self.system_disk_size = system_disk_size
        self.user_disk_size = user_disk_size

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.bandwidth is not None:
            result['Bandwidth'] = self.bandwidth

        if self.cpu_core_count is not None:
            result['CpuCoreCount'] = self.cpu_core_count

        if self.desc is not None:
            result['Desc'] = self.desc

        if self.gpu_count is not None:
            result['GpuCount'] = self.gpu_count

        if self.gpu_spec is not None:
            result['GpuSpec'] = self.gpu_spec

        if self.group is not None:
            result['Group'] = self.group

        if self.memory_size is not None:
            result['MemorySize'] = self.memory_size

        if self.name is not None:
            result['Name'] = self.name

        if self.os_type is not None:
            result['OsType'] = self.os_type

        if self.price_id is not None:
            result['PriceId'] = self.price_id

        if self.spec_code is not None:
            result['SpecCode'] = self.spec_code

        if self.system_disk_size is not None:
            result['SystemDiskSize'] = self.system_disk_size

        if self.user_disk_size is not None:
            result['UserDiskSize'] = self.user_disk_size

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Bandwidth') is not None:
            self.bandwidth = m.get('Bandwidth')

        if m.get('CpuCoreCount') is not None:
            self.cpu_core_count = m.get('CpuCoreCount')

        if m.get('Desc') is not None:
            self.desc = m.get('Desc')

        if m.get('GpuCount') is not None:
            self.gpu_count = m.get('GpuCount')

        if m.get('GpuSpec') is not None:
            self.gpu_spec = m.get('GpuSpec')

        if m.get('Group') is not None:
            self.group = m.get('Group')

        if m.get('MemorySize') is not None:
            self.memory_size = m.get('MemorySize')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('OsType') is not None:
            self.os_type = m.get('OsType')

        if m.get('PriceId') is not None:
            self.price_id = m.get('PriceId')

        if m.get('SpecCode') is not None:
            self.spec_code = m.get('SpecCode')

        if m.get('SystemDiskSize') is not None:
            self.system_disk_size = m.get('SystemDiskSize')

        if m.get('UserDiskSize') is not None:
            self.user_disk_size = m.get('UserDiskSize')

        return self

