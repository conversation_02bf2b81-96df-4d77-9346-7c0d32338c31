# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateBenefitPoolByOrderRequest(DaraModel):
    def __init__(
        self,
        available_num: int = None,
        benefit_type: str = None,
        channel_id: str = None,
        description: str = None,
        expire: str = None,
        name: str = None,
        operator_id: str = None,
        order_id: str = None,
        spec: str = None,
    ):
        self.available_num = available_num
        self.benefit_type = benefit_type
        self.channel_id = channel_id
        self.description = description
        self.expire = expire
        self.name = name
        self.operator_id = operator_id
        self.order_id = order_id
        self.spec = spec

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.available_num is not None:
            result['AvailableNum'] = self.available_num

        if self.benefit_type is not None:
            result['BenefitType'] = self.benefit_type

        if self.channel_id is not None:
            result['ChannelId'] = self.channel_id

        if self.description is not None:
            result['Description'] = self.description

        if self.expire is not None:
            result['Expire'] = self.expire

        if self.name is not None:
            result['Name'] = self.name

        if self.operator_id is not None:
            result['OperatorId'] = self.operator_id

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.spec is not None:
            result['Spec'] = self.spec

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AvailableNum') is not None:
            self.available_num = m.get('AvailableNum')

        if m.get('BenefitType') is not None:
            self.benefit_type = m.get('BenefitType')

        if m.get('ChannelId') is not None:
            self.channel_id = m.get('ChannelId')

        if m.get('Description') is not None:
            self.description = m.get('Description')

        if m.get('Expire') is not None:
            self.expire = m.get('Expire')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('OperatorId') is not None:
            self.operator_id = m.get('OperatorId')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('Spec') is not None:
            self.spec = m.get('Spec')

        return self

