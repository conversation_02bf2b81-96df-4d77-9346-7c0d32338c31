# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeProductListShrinkRequest(DaraModel):
    def __init__(
        self,
        end_user_id: str = None,
        login_ali_uid: int = None,
        product_code_list_json_shrink: str = None,
        product_type_list_json_shrink: str = None,
        scene: str = None,
        skip_saleable_filter: bool = None,
    ):
        # This parameter is required.
        self.end_user_id = end_user_id
        # This parameter is required.
        self.login_ali_uid = login_ali_uid
        self.product_code_list_json_shrink = product_code_list_json_shrink
        self.product_type_list_json_shrink = product_type_list_json_shrink
        self.scene = scene
        self.skip_saleable_filter = skip_saleable_filter

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.login_ali_uid is not None:
            result['LoginAliUid'] = self.login_ali_uid

        if self.product_code_list_json_shrink is not None:
            result['ProductCodeListJson'] = self.product_code_list_json_shrink

        if self.product_type_list_json_shrink is not None:
            result['ProductTypeListJson'] = self.product_type_list_json_shrink

        if self.scene is not None:
            result['Scene'] = self.scene

        if self.skip_saleable_filter is not None:
            result['SkipSaleableFilter'] = self.skip_saleable_filter

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('LoginAliUid') is not None:
            self.login_ali_uid = m.get('LoginAliUid')

        if m.get('ProductCodeListJson') is not None:
            self.product_code_list_json_shrink = m.get('ProductCodeListJson')

        if m.get('ProductTypeListJson') is not None:
            self.product_type_list_json_shrink = m.get('ProductTypeListJson')

        if m.get('Scene') is not None:
            self.scene = m.get('Scene')

        if m.get('SkipSaleableFilter') is not None:
            self.skip_saleable_filter = m.get('SkipSaleableFilter')

        return self

