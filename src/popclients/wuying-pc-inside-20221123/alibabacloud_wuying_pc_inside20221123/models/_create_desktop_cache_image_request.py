# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateDesktopCacheImageRequest(DaraModel):
    def __init__(
        self,
        app_ids: str = None,
        code_image_id: str = None,
        image_family_code: str = None,
    ):
        self.app_ids = app_ids
        self.code_image_id = code_image_id
        self.image_family_code = image_family_code

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.app_ids is not None:
            result['AppIds'] = self.app_ids

        if self.code_image_id is not None:
            result['CodeImageId'] = self.code_image_id

        if self.image_family_code is not None:
            result['ImageFamilyCode'] = self.image_family_code

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AppIds') is not None:
            self.app_ids = m.get('AppIds')

        if m.get('CodeImageId') is not None:
            self.code_image_id = m.get('CodeImageId')

        if m.get('ImageFamilyCode') is not None:
            self.image_family_code = m.get('ImageFamilyCode')

        return self

