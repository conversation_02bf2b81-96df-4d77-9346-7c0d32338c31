# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class ConsumePoolBenefitRequest(DaraModel):
    def __init__(
        self,
        operator_id: str = None,
        order_id: str = None,
        scene: str = None,
    ):
        self.operator_id = operator_id
        self.order_id = order_id
        self.scene = scene

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.operator_id is not None:
            result['OperatorId'] = self.operator_id

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.scene is not None:
            result['Scene'] = self.scene

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('OperatorId') is not None:
            self.operator_id = m.get('OperatorId')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('Scene') is not None:
            self.scene = m.get('Scene')

        return self

