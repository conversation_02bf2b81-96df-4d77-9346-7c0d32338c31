# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateCloudAppInnerRequest(DaraModel):
    def __init__(
        self,
        app_feature: str = None,
        app_id: str = None,
        app_intro: str = None,
        app_name: str = None,
        desc_image_list: str = None,
        developer: str = None,
        home_image: str = None,
        operator: str = None,
        release_date: str = None,
        tags: str = None,
        theme_type: str = None,
    ):
        self.app_feature = app_feature
        self.app_id = app_id
        self.app_intro = app_intro
        self.app_name = app_name
        self.desc_image_list = desc_image_list
        self.developer = developer
        self.home_image = home_image
        self.operator = operator
        self.release_date = release_date
        self.tags = tags
        self.theme_type = theme_type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.app_feature is not None:
            result['AppFeature'] = self.app_feature

        if self.app_id is not None:
            result['AppId'] = self.app_id

        if self.app_intro is not None:
            result['AppIntro'] = self.app_intro

        if self.app_name is not None:
            result['AppName'] = self.app_name

        if self.desc_image_list is not None:
            result['DescImageList'] = self.desc_image_list

        if self.developer is not None:
            result['Developer'] = self.developer

        if self.home_image is not None:
            result['HomeImage'] = self.home_image

        if self.operator is not None:
            result['Operator'] = self.operator

        if self.release_date is not None:
            result['ReleaseDate'] = self.release_date

        if self.tags is not None:
            result['Tags'] = self.tags

        if self.theme_type is not None:
            result['ThemeType'] = self.theme_type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AppFeature') is not None:
            self.app_feature = m.get('AppFeature')

        if m.get('AppId') is not None:
            self.app_id = m.get('AppId')

        if m.get('AppIntro') is not None:
            self.app_intro = m.get('AppIntro')

        if m.get('AppName') is not None:
            self.app_name = m.get('AppName')

        if m.get('DescImageList') is not None:
            self.desc_image_list = m.get('DescImageList')

        if m.get('Developer') is not None:
            self.developer = m.get('Developer')

        if m.get('HomeImage') is not None:
            self.home_image = m.get('HomeImage')

        if m.get('Operator') is not None:
            self.operator = m.get('Operator')

        if m.get('ReleaseDate') is not None:
            self.release_date = m.get('ReleaseDate')

        if m.get('Tags') is not None:
            self.tags = m.get('Tags')

        if m.get('ThemeType') is not None:
            self.theme_type = m.get('ThemeType')

        return self

