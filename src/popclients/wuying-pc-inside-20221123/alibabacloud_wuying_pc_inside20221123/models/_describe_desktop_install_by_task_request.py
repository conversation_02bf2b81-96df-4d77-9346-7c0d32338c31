# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from typing import List


class DescribeDesktopInstallByTaskRequest(DaraModel):
    def __init__(
        self,
        install_task_ids: List[str] = None,
    ):
        self.install_task_ids = install_task_ids

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.install_task_ids is not None:
            result['InstallTaskIds'] = self.install_task_ids

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('InstallTaskIds') is not None:
            self.install_task_ids = m.get('InstallTaskIds')

        return self

