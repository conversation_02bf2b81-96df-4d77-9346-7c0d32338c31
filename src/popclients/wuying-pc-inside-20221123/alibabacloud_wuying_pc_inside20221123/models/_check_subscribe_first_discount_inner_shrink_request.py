# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CheckSubscribeFirstDiscountInnerShrinkRequest(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        instance_id: str = None,
        operation_type: str = None,
        package_bundles_json_shrink: str = None,
    ):
        self.ali_uid = ali_uid
        self.instance_id = instance_id
        self.operation_type = operation_type
        self.package_bundles_json_shrink = package_bundles_json_shrink

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.instance_id is not None:
            result['InstanceId'] = self.instance_id

        if self.operation_type is not None:
            result['OperationType'] = self.operation_type

        if self.package_bundles_json_shrink is not None:
            result['PackageBundlesJson'] = self.package_bundles_json_shrink

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('InstanceId') is not None:
            self.instance_id = m.get('InstanceId')

        if m.get('OperationType') is not None:
            self.operation_type = m.get('OperationType')

        if m.get('PackageBundlesJson') is not None:
            self.package_bundles_json_shrink = m.get('PackageBundlesJson')

        return self

