# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from typing import List


class CreateBenefitRequest(DaraModel):
    def __init__(
        self,
        description: str = None,
        meta_list: List[str] = None,
        meta_param: str = None,
        name: str = None,
        type: str = None,
    ):
        self.description = description
        self.meta_list = meta_list
        self.meta_param = meta_param
        self.name = name
        self.type = type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.description is not None:
            result['description'] = self.description

        if self.meta_list is not None:
            result['metaList'] = self.meta_list

        if self.meta_param is not None:
            result['metaParam'] = self.meta_param

        if self.name is not None:
            result['name'] = self.name

        if self.type is not None:
            result['type'] = self.type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('description') is not None:
            self.description = m.get('description')

        if m.get('metaList') is not None:
            self.meta_list = m.get('metaList')

        if m.get('metaParam') is not None:
            self.meta_param = m.get('metaParam')

        if m.get('name') is not None:
            self.name = m.get('name')

        if m.get('type') is not None:
            self.type = m.get('type')

        return self

