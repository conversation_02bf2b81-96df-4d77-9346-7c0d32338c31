# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from typing import List


class InnerDescribeCommunityImagesRequest(DaraModel):
    def __init__(
        self,
        community_image_ids: List[str] = None,
        prm_image_ids: List[str] = None,
        show_status: str = None,
    ):
        self.community_image_ids = community_image_ids
        self.prm_image_ids = prm_image_ids
        self.show_status = show_status

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.community_image_ids is not None:
            result['CommunityImageIds'] = self.community_image_ids

        if self.prm_image_ids is not None:
            result['PrmImageIds'] = self.prm_image_ids

        if self.show_status is not None:
            result['ShowStatus'] = self.show_status

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CommunityImageIds') is not None:
            self.community_image_ids = m.get('CommunityImageIds')

        if m.get('PrmImageIds') is not None:
            self.prm_image_ids = m.get('PrmImageIds')

        if m.get('ShowStatus') is not None:
            self.show_status = m.get('ShowStatus')

        return self

