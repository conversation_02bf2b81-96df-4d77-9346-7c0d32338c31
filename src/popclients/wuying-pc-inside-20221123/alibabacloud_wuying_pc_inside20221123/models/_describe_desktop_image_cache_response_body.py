# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeDesktopImageCacheResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeDesktopImageCacheResponseBodyData = None,
        request_id: str = None,
    ):
        self.code = code
        self.data = data
        self.request_id = request_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.request_id is not None:
            result['requestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeDesktopImageCacheResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('requestId') is not None:
            self.request_id = m.get('requestId')

        return self

class DescribeDesktopImageCacheResponseBodyData(DaraModel):
    def __init__(
        self,
        cache_app_ids: List[str] = None,
        code_image_id: str = None,
        image_family_code: str = None,
    ):
        self.cache_app_ids = cache_app_ids
        self.code_image_id = code_image_id
        self.image_family_code = image_family_code

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.cache_app_ids is not None:
            result['CacheAppIds'] = self.cache_app_ids

        if self.code_image_id is not None:
            result['CodeImageId'] = self.code_image_id

        if self.image_family_code is not None:
            result['ImageFamilyCode'] = self.image_family_code

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CacheAppIds') is not None:
            self.cache_app_ids = m.get('CacheAppIds')

        if m.get('CodeImageId') is not None:
            self.code_image_id = m.get('CodeImageId')

        if m.get('ImageFamilyCode') is not None:
            self.image_family_code = m.get('ImageFamilyCode')

        return self

