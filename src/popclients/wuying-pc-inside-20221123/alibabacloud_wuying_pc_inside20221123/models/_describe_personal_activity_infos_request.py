# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribePersonalActivityInfosRequest(DaraModel):
    def __init__(
        self,
        activity_scene: str = None,
        activity_type: str = None,
        aliyun_uid: str = None,
    ):
        self.activity_scene = activity_scene
        self.activity_type = activity_type
        self.aliyun_uid = aliyun_uid

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity_scene is not None:
            result['ActivityScene'] = self.activity_scene

        if self.activity_type is not None:
            result['ActivityType'] = self.activity_type

        if self.aliyun_uid is not None:
            result['AliyunUid'] = self.aliyun_uid

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivityScene') is not None:
            self.activity_scene = m.get('ActivityScene')

        if m.get('ActivityType') is not None:
            self.activity_type = m.get('ActivityType')

        if m.get('AliyunUid') is not None:
            self.aliyun_uid = m.get('AliyunUid')

        return self

