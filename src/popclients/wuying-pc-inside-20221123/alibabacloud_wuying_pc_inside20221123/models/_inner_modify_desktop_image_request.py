# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class InnerModifyDesktopImageRequest(DaraModel):
    def __init__(
        self,
        desktop_id: str = None,
        image_id: str = None,
        is_replaced: bool = None,
    ):
        self.desktop_id = desktop_id
        self.image_id = image_id
        self.is_replaced = is_replaced

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.image_id is not None:
            result['ImageId'] = self.image_id

        if self.is_replaced is not None:
            result['IsReplaced'] = self.is_replaced

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('ImageId') is not None:
            self.image_id = m.get('ImageId')

        if m.get('IsReplaced') is not None:
            self.is_replaced = m.get('IsReplaced')

        return self

