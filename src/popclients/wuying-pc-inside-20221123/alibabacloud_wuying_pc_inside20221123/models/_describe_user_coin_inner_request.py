# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeUserCoinInnerRequest(DaraModel):
    def __init__(
        self,
        aliyun_uid: str = None,
        union_id: str = None,
    ):
        # This parameter is required.
        self.aliyun_uid = aliyun_uid
        # This parameter is required.
        self.union_id = union_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.aliyun_uid is not None:
            result['AliyunUid'] = self.aliyun_uid

        if self.union_id is not None:
            result['UnionId'] = self.union_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliyunUid') is not None:
            self.aliyun_uid = m.get('AliyunUid')

        if m.get('UnionId') is not None:
            self.union_id = m.get('UnionId')

        return self

