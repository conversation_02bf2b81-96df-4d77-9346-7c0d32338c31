# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from typing import List


class DescribeDynamicResourceListRequest(DaraModel):
    def __init__(
        self,
        login_ali_uid: int = None,
        resource_id_list_json: List[str] = None,
        resource_status_list_json: List[str] = None,
        resource_type_list_json: List[str] = None,
    ):
        # This parameter is required.
        self.login_ali_uid = login_ali_uid
        self.resource_id_list_json = resource_id_list_json
        self.resource_status_list_json = resource_status_list_json
        self.resource_type_list_json = resource_type_list_json

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.login_ali_uid is not None:
            result['LoginAliUid'] = self.login_ali_uid

        if self.resource_id_list_json is not None:
            result['ResourceIdListJson'] = self.resource_id_list_json

        if self.resource_status_list_json is not None:
            result['ResourceStatusListJson'] = self.resource_status_list_json

        if self.resource_type_list_json is not None:
            result['ResourceTypeListJson'] = self.resource_type_list_json

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('LoginAliUid') is not None:
            self.login_ali_uid = m.get('LoginAliUid')

        if m.get('ResourceIdListJson') is not None:
            self.resource_id_list_json = m.get('ResourceIdListJson')

        if m.get('ResourceStatusListJson') is not None:
            self.resource_status_list_json = m.get('ResourceStatusListJson')

        if m.get('ResourceTypeListJson') is not None:
            self.resource_type_list_json = m.get('ResourceTypeListJson')

        return self

