# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeUserCoinInfoShrinkRequest(DaraModel):
    def __init__(
        self,
        user_id: str = None,
        user_ids_shrink: str = None,
    ):
        self.user_id = user_id
        self.user_ids_shrink = user_ids_shrink

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.user_id is not None:
            result['UserId'] = self.user_id

        if self.user_ids_shrink is not None:
            result['UserIds'] = self.user_ids_shrink

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        if m.get('UserIds') is not None:
            self.user_ids_shrink = m.get('UserIds')

        return self

