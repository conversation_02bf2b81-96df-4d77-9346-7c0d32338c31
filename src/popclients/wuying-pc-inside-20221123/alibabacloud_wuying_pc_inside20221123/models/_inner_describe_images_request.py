# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from typing import List


class InnerDescribeImagesRequest(DaraModel):
    def __init__(
        self,
        image_ids: List[str] = None,
        image_share_codes: List[str] = None,
        is_asc: bool = None,
        order_by: str = None,
    ):
        self.image_ids = image_ids
        self.image_share_codes = image_share_codes
        self.is_asc = is_asc
        self.order_by = order_by

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.image_ids is not None:
            result['ImageIds'] = self.image_ids

        if self.image_share_codes is not None:
            result['ImageShareCodes'] = self.image_share_codes

        if self.is_asc is not None:
            result['IsAsc'] = self.is_asc

        if self.order_by is not None:
            result['OrderBy'] = self.order_by

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ImageIds') is not None:
            self.image_ids = m.get('ImageIds')

        if m.get('ImageShareCodes') is not None:
            self.image_share_codes = m.get('ImageShareCodes')

        if m.get('IsAsc') is not None:
            self.is_asc = m.get('IsAsc')

        if m.get('OrderBy') is not None:
            self.order_by = m.get('OrderBy')

        return self

