# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class InnerDescribeProductListShrinkRequest(DaraModel):
    def __init__(
        self,
        product_code_list_json_shrink: str = None,
        product_type_list_json_shrink: str = None,
        skip_saleable_filter: bool = None,
    ):
        self.product_code_list_json_shrink = product_code_list_json_shrink
        self.product_type_list_json_shrink = product_type_list_json_shrink
        self.skip_saleable_filter = skip_saleable_filter

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.product_code_list_json_shrink is not None:
            result['ProductCodeListJson'] = self.product_code_list_json_shrink

        if self.product_type_list_json_shrink is not None:
            result['ProductTypeListJson'] = self.product_type_list_json_shrink

        if self.skip_saleable_filter is not None:
            result['skipSaleableFilter'] = self.skip_saleable_filter

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ProductCodeListJson') is not None:
            self.product_code_list_json_shrink = m.get('ProductCodeListJson')

        if m.get('ProductTypeListJson') is not None:
            self.product_type_list_json_shrink = m.get('ProductTypeListJson')

        if m.get('skipSaleableFilter') is not None:
            self.skip_saleable_filter = m.get('skipSaleableFilter')

        return self

