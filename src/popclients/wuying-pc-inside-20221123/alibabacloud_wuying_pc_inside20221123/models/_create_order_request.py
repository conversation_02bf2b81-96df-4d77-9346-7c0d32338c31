# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateOrderRequest(DaraModel):
    def __init__(
        self,
        amount: int = None,
        bundle_id: str = None,
        bundle_sku_id: str = None,
        bundle_type: str = None,
        channel_ali_uid: int = None,
        channel_id: str = None,
        charge_type: str = None,
        desktop_name: str = None,
        from_: str = None,
        order_label: str = None,
        period: int = None,
        period_unit: str = None,
    ):
        self.amount = amount
        self.bundle_id = bundle_id
        self.bundle_sku_id = bundle_sku_id
        self.bundle_type = bundle_type
        self.channel_ali_uid = channel_ali_uid
        self.channel_id = channel_id
        self.charge_type = charge_type
        self.desktop_name = desktop_name
        self.from_ = from_
        self.order_label = order_label
        self.period = period
        self.period_unit = period_unit

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.amount is not None:
            result['Amount'] = self.amount

        if self.bundle_id is not None:
            result['BundleId'] = self.bundle_id

        if self.bundle_sku_id is not None:
            result['BundleSkuId'] = self.bundle_sku_id

        if self.bundle_type is not None:
            result['BundleType'] = self.bundle_type

        if self.channel_ali_uid is not None:
            result['ChannelAliUid'] = self.channel_ali_uid

        if self.channel_id is not None:
            result['ChannelId'] = self.channel_id

        if self.charge_type is not None:
            result['ChargeType'] = self.charge_type

        if self.desktop_name is not None:
            result['DesktopName'] = self.desktop_name

        if self.from_ is not None:
            result['From'] = self.from_

        if self.order_label is not None:
            result['OrderLabel'] = self.order_label

        if self.period is not None:
            result['Period'] = self.period

        if self.period_unit is not None:
            result['PeriodUnit'] = self.period_unit

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Amount') is not None:
            self.amount = m.get('Amount')

        if m.get('BundleId') is not None:
            self.bundle_id = m.get('BundleId')

        if m.get('BundleSkuId') is not None:
            self.bundle_sku_id = m.get('BundleSkuId')

        if m.get('BundleType') is not None:
            self.bundle_type = m.get('BundleType')

        if m.get('ChannelAliUid') is not None:
            self.channel_ali_uid = m.get('ChannelAliUid')

        if m.get('ChannelId') is not None:
            self.channel_id = m.get('ChannelId')

        if m.get('ChargeType') is not None:
            self.charge_type = m.get('ChargeType')

        if m.get('DesktopName') is not None:
            self.desktop_name = m.get('DesktopName')

        if m.get('From') is not None:
            self.from_ = m.get('From')

        if m.get('OrderLabel') is not None:
            self.order_label = m.get('OrderLabel')

        if m.get('Period') is not None:
            self.period = m.get('Period')

        if m.get('PeriodUnit') is not None:
            self.period_unit = m.get('PeriodUnit')

        return self

