# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 


class ModifyActivityProductConfigResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
        update_activity_product_config_response: main_models.ModifyActivityProductConfigResponseBodyUpdateActivityProductConfigResponse = None,
    ):
        self.code = code
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id
        self.update_activity_product_config_response = update_activity_product_config_response

    def validate(self):
        if self.update_activity_product_config_response:
            self.update_activity_product_config_response.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        if self.update_activity_product_config_response is not None:
            result['UpdateActivityProductConfigResponse'] = self.update_activity_product_config_response.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        if m.get('UpdateActivityProductConfigResponse') is not None:
            temp_model = main_models.ModifyActivityProductConfigResponseBodyUpdateActivityProductConfigResponse()
            self.update_activity_product_config_response = temp_model.from_map(m.get('UpdateActivityProductConfigResponse'))

        return self

class ModifyActivityProductConfigResponseBodyUpdateActivityProductConfigResponse(DaraModel):
    def __init__(
        self,
        success: bool = None,
    ):
        self.success = success

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.success is not None:
            result['Success'] = self.success

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Success') is not None:
            self.success = m.get('Success')

        return self

