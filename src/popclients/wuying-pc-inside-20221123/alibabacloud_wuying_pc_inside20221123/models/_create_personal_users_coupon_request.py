# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreatePersonalUsersCouponRequest(DaraModel):
    def __init__(
        self,
        customer_activity_id: str = None,
        user_union_ids: str = None,
    ):
        self.customer_activity_id = customer_activity_id
        self.user_union_ids = user_union_ids

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.customer_activity_id is not None:
            result['customerActivityId'] = self.customer_activity_id

        if self.user_union_ids is not None:
            result['userUnionIds'] = self.user_union_ids

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('customerActivityId') is not None:
            self.customer_activity_id = m.get('customerActivityId')

        if m.get('userUnionIds') is not None:
            self.user_union_ids = m.get('userUnionIds')

        return self

