# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateUserDesktopInstallRequest(DaraModel):
    def __init__(
        self,
        app_ids: str = None,
        auto_arrange_desktop_icons: bool = None,
        desktop_id: str = None,
    ):
        self.app_ids = app_ids
        self.auto_arrange_desktop_icons = auto_arrange_desktop_icons
        self.desktop_id = desktop_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.app_ids is not None:
            result['AppIds'] = self.app_ids

        if self.auto_arrange_desktop_icons is not None:
            result['AutoArrangeDesktopIcons'] = self.auto_arrange_desktop_icons

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AppIds') is not None:
            self.app_ids = m.get('AppIds')

        if m.get('AutoArrangeDesktopIcons') is not None:
            self.auto_arrange_desktop_icons = m.get('AutoArrangeDesktopIcons')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        return self

