# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateStandardOrderRequest(DaraModel):
    def __init__(
        self,
        amount: int = None,
        caller_parent_id: int = None,
        caller_uid: int = None,
        city_name: str = None,
        commodity_code: str = None,
        desktop_id: str = None,
        desktop_name: str = None,
        discount_money: float = None,
        end_user_id: str = None,
        from_: str = None,
        label: str = None,
        original_money: float = None,
        package_code: str = None,
        paid_call_back_url: str = None,
        pay_money: float = None,
        spec_code: str = None,
        team_id: str = None,
        type: str = None,
    ):
        self.amount = amount
        self.caller_parent_id = caller_parent_id
        self.caller_uid = caller_uid
        self.city_name = city_name
        self.commodity_code = commodity_code
        self.desktop_id = desktop_id
        self.desktop_name = desktop_name
        self.discount_money = discount_money
        self.end_user_id = end_user_id
        self.from_ = from_
        self.label = label
        self.original_money = original_money
        self.package_code = package_code
        self.paid_call_back_url = paid_call_back_url
        self.pay_money = pay_money
        self.spec_code = spec_code
        self.team_id = team_id
        self.type = type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.amount is not None:
            result['Amount'] = self.amount

        if self.caller_parent_id is not None:
            result['CallerParentId'] = self.caller_parent_id

        if self.caller_uid is not None:
            result['CallerUid'] = self.caller_uid

        if self.city_name is not None:
            result['CityName'] = self.city_name

        if self.commodity_code is not None:
            result['CommodityCode'] = self.commodity_code

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.desktop_name is not None:
            result['DesktopName'] = self.desktop_name

        if self.discount_money is not None:
            result['DiscountMoney'] = self.discount_money

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.from_ is not None:
            result['From'] = self.from_

        if self.label is not None:
            result['Label'] = self.label

        if self.original_money is not None:
            result['OriginalMoney'] = self.original_money

        if self.package_code is not None:
            result['PackageCode'] = self.package_code

        if self.paid_call_back_url is not None:
            result['PaidCallBackUrl'] = self.paid_call_back_url

        if self.pay_money is not None:
            result['PayMoney'] = self.pay_money

        if self.spec_code is not None:
            result['SpecCode'] = self.spec_code

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        if self.type is not None:
            result['Type'] = self.type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Amount') is not None:
            self.amount = m.get('Amount')

        if m.get('CallerParentId') is not None:
            self.caller_parent_id = m.get('CallerParentId')

        if m.get('CallerUid') is not None:
            self.caller_uid = m.get('CallerUid')

        if m.get('CityName') is not None:
            self.city_name = m.get('CityName')

        if m.get('CommodityCode') is not None:
            self.commodity_code = m.get('CommodityCode')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('DesktopName') is not None:
            self.desktop_name = m.get('DesktopName')

        if m.get('DiscountMoney') is not None:
            self.discount_money = m.get('DiscountMoney')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('From') is not None:
            self.from_ = m.get('From')

        if m.get('Label') is not None:
            self.label = m.get('Label')

        if m.get('OriginalMoney') is not None:
            self.original_money = m.get('OriginalMoney')

        if m.get('PackageCode') is not None:
            self.package_code = m.get('PackageCode')

        if m.get('PaidCallBackUrl') is not None:
            self.paid_call_back_url = m.get('PaidCallBackUrl')

        if m.get('PayMoney') is not None:
            self.pay_money = m.get('PayMoney')

        if m.get('SpecCode') is not None:
            self.spec_code = m.get('SpecCode')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        return self

