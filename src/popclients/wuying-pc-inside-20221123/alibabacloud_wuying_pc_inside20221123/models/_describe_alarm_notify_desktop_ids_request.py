# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeAlarmNotifyDesktopIdsRequest(DaraModel):
    def __init__(
        self,
        alarm_action: str = None,
        team_id: str = None,
        type: str = None,
        user_id: str = None,
    ):
        # This parameter is required.
        self.alarm_action = alarm_action
        self.team_id = team_id
        self.type = type
        self.user_id = user_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.alarm_action is not None:
            result['AlarmAction'] = self.alarm_action

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        if self.type is not None:
            result['Type'] = self.type

        if self.user_id is not None:
            result['UserId'] = self.user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AlarmAction') is not None:
            self.alarm_action = m.get('AlarmAction')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        return self

