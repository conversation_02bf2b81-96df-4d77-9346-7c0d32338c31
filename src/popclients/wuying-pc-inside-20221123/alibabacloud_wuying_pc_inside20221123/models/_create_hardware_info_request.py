# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateHardwareInfoRequest(DaraModel):
    def __init__(
        self,
        model: str = None,
        serial_num: str = None,
    ):
        self.model = model
        self.serial_num = serial_num

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.model is not None:
            result['model'] = self.model

        if self.serial_num is not None:
            result['serialNum'] = self.serial_num

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('model') is not None:
            self.model = m.get('model')

        if m.get('serialNum') is not None:
            self.serial_num = m.get('serialNum')

        return self

