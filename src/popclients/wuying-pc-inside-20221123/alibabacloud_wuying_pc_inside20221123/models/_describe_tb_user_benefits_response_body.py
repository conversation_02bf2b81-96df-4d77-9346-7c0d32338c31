# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeTbUserBenefitsResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.DescribeTbUserBenefitsResponseBodyData] = None,
        err_code: str = None,
        http_status_code: int = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.err_code = err_code
        self.http_status_code = http_status_code
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.err_code is not None:
            result['ErrCode'] = self.err_code

        if self.http_status_code is not None:
            result['HttpStatusCode'] = self.http_status_code

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.DescribeTbUserBenefitsResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('ErrCode') is not None:
            self.err_code = m.get('ErrCode')

        if m.get('HttpStatusCode') is not None:
            self.http_status_code = m.get('HttpStatusCode')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeTbUserBenefitsResponseBodyData(DaraModel):
    def __init__(
        self,
        aliyun_uid: str = None,
        benefits_id: str = None,
        sku_code: str = None,
        status: str = None,
        tb_order_no: str = None,
        telephone_number: str = None,
    ):
        self.aliyun_uid = aliyun_uid
        self.benefits_id = benefits_id
        self.sku_code = sku_code
        self.status = status
        self.tb_order_no = tb_order_no
        self.telephone_number = telephone_number

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.aliyun_uid is not None:
            result['AliyunUid'] = self.aliyun_uid

        if self.benefits_id is not None:
            result['BenefitsId'] = self.benefits_id

        if self.sku_code is not None:
            result['SkuCode'] = self.sku_code

        if self.status is not None:
            result['Status'] = self.status

        if self.tb_order_no is not None:
            result['TbOrderNo'] = self.tb_order_no

        if self.telephone_number is not None:
            result['TelephoneNumber'] = self.telephone_number

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliyunUid') is not None:
            self.aliyun_uid = m.get('AliyunUid')

        if m.get('BenefitsId') is not None:
            self.benefits_id = m.get('BenefitsId')

        if m.get('SkuCode') is not None:
            self.sku_code = m.get('SkuCode')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        if m.get('TbOrderNo') is not None:
            self.tb_order_no = m.get('TbOrderNo')

        if m.get('TelephoneNumber') is not None:
            self.telephone_number = m.get('TelephoneNumber')

        return self

