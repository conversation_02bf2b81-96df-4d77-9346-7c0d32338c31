# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeActivityInnerRequest(DaraModel):
    def __init__(
        self,
        desktop_ids: str = None,
    ):
        # This parameter is required.
        self.desktop_ids = desktop_ids

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.desktop_ids is not None:
            result['DesktopIds'] = self.desktop_ids

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DesktopIds') is not None:
            self.desktop_ids = m.get('DesktopIds')

        return self

