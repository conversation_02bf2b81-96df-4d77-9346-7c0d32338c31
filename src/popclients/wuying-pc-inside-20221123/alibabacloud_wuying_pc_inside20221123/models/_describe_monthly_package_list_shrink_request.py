# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeMonthlyPackageListShrinkRequest(DaraModel):
    def __init__(
        self,
        end_user_id: str = None,
        login_ali_uid: int = None,
        query_redeem_coupon_info: bool = None,
        resource_ids_in_json_format_shrink: str = None,
        should_query_billing_info: bool = None,
        activity_status_list_in_json_format_shrink: str = None,
    ):
        # This parameter is required.
        self.end_user_id = end_user_id
        # This parameter is required.
        self.login_ali_uid = login_ali_uid
        self.query_redeem_coupon_info = query_redeem_coupon_info
        self.resource_ids_in_json_format_shrink = resource_ids_in_json_format_shrink
        self.should_query_billing_info = should_query_billing_info
        self.activity_status_list_in_json_format_shrink = activity_status_list_in_json_format_shrink

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.login_ali_uid is not None:
            result['LoginAliUid'] = self.login_ali_uid

        if self.query_redeem_coupon_info is not None:
            result['QueryRedeemCouponInfo'] = self.query_redeem_coupon_info

        if self.resource_ids_in_json_format_shrink is not None:
            result['ResourceIdsInJsonFormat'] = self.resource_ids_in_json_format_shrink

        if self.should_query_billing_info is not None:
            result['ShouldQueryBillingInfo'] = self.should_query_billing_info

        if self.activity_status_list_in_json_format_shrink is not None:
            result['activityStatusListInJsonFormat'] = self.activity_status_list_in_json_format_shrink

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('LoginAliUid') is not None:
            self.login_ali_uid = m.get('LoginAliUid')

        if m.get('QueryRedeemCouponInfo') is not None:
            self.query_redeem_coupon_info = m.get('QueryRedeemCouponInfo')

        if m.get('ResourceIdsInJsonFormat') is not None:
            self.resource_ids_in_json_format_shrink = m.get('ResourceIdsInJsonFormat')

        if m.get('ShouldQueryBillingInfo') is not None:
            self.should_query_billing_info = m.get('ShouldQueryBillingInfo')

        if m.get('activityStatusListInJsonFormat') is not None:
            self.activity_status_list_in_json_format_shrink = m.get('activityStatusListInJsonFormat')

        return self

