# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeCustomerActivityConfigByKeyRequest(DaraModel):
    def __init__(
        self,
        activity_config_key: str = None,
        activity_id: str = None,
    ):
        self.activity_config_key = activity_config_key
        self.activity_id = activity_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity_config_key is not None:
            result['activityConfigKey'] = self.activity_config_key

        if self.activity_id is not None:
            result['activityId'] = self.activity_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('activityConfigKey') is not None:
            self.activity_config_key = m.get('activityConfigKey')

        if m.get('activityId') is not None:
            self.activity_id = m.get('activityId')

        return self

