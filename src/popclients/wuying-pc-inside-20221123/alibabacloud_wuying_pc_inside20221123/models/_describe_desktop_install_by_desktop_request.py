# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from typing import List


class DescribeDesktopInstallByDesktopRequest(DaraModel):
    def __init__(
        self,
        desktop_ids: List[str] = None,
        query_based_on: str = None,
        query_condition: str = None,
    ):
        self.desktop_ids = desktop_ids
        self.query_based_on = query_based_on
        self.query_condition = query_condition

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.desktop_ids is not None:
            result['DesktopIds'] = self.desktop_ids

        if self.query_based_on is not None:
            result['QueryBasedOn'] = self.query_based_on

        if self.query_condition is not None:
            result['QueryCondition'] = self.query_condition

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DesktopIds') is not None:
            self.desktop_ids = m.get('DesktopIds')

        if m.get('QueryBasedOn') is not None:
            self.query_based_on = m.get('QueryBasedOn')

        if m.get('QueryCondition') is not None:
            self.query_condition = m.get('QueryCondition')

        return self

