# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribePersonalCodeRelationInnerResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.DescribePersonalCodeRelationInnerResponseBodyData] = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.DescribePersonalCodeRelationInnerResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribePersonalCodeRelationInnerResponseBodyData(DaraModel):
    def __init__(
        self,
        event_code: str = None,
        event_type: str = None,
        last_related_time: str = None,
        owner_ali_uid: int = None,
        owner_end_user_id: str = None,
        related_ali_uid: int = None,
        related_end_user_id: str = None,
        relation_id: str = None,
        start_related_time: str = None,
    ):
        self.event_code = event_code
        self.event_type = event_type
        self.last_related_time = last_related_time
        self.owner_ali_uid = owner_ali_uid
        self.owner_end_user_id = owner_end_user_id
        self.related_ali_uid = related_ali_uid
        self.related_end_user_id = related_end_user_id
        self.relation_id = relation_id
        self.start_related_time = start_related_time

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.event_code is not None:
            result['EventCode'] = self.event_code

        if self.event_type is not None:
            result['EventType'] = self.event_type

        if self.last_related_time is not None:
            result['LastRelatedTime'] = self.last_related_time

        if self.owner_ali_uid is not None:
            result['OwnerAliUid'] = self.owner_ali_uid

        if self.owner_end_user_id is not None:
            result['OwnerEndUserId'] = self.owner_end_user_id

        if self.related_ali_uid is not None:
            result['RelatedAliUid'] = self.related_ali_uid

        if self.related_end_user_id is not None:
            result['RelatedEndUserId'] = self.related_end_user_id

        if self.relation_id is not None:
            result['RelationId'] = self.relation_id

        if self.start_related_time is not None:
            result['StartRelatedTime'] = self.start_related_time

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('EventCode') is not None:
            self.event_code = m.get('EventCode')

        if m.get('EventType') is not None:
            self.event_type = m.get('EventType')

        if m.get('LastRelatedTime') is not None:
            self.last_related_time = m.get('LastRelatedTime')

        if m.get('OwnerAliUid') is not None:
            self.owner_ali_uid = m.get('OwnerAliUid')

        if m.get('OwnerEndUserId') is not None:
            self.owner_end_user_id = m.get('OwnerEndUserId')

        if m.get('RelatedAliUid') is not None:
            self.related_ali_uid = m.get('RelatedAliUid')

        if m.get('RelatedEndUserId') is not None:
            self.related_end_user_id = m.get('RelatedEndUserId')

        if m.get('RelationId') is not None:
            self.relation_id = m.get('RelationId')

        if m.get('StartRelatedTime') is not None:
            self.start_related_time = m.get('StartRelatedTime')

        return self

