# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 


class DescribeTerminalSerialNumberByTokenResponseBody(DaraModel):
    def __init__(
        self,
        data: main_models.DescribeTerminalSerialNumberByTokenResponseBodyData = None,
        err_code: str = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.data = data
        self.err_code = err_code
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.err_code is not None:
            result['ErrCode'] = self.err_code

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Data') is not None:
            temp_model = main_models.DescribeTerminalSerialNumberByTokenResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('ErrCode') is not None:
            self.err_code = m.get('ErrCode')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeTerminalSerialNumberByTokenResponseBodyData(DaraModel):
    def __init__(
        self,
        result_code: str = None,
        sn: str = None,
    ):
        self.result_code = result_code
        self.sn = sn

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.result_code is not None:
            result['ResultCode'] = self.result_code

        if self.sn is not None:
            result['Sn'] = self.sn

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ResultCode') is not None:
            self.result_code = m.get('ResultCode')

        if m.get('Sn') is not None:
            self.sn = m.get('Sn')

        return self

