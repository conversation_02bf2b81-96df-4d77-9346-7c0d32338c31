# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class InnerUpdateImageShrinkRequest(DaraModel):
    def __init__(
        self,
        description: str = None,
        forbid_admin_operate: bool = None,
        image_ids_shrink: str = None,
        image_name: str = None,
    ):
        self.description = description
        self.forbid_admin_operate = forbid_admin_operate
        self.image_ids_shrink = image_ids_shrink
        self.image_name = image_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.description is not None:
            result['Description'] = self.description

        if self.forbid_admin_operate is not None:
            result['ForbidAdminOperate'] = self.forbid_admin_operate

        if self.image_ids_shrink is not None:
            result['ImageIds'] = self.image_ids_shrink

        if self.image_name is not None:
            result['ImageName'] = self.image_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Description') is not None:
            self.description = m.get('Description')

        if m.get('ForbidAdminOperate') is not None:
            self.forbid_admin_operate = m.get('ForbidAdminOperate')

        if m.get('ImageIds') is not None:
            self.image_ids_shrink = m.get('ImageIds')

        if m.get('ImageName') is not None:
            self.image_name = m.get('ImageName')

        return self

