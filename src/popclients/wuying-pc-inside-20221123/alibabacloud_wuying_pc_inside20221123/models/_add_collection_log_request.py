# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class AddCollectionLogRequest(DaraModel):
    def __init__(
        self,
        collection_channel_id: str = None,
        desktop_id: str = None,
        init_channel_id: str = None,
        last_channel_id: str = None,
        name: str = None,
        order_id: str = None,
        redeem_code: str = None,
        user_id: str = None,
    ):
        self.collection_channel_id = collection_channel_id
        self.desktop_id = desktop_id
        self.init_channel_id = init_channel_id
        self.last_channel_id = last_channel_id
        self.name = name
        self.order_id = order_id
        self.redeem_code = redeem_code
        self.user_id = user_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.collection_channel_id is not None:
            result['CollectionChannelId'] = self.collection_channel_id

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.init_channel_id is not None:
            result['InitChannelId'] = self.init_channel_id

        if self.last_channel_id is not None:
            result['LastChannelId'] = self.last_channel_id

        if self.name is not None:
            result['Name'] = self.name

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.redeem_code is not None:
            result['RedeemCode'] = self.redeem_code

        if self.user_id is not None:
            result['UserId'] = self.user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CollectionChannelId') is not None:
            self.collection_channel_id = m.get('CollectionChannelId')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('InitChannelId') is not None:
            self.init_channel_id = m.get('InitChannelId')

        if m.get('LastChannelId') is not None:
            self.last_channel_id = m.get('LastChannelId')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('RedeemCode') is not None:
            self.redeem_code = m.get('RedeemCode')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        return self

