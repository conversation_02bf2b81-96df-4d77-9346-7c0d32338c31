# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeSubscribePackagesInnerResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeSubscribePackagesInnerResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeSubscribePackagesInnerResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeSubscribePackagesInnerResponseBodyData(DaraModel):
    def __init__(
        self,
        instance_infos: List[main_models.DescribeSubscribePackagesInnerResponseBodyDataInstanceInfos] = None,
    ):
        self.instance_infos = instance_infos

    def validate(self):
        if self.instance_infos:
            for v1 in self.instance_infos:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['InstanceInfos'] = []
        if self.instance_infos is not None:
            for k1 in self.instance_infos:
                result['InstanceInfos'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.instance_infos = []
        if m.get('InstanceInfos') is not None:
            for k1 in m.get('InstanceInfos'):
                temp_model = main_models.DescribeSubscribePackagesInnerResponseBodyDataInstanceInfos()
                self.instance_infos.append(temp_model.from_map(k1))

        return self

class DescribeSubscribePackagesInnerResponseBodyDataInstanceInfos(DaraModel):
    def __init__(
        self,
        bundle_info: str = None,
        first_discount_price: float = None,
        instance_id: str = None,
        instance_name: str = None,
        is_first_period: bool = None,
        next_withhold_time: str = None,
        subscribe_price: float = None,
        subscribe_start_time: str = None,
        subscribe_status: str = None,
        withhold_channel: str = None,
    ):
        self.bundle_info = bundle_info
        self.first_discount_price = first_discount_price
        self.instance_id = instance_id
        self.instance_name = instance_name
        self.is_first_period = is_first_period
        self.next_withhold_time = next_withhold_time
        self.subscribe_price = subscribe_price
        self.subscribe_start_time = subscribe_start_time
        self.subscribe_status = subscribe_status
        self.withhold_channel = withhold_channel

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.bundle_info is not None:
            result['BundleInfo'] = self.bundle_info

        if self.first_discount_price is not None:
            result['FirstDiscountPrice'] = self.first_discount_price

        if self.instance_id is not None:
            result['InstanceId'] = self.instance_id

        if self.instance_name is not None:
            result['InstanceName'] = self.instance_name

        if self.is_first_period is not None:
            result['IsFirstPeriod'] = self.is_first_period

        if self.next_withhold_time is not None:
            result['NextWithholdTime'] = self.next_withhold_time

        if self.subscribe_price is not None:
            result['SubscribePrice'] = self.subscribe_price

        if self.subscribe_start_time is not None:
            result['SubscribeStartTime'] = self.subscribe_start_time

        if self.subscribe_status is not None:
            result['SubscribeStatus'] = self.subscribe_status

        if self.withhold_channel is not None:
            result['WithholdChannel'] = self.withhold_channel

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('BundleInfo') is not None:
            self.bundle_info = m.get('BundleInfo')

        if m.get('FirstDiscountPrice') is not None:
            self.first_discount_price = m.get('FirstDiscountPrice')

        if m.get('InstanceId') is not None:
            self.instance_id = m.get('InstanceId')

        if m.get('InstanceName') is not None:
            self.instance_name = m.get('InstanceName')

        if m.get('IsFirstPeriod') is not None:
            self.is_first_period = m.get('IsFirstPeriod')

        if m.get('NextWithholdTime') is not None:
            self.next_withhold_time = m.get('NextWithholdTime')

        if m.get('SubscribePrice') is not None:
            self.subscribe_price = m.get('SubscribePrice')

        if m.get('SubscribeStartTime') is not None:
            self.subscribe_start_time = m.get('SubscribeStartTime')

        if m.get('SubscribeStatus') is not None:
            self.subscribe_status = m.get('SubscribeStatus')

        if m.get('WithholdChannel') is not None:
            self.withhold_channel = m.get('WithholdChannel')

        return self

