# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CheckQuotaRequest(DaraModel):
    def __init__(
        self,
        amount: int = None,
        end_user_id: str = None,
        login_ali_uid: int = None,
    ):
        # This parameter is required.
        self.amount = amount
        self.end_user_id = end_user_id
        # This parameter is required.
        self.login_ali_uid = login_ali_uid

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.amount is not None:
            result['Amount'] = self.amount

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.login_ali_uid is not None:
            result['LoginAliUid'] = self.login_ali_uid

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Amount') is not None:
            self.amount = m.get('Amount')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('LoginAliUid') is not None:
            self.login_ali_uid = m.get('LoginAliUid')

        return self

