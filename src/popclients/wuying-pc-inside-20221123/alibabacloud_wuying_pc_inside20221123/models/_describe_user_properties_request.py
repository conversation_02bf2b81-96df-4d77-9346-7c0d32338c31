# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeUserPropertiesRequest(DaraModel):
    def __init__(
        self,
        from_: str = None,
        personal_aliyun_uid: str = None,
        team_aliyun_uid: str = None,
        team_id: str = None,
        user_id: str = None,
    ):
        self.from_ = from_
        self.personal_aliyun_uid = personal_aliyun_uid
        self.team_aliyun_uid = team_aliyun_uid
        self.team_id = team_id
        self.user_id = user_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.from_ is not None:
            result['From'] = self.from_

        if self.personal_aliyun_uid is not None:
            result['PersonalAliyunUid'] = self.personal_aliyun_uid

        if self.team_aliyun_uid is not None:
            result['TeamAliyunUid'] = self.team_aliyun_uid

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        if self.user_id is not None:
            result['UserId'] = self.user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('From') is not None:
            self.from_ = m.get('From')

        if m.get('PersonalAliyunUid') is not None:
            self.personal_aliyun_uid = m.get('PersonalAliyunUid')

        if m.get('TeamAliyunUid') is not None:
            self.team_aliyun_uid = m.get('TeamAliyunUid')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        return self

