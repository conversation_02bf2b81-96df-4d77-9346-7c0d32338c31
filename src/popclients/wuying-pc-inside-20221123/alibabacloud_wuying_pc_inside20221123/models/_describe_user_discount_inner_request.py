# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeUserDiscountInnerRequest(DaraModel):
    def __init__(
        self,
        customer_activity_id_list: str = None,
        user_union_id: str = None,
    ):
        self.customer_activity_id_list = customer_activity_id_list
        self.user_union_id = user_union_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.customer_activity_id_list is not None:
            result['customerActivityIdList'] = self.customer_activity_id_list

        if self.user_union_id is not None:
            result['userUnionId'] = self.user_union_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('customerActivityIdList') is not None:
            self.customer_activity_id_list = m.get('customerActivityIdList')

        if m.get('userUnionId') is not None:
            self.user_union_id = m.get('userUnionId')

        return self

