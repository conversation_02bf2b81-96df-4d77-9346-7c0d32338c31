# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeMonthlyPackageListResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        message: str = None,
        monthly_package_list: List[main_models.DescribeMonthlyPackageListResponseBodyMonthlyPackageList] = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.message = message
        self.monthly_package_list = monthly_package_list
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.monthly_package_list:
            for v1 in self.monthly_package_list:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.message is not None:
            result['Message'] = self.message

        result['MonthlyPackageList'] = []
        if self.monthly_package_list is not None:
            for k1 in self.monthly_package_list:
                result['MonthlyPackageList'].append(k1.to_map() if k1 else None)

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        self.monthly_package_list = []
        if m.get('MonthlyPackageList') is not None:
            for k1 in m.get('MonthlyPackageList'):
                temp_model = main_models.DescribeMonthlyPackageListResponseBodyMonthlyPackageList()
                self.monthly_package_list.append(temp_model.from_map(k1))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeMonthlyPackageListResponseBodyMonthlyPackageList(DaraModel):
    def __init__(
        self,
        activate_status: str = None,
        activation_link: str = None,
        desktop_id: str = None,
        name: str = None,
        owner_ali_uid: str = None,
        owner_end_user_id: str = None,
        package_billing_info_list: List[main_models.DescribeMonthlyPackageListResponseBodyMonthlyPackageListPackageBillingInfoList] = None,
        produc_sku_code: str = None,
        product_code: str = None,
        redeem_coupon_info: main_models.DescribeMonthlyPackageListResponseBodyMonthlyPackageListRedeemCouponInfo = None,
        resource_id: str = None,
        resource_status: str = None,
    ):
        self.activate_status = activate_status
        self.activation_link = activation_link
        self.desktop_id = desktop_id
        self.name = name
        self.owner_ali_uid = owner_ali_uid
        self.owner_end_user_id = owner_end_user_id
        self.package_billing_info_list = package_billing_info_list
        self.produc_sku_code = produc_sku_code
        self.product_code = product_code
        self.redeem_coupon_info = redeem_coupon_info
        self.resource_id = resource_id
        self.resource_status = resource_status

    def validate(self):
        if self.package_billing_info_list:
            for v1 in self.package_billing_info_list:
                 if v1:
                    v1.validate()
        if self.redeem_coupon_info:
            self.redeem_coupon_info.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activate_status is not None:
            result['ActivateStatus'] = self.activate_status

        if self.activation_link is not None:
            result['ActivationLink'] = self.activation_link

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.name is not None:
            result['Name'] = self.name

        if self.owner_ali_uid is not None:
            result['OwnerAliUid'] = self.owner_ali_uid

        if self.owner_end_user_id is not None:
            result['OwnerEndUserId'] = self.owner_end_user_id

        result['PackageBillingInfoList'] = []
        if self.package_billing_info_list is not None:
            for k1 in self.package_billing_info_list:
                result['PackageBillingInfoList'].append(k1.to_map() if k1 else None)

        if self.produc_sku_code is not None:
            result['ProducSkuCode'] = self.produc_sku_code

        if self.product_code is not None:
            result['ProductCode'] = self.product_code

        if self.redeem_coupon_info is not None:
            result['RedeemCouponInfo'] = self.redeem_coupon_info.to_map()

        if self.resource_id is not None:
            result['ResourceId'] = self.resource_id

        if self.resource_status is not None:
            result['ResourceStatus'] = self.resource_status

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivateStatus') is not None:
            self.activate_status = m.get('ActivateStatus')

        if m.get('ActivationLink') is not None:
            self.activation_link = m.get('ActivationLink')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('OwnerAliUid') is not None:
            self.owner_ali_uid = m.get('OwnerAliUid')

        if m.get('OwnerEndUserId') is not None:
            self.owner_end_user_id = m.get('OwnerEndUserId')

        self.package_billing_info_list = []
        if m.get('PackageBillingInfoList') is not None:
            for k1 in m.get('PackageBillingInfoList'):
                temp_model = main_models.DescribeMonthlyPackageListResponseBodyMonthlyPackageListPackageBillingInfoList()
                self.package_billing_info_list.append(temp_model.from_map(k1))

        if m.get('ProducSkuCode') is not None:
            self.produc_sku_code = m.get('ProducSkuCode')

        if m.get('ProductCode') is not None:
            self.product_code = m.get('ProductCode')

        if m.get('RedeemCouponInfo') is not None:
            temp_model = main_models.DescribeMonthlyPackageListResponseBodyMonthlyPackageListRedeemCouponInfo()
            self.redeem_coupon_info = temp_model.from_map(m.get('RedeemCouponInfo'))

        if m.get('ResourceId') is not None:
            self.resource_id = m.get('ResourceId')

        if m.get('ResourceStatus') is not None:
            self.resource_status = m.get('ResourceStatus')

        return self

class DescribeMonthlyPackageListResponseBodyMonthlyPackageListRedeemCouponInfo(DaraModel):
    def __init__(
        self,
        coupon_id: str = None,
    ):
        self.coupon_id = coupon_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.coupon_id is not None:
            result['CouponId'] = self.coupon_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CouponId') is not None:
            self.coupon_id = m.get('CouponId')

        return self

class DescribeMonthlyPackageListResponseBodyMonthlyPackageListPackageBillingInfoList(DaraModel):
    def __init__(
        self,
        expired_time: str = None,
        instance_id: str = None,
        is_auto_pay: bool = None,
        is_renewable: bool = None,
        ised_core_hours: float = None,
        period_end_time: str = None,
        period_start_time: str = None,
        remaining_core_hours: float = None,
        remaining_package_month: int = None,
        start_time: str = None,
        state: str = None,
        total_core_hours: float = None,
    ):
        self.expired_time = expired_time
        self.instance_id = instance_id
        self.is_auto_pay = is_auto_pay
        self.is_renewable = is_renewable
        self.ised_core_hours = ised_core_hours
        self.period_end_time = period_end_time
        self.period_start_time = period_start_time
        self.remaining_core_hours = remaining_core_hours
        self.remaining_package_month = remaining_package_month
        self.start_time = start_time
        self.state = state
        self.total_core_hours = total_core_hours

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.expired_time is not None:
            result['ExpiredTime'] = self.expired_time

        if self.instance_id is not None:
            result['InstanceId'] = self.instance_id

        if self.is_auto_pay is not None:
            result['IsAutoPay'] = self.is_auto_pay

        if self.is_renewable is not None:
            result['IsRenewable'] = self.is_renewable

        if self.ised_core_hours is not None:
            result['IsedCoreHours'] = self.ised_core_hours

        if self.period_end_time is not None:
            result['PeriodEndTime'] = self.period_end_time

        if self.period_start_time is not None:
            result['PeriodStartTime'] = self.period_start_time

        if self.remaining_core_hours is not None:
            result['RemainingCoreHours'] = self.remaining_core_hours

        if self.remaining_package_month is not None:
            result['RemainingPackageMonth'] = self.remaining_package_month

        if self.start_time is not None:
            result['StartTime'] = self.start_time

        if self.state is not None:
            result['State'] = self.state

        if self.total_core_hours is not None:
            result['TotalCoreHours'] = self.total_core_hours

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ExpiredTime') is not None:
            self.expired_time = m.get('ExpiredTime')

        if m.get('InstanceId') is not None:
            self.instance_id = m.get('InstanceId')

        if m.get('IsAutoPay') is not None:
            self.is_auto_pay = m.get('IsAutoPay')

        if m.get('IsRenewable') is not None:
            self.is_renewable = m.get('IsRenewable')

        if m.get('IsedCoreHours') is not None:
            self.ised_core_hours = m.get('IsedCoreHours')

        if m.get('PeriodEndTime') is not None:
            self.period_end_time = m.get('PeriodEndTime')

        if m.get('PeriodStartTime') is not None:
            self.period_start_time = m.get('PeriodStartTime')

        if m.get('RemainingCoreHours') is not None:
            self.remaining_core_hours = m.get('RemainingCoreHours')

        if m.get('RemainingPackageMonth') is not None:
            self.remaining_package_month = m.get('RemainingPackageMonth')

        if m.get('StartTime') is not None:
            self.start_time = m.get('StartTime')

        if m.get('State') is not None:
            self.state = m.get('State')

        if m.get('TotalCoreHours') is not None:
            self.total_core_hours = m.get('TotalCoreHours')

        return self

