# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeUserPersonalCodeInnerResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeUserPersonalCodeInnerResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeUserPersonalCodeInnerResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeUserPersonalCodeInnerResponseBodyData(DaraModel):
    def __init__(
        self,
        code_info: List[main_models.DescribeUserPersonalCodeInnerResponseBodyDataCodeInfo] = None,
        code_status: str = None,
        code_type: str = None,
        coupon_code: str = None,
    ):
        self.code_info = code_info
        self.code_status = code_status
        self.code_type = code_type
        self.coupon_code = coupon_code

    def validate(self):
        if self.code_info:
            for v1 in self.code_info:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['CodeInfo'] = []
        if self.code_info is not None:
            for k1 in self.code_info:
                result['CodeInfo'].append(k1.to_map() if k1 else None)

        if self.code_status is not None:
            result['CodeStatus'] = self.code_status

        if self.code_type is not None:
            result['CodeType'] = self.code_type

        if self.coupon_code is not None:
            result['CouponCode'] = self.coupon_code

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.code_info = []
        if m.get('CodeInfo') is not None:
            for k1 in m.get('CodeInfo'):
                temp_model = main_models.DescribeUserPersonalCodeInnerResponseBodyDataCodeInfo()
                self.code_info.append(temp_model.from_map(k1))

        if m.get('CodeStatus') is not None:
            self.code_status = m.get('CodeStatus')

        if m.get('CodeType') is not None:
            self.code_type = m.get('CodeType')

        if m.get('CouponCode') is not None:
            self.coupon_code = m.get('CouponCode')

        return self

class DescribeUserPersonalCodeInnerResponseBodyDataCodeInfo(DaraModel):
    def __init__(
        self,
        item_style: str = None,
        item_type: str = None,
    ):
        self.item_style = item_style
        self.item_type = item_type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.item_style is not None:
            result['ItemStyle'] = self.item_style

        if self.item_type is not None:
            result['ItemType'] = self.item_type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ItemStyle') is not None:
            self.item_style = m.get('ItemStyle')

        if m.get('ItemType') is not None:
            self.item_type = m.get('ItemType')

        return self

