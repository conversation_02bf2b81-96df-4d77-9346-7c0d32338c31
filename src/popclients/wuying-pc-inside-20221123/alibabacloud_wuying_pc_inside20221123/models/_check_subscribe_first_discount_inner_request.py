# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class CheckSubscribeFirstDiscountInnerRequest(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        instance_id: str = None,
        operation_type: str = None,
        package_bundles_json: List[main_models.CheckSubscribeFirstDiscountInnerRequestPackageBundlesJson] = None,
    ):
        self.ali_uid = ali_uid
        self.instance_id = instance_id
        self.operation_type = operation_type
        self.package_bundles_json = package_bundles_json

    def validate(self):
        if self.package_bundles_json:
            for v1 in self.package_bundles_json:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.instance_id is not None:
            result['InstanceId'] = self.instance_id

        if self.operation_type is not None:
            result['OperationType'] = self.operation_type

        result['PackageBundlesJson'] = []
        if self.package_bundles_json is not None:
            for k1 in self.package_bundles_json:
                result['PackageBundlesJson'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('InstanceId') is not None:
            self.instance_id = m.get('InstanceId')

        if m.get('OperationType') is not None:
            self.operation_type = m.get('OperationType')

        self.package_bundles_json = []
        if m.get('PackageBundlesJson') is not None:
            for k1 in m.get('PackageBundlesJson'):
                temp_model = main_models.CheckSubscribeFirstDiscountInnerRequestPackageBundlesJson()
                self.package_bundles_json.append(temp_model.from_map(k1))

        return self

class CheckSubscribeFirstDiscountInnerRequestPackageBundlesJson(DaraModel):
    def __init__(
        self,
        bundle_id: str = None,
        sku_code: str = None,
    ):
        self.bundle_id = bundle_id
        self.sku_code = sku_code

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.bundle_id is not None:
            result['bundleId'] = self.bundle_id

        if self.sku_code is not None:
            result['skuCode'] = self.sku_code

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('bundleId') is not None:
            self.bundle_id = m.get('bundleId')

        if m.get('skuCode') is not None:
            self.sku_code = m.get('skuCode')

        return self

