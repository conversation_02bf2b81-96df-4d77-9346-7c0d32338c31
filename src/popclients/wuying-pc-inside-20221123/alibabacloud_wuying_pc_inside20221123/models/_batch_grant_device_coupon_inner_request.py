# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class BatchGrantDeviceCouponInnerRequest(DaraModel):
    def __init__(
        self,
        coupon_content: str = None,
        coupon_infos: str = None,
        description: str = None,
        expire_time: str = None,
        name: str = None,
        source: str = None,
        type: str = None,
    ):
        self.coupon_content = coupon_content
        self.coupon_infos = coupon_infos
        self.description = description
        self.expire_time = expire_time
        self.name = name
        self.source = source
        self.type = type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.coupon_content is not None:
            result['CouponContent'] = self.coupon_content

        if self.coupon_infos is not None:
            result['CouponInfos'] = self.coupon_infos

        if self.description is not None:
            result['Description'] = self.description

        if self.expire_time is not None:
            result['ExpireTime'] = self.expire_time

        if self.name is not None:
            result['Name'] = self.name

        if self.source is not None:
            result['Source'] = self.source

        if self.type is not None:
            result['Type'] = self.type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CouponContent') is not None:
            self.coupon_content = m.get('CouponContent')

        if m.get('CouponInfos') is not None:
            self.coupon_infos = m.get('CouponInfos')

        if m.get('Description') is not None:
            self.description = m.get('Description')

        if m.get('ExpireTime') is not None:
            self.expire_time = m.get('ExpireTime')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('Source') is not None:
            self.source = m.get('Source')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        return self

