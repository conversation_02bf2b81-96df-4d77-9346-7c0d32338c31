# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeStandardTeamAvailableOrdersResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.DescribeStandardTeamAvailableOrdersResponseBodyData] = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.DescribeStandardTeamAvailableOrdersResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeStandardTeamAvailableOrdersResponseBodyData(DaraModel):
    def __init__(
        self,
        amount: int = None,
        available_amount: int = None,
        bandwidth: int = None,
        charge_type: str = None,
        commodity_name: str = None,
        cpu: int = None,
        create_order_time: str = None,
        desktop_spec_code: str = None,
        duration_time: int = None,
        expire_date: str = None,
        gpu_count: int = None,
        memory: int = None,
        order_id: str = None,
        os_type: str = None,
        package_period: str = None,
        pay_number: float = None,
        pay_time: str = None,
        period: int = None,
        period_unit: str = None,
        spec_name: str = None,
        system_disk_size: int = None,
        user_disk_size: int = None,
        user_id: str = None,
    ):
        self.amount = amount
        self.available_amount = available_amount
        self.bandwidth = bandwidth
        self.charge_type = charge_type
        self.commodity_name = commodity_name
        self.cpu = cpu
        self.create_order_time = create_order_time
        self.desktop_spec_code = desktop_spec_code
        self.duration_time = duration_time
        self.expire_date = expire_date
        self.gpu_count = gpu_count
        self.memory = memory
        self.order_id = order_id
        self.os_type = os_type
        self.package_period = package_period
        self.pay_number = pay_number
        self.pay_time = pay_time
        self.period = period
        self.period_unit = period_unit
        self.spec_name = spec_name
        self.system_disk_size = system_disk_size
        self.user_disk_size = user_disk_size
        self.user_id = user_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.amount is not None:
            result['Amount'] = self.amount

        if self.available_amount is not None:
            result['AvailableAmount'] = self.available_amount

        if self.bandwidth is not None:
            result['Bandwidth'] = self.bandwidth

        if self.charge_type is not None:
            result['ChargeType'] = self.charge_type

        if self.commodity_name is not None:
            result['CommodityName'] = self.commodity_name

        if self.cpu is not None:
            result['Cpu'] = self.cpu

        if self.create_order_time is not None:
            result['CreateOrderTime'] = self.create_order_time

        if self.desktop_spec_code is not None:
            result['DesktopSpecCode'] = self.desktop_spec_code

        if self.duration_time is not None:
            result['DurationTime'] = self.duration_time

        if self.expire_date is not None:
            result['ExpireDate'] = self.expire_date

        if self.gpu_count is not None:
            result['GpuCount'] = self.gpu_count

        if self.memory is not None:
            result['Memory'] = self.memory

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.os_type is not None:
            result['OsType'] = self.os_type

        if self.package_period is not None:
            result['PackagePeriod'] = self.package_period

        if self.pay_number is not None:
            result['PayNumber'] = self.pay_number

        if self.pay_time is not None:
            result['PayTime'] = self.pay_time

        if self.period is not None:
            result['Period'] = self.period

        if self.period_unit is not None:
            result['PeriodUnit'] = self.period_unit

        if self.spec_name is not None:
            result['SpecName'] = self.spec_name

        if self.system_disk_size is not None:
            result['SystemDiskSize'] = self.system_disk_size

        if self.user_disk_size is not None:
            result['UserDiskSize'] = self.user_disk_size

        if self.user_id is not None:
            result['UserId'] = self.user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Amount') is not None:
            self.amount = m.get('Amount')

        if m.get('AvailableAmount') is not None:
            self.available_amount = m.get('AvailableAmount')

        if m.get('Bandwidth') is not None:
            self.bandwidth = m.get('Bandwidth')

        if m.get('ChargeType') is not None:
            self.charge_type = m.get('ChargeType')

        if m.get('CommodityName') is not None:
            self.commodity_name = m.get('CommodityName')

        if m.get('Cpu') is not None:
            self.cpu = m.get('Cpu')

        if m.get('CreateOrderTime') is not None:
            self.create_order_time = m.get('CreateOrderTime')

        if m.get('DesktopSpecCode') is not None:
            self.desktop_spec_code = m.get('DesktopSpecCode')

        if m.get('DurationTime') is not None:
            self.duration_time = m.get('DurationTime')

        if m.get('ExpireDate') is not None:
            self.expire_date = m.get('ExpireDate')

        if m.get('GpuCount') is not None:
            self.gpu_count = m.get('GpuCount')

        if m.get('Memory') is not None:
            self.memory = m.get('Memory')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('OsType') is not None:
            self.os_type = m.get('OsType')

        if m.get('PackagePeriod') is not None:
            self.package_period = m.get('PackagePeriod')

        if m.get('PayNumber') is not None:
            self.pay_number = m.get('PayNumber')

        if m.get('PayTime') is not None:
            self.pay_time = m.get('PayTime')

        if m.get('Period') is not None:
            self.period = m.get('Period')

        if m.get('PeriodUnit') is not None:
            self.period_unit = m.get('PeriodUnit')

        if m.get('SpecName') is not None:
            self.spec_name = m.get('SpecName')

        if m.get('SystemDiskSize') is not None:
            self.system_disk_size = m.get('SystemDiskSize')

        if m.get('UserDiskSize') is not None:
            self.user_disk_size = m.get('UserDiskSize')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        return self

