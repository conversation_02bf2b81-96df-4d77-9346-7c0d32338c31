# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class UseBusinessRightsShrinkRequest(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        end_user_ids_shrink: str = None,
        hibernate_group_id: str = None,
        image_id: str = None,
        instance_ids_shrink: str = None,
        office_site_id: str = None,
        policy_group_id: str = None,
        right_ids_shrink: str = None,
        right_region_id: str = None,
        type: str = None,
    ):
        self.ali_uid = ali_uid
        self.end_user_ids_shrink = end_user_ids_shrink
        self.hibernate_group_id = hibernate_group_id
        self.image_id = image_id
        self.instance_ids_shrink = instance_ids_shrink
        self.office_site_id = office_site_id
        self.policy_group_id = policy_group_id
        self.right_ids_shrink = right_ids_shrink
        self.right_region_id = right_region_id
        self.type = type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.end_user_ids_shrink is not None:
            result['EndUserIds'] = self.end_user_ids_shrink

        if self.hibernate_group_id is not None:
            result['HibernateGroupId'] = self.hibernate_group_id

        if self.image_id is not None:
            result['ImageId'] = self.image_id

        if self.instance_ids_shrink is not None:
            result['InstanceIds'] = self.instance_ids_shrink

        if self.office_site_id is not None:
            result['OfficeSiteId'] = self.office_site_id

        if self.policy_group_id is not None:
            result['PolicyGroupId'] = self.policy_group_id

        if self.right_ids_shrink is not None:
            result['RightIds'] = self.right_ids_shrink

        if self.right_region_id is not None:
            result['RightRegionId'] = self.right_region_id

        if self.type is not None:
            result['Type'] = self.type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('EndUserIds') is not None:
            self.end_user_ids_shrink = m.get('EndUserIds')

        if m.get('HibernateGroupId') is not None:
            self.hibernate_group_id = m.get('HibernateGroupId')

        if m.get('ImageId') is not None:
            self.image_id = m.get('ImageId')

        if m.get('InstanceIds') is not None:
            self.instance_ids_shrink = m.get('InstanceIds')

        if m.get('OfficeSiteId') is not None:
            self.office_site_id = m.get('OfficeSiteId')

        if m.get('PolicyGroupId') is not None:
            self.policy_group_id = m.get('PolicyGroupId')

        if m.get('RightIds') is not None:
            self.right_ids_shrink = m.get('RightIds')

        if m.get('RightRegionId') is not None:
            self.right_region_id = m.get('RightRegionId')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        return self

