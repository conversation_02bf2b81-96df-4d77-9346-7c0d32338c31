# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class GetChannelGeneralAgentRequest(DaraModel):
    def __init__(
        self,
        aliu_id: str = None,
        desktop_id: str = None,
    ):
        self.aliu_id = aliu_id
        self.desktop_id = desktop_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.aliu_id is not None:
            result['AliuId'] = self.aliu_id

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliuId') is not None:
            self.aliu_id = m.get('AliuId')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        return self

