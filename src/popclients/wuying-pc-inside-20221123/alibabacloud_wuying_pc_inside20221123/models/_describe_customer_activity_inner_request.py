# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeCustomerActivityInnerRequest(DaraModel):
    def __init__(
        self,
        activity_code: str = None,
        activity_id: str = None,
        ali_uid: int = None,
        end_user_id: str = None,
        login_token: str = None,
        session_id: str = None,
    ):
        self.activity_code = activity_code
        self.activity_id = activity_id
        self.ali_uid = ali_uid
        self.end_user_id = end_user_id
        self.login_token = login_token
        self.session_id = session_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity_code is not None:
            result['activityCode'] = self.activity_code

        if self.activity_id is not None:
            result['activityId'] = self.activity_id

        if self.ali_uid is not None:
            result['aliUid'] = self.ali_uid

        if self.end_user_id is not None:
            result['endUserId'] = self.end_user_id

        if self.login_token is not None:
            result['loginToken'] = self.login_token

        if self.session_id is not None:
            result['sessionId'] = self.session_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('activityCode') is not None:
            self.activity_code = m.get('activityCode')

        if m.get('activityId') is not None:
            self.activity_id = m.get('activityId')

        if m.get('aliUid') is not None:
            self.ali_uid = m.get('aliUid')

        if m.get('endUserId') is not None:
            self.end_user_id = m.get('endUserId')

        if m.get('loginToken') is not None:
            self.login_token = m.get('loginToken')

        if m.get('sessionId') is not None:
            self.session_id = m.get('sessionId')

        return self

