# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class OfflineCustomerActivityRequest(DaraModel):
    def __init__(
        self,
        activity_id: str = None,
        operator: str = None,
    ):
        self.activity_id = activity_id
        self.operator = operator

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity_id is not None:
            result['activityId'] = self.activity_id

        if self.operator is not None:
            result['operator'] = self.operator

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('activityId') is not None:
            self.activity_id = m.get('activityId')

        if m.get('operator') is not None:
            self.operator = m.get('operator')

        return self

