# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class VerifyUserApplyCouponRequest(DaraModel):
    def __init__(
        self,
        coupon_id: str = None,
        user_union_id: str = None,
    ):
        self.coupon_id = coupon_id
        self.user_union_id = user_union_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.coupon_id is not None:
            result['couponId'] = self.coupon_id

        if self.user_union_id is not None:
            result['userUnionId'] = self.user_union_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('couponId') is not None:
            self.coupon_id = m.get('couponId')

        if m.get('userUnionId') is not None:
            self.user_union_id = m.get('userUnionId')

        return self

