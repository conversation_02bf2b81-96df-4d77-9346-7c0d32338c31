# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class AddChannelUserRequest(DaraModel):
    def __init__(
        self,
        channel_id: str = None,
        channel_info: str = None,
        phone_number: str = None,
        tenant_id: int = None,
        user_id: str = None,
    ):
        self.channel_id = channel_id
        self.channel_info = channel_info
        self.phone_number = phone_number
        self.tenant_id = tenant_id
        self.user_id = user_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.channel_id is not None:
            result['ChannelId'] = self.channel_id

        if self.channel_info is not None:
            result['ChannelInfo'] = self.channel_info

        if self.phone_number is not None:
            result['PhoneNumber'] = self.phone_number

        if self.tenant_id is not None:
            result['TenantId'] = self.tenant_id

        if self.user_id is not None:
            result['UserId'] = self.user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ChannelId') is not None:
            self.channel_id = m.get('ChannelId')

        if m.get('ChannelInfo') is not None:
            self.channel_info = m.get('ChannelInfo')

        if m.get('PhoneNumber') is not None:
            self.phone_number = m.get('PhoneNumber')

        if m.get('TenantId') is not None:
            self.tenant_id = m.get('TenantId')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        return self

