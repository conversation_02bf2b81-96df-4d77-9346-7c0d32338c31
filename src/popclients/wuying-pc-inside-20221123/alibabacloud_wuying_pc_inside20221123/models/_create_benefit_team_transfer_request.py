# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateBenefitTeamTransferRequest(DaraModel):
    def __init__(
        self,
        benefit_id: str = None,
        benefit_pool_id: str = None,
        channel_id: str = None,
        executor: str = None,
        quantity: int = None,
        team_id: str = None,
    ):
        self.benefit_id = benefit_id
        self.benefit_pool_id = benefit_pool_id
        self.channel_id = channel_id
        self.executor = executor
        self.quantity = quantity
        self.team_id = team_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.benefit_id is not None:
            result['benefitId'] = self.benefit_id

        if self.benefit_pool_id is not None:
            result['benefitPoolId'] = self.benefit_pool_id

        if self.channel_id is not None:
            result['channelId'] = self.channel_id

        if self.executor is not None:
            result['executor'] = self.executor

        if self.quantity is not None:
            result['quantity'] = self.quantity

        if self.team_id is not None:
            result['teamId'] = self.team_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('benefitId') is not None:
            self.benefit_id = m.get('benefitId')

        if m.get('benefitPoolId') is not None:
            self.benefit_pool_id = m.get('benefitPoolId')

        if m.get('channelId') is not None:
            self.channel_id = m.get('channelId')

        if m.get('executor') is not None:
            self.executor = m.get('executor')

        if m.get('quantity') is not None:
            self.quantity = m.get('quantity')

        if m.get('teamId') is not None:
            self.team_id = m.get('teamId')

        return self

