# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class InnerCreateImageRequest(DaraModel):
    def __init__(
        self,
        auto_clean_user_data: bool = None,
        desktop_id: str = None,
        disk_type: str = None,
        image_name: str = None,
    ):
        self.auto_clean_user_data = auto_clean_user_data
        self.desktop_id = desktop_id
        self.disk_type = disk_type
        self.image_name = image_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.auto_clean_user_data is not None:
            result['AutoCleanUserData'] = self.auto_clean_user_data

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.disk_type is not None:
            result['DiskType'] = self.disk_type

        if self.image_name is not None:
            result['ImageName'] = self.image_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AutoCleanUserData') is not None:
            self.auto_clean_user_data = m.get('AutoCleanUserData')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('DiskType') is not None:
            self.disk_type = m.get('DiskType')

        if m.get('ImageName') is not None:
            self.image_name = m.get('ImageName')

        return self

