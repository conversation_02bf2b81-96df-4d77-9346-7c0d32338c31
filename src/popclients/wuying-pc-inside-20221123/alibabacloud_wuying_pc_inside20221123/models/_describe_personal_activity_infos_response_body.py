# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribePersonalActivityInfosResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.DescribePersonalActivityInfosResponseBodyData] = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.DescribePersonalActivityInfosResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribePersonalActivityInfosResponseBodyData(DaraModel):
    def __init__(
        self,
        activity_code: str = None,
        activity_create_time_stamp: int = None,
        activity_name: str = None,
        activity_onwer_id: str = None,
        alipay_url: str = None,
        dashi_code: str = None,
        image_infos: List[main_models.DescribePersonalActivityInfosResponseBodyDataImageInfos] = None,
        monthly_buy_code: str = None,
        monthly_sku_code: str = None,
        qr_code_url: str = None,
        spec_code: str = None,
        status: str = None,
        wechat_url: str = None,
    ):
        self.activity_code = activity_code
        self.activity_create_time_stamp = activity_create_time_stamp
        self.activity_name = activity_name
        self.activity_onwer_id = activity_onwer_id
        self.alipay_url = alipay_url
        self.dashi_code = dashi_code
        self.image_infos = image_infos
        self.monthly_buy_code = monthly_buy_code
        self.monthly_sku_code = monthly_sku_code
        self.qr_code_url = qr_code_url
        self.spec_code = spec_code
        self.status = status
        self.wechat_url = wechat_url

    def validate(self):
        if self.image_infos:
            for v1 in self.image_infos:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity_code is not None:
            result['ActivityCode'] = self.activity_code

        if self.activity_create_time_stamp is not None:
            result['ActivityCreateTimeStamp'] = self.activity_create_time_stamp

        if self.activity_name is not None:
            result['ActivityName'] = self.activity_name

        if self.activity_onwer_id is not None:
            result['ActivityOnwerId'] = self.activity_onwer_id

        if self.alipay_url is not None:
            result['AlipayUrl'] = self.alipay_url

        if self.dashi_code is not None:
            result['DashiCode'] = self.dashi_code

        result['ImageInfos'] = []
        if self.image_infos is not None:
            for k1 in self.image_infos:
                result['ImageInfos'].append(k1.to_map() if k1 else None)

        if self.monthly_buy_code is not None:
            result['MonthlyBuyCode'] = self.monthly_buy_code

        if self.monthly_sku_code is not None:
            result['MonthlySkuCode'] = self.monthly_sku_code

        if self.qr_code_url is not None:
            result['QrCodeUrl'] = self.qr_code_url

        if self.spec_code is not None:
            result['SpecCode'] = self.spec_code

        if self.status is not None:
            result['Status'] = self.status

        if self.wechat_url is not None:
            result['WechatUrl'] = self.wechat_url

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivityCode') is not None:
            self.activity_code = m.get('ActivityCode')

        if m.get('ActivityCreateTimeStamp') is not None:
            self.activity_create_time_stamp = m.get('ActivityCreateTimeStamp')

        if m.get('ActivityName') is not None:
            self.activity_name = m.get('ActivityName')

        if m.get('ActivityOnwerId') is not None:
            self.activity_onwer_id = m.get('ActivityOnwerId')

        if m.get('AlipayUrl') is not None:
            self.alipay_url = m.get('AlipayUrl')

        if m.get('DashiCode') is not None:
            self.dashi_code = m.get('DashiCode')

        self.image_infos = []
        if m.get('ImageInfos') is not None:
            for k1 in m.get('ImageInfos'):
                temp_model = main_models.DescribePersonalActivityInfosResponseBodyDataImageInfos()
                self.image_infos.append(temp_model.from_map(k1))

        if m.get('MonthlyBuyCode') is not None:
            self.monthly_buy_code = m.get('MonthlyBuyCode')

        if m.get('MonthlySkuCode') is not None:
            self.monthly_sku_code = m.get('MonthlySkuCode')

        if m.get('QrCodeUrl') is not None:
            self.qr_code_url = m.get('QrCodeUrl')

        if m.get('SpecCode') is not None:
            self.spec_code = m.get('SpecCode')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        if m.get('WechatUrl') is not None:
            self.wechat_url = m.get('WechatUrl')

        return self

class DescribePersonalActivityInfosResponseBodyDataImageInfos(DaraModel):
    def __init__(
        self,
        image_code: str = None,
        image_name: str = None,
    ):
        self.image_code = image_code
        self.image_name = image_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.image_code is not None:
            result['ImageCode'] = self.image_code

        if self.image_name is not None:
            result['ImageName'] = self.image_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ImageCode') is not None:
            self.image_code = m.get('ImageCode')

        if m.get('ImageName') is not None:
            self.image_name = m.get('ImageName')

        return self

