# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class InnerAnalyzeUserCouponCodeRequest(DaraModel):
    def __init__(
        self,
        aliyun_uid: int = None,
        code_source: str = None,
        code_type: str = None,
        coupon_code: str = None,
        end_user_id: str = None,
        scene: str = None,
        source: str = None,
    ):
        self.aliyun_uid = aliyun_uid
        self.code_source = code_source
        self.code_type = code_type
        self.coupon_code = coupon_code
        self.end_user_id = end_user_id
        self.scene = scene
        self.source = source

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.aliyun_uid is not None:
            result['AliyunUid'] = self.aliyun_uid

        if self.code_source is not None:
            result['CodeSource'] = self.code_source

        if self.code_type is not None:
            result['CodeType'] = self.code_type

        if self.coupon_code is not None:
            result['CouponCode'] = self.coupon_code

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.scene is not None:
            result['Scene'] = self.scene

        if self.source is not None:
            result['Source'] = self.source

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliyunUid') is not None:
            self.aliyun_uid = m.get('AliyunUid')

        if m.get('CodeSource') is not None:
            self.code_source = m.get('CodeSource')

        if m.get('CodeType') is not None:
            self.code_type = m.get('CodeType')

        if m.get('CouponCode') is not None:
            self.coupon_code = m.get('CouponCode')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('Scene') is not None:
            self.scene = m.get('Scene')

        if m.get('Source') is not None:
            self.source = m.get('Source')

        return self

