# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class InnerDescribeImageDetailRequest(DaraModel):
    def __init__(
        self,
        image_instance_id: str = None,
        share_code: str = None,
    ):
        self.image_instance_id = image_instance_id
        self.share_code = share_code

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.image_instance_id is not None:
            result['ImageInstanceId'] = self.image_instance_id

        if self.share_code is not None:
            result['ShareCode'] = self.share_code

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ImageInstanceId') is not None:
            self.image_instance_id = m.get('ImageInstanceId')

        if m.get('ShareCode') is not None:
            self.share_code = m.get('ShareCode')

        return self

