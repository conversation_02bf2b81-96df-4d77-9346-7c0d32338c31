# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeMultiplePricesResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        message: str = None,
        price_response: main_models.DescribeMultiplePricesResponseBodyPriceResponse = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.message = message
        self.price_response = price_response
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.price_response:
            self.price_response.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.message is not None:
            result['Message'] = self.message

        if self.price_response is not None:
            result['PriceResponse'] = self.price_response.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('PriceResponse') is not None:
            temp_model = main_models.DescribeMultiplePricesResponseBodyPriceResponse()
            self.price_response = temp_model.from_map(m.get('PriceResponse'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeMultiplePricesResponseBodyPriceResponse(DaraModel):
    def __init__(
        self,
        price_detail_list: List[main_models.DescribeMultiplePricesResponseBodyPriceResponsePriceDetailList] = None,
        promotion_list: List[main_models.DescribeMultiplePricesResponseBodyPriceResponsePromotionList] = None,
        rule_list: List[main_models.DescribeMultiplePricesResponseBodyPriceResponseRuleList] = None,
        summary_price: main_models.DescribeMultiplePricesResponseBodyPriceResponseSummaryPrice = None,
    ):
        self.price_detail_list = price_detail_list
        self.promotion_list = promotion_list
        self.rule_list = rule_list
        self.summary_price = summary_price

    def validate(self):
        if self.price_detail_list:
            for v1 in self.price_detail_list:
                 if v1:
                    v1.validate()
        if self.promotion_list:
            for v1 in self.promotion_list:
                 if v1:
                    v1.validate()
        if self.rule_list:
            for v1 in self.rule_list:
                 if v1:
                    v1.validate()
        if self.summary_price:
            self.summary_price.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['PriceDetailList'] = []
        if self.price_detail_list is not None:
            for k1 in self.price_detail_list:
                result['PriceDetailList'].append(k1.to_map() if k1 else None)

        result['PromotionList'] = []
        if self.promotion_list is not None:
            for k1 in self.promotion_list:
                result['PromotionList'].append(k1.to_map() if k1 else None)

        result['RuleList'] = []
        if self.rule_list is not None:
            for k1 in self.rule_list:
                result['RuleList'].append(k1.to_map() if k1 else None)

        if self.summary_price is not None:
            result['SummaryPrice'] = self.summary_price.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.price_detail_list = []
        if m.get('PriceDetailList') is not None:
            for k1 in m.get('PriceDetailList'):
                temp_model = main_models.DescribeMultiplePricesResponseBodyPriceResponsePriceDetailList()
                self.price_detail_list.append(temp_model.from_map(k1))

        self.promotion_list = []
        if m.get('PromotionList') is not None:
            for k1 in m.get('PromotionList'):
                temp_model = main_models.DescribeMultiplePricesResponseBodyPriceResponsePromotionList()
                self.promotion_list.append(temp_model.from_map(k1))

        self.rule_list = []
        if m.get('RuleList') is not None:
            for k1 in m.get('RuleList'):
                temp_model = main_models.DescribeMultiplePricesResponseBodyPriceResponseRuleList()
                self.rule_list.append(temp_model.from_map(k1))

        if m.get('SummaryPrice') is not None:
            temp_model = main_models.DescribeMultiplePricesResponseBodyPriceResponseSummaryPrice()
            self.summary_price = temp_model.from_map(m.get('SummaryPrice'))

        return self

class DescribeMultiplePricesResponseBodyPriceResponseSummaryPrice(DaraModel):
    def __init__(
        self,
        currency: str = None,
        discount_price: float = None,
        original_price: float = None,
        trade_price: float = None,
    ):
        self.currency = currency
        self.discount_price = discount_price
        self.original_price = original_price
        self.trade_price = trade_price

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.currency is not None:
            result['Currency'] = self.currency

        if self.discount_price is not None:
            result['DiscountPrice'] = self.discount_price

        if self.original_price is not None:
            result['OriginalPrice'] = self.original_price

        if self.trade_price is not None:
            result['TradePrice'] = self.trade_price

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Currency') is not None:
            self.currency = m.get('Currency')

        if m.get('DiscountPrice') is not None:
            self.discount_price = m.get('DiscountPrice')

        if m.get('OriginalPrice') is not None:
            self.original_price = m.get('OriginalPrice')

        if m.get('TradePrice') is not None:
            self.trade_price = m.get('TradePrice')

        return self

class DescribeMultiplePricesResponseBodyPriceResponseRuleList(DaraModel):
    def __init__(
        self,
        desc: str = None,
        rule_id: str = None,
    ):
        self.desc = desc
        self.rule_id = rule_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.desc is not None:
            result['Desc'] = self.desc

        if self.rule_id is not None:
            result['RuleId'] = self.rule_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Desc') is not None:
            self.desc = m.get('Desc')

        if m.get('RuleId') is not None:
            self.rule_id = m.get('RuleId')

        return self

class DescribeMultiplePricesResponseBodyPriceResponsePromotionList(DaraModel):
    def __init__(
        self,
        option_code: str = None,
        promotion_description: str = None,
        promotion_id: str = None,
        promotion_name: str = None,
        selected: bool = None,
    ):
        self.option_code = option_code
        self.promotion_description = promotion_description
        self.promotion_id = promotion_id
        self.promotion_name = promotion_name
        self.selected = selected

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.option_code is not None:
            result['OptionCode'] = self.option_code

        if self.promotion_description is not None:
            result['PromotionDescription'] = self.promotion_description

        if self.promotion_id is not None:
            result['PromotionId'] = self.promotion_id

        if self.promotion_name is not None:
            result['PromotionName'] = self.promotion_name

        if self.selected is not None:
            result['Selected'] = self.selected

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('OptionCode') is not None:
            self.option_code = m.get('OptionCode')

        if m.get('PromotionDescription') is not None:
            self.promotion_description = m.get('PromotionDescription')

        if m.get('PromotionId') is not None:
            self.promotion_id = m.get('PromotionId')

        if m.get('PromotionName') is not None:
            self.promotion_name = m.get('PromotionName')

        if m.get('Selected') is not None:
            self.selected = m.get('Selected')

        return self

class DescribeMultiplePricesResponseBodyPriceResponsePriceDetailList(DaraModel):
    def __init__(
        self,
        price: main_models.DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListPrice = None,
        product_and_sku_detail: main_models.DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetail = None,
    ):
        self.price = price
        self.product_and_sku_detail = product_and_sku_detail

    def validate(self):
        if self.price:
            self.price.validate()
        if self.product_and_sku_detail:
            self.product_and_sku_detail.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.price is not None:
            result['Price'] = self.price.to_map()

        if self.product_and_sku_detail is not None:
            result['ProductAndSkuDetail'] = self.product_and_sku_detail.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Price') is not None:
            temp_model = main_models.DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListPrice()
            self.price = temp_model.from_map(m.get('Price'))

        if m.get('ProductAndSkuDetail') is not None:
            temp_model = main_models.DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetail()
            self.product_and_sku_detail = temp_model.from_map(m.get('ProductAndSkuDetail'))

        return self

class DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetail(DaraModel):
    def __init__(
        self,
        product: main_models.DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProduct = None,
        product_sku: main_models.DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProductSku = None,
    ):
        self.product = product
        self.product_sku = product_sku

    def validate(self):
        if self.product:
            self.product.validate()
        if self.product_sku:
            self.product_sku.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.product is not None:
            result['Product'] = self.product.to_map()

        if self.product_sku is not None:
            result['ProductSku'] = self.product_sku.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Product') is not None:
            temp_model = main_models.DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProduct()
            self.product = temp_model.from_map(m.get('Product'))

        if m.get('ProductSku') is not None:
            temp_model = main_models.DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProductSku()
            self.product_sku = temp_model.from_map(m.get('ProductSku'))

        return self

class DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProductSku(DaraModel):
    def __init__(
        self,
        attribute: str = None,
        sku_code: str = None,
        sku_desc: str = None,
        sku_name: str = None,
    ):
        self.attribute = attribute
        self.sku_code = sku_code
        self.sku_desc = sku_desc
        self.sku_name = sku_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.attribute is not None:
            result['Attribute'] = self.attribute

        if self.sku_code is not None:
            result['SkuCode'] = self.sku_code

        if self.sku_desc is not None:
            result['SkuDesc'] = self.sku_desc

        if self.sku_name is not None:
            result['SkuName'] = self.sku_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Attribute') is not None:
            self.attribute = m.get('Attribute')

        if m.get('SkuCode') is not None:
            self.sku_code = m.get('SkuCode')

        if m.get('SkuDesc') is not None:
            self.sku_desc = m.get('SkuDesc')

        if m.get('SkuName') is not None:
            self.sku_name = m.get('SkuName')

        return self

class DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProduct(DaraModel):
    def __init__(
        self,
        enable_discount_coupon: bool = None,
        is_first_buy: bool = None,
        is_subscribe: bool = None,
        product_code: str = None,
        product_desc: str = None,
        product_name: str = None,
    ):
        self.enable_discount_coupon = enable_discount_coupon
        self.is_first_buy = is_first_buy
        self.is_subscribe = is_subscribe
        self.product_code = product_code
        self.product_desc = product_desc
        self.product_name = product_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.enable_discount_coupon is not None:
            result['EnableDiscountCoupon'] = self.enable_discount_coupon

        if self.is_first_buy is not None:
            result['IsFirstBuy'] = self.is_first_buy

        if self.is_subscribe is not None:
            result['IsSubscribe'] = self.is_subscribe

        if self.product_code is not None:
            result['ProductCode'] = self.product_code

        if self.product_desc is not None:
            result['ProductDesc'] = self.product_desc

        if self.product_name is not None:
            result['ProductName'] = self.product_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('EnableDiscountCoupon') is not None:
            self.enable_discount_coupon = m.get('EnableDiscountCoupon')

        if m.get('IsFirstBuy') is not None:
            self.is_first_buy = m.get('IsFirstBuy')

        if m.get('IsSubscribe') is not None:
            self.is_subscribe = m.get('IsSubscribe')

        if m.get('ProductCode') is not None:
            self.product_code = m.get('ProductCode')

        if m.get('ProductDesc') is not None:
            self.product_desc = m.get('ProductDesc')

        if m.get('ProductName') is not None:
            self.product_name = m.get('ProductName')

        return self

class DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListPrice(DaraModel):
    def __init__(
        self,
        currency: str = None,
        discount_price: float = None,
        original_price: float = None,
        trade_price: float = None,
    ):
        self.currency = currency
        self.discount_price = discount_price
        self.original_price = original_price
        self.trade_price = trade_price

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.currency is not None:
            result['Currency'] = self.currency

        if self.discount_price is not None:
            result['DiscountPrice'] = self.discount_price

        if self.original_price is not None:
            result['OriginalPrice'] = self.original_price

        if self.trade_price is not None:
            result['TradePrice'] = self.trade_price

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Currency') is not None:
            self.currency = m.get('Currency')

        if m.get('DiscountPrice') is not None:
            self.discount_price = m.get('DiscountPrice')

        if m.get('OriginalPrice') is not None:
            self.original_price = m.get('OriginalPrice')

        if m.get('TradePrice') is not None:
            self.trade_price = m.get('TradePrice')

        return self

