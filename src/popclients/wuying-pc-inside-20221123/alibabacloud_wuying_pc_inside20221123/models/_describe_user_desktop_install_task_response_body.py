# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 


class DescribeUserDesktopInstallTaskResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeUserDesktopInstallTaskResponseBodyData = None,
        request_id: str = None,
    ):
        self.code = code
        self.data = data
        self.request_id = request_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.request_id is not None:
            result['requestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeUserDesktopInstallTaskResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('requestId') is not None:
            self.request_id = m.get('requestId')

        return self

class DescribeUserDesktopInstallTaskResponseBodyData(DaraModel):
    def __init__(
        self,
        process_percent: float = None,
        status: str = None,
        task_id: str = None,
    ):
        self.process_percent = process_percent
        self.status = status
        self.task_id = task_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.process_percent is not None:
            result['ProcessPercent'] = self.process_percent

        if self.status is not None:
            result['Status'] = self.status

        if self.task_id is not None:
            result['TaskId'] = self.task_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ProcessPercent') is not None:
            self.process_percent = m.get('ProcessPercent')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        if m.get('TaskId') is not None:
            self.task_id = m.get('TaskId')

        return self

