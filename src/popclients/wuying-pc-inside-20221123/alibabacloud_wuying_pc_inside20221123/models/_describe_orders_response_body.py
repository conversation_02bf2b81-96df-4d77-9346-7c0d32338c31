# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeOrdersResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.DescribeOrdersResponseBodyData] = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.DescribeOrdersResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeOrdersResponseBodyData(DaraModel):
    def __init__(
        self,
        bundle_id: str = None,
        bundle_info: str = None,
        charge_type: str = None,
        create_from: str = None,
        create_time: str = None,
        creator_ali_uid: int = None,
        desktop_ids: str = None,
        duration_time: int = None,
        order_id: str = None,
        order_label: str = None,
        order_number: str = None,
        order_status: str = None,
        pay_number: str = None,
        pay_time: str = None,
        period: int = None,
        period_unit: str = None,
        sku_code: str = None,
        sku_info: str = None,
        team_ali_uid: int = None,
        team_id: str = None,
        type: str = None,
    ):
        self.bundle_id = bundle_id
        self.bundle_info = bundle_info
        self.charge_type = charge_type
        self.create_from = create_from
        self.create_time = create_time
        self.creator_ali_uid = creator_ali_uid
        self.desktop_ids = desktop_ids
        self.duration_time = duration_time
        self.order_id = order_id
        self.order_label = order_label
        self.order_number = order_number
        self.order_status = order_status
        self.pay_number = pay_number
        self.pay_time = pay_time
        self.period = period
        self.period_unit = period_unit
        self.sku_code = sku_code
        self.sku_info = sku_info
        self.team_ali_uid = team_ali_uid
        self.team_id = team_id
        self.type = type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.bundle_id is not None:
            result['BundleId'] = self.bundle_id

        if self.bundle_info is not None:
            result['BundleInfo'] = self.bundle_info

        if self.charge_type is not None:
            result['ChargeType'] = self.charge_type

        if self.create_from is not None:
            result['CreateFrom'] = self.create_from

        if self.create_time is not None:
            result['CreateTime'] = self.create_time

        if self.creator_ali_uid is not None:
            result['CreatorAliUid'] = self.creator_ali_uid

        if self.desktop_ids is not None:
            result['DesktopIds'] = self.desktop_ids

        if self.duration_time is not None:
            result['DurationTime'] = self.duration_time

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.order_label is not None:
            result['OrderLabel'] = self.order_label

        if self.order_number is not None:
            result['OrderNumber'] = self.order_number

        if self.order_status is not None:
            result['OrderStatus'] = self.order_status

        if self.pay_number is not None:
            result['PayNumber'] = self.pay_number

        if self.pay_time is not None:
            result['PayTime'] = self.pay_time

        if self.period is not None:
            result['Period'] = self.period

        if self.period_unit is not None:
            result['PeriodUnit'] = self.period_unit

        if self.sku_code is not None:
            result['SkuCode'] = self.sku_code

        if self.sku_info is not None:
            result['SkuInfo'] = self.sku_info

        if self.team_ali_uid is not None:
            result['TeamAliUid'] = self.team_ali_uid

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        if self.type is not None:
            result['Type'] = self.type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('BundleId') is not None:
            self.bundle_id = m.get('BundleId')

        if m.get('BundleInfo') is not None:
            self.bundle_info = m.get('BundleInfo')

        if m.get('ChargeType') is not None:
            self.charge_type = m.get('ChargeType')

        if m.get('CreateFrom') is not None:
            self.create_from = m.get('CreateFrom')

        if m.get('CreateTime') is not None:
            self.create_time = m.get('CreateTime')

        if m.get('CreatorAliUid') is not None:
            self.creator_ali_uid = m.get('CreatorAliUid')

        if m.get('DesktopIds') is not None:
            self.desktop_ids = m.get('DesktopIds')

        if m.get('DurationTime') is not None:
            self.duration_time = m.get('DurationTime')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('OrderLabel') is not None:
            self.order_label = m.get('OrderLabel')

        if m.get('OrderNumber') is not None:
            self.order_number = m.get('OrderNumber')

        if m.get('OrderStatus') is not None:
            self.order_status = m.get('OrderStatus')

        if m.get('PayNumber') is not None:
            self.pay_number = m.get('PayNumber')

        if m.get('PayTime') is not None:
            self.pay_time = m.get('PayTime')

        if m.get('Period') is not None:
            self.period = m.get('Period')

        if m.get('PeriodUnit') is not None:
            self.period_unit = m.get('PeriodUnit')

        if m.get('SkuCode') is not None:
            self.sku_code = m.get('SkuCode')

        if m.get('SkuInfo') is not None:
            self.sku_info = m.get('SkuInfo')

        if m.get('TeamAliUid') is not None:
            self.team_ali_uid = m.get('TeamAliUid')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        return self

