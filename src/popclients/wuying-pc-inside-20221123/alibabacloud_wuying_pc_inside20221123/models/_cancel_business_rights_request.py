# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CancelBusinessRightsRequest(DaraModel):
    def __init__(
        self,
        rights_id: str = None,
        type: str = None,
    ):
        self.rights_id = rights_id
        self.type = type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.rights_id is not None:
            result['RightsId'] = self.rights_id

        if self.type is not None:
            result['Type'] = self.type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('RightsId') is not None:
            self.rights_id = m.get('RightsId')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        return self

