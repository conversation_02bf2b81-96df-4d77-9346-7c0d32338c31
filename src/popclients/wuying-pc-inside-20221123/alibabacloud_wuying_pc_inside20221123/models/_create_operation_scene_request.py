# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateOperationSceneRequest(DaraModel):
    def __init__(
        self,
        device_category: str = None,
        display_type: str = None,
        name: str = None,
        resource_type: str = None,
        rotation_seconds: int = None,
        scene_id: str = None,
        user_group: str = None,
    ):
        self.device_category = device_category
        self.display_type = display_type
        self.name = name
        self.resource_type = resource_type
        self.rotation_seconds = rotation_seconds
        self.scene_id = scene_id
        self.user_group = user_group

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.device_category is not None:
            result['DeviceCategory'] = self.device_category

        if self.display_type is not None:
            result['DisplayType'] = self.display_type

        if self.name is not None:
            result['Name'] = self.name

        if self.resource_type is not None:
            result['ResourceType'] = self.resource_type

        if self.rotation_seconds is not None:
            result['RotationSeconds'] = self.rotation_seconds

        if self.scene_id is not None:
            result['SceneId'] = self.scene_id

        if self.user_group is not None:
            result['UserGroup'] = self.user_group

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DeviceCategory') is not None:
            self.device_category = m.get('DeviceCategory')

        if m.get('DisplayType') is not None:
            self.display_type = m.get('DisplayType')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('ResourceType') is not None:
            self.resource_type = m.get('ResourceType')

        if m.get('RotationSeconds') is not None:
            self.rotation_seconds = m.get('RotationSeconds')

        if m.get('SceneId') is not None:
            self.scene_id = m.get('SceneId')

        if m.get('UserGroup') is not None:
            self.user_group = m.get('UserGroup')

        return self

