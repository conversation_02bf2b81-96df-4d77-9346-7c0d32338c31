# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeRouteAreaRequest(DaraModel):
    def __init__(
        self,
        city_name: str = None,
    ):
        self.city_name = city_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.city_name is not None:
            result['CityName'] = self.city_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CityName') is not None:
            self.city_name = m.get('CityName')

        return self

