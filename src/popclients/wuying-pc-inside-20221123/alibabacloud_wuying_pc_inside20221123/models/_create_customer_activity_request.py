# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateCustomerActivityRequest(DaraModel):
    def __init__(
        self,
        activity_config: str = None,
        activity_desc: str = None,
        activity_end_time: str = None,
        activity_id: str = None,
        activity_name: str = None,
        activity_start_time: str = None,
        activity_status: str = None,
        activity_type: str = None,
        and_match_rules: str = None,
        executors: str = None,
        operator: str = None,
        or_match_rules: str = None,
        public_visibility: str = None,
    ):
        self.activity_config = activity_config
        self.activity_desc = activity_desc
        self.activity_end_time = activity_end_time
        self.activity_id = activity_id
        self.activity_name = activity_name
        self.activity_start_time = activity_start_time
        self.activity_status = activity_status
        self.activity_type = activity_type
        self.and_match_rules = and_match_rules
        self.executors = executors
        self.operator = operator
        self.or_match_rules = or_match_rules
        self.public_visibility = public_visibility

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity_config is not None:
            result['ActivityConfig'] = self.activity_config

        if self.activity_desc is not None:
            result['ActivityDesc'] = self.activity_desc

        if self.activity_end_time is not None:
            result['ActivityEndTime'] = self.activity_end_time

        if self.activity_id is not None:
            result['ActivityId'] = self.activity_id

        if self.activity_name is not None:
            result['ActivityName'] = self.activity_name

        if self.activity_start_time is not None:
            result['ActivityStartTime'] = self.activity_start_time

        if self.activity_status is not None:
            result['ActivityStatus'] = self.activity_status

        if self.activity_type is not None:
            result['ActivityType'] = self.activity_type

        if self.and_match_rules is not None:
            result['AndMatchRules'] = self.and_match_rules

        if self.executors is not None:
            result['Executors'] = self.executors

        if self.operator is not None:
            result['Operator'] = self.operator

        if self.or_match_rules is not None:
            result['OrMatchRules'] = self.or_match_rules

        if self.public_visibility is not None:
            result['PublicVisibility'] = self.public_visibility

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivityConfig') is not None:
            self.activity_config = m.get('ActivityConfig')

        if m.get('ActivityDesc') is not None:
            self.activity_desc = m.get('ActivityDesc')

        if m.get('ActivityEndTime') is not None:
            self.activity_end_time = m.get('ActivityEndTime')

        if m.get('ActivityId') is not None:
            self.activity_id = m.get('ActivityId')

        if m.get('ActivityName') is not None:
            self.activity_name = m.get('ActivityName')

        if m.get('ActivityStartTime') is not None:
            self.activity_start_time = m.get('ActivityStartTime')

        if m.get('ActivityStatus') is not None:
            self.activity_status = m.get('ActivityStatus')

        if m.get('ActivityType') is not None:
            self.activity_type = m.get('ActivityType')

        if m.get('AndMatchRules') is not None:
            self.and_match_rules = m.get('AndMatchRules')

        if m.get('Executors') is not None:
            self.executors = m.get('Executors')

        if m.get('Operator') is not None:
            self.operator = m.get('Operator')

        if m.get('OrMatchRules') is not None:
            self.or_match_rules = m.get('OrMatchRules')

        if m.get('PublicVisibility') is not None:
            self.public_visibility = m.get('PublicVisibility')

        return self

