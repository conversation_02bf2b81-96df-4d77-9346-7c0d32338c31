# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 


class VerifyRedeemCodeResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.VerifyRedeemCodeResponseBodyData = None,
        err_code: str = None,
        err_msg: str = None,
        request_id: str = None,
    ):
        self.code = code
        self.data = data
        self.err_code = err_code
        self.err_msg = err_msg
        self.request_id = request_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['code'] = self.code

        if self.data is not None:
            result['data'] = self.data.to_map()

        if self.err_code is not None:
            result['errCode'] = self.err_code

        if self.err_msg is not None:
            result['errMsg'] = self.err_msg

        if self.request_id is not None:
            result['requestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('code') is not None:
            self.code = m.get('code')

        if m.get('data') is not None:
            temp_model = main_models.VerifyRedeemCodeResponseBodyData()
            self.data = temp_model.from_map(m.get('data'))

        if m.get('errCode') is not None:
            self.err_code = m.get('errCode')

        if m.get('errMsg') is not None:
            self.err_msg = m.get('errMsg')

        if m.get('requestId') is not None:
            self.request_id = m.get('requestId')

        return self

class VerifyRedeemCodeResponseBodyData(DaraModel):
    def __init__(
        self,
        channel_id: str = None,
        coupon_content: str = None,
        coupon_id: str = None,
        coupon_name: str = None,
        customer_activity_id: str = None,
        description: str = None,
        redeem_code: str = None,
        resource_order_id: str = None,
        source: str = None,
        status: str = None,
        type: str = None,
        user_union_id: str = None,
    ):
        self.channel_id = channel_id
        self.coupon_content = coupon_content
        self.coupon_id = coupon_id
        self.coupon_name = coupon_name
        self.customer_activity_id = customer_activity_id
        self.description = description
        self.redeem_code = redeem_code
        self.resource_order_id = resource_order_id
        self.source = source
        self.status = status
        self.type = type
        self.user_union_id = user_union_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.channel_id is not None:
            result['channelId'] = self.channel_id

        if self.coupon_content is not None:
            result['couponContent'] = self.coupon_content

        if self.coupon_id is not None:
            result['couponId'] = self.coupon_id

        if self.coupon_name is not None:
            result['couponName'] = self.coupon_name

        if self.customer_activity_id is not None:
            result['customerActivityId'] = self.customer_activity_id

        if self.description is not None:
            result['description'] = self.description

        if self.redeem_code is not None:
            result['redeemCode'] = self.redeem_code

        if self.resource_order_id is not None:
            result['resourceOrderId'] = self.resource_order_id

        if self.source is not None:
            result['source'] = self.source

        if self.status is not None:
            result['status'] = self.status

        if self.type is not None:
            result['type'] = self.type

        if self.user_union_id is not None:
            result['userUnionId'] = self.user_union_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('channelId') is not None:
            self.channel_id = m.get('channelId')

        if m.get('couponContent') is not None:
            self.coupon_content = m.get('couponContent')

        if m.get('couponId') is not None:
            self.coupon_id = m.get('couponId')

        if m.get('couponName') is not None:
            self.coupon_name = m.get('couponName')

        if m.get('customerActivityId') is not None:
            self.customer_activity_id = m.get('customerActivityId')

        if m.get('description') is not None:
            self.description = m.get('description')

        if m.get('redeemCode') is not None:
            self.redeem_code = m.get('redeemCode')

        if m.get('resourceOrderId') is not None:
            self.resource_order_id = m.get('resourceOrderId')

        if m.get('source') is not None:
            self.source = m.get('source')

        if m.get('status') is not None:
            self.status = m.get('status')

        if m.get('type') is not None:
            self.type = m.get('type')

        if m.get('userUnionId') is not None:
            self.user_union_id = m.get('userUnionId')

        return self

