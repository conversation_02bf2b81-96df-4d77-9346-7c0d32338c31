# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeDesktopInstallByDesktopResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.DescribeDesktopInstallByDesktopResponseBodyData] = None,
        request_id: str = None,
    ):
        self.code = code
        self.data = data
        self.request_id = request_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.request_id is not None:
            result['requestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.DescribeDesktopInstallByDesktopResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('requestId') is not None:
            self.request_id = m.get('requestId')

        return self

class DescribeDesktopInstallByDesktopResponseBodyData(DaraModel):
    def __init__(
        self,
        approximate_process_minute: int = None,
        desktop_id: str = None,
        process_percent: int = None,
        status: str = None,
        task_id: str = None,
    ):
        self.approximate_process_minute = approximate_process_minute
        self.desktop_id = desktop_id
        self.process_percent = process_percent
        self.status = status
        self.task_id = task_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.approximate_process_minute is not None:
            result['ApproximateProcessMinute'] = self.approximate_process_minute

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.process_percent is not None:
            result['ProcessPercent'] = self.process_percent

        if self.status is not None:
            result['Status'] = self.status

        if self.task_id is not None:
            result['TaskId'] = self.task_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ApproximateProcessMinute') is not None:
            self.approximate_process_minute = m.get('ApproximateProcessMinute')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('ProcessPercent') is not None:
            self.process_percent = m.get('ProcessPercent')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        if m.get('TaskId') is not None:
            self.task_id = m.get('TaskId')

        return self

