# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class InnerDescribeImageCodesPageResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.InnerDescribeImageCodesPageResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.InnerDescribeImageCodesPageResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class InnerDescribeImageCodesPageResponseBodyData(DaraModel):
    def __init__(
        self,
        current_page: int = None,
        image_code_models: List[main_models.InnerDescribeImageCodesPageResponseBodyDataImageCodeModels] = None,
        page_size: int = None,
        total_count: int = None,
    ):
        self.current_page = current_page
        self.image_code_models = image_code_models
        self.page_size = page_size
        self.total_count = total_count

    def validate(self):
        if self.image_code_models:
            for v1 in self.image_code_models:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.current_page is not None:
            result['CurrentPage'] = self.current_page

        result['ImageCodeModels'] = []
        if self.image_code_models is not None:
            for k1 in self.image_code_models:
                result['ImageCodeModels'].append(k1.to_map() if k1 else None)

        if self.page_size is not None:
            result['PageSize'] = self.page_size

        if self.total_count is not None:
            result['TotalCount'] = self.total_count

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CurrentPage') is not None:
            self.current_page = m.get('CurrentPage')

        self.image_code_models = []
        if m.get('ImageCodeModels') is not None:
            for k1 in m.get('ImageCodeModels'):
                temp_model = main_models.InnerDescribeImageCodesPageResponseBodyDataImageCodeModels()
                self.image_code_models.append(temp_model.from_map(k1))

        if m.get('PageSize') is not None:
            self.page_size = m.get('PageSize')

        if m.get('TotalCount') is not None:
            self.total_count = m.get('TotalCount')

        return self

class InnerDescribeImageCodesPageResponseBodyDataImageCodeModels(DaraModel):
    def __init__(
        self,
        gmt_created: str = None,
        gmt_modified: str = None,
        image_code: str = None,
        image_id: str = None,
        is_free: bool = None,
        redeem_count: int = None,
    ):
        self.gmt_created = gmt_created
        self.gmt_modified = gmt_modified
        self.image_code = image_code
        self.image_id = image_id
        self.is_free = is_free
        self.redeem_count = redeem_count

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.gmt_created is not None:
            result['GmtCreated'] = self.gmt_created

        if self.gmt_modified is not None:
            result['GmtModified'] = self.gmt_modified

        if self.image_code is not None:
            result['ImageCode'] = self.image_code

        if self.image_id is not None:
            result['ImageId'] = self.image_id

        if self.is_free is not None:
            result['IsFree'] = self.is_free

        if self.redeem_count is not None:
            result['RedeemCount'] = self.redeem_count

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('GmtCreated') is not None:
            self.gmt_created = m.get('GmtCreated')

        if m.get('GmtModified') is not None:
            self.gmt_modified = m.get('GmtModified')

        if m.get('ImageCode') is not None:
            self.image_code = m.get('ImageCode')

        if m.get('ImageId') is not None:
            self.image_id = m.get('ImageId')

        if m.get('IsFree') is not None:
            self.is_free = m.get('IsFree')

        if m.get('RedeemCount') is not None:
            self.redeem_count = m.get('RedeemCount')

        return self

