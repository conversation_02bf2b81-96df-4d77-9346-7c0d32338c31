# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateRedeemCodesRequest(DaraModel):
    def __init__(
        self,
        caller_uid: int = None,
        code_prefix: str = None,
        code_suffix: str = None,
        random_groups: int = None,
        spec_char: str = None,
        benefit_id: str = None,
        benefit_pool_id: str = None,
        benefit_quantity: int = None,
        channel: str = None,
        channel_activity: str = None,
        code_quantity: int = None,
        executor: str = None,
        expire_time: str = None,
    ):
        self.caller_uid = caller_uid
        self.code_prefix = code_prefix
        self.code_suffix = code_suffix
        self.random_groups = random_groups
        self.spec_char = spec_char
        self.benefit_id = benefit_id
        self.benefit_pool_id = benefit_pool_id
        self.benefit_quantity = benefit_quantity
        self.channel = channel
        self.channel_activity = channel_activity
        self.code_quantity = code_quantity
        self.executor = executor
        self.expire_time = expire_time

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.caller_uid is not None:
            result['CallerUid'] = self.caller_uid

        if self.code_prefix is not None:
            result['CodePrefix'] = self.code_prefix

        if self.code_suffix is not None:
            result['CodeSuffix'] = self.code_suffix

        if self.random_groups is not None:
            result['RandomGroups'] = self.random_groups

        if self.spec_char is not None:
            result['SpecChar'] = self.spec_char

        if self.benefit_id is not None:
            result['benefitId'] = self.benefit_id

        if self.benefit_pool_id is not None:
            result['benefitPoolId'] = self.benefit_pool_id

        if self.benefit_quantity is not None:
            result['benefitQuantity'] = self.benefit_quantity

        if self.channel is not None:
            result['channel'] = self.channel

        if self.channel_activity is not None:
            result['channelActivity'] = self.channel_activity

        if self.code_quantity is not None:
            result['codeQuantity'] = self.code_quantity

        if self.executor is not None:
            result['executor'] = self.executor

        if self.expire_time is not None:
            result['expireTime'] = self.expire_time

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CallerUid') is not None:
            self.caller_uid = m.get('CallerUid')

        if m.get('CodePrefix') is not None:
            self.code_prefix = m.get('CodePrefix')

        if m.get('CodeSuffix') is not None:
            self.code_suffix = m.get('CodeSuffix')

        if m.get('RandomGroups') is not None:
            self.random_groups = m.get('RandomGroups')

        if m.get('SpecChar') is not None:
            self.spec_char = m.get('SpecChar')

        if m.get('benefitId') is not None:
            self.benefit_id = m.get('benefitId')

        if m.get('benefitPoolId') is not None:
            self.benefit_pool_id = m.get('benefitPoolId')

        if m.get('benefitQuantity') is not None:
            self.benefit_quantity = m.get('benefitQuantity')

        if m.get('channel') is not None:
            self.channel = m.get('channel')

        if m.get('channelActivity') is not None:
            self.channel_activity = m.get('channelActivity')

        if m.get('codeQuantity') is not None:
            self.code_quantity = m.get('codeQuantity')

        if m.get('executor') is not None:
            self.executor = m.get('executor')

        if m.get('expireTime') is not None:
            self.expire_time = m.get('expireTime')

        return self

