# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class SupplyBenefitPoolRequest(DaraModel):
    def __init__(
        self,
        amount: int = None,
        benefit_id: str = None,
        pool_id: str = None,
    ):
        self.amount = amount
        self.benefit_id = benefit_id
        self.pool_id = pool_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.amount is not None:
            result['amount'] = self.amount

        if self.benefit_id is not None:
            result['benefitId'] = self.benefit_id

        if self.pool_id is not None:
            result['poolId'] = self.pool_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('amount') is not None:
            self.amount = m.get('amount')

        if m.get('benefitId') is not None:
            self.benefit_id = m.get('benefitId')

        if m.get('poolId') is not None:
            self.pool_id = m.get('poolId')

        return self

