# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeActivityProductConfigListResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeActivityProductConfigListResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeActivityProductConfigListResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeActivityProductConfigListResponseBodyData(DaraModel):
    def __init__(
        self,
        activity_config_info_list: List[main_models.DescribeActivityProductConfigListResponseBodyDataActivityConfigInfoList] = None,
    ):
        self.activity_config_info_list = activity_config_info_list

    def validate(self):
        if self.activity_config_info_list:
            for v1 in self.activity_config_info_list:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['ActivityConfigInfoList'] = []
        if self.activity_config_info_list is not None:
            for k1 in self.activity_config_info_list:
                result['ActivityConfigInfoList'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.activity_config_info_list = []
        if m.get('ActivityConfigInfoList') is not None:
            for k1 in m.get('ActivityConfigInfoList'):
                temp_model = main_models.DescribeActivityProductConfigListResponseBodyDataActivityConfigInfoList()
                self.activity_config_info_list.append(temp_model.from_map(k1))

        return self

class DescribeActivityProductConfigListResponseBodyDataActivityConfigInfoList(DaraModel):
    def __init__(
        self,
        init_resource_config: main_models.DescribeActivityProductConfigListResponseBodyDataActivityConfigInfoListInitResourceConfig = None,
        is_auto_activate: bool = None,
        lx_promotion_id: str = None,
        prm_activity_product_config_code: str = None,
        product_code: str = None,
        product_sku_code: str = None,
        product_type: str = None,
        purchase_mode: str = None,
    ):
        self.init_resource_config = init_resource_config
        self.is_auto_activate = is_auto_activate
        self.lx_promotion_id = lx_promotion_id
        self.prm_activity_product_config_code = prm_activity_product_config_code
        self.product_code = product_code
        self.product_sku_code = product_sku_code
        self.product_type = product_type
        self.purchase_mode = purchase_mode

    def validate(self):
        if self.init_resource_config:
            self.init_resource_config.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.init_resource_config is not None:
            result['InitResourceConfig'] = self.init_resource_config.to_map()

        if self.is_auto_activate is not None:
            result['IsAutoActivate'] = self.is_auto_activate

        if self.lx_promotion_id is not None:
            result['LxPromotionId'] = self.lx_promotion_id

        if self.prm_activity_product_config_code is not None:
            result['PrmActivityProductConfigCode'] = self.prm_activity_product_config_code

        if self.product_code is not None:
            result['ProductCode'] = self.product_code

        if self.product_sku_code is not None:
            result['ProductSkuCode'] = self.product_sku_code

        if self.product_type is not None:
            result['ProductType'] = self.product_type

        if self.purchase_mode is not None:
            result['PurchaseMode'] = self.purchase_mode

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('InitResourceConfig') is not None:
            temp_model = main_models.DescribeActivityProductConfigListResponseBodyDataActivityConfigInfoListInitResourceConfig()
            self.init_resource_config = temp_model.from_map(m.get('InitResourceConfig'))

        if m.get('IsAutoActivate') is not None:
            self.is_auto_activate = m.get('IsAutoActivate')

        if m.get('LxPromotionId') is not None:
            self.lx_promotion_id = m.get('LxPromotionId')

        if m.get('PrmActivityProductConfigCode') is not None:
            self.prm_activity_product_config_code = m.get('PrmActivityProductConfigCode')

        if m.get('ProductCode') is not None:
            self.product_code = m.get('ProductCode')

        if m.get('ProductSkuCode') is not None:
            self.product_sku_code = m.get('ProductSkuCode')

        if m.get('ProductType') is not None:
            self.product_type = m.get('ProductType')

        if m.get('PurchaseMode') is not None:
            self.purchase_mode = m.get('PurchaseMode')

        return self

class DescribeActivityProductConfigListResponseBodyDataActivityConfigInfoListInitResourceConfig(DaraModel):
    def __init__(
        self,
        app_id_list: List[str] = None,
        image_id: str = None,
        model_id: str = None,
    ):
        self.app_id_list = app_id_list
        self.image_id = image_id
        self.model_id = model_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.app_id_list is not None:
            result['AppIdList'] = self.app_id_list

        if self.image_id is not None:
            result['ImageId'] = self.image_id

        if self.model_id is not None:
            result['ModelId'] = self.model_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AppIdList') is not None:
            self.app_id_list = m.get('AppIdList')

        if m.get('ImageId') is not None:
            self.image_id = m.get('ImageId')

        if m.get('ModelId') is not None:
            self.model_id = m.get('ModelId')

        return self

