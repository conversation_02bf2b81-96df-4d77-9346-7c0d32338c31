# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateHardwareCouponsRequest(DaraModel):
    def __init__(
        self,
        benefit_id: str = None,
        benefit_pool_id: str = None,
        benefit_quantity: str = None,
        coupon_name: str = None,
        customer_activity_id: str = None,
        executor: str = None,
        expire_time: str = None,
        serial_nums: str = None,
    ):
        self.benefit_id = benefit_id
        self.benefit_pool_id = benefit_pool_id
        self.benefit_quantity = benefit_quantity
        self.coupon_name = coupon_name
        self.customer_activity_id = customer_activity_id
        self.executor = executor
        self.expire_time = expire_time
        self.serial_nums = serial_nums

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.benefit_id is not None:
            result['benefitId'] = self.benefit_id

        if self.benefit_pool_id is not None:
            result['benefitPoolId'] = self.benefit_pool_id

        if self.benefit_quantity is not None:
            result['benefitQuantity'] = self.benefit_quantity

        if self.coupon_name is not None:
            result['couponName'] = self.coupon_name

        if self.customer_activity_id is not None:
            result['customerActivityId'] = self.customer_activity_id

        if self.executor is not None:
            result['executor'] = self.executor

        if self.expire_time is not None:
            result['expireTime'] = self.expire_time

        if self.serial_nums is not None:
            result['serialNums'] = self.serial_nums

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('benefitId') is not None:
            self.benefit_id = m.get('benefitId')

        if m.get('benefitPoolId') is not None:
            self.benefit_pool_id = m.get('benefitPoolId')

        if m.get('benefitQuantity') is not None:
            self.benefit_quantity = m.get('benefitQuantity')

        if m.get('couponName') is not None:
            self.coupon_name = m.get('couponName')

        if m.get('customerActivityId') is not None:
            self.customer_activity_id = m.get('customerActivityId')

        if m.get('executor') is not None:
            self.executor = m.get('executor')

        if m.get('expireTime') is not None:
            self.expire_time = m.get('expireTime')

        if m.get('serialNums') is not None:
            self.serial_nums = m.get('serialNums')

        return self

