# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from typing import List


class InnerDescribeProductListRequest(DaraModel):
    def __init__(
        self,
        product_code_list_json: List[str] = None,
        product_type_list_json: List[str] = None,
        skip_saleable_filter: bool = None,
    ):
        self.product_code_list_json = product_code_list_json
        self.product_type_list_json = product_type_list_json
        self.skip_saleable_filter = skip_saleable_filter

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.product_code_list_json is not None:
            result['ProductCodeListJson'] = self.product_code_list_json

        if self.product_type_list_json is not None:
            result['ProductTypeListJson'] = self.product_type_list_json

        if self.skip_saleable_filter is not None:
            result['skipSaleableFilter'] = self.skip_saleable_filter

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ProductCodeListJson') is not None:
            self.product_code_list_json = m.get('ProductCodeListJson')

        if m.get('ProductTypeListJson') is not None:
            self.product_type_list_json = m.get('ProductTypeListJson')

        if m.get('skipSaleableFilter') is not None:
            self.skip_saleable_filter = m.get('skipSaleableFilter')

        return self

