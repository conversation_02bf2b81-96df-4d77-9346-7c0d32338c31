# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeBundleResponseBody(DaraModel):
    def __init__(
        self,
        bundles: List[main_models.DescribeBundleResponseBodyBundles] = None,
        code: str = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.bundles = bundles
        self.code = code
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.bundles:
            for v1 in self.bundles:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['Bundles'] = []
        if self.bundles is not None:
            for k1 in self.bundles:
                result['Bundles'].append(k1.to_map() if k1 else None)

        if self.code is not None:
            result['Code'] = self.code

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.bundles = []
        if m.get('Bundles') is not None:
            for k1 in m.get('Bundles'):
                temp_model = main_models.DescribeBundleResponseBodyBundles()
                self.bundles.append(temp_model.from_map(k1))

        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeBundleResponseBodyBundles(DaraModel):
    def __init__(
        self,
        bundle_id: str = None,
        bundle_name: str = None,
        bundle_type: str = None,
        description: str = None,
        desktop_type: str = None,
        desktop_type_attribute: main_models.DescribeBundleResponseBodyBundlesDesktopTypeAttribute = None,
        desktop_type_family: str = None,
        disks: List[main_models.DescribeBundleResponseBodyBundlesDisks] = None,
        os_type: str = None,
        sku_items: List[main_models.DescribeBundleResponseBodyBundlesSkuItems] = None,
    ):
        self.bundle_id = bundle_id
        self.bundle_name = bundle_name
        self.bundle_type = bundle_type
        self.description = description
        self.desktop_type = desktop_type
        self.desktop_type_attribute = desktop_type_attribute
        self.desktop_type_family = desktop_type_family
        self.disks = disks
        self.os_type = os_type
        self.sku_items = sku_items

    def validate(self):
        if self.desktop_type_attribute:
            self.desktop_type_attribute.validate()
        if self.disks:
            for v1 in self.disks:
                 if v1:
                    v1.validate()
        if self.sku_items:
            for v1 in self.sku_items:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.bundle_id is not None:
            result['BundleId'] = self.bundle_id

        if self.bundle_name is not None:
            result['BundleName'] = self.bundle_name

        if self.bundle_type is not None:
            result['BundleType'] = self.bundle_type

        if self.description is not None:
            result['Description'] = self.description

        if self.desktop_type is not None:
            result['DesktopType'] = self.desktop_type

        if self.desktop_type_attribute is not None:
            result['DesktopTypeAttribute'] = self.desktop_type_attribute.to_map()

        if self.desktop_type_family is not None:
            result['DesktopTypeFamily'] = self.desktop_type_family

        result['Disks'] = []
        if self.disks is not None:
            for k1 in self.disks:
                result['Disks'].append(k1.to_map() if k1 else None)

        if self.os_type is not None:
            result['OsType'] = self.os_type

        result['SkuItems'] = []
        if self.sku_items is not None:
            for k1 in self.sku_items:
                result['SkuItems'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('BundleId') is not None:
            self.bundle_id = m.get('BundleId')

        if m.get('BundleName') is not None:
            self.bundle_name = m.get('BundleName')

        if m.get('BundleType') is not None:
            self.bundle_type = m.get('BundleType')

        if m.get('Description') is not None:
            self.description = m.get('Description')

        if m.get('DesktopType') is not None:
            self.desktop_type = m.get('DesktopType')

        if m.get('DesktopTypeAttribute') is not None:
            temp_model = main_models.DescribeBundleResponseBodyBundlesDesktopTypeAttribute()
            self.desktop_type_attribute = temp_model.from_map(m.get('DesktopTypeAttribute'))

        if m.get('DesktopTypeFamily') is not None:
            self.desktop_type_family = m.get('DesktopTypeFamily')

        self.disks = []
        if m.get('Disks') is not None:
            for k1 in m.get('Disks'):
                temp_model = main_models.DescribeBundleResponseBodyBundlesDisks()
                self.disks.append(temp_model.from_map(k1))

        if m.get('OsType') is not None:
            self.os_type = m.get('OsType')

        self.sku_items = []
        if m.get('SkuItems') is not None:
            for k1 in m.get('SkuItems'):
                temp_model = main_models.DescribeBundleResponseBodyBundlesSkuItems()
                self.sku_items.append(temp_model.from_map(k1))

        return self

class DescribeBundleResponseBodyBundlesSkuItems(DaraModel):
    def __init__(
        self,
        charge_type: str = None,
        code: str = None,
        duration_time: int = None,
        name: str = None,
        period: int = None,
        period_unit: str = None,
        type: str = None,
    ):
        self.charge_type = charge_type
        self.code = code
        self.duration_time = duration_time
        self.name = name
        self.period = period
        self.period_unit = period_unit
        self.type = type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.charge_type is not None:
            result['ChargeType'] = self.charge_type

        if self.code is not None:
            result['Code'] = self.code

        if self.duration_time is not None:
            result['DurationTime'] = self.duration_time

        if self.name is not None:
            result['Name'] = self.name

        if self.period is not None:
            result['Period'] = self.period

        if self.period_unit is not None:
            result['PeriodUnit'] = self.period_unit

        if self.type is not None:
            result['Type'] = self.type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ChargeType') is not None:
            self.charge_type = m.get('ChargeType')

        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('DurationTime') is not None:
            self.duration_time = m.get('DurationTime')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('Period') is not None:
            self.period = m.get('Period')

        if m.get('PeriodUnit') is not None:
            self.period_unit = m.get('PeriodUnit')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        return self

class DescribeBundleResponseBodyBundlesDisks(DaraModel):
    def __init__(
        self,
        disk_size: str = None,
        disk_type: str = None,
    ):
        self.disk_size = disk_size
        self.disk_type = disk_type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.disk_size is not None:
            result['DiskSize'] = self.disk_size

        if self.disk_type is not None:
            result['DiskType'] = self.disk_type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DiskSize') is not None:
            self.disk_size = m.get('DiskSize')

        if m.get('DiskType') is not None:
            self.disk_type = m.get('DiskType')

        return self

class DescribeBundleResponseBodyBundlesDesktopTypeAttribute(DaraModel):
    def __init__(
        self,
        cpu_count: int = None,
        gpu_count: int = None,
        memory_size: int = None,
    ):
        self.cpu_count = cpu_count
        self.gpu_count = gpu_count
        self.memory_size = memory_size

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.cpu_count is not None:
            result['CpuCount'] = self.cpu_count

        if self.gpu_count is not None:
            result['GpuCount'] = self.gpu_count

        if self.memory_size is not None:
            result['MemorySize'] = self.memory_size

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CpuCount') is not None:
            self.cpu_count = m.get('CpuCount')

        if m.get('GpuCount') is not None:
            self.gpu_count = m.get('GpuCount')

        if m.get('MemorySize') is not None:
            self.memory_size = m.get('MemorySize')

        return self

