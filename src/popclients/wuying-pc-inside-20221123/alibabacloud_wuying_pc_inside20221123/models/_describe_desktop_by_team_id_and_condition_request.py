# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeDesktopByTeamIdAndConditionRequest(DaraModel):
    def __init__(
        self,
        activate_status_condition: str = None,
        bundle_id_condition: str = None,
        bundle_sku_code_condition: str = None,
        cur_page: int = None,
        desktop_type_condition: str = None,
        is_asc: bool = None,
        order_column: str = None,
        page_size: int = None,
        team_id: str = None,
    ):
        self.activate_status_condition = activate_status_condition
        self.bundle_id_condition = bundle_id_condition
        self.bundle_sku_code_condition = bundle_sku_code_condition
        self.cur_page = cur_page
        self.desktop_type_condition = desktop_type_condition
        self.is_asc = is_asc
        self.order_column = order_column
        self.page_size = page_size
        # This parameter is required.
        self.team_id = team_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activate_status_condition is not None:
            result['ActivateStatusCondition'] = self.activate_status_condition

        if self.bundle_id_condition is not None:
            result['BundleIdCondition'] = self.bundle_id_condition

        if self.bundle_sku_code_condition is not None:
            result['BundleSkuCodeCondition'] = self.bundle_sku_code_condition

        if self.cur_page is not None:
            result['CurPage'] = self.cur_page

        if self.desktop_type_condition is not None:
            result['DesktopTypeCondition'] = self.desktop_type_condition

        if self.is_asc is not None:
            result['IsAsc'] = self.is_asc

        if self.order_column is not None:
            result['OrderColumn'] = self.order_column

        if self.page_size is not None:
            result['PageSize'] = self.page_size

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivateStatusCondition') is not None:
            self.activate_status_condition = m.get('ActivateStatusCondition')

        if m.get('BundleIdCondition') is not None:
            self.bundle_id_condition = m.get('BundleIdCondition')

        if m.get('BundleSkuCodeCondition') is not None:
            self.bundle_sku_code_condition = m.get('BundleSkuCodeCondition')

        if m.get('CurPage') is not None:
            self.cur_page = m.get('CurPage')

        if m.get('DesktopTypeCondition') is not None:
            self.desktop_type_condition = m.get('DesktopTypeCondition')

        if m.get('IsAsc') is not None:
            self.is_asc = m.get('IsAsc')

        if m.get('OrderColumn') is not None:
            self.order_column = m.get('OrderColumn')

        if m.get('PageSize') is not None:
            self.page_size = m.get('PageSize')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        return self

