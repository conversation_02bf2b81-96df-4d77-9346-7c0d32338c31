# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class InnerDescribeDesktopLocalInfoResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.InnerDescribeDesktopLocalInfoResponseBodyData] = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.InnerDescribeDesktopLocalInfoResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class InnerDescribeDesktopLocalInfoResponseBodyData(DaraModel):
    def __init__(
        self,
        activate_status: str = None,
        ali_uid: int = None,
        assignment_status: str = None,
        bundle_id: str = None,
        bundle_sku_code: str = None,
        buy_channel: str = None,
        city: str = None,
        create_by: str = None,
        desktop_center_id: str = None,
        desktop_id: str = None,
        desktop_label: str = None,
        desktop_type: str = None,
        duration: int = None,
        effective_time: str = None,
        effective_time_unit: str = None,
        end_user_id: str = None,
        expire_time: str = None,
        order_id: str = None,
        package_id: str = None,
        region_id: str = None,
        resource_id: str = None,
        resource_type_desc: str = None,
        spec_name: str = None,
        team_id: str = None,
        update_time: str = None,
    ):
        self.activate_status = activate_status
        self.ali_uid = ali_uid
        self.assignment_status = assignment_status
        self.bundle_id = bundle_id
        self.bundle_sku_code = bundle_sku_code
        self.buy_channel = buy_channel
        self.city = city
        self.create_by = create_by
        self.desktop_center_id = desktop_center_id
        self.desktop_id = desktop_id
        self.desktop_label = desktop_label
        self.desktop_type = desktop_type
        self.duration = duration
        self.effective_time = effective_time
        self.effective_time_unit = effective_time_unit
        self.end_user_id = end_user_id
        self.expire_time = expire_time
        self.order_id = order_id
        self.package_id = package_id
        self.region_id = region_id
        self.resource_id = resource_id
        self.resource_type_desc = resource_type_desc
        self.spec_name = spec_name
        self.team_id = team_id
        self.update_time = update_time

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activate_status is not None:
            result['ActivateStatus'] = self.activate_status

        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.assignment_status is not None:
            result['AssignmentStatus'] = self.assignment_status

        if self.bundle_id is not None:
            result['BundleId'] = self.bundle_id

        if self.bundle_sku_code is not None:
            result['BundleSkuCode'] = self.bundle_sku_code

        if self.buy_channel is not None:
            result['BuyChannel'] = self.buy_channel

        if self.city is not None:
            result['City'] = self.city

        if self.create_by is not None:
            result['CreateBy'] = self.create_by

        if self.desktop_center_id is not None:
            result['DesktopCenterId'] = self.desktop_center_id

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.desktop_label is not None:
            result['DesktopLabel'] = self.desktop_label

        if self.desktop_type is not None:
            result['DesktopType'] = self.desktop_type

        if self.duration is not None:
            result['Duration'] = self.duration

        if self.effective_time is not None:
            result['EffectiveTime'] = self.effective_time

        if self.effective_time_unit is not None:
            result['EffectiveTimeUnit'] = self.effective_time_unit

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.expire_time is not None:
            result['ExpireTime'] = self.expire_time

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.package_id is not None:
            result['PackageId'] = self.package_id

        if self.region_id is not None:
            result['RegionId'] = self.region_id

        if self.resource_id is not None:
            result['ResourceId'] = self.resource_id

        if self.resource_type_desc is not None:
            result['ResourceTypeDesc'] = self.resource_type_desc

        if self.spec_name is not None:
            result['SpecName'] = self.spec_name

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        if self.update_time is not None:
            result['UpdateTime'] = self.update_time

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivateStatus') is not None:
            self.activate_status = m.get('ActivateStatus')

        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('AssignmentStatus') is not None:
            self.assignment_status = m.get('AssignmentStatus')

        if m.get('BundleId') is not None:
            self.bundle_id = m.get('BundleId')

        if m.get('BundleSkuCode') is not None:
            self.bundle_sku_code = m.get('BundleSkuCode')

        if m.get('BuyChannel') is not None:
            self.buy_channel = m.get('BuyChannel')

        if m.get('City') is not None:
            self.city = m.get('City')

        if m.get('CreateBy') is not None:
            self.create_by = m.get('CreateBy')

        if m.get('DesktopCenterId') is not None:
            self.desktop_center_id = m.get('DesktopCenterId')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('DesktopLabel') is not None:
            self.desktop_label = m.get('DesktopLabel')

        if m.get('DesktopType') is not None:
            self.desktop_type = m.get('DesktopType')

        if m.get('Duration') is not None:
            self.duration = m.get('Duration')

        if m.get('EffectiveTime') is not None:
            self.effective_time = m.get('EffectiveTime')

        if m.get('EffectiveTimeUnit') is not None:
            self.effective_time_unit = m.get('EffectiveTimeUnit')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('ExpireTime') is not None:
            self.expire_time = m.get('ExpireTime')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('PackageId') is not None:
            self.package_id = m.get('PackageId')

        if m.get('RegionId') is not None:
            self.region_id = m.get('RegionId')

        if m.get('ResourceId') is not None:
            self.resource_id = m.get('ResourceId')

        if m.get('ResourceTypeDesc') is not None:
            self.resource_type_desc = m.get('ResourceTypeDesc')

        if m.get('SpecName') is not None:
            self.spec_name = m.get('SpecName')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        if m.get('UpdateTime') is not None:
            self.update_time = m.get('UpdateTime')

        return self

