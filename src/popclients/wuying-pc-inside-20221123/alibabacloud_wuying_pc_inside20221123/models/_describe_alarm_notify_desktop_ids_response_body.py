# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeAlarmNotifyDesktopIdsResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeAlarmNotifyDesktopIdsResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeAlarmNotifyDesktopIdsResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeAlarmNotifyDesktopIdsResponseBodyData(DaraModel):
    def __init__(
        self,
        expiring_days: int = None,
        releasing_days: int = None,
        will_expiring_desktop_ids: List[main_models.DescribeAlarmNotifyDesktopIdsResponseBodyDataWillExpiringDesktopIds] = None,
        will_release_desktop_ids: List[main_models.DescribeAlarmNotifyDesktopIdsResponseBodyDataWillReleaseDesktopIds] = None,
        will_time_pack_exhausting_desktop_ids: List[main_models.DescribeAlarmNotifyDesktopIdsResponseBodyDataWillTimePackExhaustingDesktopIds] = None,
    ):
        self.expiring_days = expiring_days
        self.releasing_days = releasing_days
        self.will_expiring_desktop_ids = will_expiring_desktop_ids
        self.will_release_desktop_ids = will_release_desktop_ids
        self.will_time_pack_exhausting_desktop_ids = will_time_pack_exhausting_desktop_ids

    def validate(self):
        if self.will_expiring_desktop_ids:
            for v1 in self.will_expiring_desktop_ids:
                 if v1:
                    v1.validate()
        if self.will_release_desktop_ids:
            for v1 in self.will_release_desktop_ids:
                 if v1:
                    v1.validate()
        if self.will_time_pack_exhausting_desktop_ids:
            for v1 in self.will_time_pack_exhausting_desktop_ids:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.expiring_days is not None:
            result['ExpiringDays'] = self.expiring_days

        if self.releasing_days is not None:
            result['ReleasingDays'] = self.releasing_days

        result['WillExpiringDesktopIds'] = []
        if self.will_expiring_desktop_ids is not None:
            for k1 in self.will_expiring_desktop_ids:
                result['WillExpiringDesktopIds'].append(k1.to_map() if k1 else None)

        result['WillReleaseDesktopIds'] = []
        if self.will_release_desktop_ids is not None:
            for k1 in self.will_release_desktop_ids:
                result['WillReleaseDesktopIds'].append(k1.to_map() if k1 else None)

        result['willTimePackExhaustingDesktopIds'] = []
        if self.will_time_pack_exhausting_desktop_ids is not None:
            for k1 in self.will_time_pack_exhausting_desktop_ids:
                result['willTimePackExhaustingDesktopIds'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ExpiringDays') is not None:
            self.expiring_days = m.get('ExpiringDays')

        if m.get('ReleasingDays') is not None:
            self.releasing_days = m.get('ReleasingDays')

        self.will_expiring_desktop_ids = []
        if m.get('WillExpiringDesktopIds') is not None:
            for k1 in m.get('WillExpiringDesktopIds'):
                temp_model = main_models.DescribeAlarmNotifyDesktopIdsResponseBodyDataWillExpiringDesktopIds()
                self.will_expiring_desktop_ids.append(temp_model.from_map(k1))

        self.will_release_desktop_ids = []
        if m.get('WillReleaseDesktopIds') is not None:
            for k1 in m.get('WillReleaseDesktopIds'):
                temp_model = main_models.DescribeAlarmNotifyDesktopIdsResponseBodyDataWillReleaseDesktopIds()
                self.will_release_desktop_ids.append(temp_model.from_map(k1))

        self.will_time_pack_exhausting_desktop_ids = []
        if m.get('willTimePackExhaustingDesktopIds') is not None:
            for k1 in m.get('willTimePackExhaustingDesktopIds'):
                temp_model = main_models.DescribeAlarmNotifyDesktopIdsResponseBodyDataWillTimePackExhaustingDesktopIds()
                self.will_time_pack_exhausting_desktop_ids.append(temp_model.from_map(k1))

        return self

class DescribeAlarmNotifyDesktopIdsResponseBodyDataWillTimePackExhaustingDesktopIds(DaraModel):
    def __init__(
        self,
        assignment_status: str = None,
        desktop_id: str = None,
    ):
        self.assignment_status = assignment_status
        self.desktop_id = desktop_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.assignment_status is not None:
            result['AssignmentStatus'] = self.assignment_status

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AssignmentStatus') is not None:
            self.assignment_status = m.get('AssignmentStatus')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        return self

class DescribeAlarmNotifyDesktopIdsResponseBodyDataWillReleaseDesktopIds(DaraModel):
    def __init__(
        self,
        assignment_status: str = None,
        desktop_id: str = None,
    ):
        self.assignment_status = assignment_status
        self.desktop_id = desktop_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.assignment_status is not None:
            result['AssignmentStatus'] = self.assignment_status

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AssignmentStatus') is not None:
            self.assignment_status = m.get('AssignmentStatus')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        return self

class DescribeAlarmNotifyDesktopIdsResponseBodyDataWillExpiringDesktopIds(DaraModel):
    def __init__(
        self,
        assignment_status: str = None,
        desktop_id: str = None,
    ):
        self.assignment_status = assignment_status
        self.desktop_id = desktop_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.assignment_status is not None:
            result['AssignmentStatus'] = self.assignment_status

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AssignmentStatus') is not None:
            self.assignment_status = m.get('AssignmentStatus')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        return self

