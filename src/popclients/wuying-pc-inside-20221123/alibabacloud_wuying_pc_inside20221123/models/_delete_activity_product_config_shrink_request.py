# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DeleteActivityProductConfigShrinkRequest(DaraModel):
    def __init__(
        self,
        config_code_list_shrink: str = None,
    ):
        # This parameter is required.
        self.config_code_list_shrink = config_code_list_shrink

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.config_code_list_shrink is not None:
            result['ConfigCodeList'] = self.config_code_list_shrink

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ConfigCodeList') is not None:
            self.config_code_list_shrink = m.get('ConfigCodeList')

        return self

