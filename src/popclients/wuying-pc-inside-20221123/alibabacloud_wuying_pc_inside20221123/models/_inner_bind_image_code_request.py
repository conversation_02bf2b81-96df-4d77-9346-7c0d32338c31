# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class InnerBindImageCodeRequest(DaraModel):
    def __init__(
        self,
        image_code: str = None,
        image_id: str = None,
    ):
        self.image_code = image_code
        self.image_id = image_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.image_code is not None:
            result['ImageCode'] = self.image_code

        if self.image_id is not None:
            result['ImageId'] = self.image_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ImageCode') is not None:
            self.image_code = m.get('ImageCode')

        if m.get('ImageId') is not None:
            self.image_id = m.get('ImageId')

        return self

