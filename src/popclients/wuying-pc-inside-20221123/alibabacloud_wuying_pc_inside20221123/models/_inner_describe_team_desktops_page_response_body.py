# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class InnerDescribeTeamDesktopsPageResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.InnerDescribeTeamDesktopsPageResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.InnerDescribeTeamDesktopsPageResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class InnerDescribeTeamDesktopsPageResponseBodyData(DaraModel):
    def __init__(
        self,
        current_page: int = None,
        desktops: List[main_models.InnerDescribeTeamDesktopsPageResponseBodyDataDesktops] = None,
        page_size: int = None,
        total_count: int = None,
    ):
        self.current_page = current_page
        self.desktops = desktops
        self.page_size = page_size
        self.total_count = total_count

    def validate(self):
        if self.desktops:
            for v1 in self.desktops:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.current_page is not None:
            result['CurrentPage'] = self.current_page

        result['Desktops'] = []
        if self.desktops is not None:
            for k1 in self.desktops:
                result['Desktops'].append(k1.to_map() if k1 else None)

        if self.page_size is not None:
            result['PageSize'] = self.page_size

        if self.total_count is not None:
            result['TotalCount'] = self.total_count

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CurrentPage') is not None:
            self.current_page = m.get('CurrentPage')

        self.desktops = []
        if m.get('Desktops') is not None:
            for k1 in m.get('Desktops'):
                temp_model = main_models.InnerDescribeTeamDesktopsPageResponseBodyDataDesktops()
                self.desktops.append(temp_model.from_map(k1))

        if m.get('PageSize') is not None:
            self.page_size = m.get('PageSize')

        if m.get('TotalCount') is not None:
            self.total_count = m.get('TotalCount')

        return self

class InnerDescribeTeamDesktopsPageResponseBodyDataDesktops(DaraModel):
    def __init__(
        self,
        activate_status: str = None,
        assignment_status: str = None,
        buy_channel: str = None,
        connection_status: str = None,
        delete_time: str = None,
        desktop_center_id: str = None,
        desktop_id: str = None,
        desktop_name: str = None,
        desktop_status: str = None,
        desktop_type: str = None,
        duration_list: List[main_models.InnerDescribeTeamDesktopsPageResponseBodyDataDesktopsDurationList] = None,
        effective_time: int = None,
        effective_time_unit: str = None,
        end_user_id: str = None,
        expire_time: str = None,
        package_id: str = None,
        remaining_duration: int = None,
        resource_type_desc: str = None,
        spec_name: str = None,
        update_time: str = None,
        user_nick_name: str = None,
    ):
        self.activate_status = activate_status
        self.assignment_status = assignment_status
        self.buy_channel = buy_channel
        self.connection_status = connection_status
        self.delete_time = delete_time
        self.desktop_center_id = desktop_center_id
        self.desktop_id = desktop_id
        self.desktop_name = desktop_name
        self.desktop_status = desktop_status
        self.desktop_type = desktop_type
        self.duration_list = duration_list
        self.effective_time = effective_time
        self.effective_time_unit = effective_time_unit
        self.end_user_id = end_user_id
        self.expire_time = expire_time
        self.package_id = package_id
        self.remaining_duration = remaining_duration
        self.resource_type_desc = resource_type_desc
        self.spec_name = spec_name
        self.update_time = update_time
        self.user_nick_name = user_nick_name

    def validate(self):
        if self.duration_list:
            for v1 in self.duration_list:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activate_status is not None:
            result['ActivateStatus'] = self.activate_status

        if self.assignment_status is not None:
            result['AssignmentStatus'] = self.assignment_status

        if self.buy_channel is not None:
            result['BuyChannel'] = self.buy_channel

        if self.connection_status is not None:
            result['ConnectionStatus'] = self.connection_status

        if self.delete_time is not None:
            result['DeleteTime'] = self.delete_time

        if self.desktop_center_id is not None:
            result['DesktopCenterId'] = self.desktop_center_id

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.desktop_name is not None:
            result['DesktopName'] = self.desktop_name

        if self.desktop_status is not None:
            result['DesktopStatus'] = self.desktop_status

        if self.desktop_type is not None:
            result['DesktopType'] = self.desktop_type

        result['DurationList'] = []
        if self.duration_list is not None:
            for k1 in self.duration_list:
                result['DurationList'].append(k1.to_map() if k1 else None)

        if self.effective_time is not None:
            result['EffectiveTime'] = self.effective_time

        if self.effective_time_unit is not None:
            result['EffectiveTimeUnit'] = self.effective_time_unit

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.expire_time is not None:
            result['ExpireTime'] = self.expire_time

        if self.package_id is not None:
            result['PackageId'] = self.package_id

        if self.remaining_duration is not None:
            result['RemainingDuration'] = self.remaining_duration

        if self.resource_type_desc is not None:
            result['ResourceTypeDesc'] = self.resource_type_desc

        if self.spec_name is not None:
            result['SpecName'] = self.spec_name

        if self.update_time is not None:
            result['UpdateTime'] = self.update_time

        if self.user_nick_name is not None:
            result['UserNickName'] = self.user_nick_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivateStatus') is not None:
            self.activate_status = m.get('ActivateStatus')

        if m.get('AssignmentStatus') is not None:
            self.assignment_status = m.get('AssignmentStatus')

        if m.get('BuyChannel') is not None:
            self.buy_channel = m.get('BuyChannel')

        if m.get('ConnectionStatus') is not None:
            self.connection_status = m.get('ConnectionStatus')

        if m.get('DeleteTime') is not None:
            self.delete_time = m.get('DeleteTime')

        if m.get('DesktopCenterId') is not None:
            self.desktop_center_id = m.get('DesktopCenterId')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('DesktopName') is not None:
            self.desktop_name = m.get('DesktopName')

        if m.get('DesktopStatus') is not None:
            self.desktop_status = m.get('DesktopStatus')

        if m.get('DesktopType') is not None:
            self.desktop_type = m.get('DesktopType')

        self.duration_list = []
        if m.get('DurationList') is not None:
            for k1 in m.get('DurationList'):
                temp_model = main_models.InnerDescribeTeamDesktopsPageResponseBodyDataDesktopsDurationList()
                self.duration_list.append(temp_model.from_map(k1))

        if m.get('EffectiveTime') is not None:
            self.effective_time = m.get('EffectiveTime')

        if m.get('EffectiveTimeUnit') is not None:
            self.effective_time_unit = m.get('EffectiveTimeUnit')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('ExpireTime') is not None:
            self.expire_time = m.get('ExpireTime')

        if m.get('PackageId') is not None:
            self.package_id = m.get('PackageId')

        if m.get('RemainingDuration') is not None:
            self.remaining_duration = m.get('RemainingDuration')

        if m.get('ResourceTypeDesc') is not None:
            self.resource_type_desc = m.get('ResourceTypeDesc')

        if m.get('SpecName') is not None:
            self.spec_name = m.get('SpecName')

        if m.get('UpdateTime') is not None:
            self.update_time = m.get('UpdateTime')

        if m.get('UserNickName') is not None:
            self.user_nick_name = m.get('UserNickName')

        return self

class InnerDescribeTeamDesktopsPageResponseBodyDataDesktopsDurationList(DaraModel):
    def __init__(
        self,
        package_id: str = None,
        package_status: str = None,
        remaining_duration: int = None,
        total_duration: int = None,
        used_duration: int = None,
    ):
        self.package_id = package_id
        self.package_status = package_status
        self.remaining_duration = remaining_duration
        self.total_duration = total_duration
        self.used_duration = used_duration

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.package_id is not None:
            result['PackageId'] = self.package_id

        if self.package_status is not None:
            result['PackageStatus'] = self.package_status

        if self.remaining_duration is not None:
            result['RemainingDuration'] = self.remaining_duration

        if self.total_duration is not None:
            result['TotalDuration'] = self.total_duration

        if self.used_duration is not None:
            result['UsedDuration'] = self.used_duration

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('PackageId') is not None:
            self.package_id = m.get('PackageId')

        if m.get('PackageStatus') is not None:
            self.package_status = m.get('PackageStatus')

        if m.get('RemainingDuration') is not None:
            self.remaining_duration = m.get('RemainingDuration')

        if m.get('TotalDuration') is not None:
            self.total_duration = m.get('TotalDuration')

        if m.get('UsedDuration') is not None:
            self.used_duration = m.get('UsedDuration')

        return self

