# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from typing import List


class BindPersonalHardwareTerminalBenefitRequest(DaraModel):
    def __init__(
        self,
        order_id: str = None,
        sn_codes: List[str] = None,
        spec_code: str = None,
        type: str = None,
    ):
        # This parameter is required.
        self.order_id = order_id
        # This parameter is required.
        self.sn_codes = sn_codes
        self.spec_code = spec_code
        # This parameter is required.
        self.type = type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.sn_codes is not None:
            result['SnCodes'] = self.sn_codes

        if self.spec_code is not None:
            result['SpecCode'] = self.spec_code

        if self.type is not None:
            result['Type'] = self.type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('SnCodes') is not None:
            self.sn_codes = m.get('SnCodes')

        if m.get('SpecCode') is not None:
            self.spec_code = m.get('SpecCode')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        return self

