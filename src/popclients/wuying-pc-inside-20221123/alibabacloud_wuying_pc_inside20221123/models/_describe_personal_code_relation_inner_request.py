# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribePersonalCodeRelationInnerRequest(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        end_user_id: str = None,
        event_type: str = None,
        relation: str = None,
        time_after: str = None,
    ):
        self.ali_uid = ali_uid
        self.end_user_id = end_user_id
        self.event_type = event_type
        self.relation = relation
        self.time_after = time_after

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.event_type is not None:
            result['EventType'] = self.event_type

        if self.relation is not None:
            result['Relation'] = self.relation

        if self.time_after is not None:
            result['TimeAfter'] = self.time_after

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('EventType') is not None:
            self.event_type = m.get('EventType')

        if m.get('Relation') is not None:
            self.relation = m.get('Relation')

        if m.get('TimeAfter') is not None:
            self.time_after = m.get('TimeAfter')

        return self

