# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class InnerDescribeTeamDesktopsPageShrinkRequest(DaraModel):
    def __init__(
        self,
        condition: str = None,
        current_page: int = None,
        desktop_ids_shrink: str = None,
        is_asc: bool = None,
        order_by: str = None,
        page_size: int = None,
        team_id: str = None,
        wy_id_list_shrink: str = None,
    ):
        self.condition = condition
        self.current_page = current_page
        self.desktop_ids_shrink = desktop_ids_shrink
        self.is_asc = is_asc
        self.order_by = order_by
        self.page_size = page_size
        # This parameter is required.
        self.team_id = team_id
        self.wy_id_list_shrink = wy_id_list_shrink

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.condition is not None:
            result['Condition'] = self.condition

        if self.current_page is not None:
            result['CurrentPage'] = self.current_page

        if self.desktop_ids_shrink is not None:
            result['DesktopIds'] = self.desktop_ids_shrink

        if self.is_asc is not None:
            result['IsAsc'] = self.is_asc

        if self.order_by is not None:
            result['OrderBy'] = self.order_by

        if self.page_size is not None:
            result['PageSize'] = self.page_size

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        if self.wy_id_list_shrink is not None:
            result['WyIdList'] = self.wy_id_list_shrink

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Condition') is not None:
            self.condition = m.get('Condition')

        if m.get('CurrentPage') is not None:
            self.current_page = m.get('CurrentPage')

        if m.get('DesktopIds') is not None:
            self.desktop_ids_shrink = m.get('DesktopIds')

        if m.get('IsAsc') is not None:
            self.is_asc = m.get('IsAsc')

        if m.get('OrderBy') is not None:
            self.order_by = m.get('OrderBy')

        if m.get('PageSize') is not None:
            self.page_size = m.get('PageSize')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        if m.get('WyIdList') is not None:
            self.wy_id_list_shrink = m.get('WyIdList')

        return self

