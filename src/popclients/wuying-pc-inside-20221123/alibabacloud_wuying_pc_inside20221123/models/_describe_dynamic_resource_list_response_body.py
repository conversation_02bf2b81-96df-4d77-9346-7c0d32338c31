# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeDynamicResourceListResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeDynamicResourceListResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeDynamicResourceListResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeDynamicResourceListResponseBodyData(DaraModel):
    def __init__(
        self,
        resource_list: List[main_models.DescribeDynamicResourceListResponseBodyDataResourceList] = None,
    ):
        self.resource_list = resource_list

    def validate(self):
        if self.resource_list:
            for v1 in self.resource_list:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['ResourceList'] = []
        if self.resource_list is not None:
            for k1 in self.resource_list:
                result['ResourceList'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.resource_list = []
        if m.get('ResourceList') is not None:
            for k1 in m.get('ResourceList'):
                temp_model = main_models.DescribeDynamicResourceListResponseBodyDataResourceList()
                self.resource_list.append(temp_model.from_map(k1))

        return self

class DescribeDynamicResourceListResponseBodyDataResourceList(DaraModel):
    def __init__(
        self,
        activation_link: str = None,
        order_id: str = None,
        owner_ali_uid: str = None,
        owner_end_user_id: str = None,
        product_code: str = None,
        resource_id: str = None,
        resource_status: str = None,
        resource_type: str = None,
        sn: str = None,
    ):
        self.activation_link = activation_link
        self.order_id = order_id
        self.owner_ali_uid = owner_ali_uid
        self.owner_end_user_id = owner_end_user_id
        self.product_code = product_code
        self.resource_id = resource_id
        self.resource_status = resource_status
        self.resource_type = resource_type
        self.sn = sn

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activation_link is not None:
            result['ActivationLink'] = self.activation_link

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.owner_ali_uid is not None:
            result['OwnerAliUid'] = self.owner_ali_uid

        if self.owner_end_user_id is not None:
            result['OwnerEndUserId'] = self.owner_end_user_id

        if self.product_code is not None:
            result['ProductCode'] = self.product_code

        if self.resource_id is not None:
            result['ResourceId'] = self.resource_id

        if self.resource_status is not None:
            result['ResourceStatus'] = self.resource_status

        if self.resource_type is not None:
            result['ResourceType'] = self.resource_type

        if self.sn is not None:
            result['Sn'] = self.sn

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivationLink') is not None:
            self.activation_link = m.get('ActivationLink')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('OwnerAliUid') is not None:
            self.owner_ali_uid = m.get('OwnerAliUid')

        if m.get('OwnerEndUserId') is not None:
            self.owner_end_user_id = m.get('OwnerEndUserId')

        if m.get('ProductCode') is not None:
            self.product_code = m.get('ProductCode')

        if m.get('ResourceId') is not None:
            self.resource_id = m.get('ResourceId')

        if m.get('ResourceStatus') is not None:
            self.resource_status = m.get('ResourceStatus')

        if m.get('ResourceType') is not None:
            self.resource_type = m.get('ResourceType')

        if m.get('Sn') is not None:
            self.sn = m.get('Sn')

        return self

