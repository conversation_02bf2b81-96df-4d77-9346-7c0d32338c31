# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class CreatePackageOrderRequest(DaraModel):
    def __init__(
        self,
        activity_id: str = None,
        amount: str = None,
        auto_pay: bool = None,
        code_type: str = None,
        enable_discount_coupon: bool = None,
        end_user_id: str = None,
        event_code: str = None,
        iap_order: bool = None,
        is_first_buy: bool = None,
        is_subscribe: bool = None,
        login_ali_uid: int = None,
        lx_promotion_id: str = None,
        operation_type: str = None,
        order_from: str = None,
        package_code: str = None,
        product_code: str = None,
        product_list_json: List[main_models.CreatePackageOrderRequestProductListJson] = None,
        product_type: str = None,
        promotion_id: str = None,
        resource_id: str = None,
        sku_code: str = None,
        user_ip: str = None,
    ):
        self.activity_id = activity_id
        self.amount = amount
        self.auto_pay = auto_pay
        self.code_type = code_type
        self.enable_discount_coupon = enable_discount_coupon
        # This parameter is required.
        self.end_user_id = end_user_id
        self.event_code = event_code
        self.iap_order = iap_order
        self.is_first_buy = is_first_buy
        self.is_subscribe = is_subscribe
        # This parameter is required.
        self.login_ali_uid = login_ali_uid
        self.lx_promotion_id = lx_promotion_id
        # This parameter is required.
        self.operation_type = operation_type
        # This parameter is required.
        self.order_from = order_from
        self.package_code = package_code
        self.product_code = product_code
        self.product_list_json = product_list_json
        self.product_type = product_type
        self.promotion_id = promotion_id
        self.resource_id = resource_id
        self.sku_code = sku_code
        self.user_ip = user_ip

    def validate(self):
        if self.product_list_json:
            for v1 in self.product_list_json:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity_id is not None:
            result['ActivityId'] = self.activity_id

        if self.amount is not None:
            result['Amount'] = self.amount

        if self.auto_pay is not None:
            result['AutoPay'] = self.auto_pay

        if self.code_type is not None:
            result['CodeType'] = self.code_type

        if self.enable_discount_coupon is not None:
            result['EnableDiscountCoupon'] = self.enable_discount_coupon

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.event_code is not None:
            result['EventCode'] = self.event_code

        if self.iap_order is not None:
            result['IapOrder'] = self.iap_order

        if self.is_first_buy is not None:
            result['IsFirstBuy'] = self.is_first_buy

        if self.is_subscribe is not None:
            result['IsSubscribe'] = self.is_subscribe

        if self.login_ali_uid is not None:
            result['LoginAliUid'] = self.login_ali_uid

        if self.lx_promotion_id is not None:
            result['LxPromotionId'] = self.lx_promotion_id

        if self.operation_type is not None:
            result['OperationType'] = self.operation_type

        if self.order_from is not None:
            result['OrderFrom'] = self.order_from

        if self.package_code is not None:
            result['PackageCode'] = self.package_code

        if self.product_code is not None:
            result['ProductCode'] = self.product_code

        result['ProductListJson'] = []
        if self.product_list_json is not None:
            for k1 in self.product_list_json:
                result['ProductListJson'].append(k1.to_map() if k1 else None)

        if self.product_type is not None:
            result['ProductType'] = self.product_type

        if self.promotion_id is not None:
            result['PromotionId'] = self.promotion_id

        if self.resource_id is not None:
            result['ResourceId'] = self.resource_id

        if self.sku_code is not None:
            result['SkuCode'] = self.sku_code

        if self.user_ip is not None:
            result['UserIp'] = self.user_ip

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivityId') is not None:
            self.activity_id = m.get('ActivityId')

        if m.get('Amount') is not None:
            self.amount = m.get('Amount')

        if m.get('AutoPay') is not None:
            self.auto_pay = m.get('AutoPay')

        if m.get('CodeType') is not None:
            self.code_type = m.get('CodeType')

        if m.get('EnableDiscountCoupon') is not None:
            self.enable_discount_coupon = m.get('EnableDiscountCoupon')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('EventCode') is not None:
            self.event_code = m.get('EventCode')

        if m.get('IapOrder') is not None:
            self.iap_order = m.get('IapOrder')

        if m.get('IsFirstBuy') is not None:
            self.is_first_buy = m.get('IsFirstBuy')

        if m.get('IsSubscribe') is not None:
            self.is_subscribe = m.get('IsSubscribe')

        if m.get('LoginAliUid') is not None:
            self.login_ali_uid = m.get('LoginAliUid')

        if m.get('LxPromotionId') is not None:
            self.lx_promotion_id = m.get('LxPromotionId')

        if m.get('OperationType') is not None:
            self.operation_type = m.get('OperationType')

        if m.get('OrderFrom') is not None:
            self.order_from = m.get('OrderFrom')

        if m.get('PackageCode') is not None:
            self.package_code = m.get('PackageCode')

        if m.get('ProductCode') is not None:
            self.product_code = m.get('ProductCode')

        self.product_list_json = []
        if m.get('ProductListJson') is not None:
            for k1 in m.get('ProductListJson'):
                temp_model = main_models.CreatePackageOrderRequestProductListJson()
                self.product_list_json.append(temp_model.from_map(k1))

        if m.get('ProductType') is not None:
            self.product_type = m.get('ProductType')

        if m.get('PromotionId') is not None:
            self.promotion_id = m.get('PromotionId')

        if m.get('ResourceId') is not None:
            self.resource_id = m.get('ResourceId')

        if m.get('SkuCode') is not None:
            self.sku_code = m.get('SkuCode')

        if m.get('UserIp') is not None:
            self.user_ip = m.get('UserIp')

        return self

class CreatePackageOrderRequestProductListJson(DaraModel):
    def __init__(
        self,
        amount: int = None,
        auto_activate_config: main_models.CreatePackageOrderRequestProductListJsonAutoActivateConfig = None,
        certificate_id: str = None,
        is_auto_activate: bool = None,
        is_first_buy: bool = None,
        is_subscribe: bool = None,
        lx_promotion_id: str = None,
        prm_activity_product_config_code: str = None,
        product_code: str = None,
        product_sku_code: str = None,
        product_type: str = None,
        promotion_id: str = None,
        resource_id_list: List[str] = None,
    ):
        self.amount = amount
        self.auto_activate_config = auto_activate_config
        self.certificate_id = certificate_id
        self.is_auto_activate = is_auto_activate
        self.is_first_buy = is_first_buy
        self.is_subscribe = is_subscribe
        self.lx_promotion_id = lx_promotion_id
        self.prm_activity_product_config_code = prm_activity_product_config_code
        self.product_code = product_code
        self.product_sku_code = product_sku_code
        self.product_type = product_type
        self.promotion_id = promotion_id
        self.resource_id_list = resource_id_list

    def validate(self):
        if self.auto_activate_config:
            self.auto_activate_config.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.amount is not None:
            result['Amount'] = self.amount

        if self.auto_activate_config is not None:
            result['AutoActivateConfig'] = self.auto_activate_config.to_map()

        if self.certificate_id is not None:
            result['CertificateId'] = self.certificate_id

        if self.is_auto_activate is not None:
            result['IsAutoActivate'] = self.is_auto_activate

        if self.is_first_buy is not None:
            result['IsFirstBuy'] = self.is_first_buy

        if self.is_subscribe is not None:
            result['IsSubscribe'] = self.is_subscribe

        if self.lx_promotion_id is not None:
            result['LxPromotionId'] = self.lx_promotion_id

        if self.prm_activity_product_config_code is not None:
            result['PrmActivityProductConfigCode'] = self.prm_activity_product_config_code

        if self.product_code is not None:
            result['ProductCode'] = self.product_code

        if self.product_sku_code is not None:
            result['ProductSkuCode'] = self.product_sku_code

        if self.product_type is not None:
            result['ProductType'] = self.product_type

        if self.promotion_id is not None:
            result['PromotionId'] = self.promotion_id

        if self.resource_id_list is not None:
            result['ResourceIdList'] = self.resource_id_list

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Amount') is not None:
            self.amount = m.get('Amount')

        if m.get('AutoActivateConfig') is not None:
            temp_model = main_models.CreatePackageOrderRequestProductListJsonAutoActivateConfig()
            self.auto_activate_config = temp_model.from_map(m.get('AutoActivateConfig'))

        if m.get('CertificateId') is not None:
            self.certificate_id = m.get('CertificateId')

        if m.get('IsAutoActivate') is not None:
            self.is_auto_activate = m.get('IsAutoActivate')

        if m.get('IsFirstBuy') is not None:
            self.is_first_buy = m.get('IsFirstBuy')

        if m.get('IsSubscribe') is not None:
            self.is_subscribe = m.get('IsSubscribe')

        if m.get('LxPromotionId') is not None:
            self.lx_promotion_id = m.get('LxPromotionId')

        if m.get('PrmActivityProductConfigCode') is not None:
            self.prm_activity_product_config_code = m.get('PrmActivityProductConfigCode')

        if m.get('ProductCode') is not None:
            self.product_code = m.get('ProductCode')

        if m.get('ProductSkuCode') is not None:
            self.product_sku_code = m.get('ProductSkuCode')

        if m.get('ProductType') is not None:
            self.product_type = m.get('ProductType')

        if m.get('PromotionId') is not None:
            self.promotion_id = m.get('PromotionId')

        if m.get('ResourceIdList') is not None:
            self.resource_id_list = m.get('ResourceIdList')

        return self

class CreatePackageOrderRequestProductListJsonAutoActivateConfig(DaraModel):
    def __init__(
        self,
        city_name: str = None,
        end_user_id: str = None,
        user_defined_desktop_name: str = None,
        user_expected_region_id: str = None,
    ):
        self.city_name = city_name
        self.end_user_id = end_user_id
        self.user_defined_desktop_name = user_defined_desktop_name
        self.user_expected_region_id = user_expected_region_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.city_name is not None:
            result['CityName'] = self.city_name

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.user_defined_desktop_name is not None:
            result['UserDefinedDesktopName'] = self.user_defined_desktop_name

        if self.user_expected_region_id is not None:
            result['UserExpectedRegionId'] = self.user_expected_region_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CityName') is not None:
            self.city_name = m.get('CityName')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('UserDefinedDesktopName') is not None:
            self.user_defined_desktop_name = m.get('UserDefinedDesktopName')

        if m.get('UserExpectedRegionId') is not None:
            self.user_expected_region_id = m.get('UserExpectedRegionId')

        return self

