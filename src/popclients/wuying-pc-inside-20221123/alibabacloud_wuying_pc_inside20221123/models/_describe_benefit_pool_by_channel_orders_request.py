# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeBenefitPoolByChannelOrdersRequest(DaraModel):
    def __init__(
        self,
        channel_id: str = None,
        orders: str = None,
    ):
        self.channel_id = channel_id
        self.orders = orders

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.channel_id is not None:
            result['channelId'] = self.channel_id

        if self.orders is not None:
            result['orders'] = self.orders

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('channelId') is not None:
            self.channel_id = m.get('channelId')

        if m.get('orders') is not None:
            self.orders = m.get('orders')

        return self

