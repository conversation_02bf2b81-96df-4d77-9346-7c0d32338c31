# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 


class CountUserDesktopAndPackageResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        message: str = None,
        request_id: str = None,
        resource_statistics: main_models.CountUserDesktopAndPackageResponseBodyResourceStatistics = None,
        trace_id: str = None,
    ):
        self.code = code
        self.message = message
        self.request_id = request_id
        self.resource_statistics = resource_statistics
        self.trace_id = trace_id

    def validate(self):
        if self.resource_statistics:
            self.resource_statistics.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.resource_statistics is not None:
            result['ResourceStatistics'] = self.resource_statistics.to_map()

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('ResourceStatistics') is not None:
            temp_model = main_models.CountUserDesktopAndPackageResponseBodyResourceStatistics()
            self.resource_statistics = temp_model.from_map(m.get('ResourceStatistics'))

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class CountUserDesktopAndPackageResponseBodyResourceStatistics(DaraModel):
    def __init__(
        self,
        desktop_count: int = None,
        package_count: int = None,
    ):
        self.desktop_count = desktop_count
        self.package_count = package_count

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.desktop_count is not None:
            result['DesktopCount'] = self.desktop_count

        if self.package_count is not None:
            result['PackageCount'] = self.package_count

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DesktopCount') is not None:
            self.desktop_count = m.get('DesktopCount')

        if m.get('PackageCount') is not None:
            self.package_count = m.get('PackageCount')

        return self

