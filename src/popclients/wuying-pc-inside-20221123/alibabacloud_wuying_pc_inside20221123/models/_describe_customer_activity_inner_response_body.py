# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeCustomerActivityInnerResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeCustomerActivityInnerResponseBodyData = None,
        request_id: str = None,
    ):
        self.code = code
        self.data = data
        self.request_id = request_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeCustomerActivityInnerResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class DescribeCustomerActivityInnerResponseBodyData(DaraModel):
    def __init__(
        self,
        activity_id: str = None,
        benefit_info: main_models.DescribeCustomerActivityInnerResponseBodyDataBenefitInfo = None,
        status: str = None,
        verify_step_info: main_models.DescribeCustomerActivityInnerResponseBodyDataVerifyStepInfo = None,
    ):
        self.activity_id = activity_id
        self.benefit_info = benefit_info
        self.status = status
        self.verify_step_info = verify_step_info

    def validate(self):
        if self.benefit_info:
            self.benefit_info.validate()
        if self.verify_step_info:
            self.verify_step_info.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity_id is not None:
            result['ActivityId'] = self.activity_id

        if self.benefit_info is not None:
            result['BenefitInfo'] = self.benefit_info.to_map()

        if self.status is not None:
            result['Status'] = self.status

        if self.verify_step_info is not None:
            result['VerifyStepInfo'] = self.verify_step_info.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivityId') is not None:
            self.activity_id = m.get('ActivityId')

        if m.get('BenefitInfo') is not None:
            temp_model = main_models.DescribeCustomerActivityInnerResponseBodyDataBenefitInfo()
            self.benefit_info = temp_model.from_map(m.get('BenefitInfo'))

        if m.get('Status') is not None:
            self.status = m.get('Status')

        if m.get('VerifyStepInfo') is not None:
            temp_model = main_models.DescribeCustomerActivityInnerResponseBodyDataVerifyStepInfo()
            self.verify_step_info = temp_model.from_map(m.get('VerifyStepInfo'))

        return self

class DescribeCustomerActivityInnerResponseBodyDataVerifyStepInfo(DaraModel):
    def __init__(
        self,
        current_step: int = None,
        total_step: int = None,
        verify_ack_order_id: List[str] = None,
        verify_status: str = None,
    ):
        self.current_step = current_step
        self.total_step = total_step
        self.verify_ack_order_id = verify_ack_order_id
        self.verify_status = verify_status

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.current_step is not None:
            result['CurrentStep'] = self.current_step

        if self.total_step is not None:
            result['TotalStep'] = self.total_step

        if self.verify_ack_order_id is not None:
            result['VerifyAckOrderId'] = self.verify_ack_order_id

        if self.verify_status is not None:
            result['VerifyStatus'] = self.verify_status

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CurrentStep') is not None:
            self.current_step = m.get('CurrentStep')

        if m.get('TotalStep') is not None:
            self.total_step = m.get('TotalStep')

        if m.get('VerifyAckOrderId') is not None:
            self.verify_ack_order_id = m.get('VerifyAckOrderId')

        if m.get('VerifyStatus') is not None:
            self.verify_status = m.get('VerifyStatus')

        return self

class DescribeCustomerActivityInnerResponseBodyDataBenefitInfo(DaraModel):
    def __init__(
        self,
        ack_status: str = None,
        content: str = None,
        current_step_id: str = None,
        type: str = None,
        verify_ack_order_id: List[str] = None,
        verify_status: str = None,
        verify_step_total: int = None,
    ):
        self.ack_status = ack_status
        self.content = content
        self.current_step_id = current_step_id
        self.type = type
        self.verify_ack_order_id = verify_ack_order_id
        self.verify_status = verify_status
        self.verify_step_total = verify_step_total

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ack_status is not None:
            result['AckStatus'] = self.ack_status

        if self.content is not None:
            result['Content'] = self.content

        if self.current_step_id is not None:
            result['CurrentStepId'] = self.current_step_id

        if self.type is not None:
            result['Type'] = self.type

        if self.verify_ack_order_id is not None:
            result['VerifyAckOrderId'] = self.verify_ack_order_id

        if self.verify_status is not None:
            result['VerifyStatus'] = self.verify_status

        if self.verify_step_total is not None:
            result['VerifyStepTotal'] = self.verify_step_total

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AckStatus') is not None:
            self.ack_status = m.get('AckStatus')

        if m.get('Content') is not None:
            self.content = m.get('Content')

        if m.get('CurrentStepId') is not None:
            self.current_step_id = m.get('CurrentStepId')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        if m.get('VerifyAckOrderId') is not None:
            self.verify_ack_order_id = m.get('VerifyAckOrderId')

        if m.get('VerifyStatus') is not None:
            self.verify_status = m.get('VerifyStatus')

        if m.get('VerifyStepTotal') is not None:
            self.verify_step_total = m.get('VerifyStepTotal')

        return self

