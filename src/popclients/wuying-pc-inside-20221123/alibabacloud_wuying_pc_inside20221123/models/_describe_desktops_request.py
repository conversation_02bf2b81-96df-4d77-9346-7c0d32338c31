# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeDesktopsRequest(DaraModel):
    def __init__(
        self,
        create_user_id: str = None,
        display_type: str = None,
        end_user_id: str = None,
        team_id: str = None,
    ):
        self.create_user_id = create_user_id
        self.display_type = display_type
        self.end_user_id = end_user_id
        self.team_id = team_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.create_user_id is not None:
            result['CreateUserId'] = self.create_user_id

        if self.display_type is not None:
            result['DisplayType'] = self.display_type

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CreateUserId') is not None:
            self.create_user_id = m.get('CreateUserId')

        if m.get('DisplayType') is not None:
            self.display_type = m.get('DisplayType')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        return self

