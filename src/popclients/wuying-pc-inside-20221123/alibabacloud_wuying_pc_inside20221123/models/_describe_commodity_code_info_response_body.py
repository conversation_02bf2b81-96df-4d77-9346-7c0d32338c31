# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 


class DescribeCommodityCodeInfoResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeCommodityCodeInfoResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeCommodityCodeInfoResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeCommodityCodeInfoResponseBodyData(DaraModel):
    def __init__(
        self,
        describe_commodity_type_dto: main_models.DescribeCommodityCodeInfoResponseBodyDataDescribeCommodityTypeDto = None,
        describe_package_dto: main_models.DescribeCommodityCodeInfoResponseBodyDataDescribePackageDto = None,
        describe_spec_code_dto: main_models.DescribeCommodityCodeInfoResponseBodyDataDescribeSpecCodeDto = None,
    ):
        self.describe_commodity_type_dto = describe_commodity_type_dto
        self.describe_package_dto = describe_package_dto
        self.describe_spec_code_dto = describe_spec_code_dto

    def validate(self):
        if self.describe_commodity_type_dto:
            self.describe_commodity_type_dto.validate()
        if self.describe_package_dto:
            self.describe_package_dto.validate()
        if self.describe_spec_code_dto:
            self.describe_spec_code_dto.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.describe_commodity_type_dto is not None:
            result['DescribeCommodityTypeDto'] = self.describe_commodity_type_dto.to_map()

        if self.describe_package_dto is not None:
            result['DescribePackageDto'] = self.describe_package_dto.to_map()

        if self.describe_spec_code_dto is not None:
            result['DescribeSpecCodeDto'] = self.describe_spec_code_dto.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DescribeCommodityTypeDto') is not None:
            temp_model = main_models.DescribeCommodityCodeInfoResponseBodyDataDescribeCommodityTypeDto()
            self.describe_commodity_type_dto = temp_model.from_map(m.get('DescribeCommodityTypeDto'))

        if m.get('DescribePackageDto') is not None:
            temp_model = main_models.DescribeCommodityCodeInfoResponseBodyDataDescribePackageDto()
            self.describe_package_dto = temp_model.from_map(m.get('DescribePackageDto'))

        if m.get('DescribeSpecCodeDto') is not None:
            temp_model = main_models.DescribeCommodityCodeInfoResponseBodyDataDescribeSpecCodeDto()
            self.describe_spec_code_dto = temp_model.from_map(m.get('DescribeSpecCodeDto'))

        return self

class DescribeCommodityCodeInfoResponseBodyDataDescribeSpecCodeDto(DaraModel):
    def __init__(
        self,
        bandwidth: int = None,
        cpu_core_count: int = None,
        desc: str = None,
        gpu_count: int = None,
        memory_size: int = None,
        name: str = None,
        os_type: str = None,
        spec_code: str = None,
        system_disk_size: int = None,
        user_disk_size: int = None,
    ):
        self.bandwidth = bandwidth
        self.cpu_core_count = cpu_core_count
        self.desc = desc
        self.gpu_count = gpu_count
        self.memory_size = memory_size
        self.name = name
        self.os_type = os_type
        self.spec_code = spec_code
        self.system_disk_size = system_disk_size
        self.user_disk_size = user_disk_size

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.bandwidth is not None:
            result['Bandwidth'] = self.bandwidth

        if self.cpu_core_count is not None:
            result['CpuCoreCount'] = self.cpu_core_count

        if self.desc is not None:
            result['Desc'] = self.desc

        if self.gpu_count is not None:
            result['GpuCount'] = self.gpu_count

        if self.memory_size is not None:
            result['MemorySize'] = self.memory_size

        if self.name is not None:
            result['Name'] = self.name

        if self.os_type is not None:
            result['OsType'] = self.os_type

        if self.spec_code is not None:
            result['SpecCode'] = self.spec_code

        if self.system_disk_size is not None:
            result['SystemDiskSize'] = self.system_disk_size

        if self.user_disk_size is not None:
            result['UserDiskSize'] = self.user_disk_size

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Bandwidth') is not None:
            self.bandwidth = m.get('Bandwidth')

        if m.get('CpuCoreCount') is not None:
            self.cpu_core_count = m.get('CpuCoreCount')

        if m.get('Desc') is not None:
            self.desc = m.get('Desc')

        if m.get('GpuCount') is not None:
            self.gpu_count = m.get('GpuCount')

        if m.get('MemorySize') is not None:
            self.memory_size = m.get('MemorySize')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('OsType') is not None:
            self.os_type = m.get('OsType')

        if m.get('SpecCode') is not None:
            self.spec_code = m.get('SpecCode')

        if m.get('SystemDiskSize') is not None:
            self.system_disk_size = m.get('SystemDiskSize')

        if m.get('UserDiskSize') is not None:
            self.user_disk_size = m.get('UserDiskSize')

        return self

class DescribeCommodityCodeInfoResponseBodyDataDescribePackageDto(DaraModel):
    def __init__(
        self,
        code: str = None,
        duration_time: int = None,
        name: str = None,
        period: int = None,
        period_unit: str = None,
    ):
        self.code = code
        self.duration_time = duration_time
        self.name = name
        self.period = period
        self.period_unit = period_unit

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.duration_time is not None:
            result['DurationTime'] = self.duration_time

        if self.name is not None:
            result['Name'] = self.name

        if self.period is not None:
            result['Period'] = self.period

        if self.period_unit is not None:
            result['PeriodUnit'] = self.period_unit

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('DurationTime') is not None:
            self.duration_time = m.get('DurationTime')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('Period') is not None:
            self.period = m.get('Period')

        if m.get('PeriodUnit') is not None:
            self.period_unit = m.get('PeriodUnit')

        return self

class DescribeCommodityCodeInfoResponseBodyDataDescribeCommodityTypeDto(DaraModel):
    def __init__(
        self,
        commodity_code: str = None,
        commodity_name: str = None,
    ):
        self.commodity_code = commodity_code
        self.commodity_name = commodity_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.commodity_code is not None:
            result['CommodityCode'] = self.commodity_code

        if self.commodity_name is not None:
            result['CommodityName'] = self.commodity_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CommodityCode') is not None:
            self.commodity_code = m.get('CommodityCode')

        if m.get('CommodityName') is not None:
            self.commodity_name = m.get('CommodityName')

        return self

