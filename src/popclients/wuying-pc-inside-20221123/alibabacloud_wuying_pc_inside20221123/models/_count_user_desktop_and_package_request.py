# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CountUserDesktopAndPackageRequest(DaraModel):
    def __init__(
        self,
        creation_time_end: int = None,
        creation_time_start: int = None,
        include_deleted: bool = None,
        login_ali_uid: int = None,
    ):
        self.creation_time_end = creation_time_end
        self.creation_time_start = creation_time_start
        self.include_deleted = include_deleted
        # This parameter is required.
        self.login_ali_uid = login_ali_uid

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.creation_time_end is not None:
            result['CreationTimeEnd'] = self.creation_time_end

        if self.creation_time_start is not None:
            result['CreationTimeStart'] = self.creation_time_start

        if self.include_deleted is not None:
            result['IncludeDeleted'] = self.include_deleted

        if self.login_ali_uid is not None:
            result['LoginAliUid'] = self.login_ali_uid

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CreationTimeEnd') is not None:
            self.creation_time_end = m.get('CreationTimeEnd')

        if m.get('CreationTimeStart') is not None:
            self.creation_time_start = m.get('CreationTimeStart')

        if m.get('IncludeDeleted') is not None:
            self.include_deleted = m.get('IncludeDeleted')

        if m.get('LoginAliUid') is not None:
            self.login_ali_uid = m.get('LoginAliUid')

        return self

