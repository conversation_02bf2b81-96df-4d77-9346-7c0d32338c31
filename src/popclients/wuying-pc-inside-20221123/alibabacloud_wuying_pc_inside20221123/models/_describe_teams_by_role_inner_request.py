# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeTeamsByRoleInnerRequest(DaraModel):
    def __init__(
        self,
        role: str = None,
        union_id: str = None,
    ):
        self.role = role
        self.union_id = union_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.role is not None:
            result['Role'] = self.role

        if self.union_id is not None:
            result['UnionId'] = self.union_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Role') is not None:
            self.role = m.get('Role')

        if m.get('UnionId') is not None:
            self.union_id = m.get('UnionId')

        return self

