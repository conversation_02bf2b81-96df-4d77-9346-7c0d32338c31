# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeUserPurchaseCouponsInnerResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeUserPurchaseCouponsInnerResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeUserPurchaseCouponsInnerResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeUserPurchaseCouponsInnerResponseBodyData(DaraModel):
    def __init__(
        self,
        coupon_list: List[main_models.DescribeUserPurchaseCouponsInnerResponseBodyDataCouponList] = None,
        page_no: int = None,
        page_size: int = None,
        total_count: int = None,
    ):
        self.coupon_list = coupon_list
        self.page_no = page_no
        self.page_size = page_size
        self.total_count = total_count

    def validate(self):
        if self.coupon_list:
            for v1 in self.coupon_list:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['CouponList'] = []
        if self.coupon_list is not None:
            for k1 in self.coupon_list:
                result['CouponList'].append(k1.to_map() if k1 else None)

        if self.page_no is not None:
            result['PageNo'] = self.page_no

        if self.page_size is not None:
            result['PageSize'] = self.page_size

        if self.total_count is not None:
            result['TotalCount'] = self.total_count

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.coupon_list = []
        if m.get('CouponList') is not None:
            for k1 in m.get('CouponList'):
                temp_model = main_models.DescribeUserPurchaseCouponsInnerResponseBodyDataCouponList()
                self.coupon_list.append(temp_model.from_map(k1))

        if m.get('PageNo') is not None:
            self.page_no = m.get('PageNo')

        if m.get('PageSize') is not None:
            self.page_size = m.get('PageSize')

        if m.get('TotalCount') is not None:
            self.total_count = m.get('TotalCount')

        return self

class DescribeUserPurchaseCouponsInnerResponseBodyDataCouponList(DaraModel):
    def __init__(
        self,
        coupon_id: int = None,
        coupon_tile_hint: str = None,
        coupon_title: str = None,
        description: str = None,
        end_time: str = None,
        start_time: str = None,
        status: str = None,
    ):
        self.coupon_id = coupon_id
        self.coupon_tile_hint = coupon_tile_hint
        self.coupon_title = coupon_title
        self.description = description
        self.end_time = end_time
        self.start_time = start_time
        self.status = status

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.coupon_id is not None:
            result['CouponId'] = self.coupon_id

        if self.coupon_tile_hint is not None:
            result['CouponTileHint'] = self.coupon_tile_hint

        if self.coupon_title is not None:
            result['CouponTitle'] = self.coupon_title

        if self.description is not None:
            result['Description'] = self.description

        if self.end_time is not None:
            result['EndTime'] = self.end_time

        if self.start_time is not None:
            result['StartTime'] = self.start_time

        if self.status is not None:
            result['Status'] = self.status

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CouponId') is not None:
            self.coupon_id = m.get('CouponId')

        if m.get('CouponTileHint') is not None:
            self.coupon_tile_hint = m.get('CouponTileHint')

        if m.get('CouponTitle') is not None:
            self.coupon_title = m.get('CouponTitle')

        if m.get('Description') is not None:
            self.description = m.get('Description')

        if m.get('EndTime') is not None:
            self.end_time = m.get('EndTime')

        if m.get('StartTime') is not None:
            self.start_time = m.get('StartTime')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        return self

