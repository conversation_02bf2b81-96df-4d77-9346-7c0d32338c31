# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class InnerDescribeImagesShrinkRequest(DaraModel):
    def __init__(
        self,
        image_ids_shrink: str = None,
        image_share_codes_shrink: str = None,
        is_asc: bool = None,
        order_by: str = None,
    ):
        self.image_ids_shrink = image_ids_shrink
        self.image_share_codes_shrink = image_share_codes_shrink
        self.is_asc = is_asc
        self.order_by = order_by

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.image_ids_shrink is not None:
            result['ImageIds'] = self.image_ids_shrink

        if self.image_share_codes_shrink is not None:
            result['ImageShareCodes'] = self.image_share_codes_shrink

        if self.is_asc is not None:
            result['IsAsc'] = self.is_asc

        if self.order_by is not None:
            result['OrderBy'] = self.order_by

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ImageIds') is not None:
            self.image_ids_shrink = m.get('ImageIds')

        if m.get('ImageShareCodes') is not None:
            self.image_share_codes_shrink = m.get('ImageShareCodes')

        if m.get('IsAsc') is not None:
            self.is_asc = m.get('IsAsc')

        if m.get('OrderBy') is not None:
            self.order_by = m.get('OrderBy')

        return self

