# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CheckCouponCodeRuleInnerRequest(DaraModel):
    def __init__(
        self,
        code_type: str = None,
        coupon_code: str = None,
        prefix: str = None,
        regex: str = None,
    ):
        self.code_type = code_type
        self.coupon_code = coupon_code
        self.prefix = prefix
        self.regex = regex

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code_type is not None:
            result['CodeType'] = self.code_type

        if self.coupon_code is not None:
            result['CouponCode'] = self.coupon_code

        if self.prefix is not None:
            result['Prefix'] = self.prefix

        if self.regex is not None:
            result['Regex'] = self.regex

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CodeType') is not None:
            self.code_type = m.get('CodeType')

        if m.get('CouponCode') is not None:
            self.coupon_code = m.get('CouponCode')

        if m.get('Prefix') is not None:
            self.prefix = m.get('Prefix')

        if m.get('Regex') is not None:
            self.regex = m.get('Regex')

        return self

