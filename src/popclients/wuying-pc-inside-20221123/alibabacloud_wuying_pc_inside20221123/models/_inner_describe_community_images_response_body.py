# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class InnerDescribeCommunityImagesResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.InnerDescribeCommunityImagesResponseBodyData] = None,
        message: str = None,
        request_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.InnerDescribeCommunityImagesResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class InnerDescribeCommunityImagesResponseBodyData(DaraModel):
    def __init__(
        self,
        community_image_id: str = None,
        description: str = None,
        image_code: str = None,
        image_id: str = None,
        name: str = None,
        publish_time: str = None,
        simple_desc: str = None,
        status: str = None,
    ):
        self.community_image_id = community_image_id
        self.description = description
        self.image_code = image_code
        self.image_id = image_id
        self.name = name
        self.publish_time = publish_time
        self.simple_desc = simple_desc
        self.status = status

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.community_image_id is not None:
            result['CommunityImageId'] = self.community_image_id

        if self.description is not None:
            result['Description'] = self.description

        if self.image_code is not None:
            result['ImageCode'] = self.image_code

        if self.image_id is not None:
            result['ImageId'] = self.image_id

        if self.name is not None:
            result['Name'] = self.name

        if self.publish_time is not None:
            result['PublishTime'] = self.publish_time

        if self.simple_desc is not None:
            result['SimpleDesc'] = self.simple_desc

        if self.status is not None:
            result['Status'] = self.status

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CommunityImageId') is not None:
            self.community_image_id = m.get('CommunityImageId')

        if m.get('Description') is not None:
            self.description = m.get('Description')

        if m.get('ImageCode') is not None:
            self.image_code = m.get('ImageCode')

        if m.get('ImageId') is not None:
            self.image_id = m.get('ImageId')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('PublishTime') is not None:
            self.publish_time = m.get('PublishTime')

        if m.get('SimpleDesc') is not None:
            self.simple_desc = m.get('SimpleDesc')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        return self

