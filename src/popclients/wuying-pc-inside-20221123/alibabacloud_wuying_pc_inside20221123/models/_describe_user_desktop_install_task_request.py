# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeUserDesktopInstallTaskRequest(DaraModel):
    def __init__(
        self,
        install_task_id: str = None,
    ):
        self.install_task_id = install_task_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.install_task_id is not None:
            result['InstallTaskId'] = self.install_task_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('InstallTaskId') is not None:
            self.install_task_id = m.get('InstallTaskId')

        return self

