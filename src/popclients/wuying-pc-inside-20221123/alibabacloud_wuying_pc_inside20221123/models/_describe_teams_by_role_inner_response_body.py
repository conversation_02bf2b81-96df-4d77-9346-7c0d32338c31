# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeTeamsByRoleInnerResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.DescribeTeamsByRoleInnerResponseBodyData] = None,
        request_id: str = None,
    ):
        self.code = code
        self.data = data
        self.request_id = request_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.DescribeTeamsByRoleInnerResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class DescribeTeamsByRoleInnerResponseBodyData(DaraModel):
    def __init__(
        self,
        member_info: main_models.DescribeTeamsByRoleInnerResponseBodyDataMemberInfo = None,
        owner_info: main_models.DescribeTeamsByRoleInnerResponseBodyDataOwnerInfo = None,
        personal_info: main_models.DescribeTeamsByRoleInnerResponseBodyDataPersonalInfo = None,
        team_code: str = None,
        team_description: str = None,
        team_id: str = None,
        team_name: str = None,
    ):
        self.member_info = member_info
        self.owner_info = owner_info
        self.personal_info = personal_info
        self.team_code = team_code
        self.team_description = team_description
        self.team_id = team_id
        self.team_name = team_name

    def validate(self):
        if self.member_info:
            self.member_info.validate()
        if self.owner_info:
            self.owner_info.validate()
        if self.personal_info:
            self.personal_info.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.member_info is not None:
            result['MemberInfo'] = self.member_info.to_map()

        if self.owner_info is not None:
            result['OwnerInfo'] = self.owner_info.to_map()

        if self.personal_info is not None:
            result['PersonalInfo'] = self.personal_info.to_map()

        if self.team_code is not None:
            result['TeamCode'] = self.team_code

        if self.team_description is not None:
            result['TeamDescription'] = self.team_description

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        if self.team_name is not None:
            result['TeamName'] = self.team_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('MemberInfo') is not None:
            temp_model = main_models.DescribeTeamsByRoleInnerResponseBodyDataMemberInfo()
            self.member_info = temp_model.from_map(m.get('MemberInfo'))

        if m.get('OwnerInfo') is not None:
            temp_model = main_models.DescribeTeamsByRoleInnerResponseBodyDataOwnerInfo()
            self.owner_info = temp_model.from_map(m.get('OwnerInfo'))

        if m.get('PersonalInfo') is not None:
            temp_model = main_models.DescribeTeamsByRoleInnerResponseBodyDataPersonalInfo()
            self.personal_info = temp_model.from_map(m.get('PersonalInfo'))

        if m.get('TeamCode') is not None:
            self.team_code = m.get('TeamCode')

        if m.get('TeamDescription') is not None:
            self.team_description = m.get('TeamDescription')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        if m.get('TeamName') is not None:
            self.team_name = m.get('TeamName')

        return self

class DescribeTeamsByRoleInnerResponseBodyDataPersonalInfo(DaraModel):
    def __init__(
        self,
        join_time: str = None,
        nick_name: str = None,
        remark_name: str = None,
        role: str = None,
        wy_id: str = None,
    ):
        self.join_time = join_time
        self.nick_name = nick_name
        self.remark_name = remark_name
        self.role = role
        self.wy_id = wy_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.join_time is not None:
            result['JoinTime'] = self.join_time

        if self.nick_name is not None:
            result['NickName'] = self.nick_name

        if self.remark_name is not None:
            result['RemarkName'] = self.remark_name

        if self.role is not None:
            result['Role'] = self.role

        if self.wy_id is not None:
            result['WyId'] = self.wy_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('JoinTime') is not None:
            self.join_time = m.get('JoinTime')

        if m.get('NickName') is not None:
            self.nick_name = m.get('NickName')

        if m.get('RemarkName') is not None:
            self.remark_name = m.get('RemarkName')

        if m.get('Role') is not None:
            self.role = m.get('Role')

        if m.get('WyId') is not None:
            self.wy_id = m.get('WyId')

        return self

class DescribeTeamsByRoleInnerResponseBodyDataOwnerInfo(DaraModel):
    def __init__(
        self,
        owner_name: str = None,
        owner_phone: str = None,
    ):
        self.owner_name = owner_name
        self.owner_phone = owner_phone

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.owner_name is not None:
            result['OwnerName'] = self.owner_name

        if self.owner_phone is not None:
            result['OwnerPhone'] = self.owner_phone

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('OwnerName') is not None:
            self.owner_name = m.get('OwnerName')

        if m.get('OwnerPhone') is not None:
            self.owner_phone = m.get('OwnerPhone')

        return self

class DescribeTeamsByRoleInnerResponseBodyDataMemberInfo(DaraModel):
    def __init__(
        self,
        member_quota: int = None,
        member_total: int = None,
    ):
        self.member_quota = member_quota
        self.member_total = member_total

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.member_quota is not None:
            result['MemberQuota'] = self.member_quota

        if self.member_total is not None:
            result['MemberTotal'] = self.member_total

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('MemberQuota') is not None:
            self.member_quota = m.get('MemberQuota')

        if m.get('MemberTotal') is not None:
            self.member_total = m.get('MemberTotal')

        return self

