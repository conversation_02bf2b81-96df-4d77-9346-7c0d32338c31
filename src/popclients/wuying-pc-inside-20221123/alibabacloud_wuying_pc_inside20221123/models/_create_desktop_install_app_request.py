# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateDesktopInstallAppRequest(DaraModel):
    def __init__(
        self,
        display_order: int = None,
        icon: str = None,
        install_file_name: str = None,
        install_file_path: str = None,
        install_scene: str = None,
        name: str = None,
        os_type: str = None,
        silent_install: bool = None,
        silent_install_args: str = None,
        type: str = None,
    ):
        self.display_order = display_order
        self.icon = icon
        self.install_file_name = install_file_name
        self.install_file_path = install_file_path
        self.install_scene = install_scene
        self.name = name
        self.os_type = os_type
        self.silent_install = silent_install
        self.silent_install_args = silent_install_args
        self.type = type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.display_order is not None:
            result['DisplayOrder'] = self.display_order

        if self.icon is not None:
            result['Icon'] = self.icon

        if self.install_file_name is not None:
            result['InstallFileName'] = self.install_file_name

        if self.install_file_path is not None:
            result['InstallFilePath'] = self.install_file_path

        if self.install_scene is not None:
            result['InstallScene'] = self.install_scene

        if self.name is not None:
            result['Name'] = self.name

        if self.os_type is not None:
            result['OsType'] = self.os_type

        if self.silent_install is not None:
            result['SilentInstall'] = self.silent_install

        if self.silent_install_args is not None:
            result['SilentInstallArgs'] = self.silent_install_args

        if self.type is not None:
            result['Type'] = self.type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DisplayOrder') is not None:
            self.display_order = m.get('DisplayOrder')

        if m.get('Icon') is not None:
            self.icon = m.get('Icon')

        if m.get('InstallFileName') is not None:
            self.install_file_name = m.get('InstallFileName')

        if m.get('InstallFilePath') is not None:
            self.install_file_path = m.get('InstallFilePath')

        if m.get('InstallScene') is not None:
            self.install_scene = m.get('InstallScene')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('OsType') is not None:
            self.os_type = m.get('OsType')

        if m.get('SilentInstall') is not None:
            self.silent_install = m.get('SilentInstall')

        if m.get('SilentInstallArgs') is not None:
            self.silent_install_args = m.get('SilentInstallArgs')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        return self

