# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 


class DescribeTerminalSerialNumberBenefitResponseBody(DaraModel):
    def __init__(
        self,
        data: main_models.DescribeTerminalSerialNumberBenefitResponseBodyData = None,
        err_code: str = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.data = data
        self.err_code = err_code
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.err_code is not None:
            result['ErrCode'] = self.err_code

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Data') is not None:
            temp_model = main_models.DescribeTerminalSerialNumberBenefitResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('ErrCode') is not None:
            self.err_code = m.get('ErrCode')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeTerminalSerialNumberBenefitResponseBodyData(DaraModel):
    def __init__(
        self,
        order_id: str = None,
        spec_code: str = None,
        type: str = None,
    ):
        self.order_id = order_id
        self.spec_code = spec_code
        self.type = type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.spec_code is not None:
            result['SpecCode'] = self.spec_code

        if self.type is not None:
            result['Type'] = self.type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('SpecCode') is not None:
            self.spec_code = m.get('SpecCode')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        return self

