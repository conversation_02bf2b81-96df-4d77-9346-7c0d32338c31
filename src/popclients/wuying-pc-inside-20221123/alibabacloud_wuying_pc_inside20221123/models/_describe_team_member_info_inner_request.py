# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from typing import List


class DescribeTeamMemberInfoInnerRequest(DaraModel):
    def __init__(
        self,
        team_id: str = None,
        wy_ids: List[str] = None,
    ):
        self.team_id = team_id
        self.wy_ids = wy_ids

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.team_id is not None:
            result['TeamId'] = self.team_id

        if self.wy_ids is not None:
            result['WyIds'] = self.wy_ids

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        if m.get('WyIds') is not None:
            self.wy_ids = m.get('WyIds')

        return self

