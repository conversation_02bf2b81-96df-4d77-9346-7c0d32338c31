# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeTeamInfoByCodeRequest(DaraModel):
    def __init__(
        self,
        team_codes: str = None,
    ):
        self.team_codes = team_codes

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.team_codes is not None:
            result['TeamCodes'] = self.team_codes

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('TeamCodes') is not None:
            self.team_codes = m.get('TeamCodes')

        return self

