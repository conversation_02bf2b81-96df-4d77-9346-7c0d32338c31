# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class InnerDescribeDesktopLocalInfoShrinkRequest(DaraModel):
    def __init__(
        self,
        desktop_ids_shrink: str = None,
        desktop_label: str = None,
        desktop_type: str = None,
        end_user_ids_shrink: str = None,
        resource_ids_shrink: str = None,
    ):
        self.desktop_ids_shrink = desktop_ids_shrink
        self.desktop_label = desktop_label
        self.desktop_type = desktop_type
        self.end_user_ids_shrink = end_user_ids_shrink
        self.resource_ids_shrink = resource_ids_shrink

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.desktop_ids_shrink is not None:
            result['DesktopIds'] = self.desktop_ids_shrink

        if self.desktop_label is not None:
            result['DesktopLabel'] = self.desktop_label

        if self.desktop_type is not None:
            result['DesktopType'] = self.desktop_type

        if self.end_user_ids_shrink is not None:
            result['EndUserIds'] = self.end_user_ids_shrink

        if self.resource_ids_shrink is not None:
            result['ResourceIds'] = self.resource_ids_shrink

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DesktopIds') is not None:
            self.desktop_ids_shrink = m.get('DesktopIds')

        if m.get('DesktopLabel') is not None:
            self.desktop_label = m.get('DesktopLabel')

        if m.get('DesktopType') is not None:
            self.desktop_type = m.get('DesktopType')

        if m.get('EndUserIds') is not None:
            self.end_user_ids_shrink = m.get('EndUserIds')

        if m.get('ResourceIds') is not None:
            self.resource_ids_shrink = m.get('ResourceIds')

        return self

