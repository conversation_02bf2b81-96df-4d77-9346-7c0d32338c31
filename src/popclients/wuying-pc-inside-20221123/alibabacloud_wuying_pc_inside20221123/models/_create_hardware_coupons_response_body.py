# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class CreateHardwareCouponsResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.CreateHardwareCouponsResponseBodyData = None,
        err_code: str = None,
        err_msg: str = None,
        request_id: str = None,
    ):
        self.code = code
        self.data = data
        self.err_code = err_code
        self.err_msg = err_msg
        self.request_id = request_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['code'] = self.code

        if self.data is not None:
            result['data'] = self.data.to_map()

        if self.err_code is not None:
            result['errCode'] = self.err_code

        if self.err_msg is not None:
            result['errMsg'] = self.err_msg

        if self.request_id is not None:
            result['requestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('code') is not None:
            self.code = m.get('code')

        if m.get('data') is not None:
            temp_model = main_models.CreateHardwareCouponsResponseBodyData()
            self.data = temp_model.from_map(m.get('data'))

        if m.get('errCode') is not None:
            self.err_code = m.get('errCode')

        if m.get('errMsg') is not None:
            self.err_msg = m.get('errMsg')

        if m.get('requestId') is not None:
            self.request_id = m.get('requestId')

        return self

class CreateHardwareCouponsResponseBodyData(DaraModel):
    def __init__(
        self,
        fail_list: List[main_models.CreateHardwareCouponsResponseBodyDataFailList] = None,
        successful_coupons: int = None,
    ):
        self.fail_list = fail_list
        self.successful_coupons = successful_coupons

    def validate(self):
        if self.fail_list:
            for v1 in self.fail_list:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['failList'] = []
        if self.fail_list is not None:
            for k1 in self.fail_list:
                result['failList'].append(k1.to_map() if k1 else None)

        if self.successful_coupons is not None:
            result['successfulCoupons'] = self.successful_coupons

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.fail_list = []
        if m.get('failList') is not None:
            for k1 in m.get('failList'):
                temp_model = main_models.CreateHardwareCouponsResponseBodyDataFailList()
                self.fail_list.append(temp_model.from_map(k1))

        if m.get('successfulCoupons') is not None:
            self.successful_coupons = m.get('successfulCoupons')

        return self

class CreateHardwareCouponsResponseBodyDataFailList(DaraModel):
    def __init__(
        self,
        reason: str = None,
        serial_num: str = None,
    ):
        self.reason = reason
        self.serial_num = serial_num

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.reason is not None:
            result['reason'] = self.reason

        if self.serial_num is not None:
            result['serialNum'] = self.serial_num

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('reason') is not None:
            self.reason = m.get('reason')

        if m.get('serialNum') is not None:
            self.serial_num = m.get('serialNum')

        return self

