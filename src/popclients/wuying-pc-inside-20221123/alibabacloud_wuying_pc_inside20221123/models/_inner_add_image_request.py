# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class InnerAddImageRequest(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        biz_type: int = None,
        description: str = None,
        image_instance_id: str = None,
        image_name: str = None,
        image_type: str = None,
        permission: str = None,
        region_id: str = None,
        source_desktop_id: str = None,
        source_desktop_name: str = None,
    ):
        self.ali_uid = ali_uid
        self.biz_type = biz_type
        self.description = description
        self.image_instance_id = image_instance_id
        self.image_name = image_name
        self.image_type = image_type
        self.permission = permission
        self.region_id = region_id
        self.source_desktop_id = source_desktop_id
        self.source_desktop_name = source_desktop_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.biz_type is not None:
            result['BizType'] = self.biz_type

        if self.description is not None:
            result['Description'] = self.description

        if self.image_instance_id is not None:
            result['ImageInstanceId'] = self.image_instance_id

        if self.image_name is not None:
            result['ImageName'] = self.image_name

        if self.image_type is not None:
            result['ImageType'] = self.image_type

        if self.permission is not None:
            result['Permission'] = self.permission

        if self.region_id is not None:
            result['RegionId'] = self.region_id

        if self.source_desktop_id is not None:
            result['SourceDesktopId'] = self.source_desktop_id

        if self.source_desktop_name is not None:
            result['SourceDesktopName'] = self.source_desktop_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('BizType') is not None:
            self.biz_type = m.get('BizType')

        if m.get('Description') is not None:
            self.description = m.get('Description')

        if m.get('ImageInstanceId') is not None:
            self.image_instance_id = m.get('ImageInstanceId')

        if m.get('ImageName') is not None:
            self.image_name = m.get('ImageName')

        if m.get('ImageType') is not None:
            self.image_type = m.get('ImageType')

        if m.get('Permission') is not None:
            self.permission = m.get('Permission')

        if m.get('RegionId') is not None:
            self.region_id = m.get('RegionId')

        if m.get('SourceDesktopId') is not None:
            self.source_desktop_id = m.get('SourceDesktopId')

        if m.get('SourceDesktopName') is not None:
            self.source_desktop_name = m.get('SourceDesktopName')

        return self

