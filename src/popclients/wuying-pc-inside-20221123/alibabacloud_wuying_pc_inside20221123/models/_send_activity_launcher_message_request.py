# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from typing import Dict


class SendActivityLauncherMessageRequest(DaraModel):
    def __init__(
        self,
        core_hours: str = None,
        customer_params: Dict[str, str] = None,
        desktop_id: str = None,
        instance_id: str = None,
        template_id: str = None,
        user_id: str = None,
    ):
        self.core_hours = core_hours
        self.customer_params = customer_params
        self.desktop_id = desktop_id
        self.instance_id = instance_id
        self.template_id = template_id
        self.user_id = user_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.core_hours is not None:
            result['CoreHours'] = self.core_hours

        if self.customer_params is not None:
            result['CustomerParams'] = self.customer_params

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.instance_id is not None:
            result['InstanceId'] = self.instance_id

        if self.template_id is not None:
            result['TemplateId'] = self.template_id

        if self.user_id is not None:
            result['UserId'] = self.user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CoreHours') is not None:
            self.core_hours = m.get('CoreHours')

        if m.get('CustomerParams') is not None:
            self.customer_params = m.get('CustomerParams')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('InstanceId') is not None:
            self.instance_id = m.get('InstanceId')

        if m.get('TemplateId') is not None:
            self.template_id = m.get('TemplateId')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        return self

