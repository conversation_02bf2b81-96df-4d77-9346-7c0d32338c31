# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeTbUserBenefitsRequest(DaraModel):
    def __init__(
        self,
        tb_order_no: str = None,
        telephone_number: str = None,
    ):
        self.tb_order_no = tb_order_no
        self.telephone_number = telephone_number

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.tb_order_no is not None:
            result['TbOrderNo'] = self.tb_order_no

        if self.telephone_number is not None:
            result['TelephoneNumber'] = self.telephone_number

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('TbOrderNo') is not None:
            self.tb_order_no = m.get('TbOrderNo')

        if m.get('TelephoneNumber') is not None:
            self.telephone_number = m.get('TelephoneNumber')

        return self

