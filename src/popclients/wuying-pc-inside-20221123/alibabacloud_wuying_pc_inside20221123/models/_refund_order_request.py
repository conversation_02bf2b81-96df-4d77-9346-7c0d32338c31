# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class RefundOrderRequest(DaraModel):
    def __init__(
        self,
        operation_id: str = None,
        order_id: str = None,
    ):
        # This parameter is required.
        self.operation_id = operation_id
        # This parameter is required.
        self.order_id = order_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.operation_id is not None:
            result['OperationId'] = self.operation_id

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('OperationId') is not None:
            self.operation_id = m.get('OperationId')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        return self

