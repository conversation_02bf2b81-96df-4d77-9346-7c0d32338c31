# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class GetChannelTransferDesktopsRequest(DaraModel):
    def __init__(
        self,
        amount: int = None,
        city_name: str = None,
        desktop_name: str = None,
        end_user_id: str = None,
        order_id: str = None,
        personal_aliyun_uid: str = None,
        team_id: str = None,
        transfer_action: str = None,
    ):
        # This parameter is required.
        self.amount = amount
        self.city_name = city_name
        self.desktop_name = desktop_name
        self.end_user_id = end_user_id
        # This parameter is required.
        self.order_id = order_id
        self.personal_aliyun_uid = personal_aliyun_uid
        self.team_id = team_id
        # This parameter is required.
        self.transfer_action = transfer_action

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.amount is not None:
            result['Amount'] = self.amount

        if self.city_name is not None:
            result['CityName'] = self.city_name

        if self.desktop_name is not None:
            result['DesktopName'] = self.desktop_name

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.personal_aliyun_uid is not None:
            result['PersonalAliyunUid'] = self.personal_aliyun_uid

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        if self.transfer_action is not None:
            result['TransferAction'] = self.transfer_action

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Amount') is not None:
            self.amount = m.get('Amount')

        if m.get('CityName') is not None:
            self.city_name = m.get('CityName')

        if m.get('DesktopName') is not None:
            self.desktop_name = m.get('DesktopName')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('PersonalAliyunUid') is not None:
            self.personal_aliyun_uid = m.get('PersonalAliyunUid')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        if m.get('TransferAction') is not None:
            self.transfer_action = m.get('TransferAction')

        return self

