# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeChannelTransferDesktopsRequest(DaraModel):
    def __init__(
        self,
        create_user_id: str = None,
        desktop_status: int = None,
        desktop_type: str = None,
        team_id: str = None,
    ):
        self.create_user_id = create_user_id
        self.desktop_status = desktop_status
        self.desktop_type = desktop_type
        self.team_id = team_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.create_user_id is not None:
            result['CreateUserId'] = self.create_user_id

        if self.desktop_status is not None:
            result['DesktopStatus'] = self.desktop_status

        if self.desktop_type is not None:
            result['DesktopType'] = self.desktop_type

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CreateUserId') is not None:
            self.create_user_id = m.get('CreateUserId')

        if m.get('DesktopStatus') is not None:
            self.desktop_status = m.get('DesktopStatus')

        if m.get('DesktopType') is not None:
            self.desktop_type = m.get('DesktopType')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        return self

