# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations



from ._ack_redeem_code_verify_request import AckRedeemCodeVerifyRequest
from ._ack_redeem_code_verify_response_body import Ack<PERSON><PERSON>em<PERSON>odeVerifyResponseBody
from ._ack_redeem_code_verify_response import AckRedeemCodeVerifyResponse
from ._activate_resource_request import ActivateResourceRequest
from ._activate_resource_shrink_request import ActivateResourceShrinkRequest
from ._activate_resource_response_body import ActivateResourceResponseBody
from ._activate_resource_response import ActivateResourceResponse
from ._active_channel_redeem_code_request import Active<PERSON>hannel<PERSON>edeemCodeRequest
from ._active_channel_redeem_code_response_body import ActiveChannelRedeemCodeResponseBody
from ._active_channel_redeem_code_response import ActiveChannelRedeemCodeResponse
from ._active_redeem_coupon_request import ActiveRedeemCouponRequest
from ._active_redeem_coupon_response_body import <PERSON><PERSON><PERSON>em<PERSON>ouponResponseBody
from ._active_redeem_coupon_response import <PERSON>RedeemCouponResponse
from ._add_channel_user_request import AddChannelUserRequest
from ._add_channel_user_response_body import AddChannelUserResponseBody
from ._add_channel_user_response import AddChannelUserResponse
from ._add_collection_log_request import AddCollectionLogRequest
from ._add_collection_log_response_body import AddCollectionLogResponseBody
from ._add_collection_log_response import AddCollectionLogResponse
from ._add_core_hour_request import AddCoreHourRequest
from ._add_core_hour_response_body import AddCoreHourResponseBody
from ._add_core_hour_response import AddCoreHourResponse
from ._add_core_hour_instance_request import AddCoreHourInstanceRequest
from ._add_core_hour_instance_response_body import AddCoreHourInstanceResponseBody
from ._add_core_hour_instance_response import AddCoreHourInstanceResponse
from ._add_tag_info_request import AddTagInfoRequest
from ._add_tag_info_response_body import AddTagInfoResponseBody
from ._add_tag_info_response import AddTagInfoResponse
from ._add_user_tag_request import AddUserTagRequest
from ._add_user_tag_response_body import AddUserTagResponseBody
from ._add_user_tag_response import AddUserTagResponse
from ._batch_grant_device_coupon_inner_request import BatchGrantDeviceCouponInnerRequest
from ._batch_grant_device_coupon_inner_response_body import BatchGrantDeviceCouponInnerResponseBody
from ._batch_grant_device_coupon_inner_response import BatchGrantDeviceCouponInnerResponse
from ._batch_grant_user_coupon_request import BatchGrantUserCouponRequest
from ._batch_grant_user_coupon_response_body import BatchGrantUserCouponResponseBody
from ._batch_grant_user_coupon_response import BatchGrantUserCouponResponse
from ._bind_personal_hardware_terminal_benefit_request import BindPersonalHardwareTerminalBenefitRequest
from ._bind_personal_hardware_terminal_benefit_shrink_request import BindPersonalHardwareTerminalBenefitShrinkRequest
from ._bind_personal_hardware_terminal_benefit_response_body import BindPersonalHardwareTerminalBenefitResponseBody
from ._bind_personal_hardware_terminal_benefit_response import BindPersonalHardwareTerminalBenefitResponse
from ._cancel_business_rights_request import CancelBusinessRightsRequest
from ._cancel_business_rights_response_body import CancelBusinessRightsResponseBody
from ._cancel_business_rights_response import CancelBusinessRightsResponse
from ._cancel_package_order_request import CancelPackageOrderRequest
from ._cancel_package_order_response_body import CancelPackageOrderResponseBody
from ._cancel_package_order_response import CancelPackageOrderResponse
from ._check_coupon_code_rule_inner_request import CheckCouponCodeRuleInnerRequest
from ._check_coupon_code_rule_inner_response_body import CheckCouponCodeRuleInnerResponseBody
from ._check_coupon_code_rule_inner_response import CheckCouponCodeRuleInnerResponse
from ._check_personal_activity_user_request import CheckPersonalActivityUserRequest
from ._check_personal_activity_user_response_body import CheckPersonalActivityUserResponseBody
from ._check_personal_activity_user_response import CheckPersonalActivityUserResponse
from ._check_quota_request import CheckQuotaRequest
from ._check_quota_response_body import CheckQuotaResponseBody
from ._check_quota_response import CheckQuotaResponse
from ._check_subscribe_first_discount_inner_request import CheckSubscribeFirstDiscountInnerRequest
from ._check_subscribe_first_discount_inner_shrink_request import CheckSubscribeFirstDiscountInnerShrinkRequest
from ._check_subscribe_first_discount_inner_response_body import CheckSubscribeFirstDiscountInnerResponseBody
from ._check_subscribe_first_discount_inner_response import CheckSubscribeFirstDiscountInnerResponse
from ._check_user_contain_tag_request import CheckUserContainTagRequest
from ._check_user_contain_tag_response_body import CheckUserContainTagResponseBody
from ._check_user_contain_tag_response import CheckUserContainTagResponse
from ._check_user_has_desktop_or_package_request import CheckUserHasDesktopOrPackageRequest
from ._check_user_has_desktop_or_package_response_body import CheckUserHasDesktopOrPackageResponseBody
from ._check_user_has_desktop_or_package_response import CheckUserHasDesktopOrPackageResponse
from ._consume_pool_benefit_request import ConsumePoolBenefitRequest
from ._consume_pool_benefit_response_body import ConsumePoolBenefitResponseBody
from ._consume_pool_benefit_response import ConsumePoolBenefitResponse
from ._count_desktop_group_by_bundle_id_and_sku_code_request import CountDesktopGroupByBundleIdAndSkuCodeRequest
from ._count_desktop_group_by_bundle_id_and_sku_code_response_body import CountDesktopGroupByBundleIdAndSkuCodeResponseBody
from ._count_desktop_group_by_bundle_id_and_sku_code_response import CountDesktopGroupByBundleIdAndSkuCodeResponse
from ._count_user_desktop_and_package_request import CountUserDesktopAndPackageRequest
from ._count_user_desktop_and_package_response_body import CountUserDesktopAndPackageResponseBody
from ._count_user_desktop_and_package_response import CountUserDesktopAndPackageResponse
from ._create_activity_request import CreateActivityRequest
from ._create_activity_response_body import CreateActivityResponseBody
from ._create_activity_response import CreateActivityResponse
from ._create_activity_product_config_request import CreateActivityProductConfigRequest
from ._create_activity_product_config_shrink_request import CreateActivityProductConfigShrinkRequest
from ._create_activity_product_config_response_body import CreateActivityProductConfigResponseBody
from ._create_activity_product_config_response import CreateActivityProductConfigResponse
from ._create_and_activate_resource_request import CreateAndActivateResourceRequest
from ._create_and_activate_resource_shrink_request import CreateAndActivateResourceShrinkRequest
from ._create_and_activate_resource_response_body import CreateAndActivateResourceResponseBody
from ._create_and_activate_resource_response import CreateAndActivateResourceResponse
from ._create_benefit_request import CreateBenefitRequest
from ._create_benefit_shrink_request import CreateBenefitShrinkRequest
from ._create_benefit_response_body import CreateBenefitResponseBody
from ._create_benefit_response import CreateBenefitResponse
from ._create_benefit_pool_request import CreateBenefitPoolRequest
from ._create_benefit_pool_response_body import CreateBenefitPoolResponseBody
from ._create_benefit_pool_response import CreateBenefitPoolResponse
from ._create_benefit_pool_by_order_request import CreateBenefitPoolByOrderRequest
from ._create_benefit_pool_by_order_response_body import CreateBenefitPoolByOrderResponseBody
from ._create_benefit_pool_by_order_response import CreateBenefitPoolByOrderResponse
from ._create_benefit_team_transfer_request import CreateBenefitTeamTransferRequest
from ._create_benefit_team_transfer_response_body import CreateBenefitTeamTransferResponseBody
from ._create_benefit_team_transfer_response import CreateBenefitTeamTransferResponse
from ._create_business_apply_request import CreateBusinessApplyRequest
from ._create_business_apply_shrink_request import CreateBusinessApplyShrinkRequest
from ._create_business_apply_response_body import CreateBusinessApplyResponseBody
from ._create_business_apply_response import CreateBusinessApplyResponse
from ._create_cloud_app_inner_request import CreateCloudAppInnerRequest
from ._create_cloud_app_inner_response_body import CreateCloudAppInnerResponseBody
from ._create_cloud_app_inner_response import CreateCloudAppInnerResponse
from ._create_coupon_code_rule_inner_request import CreateCouponCodeRuleInnerRequest
from ._create_coupon_code_rule_inner_response_body import CreateCouponCodeRuleInnerResponseBody
from ._create_coupon_code_rule_inner_response import CreateCouponCodeRuleInnerResponse
from ._create_customer_activity_request import CreateCustomerActivityRequest
from ._create_customer_activity_response_body import CreateCustomerActivityResponseBody
from ._create_customer_activity_response import CreateCustomerActivityResponse
from ._create_desktop_request import CreateDesktopRequest
from ._create_desktop_response_body import CreateDesktopResponseBody
from ._create_desktop_response import CreateDesktopResponse
from ._create_desktop_cache_image_request import CreateDesktopCacheImageRequest
from ._create_desktop_cache_image_response_body import CreateDesktopCacheImageResponseBody
from ._create_desktop_cache_image_response import CreateDesktopCacheImageResponse
from ._create_desktop_install_app_request import CreateDesktopInstallAppRequest
from ._create_desktop_install_app_response_body import CreateDesktopInstallAppResponseBody
from ._create_desktop_install_app_response import CreateDesktopInstallAppResponse
from ._create_hardware_coupons_request import CreateHardwareCouponsRequest
from ._create_hardware_coupons_response_body import CreateHardwareCouponsResponseBody
from ._create_hardware_coupons_response import CreateHardwareCouponsResponse
from ._create_hardware_info_request import CreateHardwareInfoRequest
from ._create_hardware_info_response_body import CreateHardwareInfoResponseBody
from ._create_hardware_info_response import CreateHardwareInfoResponse
from ._create_operation_scene_request import CreateOperationSceneRequest
from ._create_operation_scene_response_body import CreateOperationSceneResponseBody
from ._create_operation_scene_response import CreateOperationSceneResponse
from ._create_order_request import CreateOrderRequest
from ._create_order_response_body import CreateOrderResponseBody
from ._create_order_response import CreateOrderResponse
from ._create_package_order_request import CreatePackageOrderRequest
from ._create_package_order_shrink_request import CreatePackageOrderShrinkRequest
from ._create_package_order_response_body import CreatePackageOrderResponseBody
from ._create_package_order_response import CreatePackageOrderResponse
from ._create_personal_users_coupon_request import CreatePersonalUsersCouponRequest
from ._create_personal_users_coupon_response_body import CreatePersonalUsersCouponResponseBody
from ._create_personal_users_coupon_response import CreatePersonalUsersCouponResponse
from ._create_redeem_codes_request import CreateRedeemCodesRequest
from ._create_redeem_codes_response_body import CreateRedeemCodesResponseBody
from ._create_redeem_codes_response import CreateRedeemCodesResponse
from ._create_resource_request import CreateResourceRequest
from ._create_resource_shrink_request import CreateResourceShrinkRequest
from ._create_resource_response_body import CreateResourceResponseBody
from ._create_resource_response import CreateResourceResponse
from ._create_resource_transfer_request import CreateResourceTransferRequest
from ._create_resource_transfer_response_body import CreateResourceTransferResponseBody
from ._create_resource_transfer_response import CreateResourceTransferResponse
from ._create_standard_order_request import CreateStandardOrderRequest
from ._create_standard_order_response_body import CreateStandardOrderResponseBody
from ._create_standard_order_response import CreateStandardOrderResponse
from ._create_user_apply_coupons_request import CreateUserApplyCouponsRequest
from ._create_user_apply_coupons_response_body import CreateUserApplyCouponsResponseBody
from ._create_user_apply_coupons_response import CreateUserApplyCouponsResponse
from ._create_user_apply_info_request import CreateUserApplyInfoRequest
from ._create_user_apply_info_response_body import CreateUserApplyInfoResponseBody
from ._create_user_apply_info_response import CreateUserApplyInfoResponse
from ._create_user_desktop_install_request import CreateUserDesktopInstallRequest
from ._create_user_desktop_install_response_body import CreateUserDesktopInstallResponseBody
from ._create_user_desktop_install_response import CreateUserDesktopInstallResponse
from ._delete_activity_product_config_request import DeleteActivityProductConfigRequest
from ._delete_activity_product_config_shrink_request import DeleteActivityProductConfigShrinkRequest
from ._delete_activity_product_config_response_body import DeleteActivityProductConfigResponseBody
from ._delete_activity_product_config_response import DeleteActivityProductConfigResponse
from ._delete_desktop_from_dbrequest import DeleteDesktopFromDBRequest
from ._delete_desktop_from_dbresponse_body import DeleteDesktopFromDBResponseBody
from ._delete_desktop_from_dbresponse import DeleteDesktopFromDBResponse
from ._delete_user_tag_request import DeleteUserTagRequest
from ._delete_user_tag_response_body import DeleteUserTagResponseBody
from ._delete_user_tag_response import DeleteUserTagResponse
from ._describe_activity_info_by_code_request import DescribeActivityInfoByCodeRequest
from ._describe_activity_info_by_code_response_body import DescribeActivityInfoByCodeResponseBody
from ._describe_activity_info_by_code_response import DescribeActivityInfoByCodeResponse
from ._describe_activity_inner_request import DescribeActivityInnerRequest
from ._describe_activity_inner_response_body import DescribeActivityInnerResponseBody
from ._describe_activity_inner_response import DescribeActivityInnerResponse
from ._describe_activity_product_config_list_request import DescribeActivityProductConfigListRequest
from ._describe_activity_product_config_list_shrink_request import DescribeActivityProductConfigListShrinkRequest
from ._describe_activity_product_config_list_response_body import DescribeActivityProductConfigListResponseBody
from ._describe_activity_product_config_list_response import DescribeActivityProductConfigListResponse
from ._describe_alarm_notify_desktop_ids_request import DescribeAlarmNotifyDesktopIdsRequest
from ._describe_alarm_notify_desktop_ids_response_body import DescribeAlarmNotifyDesktopIdsResponseBody
from ._describe_alarm_notify_desktop_ids_response import DescribeAlarmNotifyDesktopIdsResponse
from ._describe_benefit_pool_request import DescribeBenefitPoolRequest
from ._describe_benefit_pool_response_body import DescribeBenefitPoolResponseBody
from ._describe_benefit_pool_response import DescribeBenefitPoolResponse
from ._describe_benefit_pool_by_channel_orders_request import DescribeBenefitPoolByChannelOrdersRequest
from ._describe_benefit_pool_by_channel_orders_response_body import DescribeBenefitPoolByChannelOrdersResponseBody
from ._describe_benefit_pool_by_channel_orders_response import DescribeBenefitPoolByChannelOrdersResponse
from ._describe_bundle_request import DescribeBundleRequest
from ._describe_bundle_response_body import DescribeBundleResponseBody
from ._describe_bundle_response import DescribeBundleResponse
from ._describe_business_rights_request import DescribeBusinessRightsRequest
from ._describe_business_rights_shrink_request import DescribeBusinessRightsShrinkRequest
from ._describe_business_rights_response_body import DescribeBusinessRightsResponseBody
from ._describe_business_rights_response import DescribeBusinessRightsResponse
from ._describe_channel_id_by_filters_request import DescribeChannelIdByFiltersRequest
from ._describe_channel_id_by_filters_response_body import DescribeChannelIdByFiltersResponseBody
from ._describe_channel_id_by_filters_response import DescribeChannelIdByFiltersResponse
from ._describe_channel_info_by_uid_request import DescribeChannelInfoByUidRequest
from ._describe_channel_info_by_uid_response_body import DescribeChannelInfoByUidResponseBody
from ._describe_channel_info_by_uid_response import DescribeChannelInfoByUidResponse
from ._describe_channel_orders_request import DescribeChannelOrdersRequest
from ._describe_channel_orders_shrink_request import DescribeChannelOrdersShrinkRequest
from ._describe_channel_orders_response_body import DescribeChannelOrdersResponseBody
from ._describe_channel_orders_response import DescribeChannelOrdersResponse
from ._describe_channel_transfer_desktops_request import DescribeChannelTransferDesktopsRequest
from ._describe_channel_transfer_desktops_response_body import DescribeChannelTransferDesktopsResponseBody
from ._describe_channel_transfer_desktops_response import DescribeChannelTransferDesktopsResponse
from ._describe_cloud_app_infos_inner_request import DescribeCloudAppInfosInnerRequest
from ._describe_cloud_app_infos_inner_shrink_request import DescribeCloudAppInfosInnerShrinkRequest
from ._describe_cloud_app_infos_inner_response_body import DescribeCloudAppInfosInnerResponseBody
from ._describe_cloud_app_infos_inner_response import DescribeCloudAppInfosInnerResponse
from ._describe_commodities_request import DescribeCommoditiesRequest
from ._describe_commodities_response_body import DescribeCommoditiesResponseBody
from ._describe_commodities_response import DescribeCommoditiesResponse
from ._describe_commodity_code_info_request import DescribeCommodityCodeInfoRequest
from ._describe_commodity_code_info_response_body import DescribeCommodityCodeInfoResponseBody
from ._describe_commodity_code_info_response import DescribeCommodityCodeInfoResponse
from ._describe_coupon_operate_sum_request import DescribeCouponOperateSumRequest
from ._describe_coupon_operate_sum_response_body import DescribeCouponOperateSumResponseBody
from ._describe_coupon_operate_sum_response import DescribeCouponOperateSumResponse
from ._describe_customer_activity_config_by_key_request import DescribeCustomerActivityConfigByKeyRequest
from ._describe_customer_activity_config_by_key_response_body import DescribeCustomerActivityConfigByKeyResponseBody
from ._describe_customer_activity_config_by_key_response import DescribeCustomerActivityConfigByKeyResponse
from ._describe_customer_activity_inner_request import DescribeCustomerActivityInnerRequest
from ._describe_customer_activity_inner_response_body import DescribeCustomerActivityInnerResponseBody
from ._describe_customer_activity_inner_response import DescribeCustomerActivityInnerResponse
from ._describe_desktop_by_team_id_and_condition_request import DescribeDesktopByTeamIdAndConditionRequest
from ._describe_desktop_by_team_id_and_condition_response_body import DescribeDesktopByTeamIdAndConditionResponseBody
from ._describe_desktop_by_team_id_and_condition_response import DescribeDesktopByTeamIdAndConditionResponse
from ._describe_desktop_image_cache_request import DescribeDesktopImageCacheRequest
from ._describe_desktop_image_cache_response_body import DescribeDesktopImageCacheResponseBody
from ._describe_desktop_image_cache_response import DescribeDesktopImageCacheResponse
from ._describe_desktop_install_by_desktop_request import DescribeDesktopInstallByDesktopRequest
from ._describe_desktop_install_by_desktop_shrink_request import DescribeDesktopInstallByDesktopShrinkRequest
from ._describe_desktop_install_by_desktop_response_body import DescribeDesktopInstallByDesktopResponseBody
from ._describe_desktop_install_by_desktop_response import DescribeDesktopInstallByDesktopResponse
from ._describe_desktop_install_by_task_request import DescribeDesktopInstallByTaskRequest
from ._describe_desktop_install_by_task_shrink_request import DescribeDesktopInstallByTaskShrinkRequest
from ._describe_desktop_install_by_task_response_body import DescribeDesktopInstallByTaskResponseBody
from ._describe_desktop_install_by_task_response import DescribeDesktopInstallByTaskResponse
from ._describe_desktop_specs_request import DescribeDesktopSpecsRequest
from ._describe_desktop_specs_response_body import DescribeDesktopSpecsResponseBody
from ._describe_desktop_specs_response import DescribeDesktopSpecsResponse
from ._describe_desktops_request import DescribeDesktopsRequest
from ._describe_desktops_response_body import DescribeDesktopsResponseBody
from ._describe_desktops_response import DescribeDesktopsResponse
from ._describe_discount_eligibility_request import DescribeDiscountEligibilityRequest
from ._describe_discount_eligibility_response_body import DescribeDiscountEligibilityResponseBody
from ._describe_discount_eligibility_response import DescribeDiscountEligibilityResponse
from ._describe_dynamic_resource_list_request import DescribeDynamicResourceListRequest
from ._describe_dynamic_resource_list_shrink_request import DescribeDynamicResourceListShrinkRequest
from ._describe_dynamic_resource_list_response_body import DescribeDynamicResourceListResponseBody
from ._describe_dynamic_resource_list_response import DescribeDynamicResourceListResponse
from ._describe_event_data_summary_inner_request import DescribeEventDataSummaryInnerRequest
from ._describe_event_data_summary_inner_shrink_request import DescribeEventDataSummaryInnerShrinkRequest
from ._describe_event_data_summary_inner_response_body import DescribeEventDataSummaryInnerResponseBody
from ._describe_event_data_summary_inner_response import DescribeEventDataSummaryInnerResponse
from ._describe_init_channel_user_info_request import DescribeInitChannelUserInfoRequest
from ._describe_init_channel_user_info_response_body import DescribeInitChannelUserInfoResponseBody
from ._describe_init_channel_user_info_response import DescribeInitChannelUserInfoResponse
from ._describe_inner_desktop_detail_request import DescribeInnerDesktopDetailRequest
from ._describe_inner_desktop_detail_response_body import DescribeInnerDesktopDetailResponseBody
from ._describe_inner_desktop_detail_response import DescribeInnerDesktopDetailResponse
from ._describe_monthly_package_list_request import DescribeMonthlyPackageListRequest
from ._describe_monthly_package_list_shrink_request import DescribeMonthlyPackageListShrinkRequest
from ._describe_monthly_package_list_response_body import DescribeMonthlyPackageListResponseBody
from ._describe_monthly_package_list_response import DescribeMonthlyPackageListResponse
from ._describe_multiple_prices_request import DescribeMultiplePricesRequest
from ._describe_multiple_prices_shrink_request import DescribeMultiplePricesShrinkRequest
from ._describe_multiple_prices_response_body import DescribeMultiplePricesResponseBody
from ._describe_multiple_prices_response import DescribeMultiplePricesResponse
from ._describe_order_money_request import DescribeOrderMoneyRequest
from ._describe_order_money_response_body import DescribeOrderMoneyResponseBody
from ._describe_order_money_response import DescribeOrderMoneyResponse
from ._describe_orders_request import DescribeOrdersRequest
from ._describe_orders_response_body import DescribeOrdersResponseBody
from ._describe_orders_response import DescribeOrdersResponse
from ._describe_original_prices_request import DescribeOriginalPricesRequest
from ._describe_original_prices_shrink_request import DescribeOriginalPricesShrinkRequest
from ._describe_original_prices_response_body import DescribeOriginalPricesResponseBody
from ._describe_original_prices_response import DescribeOriginalPricesResponse
from ._describe_package_orders_request import DescribePackageOrdersRequest
from ._describe_package_orders_shrink_request import DescribePackageOrdersShrinkRequest
from ._describe_package_orders_response_body import DescribePackageOrdersResponseBody
from ._describe_package_orders_response import DescribePackageOrdersResponse
from ._describe_personal_activity_infos_request import DescribePersonalActivityInfosRequest
from ._describe_personal_activity_infos_response_body import DescribePersonalActivityInfosResponseBody
from ._describe_personal_activity_infos_response import DescribePersonalActivityInfosResponse
from ._describe_personal_code_relation_inner_request import DescribePersonalCodeRelationInnerRequest
from ._describe_personal_code_relation_inner_response_body import DescribePersonalCodeRelationInnerResponseBody
from ._describe_personal_code_relation_inner_response import DescribePersonalCodeRelationInnerResponse
from ._describe_personal_users_owned_benefits_inner_request import DescribePersonalUsersOwnedBenefitsInnerRequest
from ._describe_personal_users_owned_benefits_inner_response_body import DescribePersonalUsersOwnedBenefitsInnerResponseBody
from ._describe_personal_users_owned_benefits_inner_response import DescribePersonalUsersOwnedBenefitsInnerResponse
from ._describe_pick_coupon_user_list_request import DescribePickCouponUserListRequest
from ._describe_pick_coupon_user_list_response_body import DescribePickCouponUserListResponseBody
from ._describe_pick_coupon_user_list_response import DescribePickCouponUserListResponse
from ._describe_product_list_request import DescribeProductListRequest
from ._describe_product_list_shrink_request import DescribeProductListShrinkRequest
from ._describe_product_list_response_body import DescribeProductListResponseBody
from ._describe_product_list_response import DescribeProductListResponse
from ._describe_redeem_code_info_inner_request import DescribeRedeemCodeInfoInnerRequest
from ._describe_redeem_code_info_inner_response_body import DescribeRedeemCodeInfoInnerResponseBody
from ._describe_redeem_code_info_inner_response import DescribeRedeemCodeInfoInnerResponse
from ._describe_renew_product_list_request import DescribeRenewProductListRequest
from ._describe_renew_product_list_response_body import DescribeRenewProductListResponseBody
from ._describe_renew_product_list_response import DescribeRenewProductListResponse
from ._describe_route_area_request import DescribeRouteAreaRequest
from ._describe_route_area_response_body import DescribeRouteAreaResponseBody
from ._describe_route_area_response import DescribeRouteAreaResponse
from ._describe_route_area_city_info_request import DescribeRouteAreaCityInfoRequest
from ._describe_route_area_city_info_response_body import DescribeRouteAreaCityInfoResponseBody
from ._describe_route_area_city_info_response import DescribeRouteAreaCityInfoResponse
from ._describe_route_region_by_ip_request import DescribeRouteRegionByIpRequest
from ._describe_route_region_by_ip_response_body import DescribeRouteRegionByIpResponseBody
from ._describe_route_region_by_ip_response import DescribeRouteRegionByIpResponse
from ._describe_standard_orders_request import DescribeStandardOrdersRequest
from ._describe_standard_orders_response_body import DescribeStandardOrdersResponseBody
from ._describe_standard_orders_response import DescribeStandardOrdersResponse
from ._describe_standard_team_available_orders_request import DescribeStandardTeamAvailableOrdersRequest
from ._describe_standard_team_available_orders_response_body import DescribeStandardTeamAvailableOrdersResponseBody
from ._describe_standard_team_available_orders_response import DescribeStandardTeamAvailableOrdersResponse
from ._describe_subscribe_packages_inner_request import DescribeSubscribePackagesInnerRequest
from ._describe_subscribe_packages_inner_shrink_request import DescribeSubscribePackagesInnerShrinkRequest
from ._describe_subscribe_packages_inner_response_body import DescribeSubscribePackagesInnerResponseBody
from ._describe_subscribe_packages_inner_response import DescribeSubscribePackagesInnerResponse
from ._describe_tag_list_response_body import DescribeTagListResponseBody
from ._describe_tag_list_response import DescribeTagListResponse
from ._describe_tb_user_benefits_request import DescribeTbUserBenefitsRequest
from ._describe_tb_user_benefits_response_body import DescribeTbUserBenefitsResponseBody
from ._describe_tb_user_benefits_response import DescribeTbUserBenefitsResponse
from ._describe_team_info_by_code_request import DescribeTeamInfoByCodeRequest
from ._describe_team_info_by_code_response_body import DescribeTeamInfoByCodeResponseBody
from ._describe_team_info_by_code_response import DescribeTeamInfoByCodeResponse
from ._describe_team_info_by_ids_request import DescribeTeamInfoByIdsRequest
from ._describe_team_info_by_ids_response_body import DescribeTeamInfoByIdsResponseBody
from ._describe_team_info_by_ids_response import DescribeTeamInfoByIdsResponse
from ._describe_team_member_info_inner_request import DescribeTeamMemberInfoInnerRequest
from ._describe_team_member_info_inner_shrink_request import DescribeTeamMemberInfoInnerShrinkRequest
from ._describe_team_member_info_inner_response_body import DescribeTeamMemberInfoInnerResponseBody
from ._describe_team_member_info_inner_response import DescribeTeamMemberInfoInnerResponse
from ._describe_teams_by_role_inner_request import DescribeTeamsByRoleInnerRequest
from ._describe_teams_by_role_inner_response_body import DescribeTeamsByRoleInnerResponseBody
from ._describe_teams_by_role_inner_response import DescribeTeamsByRoleInnerResponse
from ._describe_terminal_serial_number_benefit_request import DescribeTerminalSerialNumberBenefitRequest
from ._describe_terminal_serial_number_benefit_response_body import DescribeTerminalSerialNumberBenefitResponseBody
from ._describe_terminal_serial_number_benefit_response import DescribeTerminalSerialNumberBenefitResponse
from ._describe_terminal_serial_number_by_token_request import DescribeTerminalSerialNumberByTokenRequest
from ._describe_terminal_serial_number_by_token_response_body import DescribeTerminalSerialNumberByTokenResponseBody
from ._describe_terminal_serial_number_by_token_response import DescribeTerminalSerialNumberByTokenResponse
from ._describe_user_coin_info_request import DescribeUserCoinInfoRequest
from ._describe_user_coin_info_shrink_request import DescribeUserCoinInfoShrinkRequest
from ._describe_user_coin_info_response_body import DescribeUserCoinInfoResponseBody
from ._describe_user_coin_info_response import DescribeUserCoinInfoResponse
from ._describe_user_coin_inner_request import DescribeUserCoinInnerRequest
from ._describe_user_coin_inner_response_body import DescribeUserCoinInnerResponseBody
from ._describe_user_coin_inner_response import DescribeUserCoinInnerResponse
from ._describe_user_desktop_ids_request import DescribeUserDesktopIdsRequest
from ._describe_user_desktop_ids_response_body import DescribeUserDesktopIdsResponseBody
from ._describe_user_desktop_ids_response import DescribeUserDesktopIdsResponse
from ._describe_user_desktop_info_request import DescribeUserDesktopInfoRequest
from ._describe_user_desktop_info_response_body import DescribeUserDesktopInfoResponseBody
from ._describe_user_desktop_info_response import DescribeUserDesktopInfoResponse
from ._describe_user_desktop_install_task_request import DescribeUserDesktopInstallTaskRequest
from ._describe_user_desktop_install_task_response_body import DescribeUserDesktopInstallTaskResponseBody
from ._describe_user_desktop_install_task_response import DescribeUserDesktopInstallTaskResponse
from ._describe_user_discount_inner_request import DescribeUserDiscountInnerRequest
from ._describe_user_discount_inner_response_body import DescribeUserDiscountInnerResponseBody
from ._describe_user_discount_inner_response import DescribeUserDiscountInnerResponse
from ._describe_user_package_orders_inner_request import DescribeUserPackageOrdersInnerRequest
from ._describe_user_package_orders_inner_shrink_request import DescribeUserPackageOrdersInnerShrinkRequest
from ._describe_user_package_orders_inner_response_body import DescribeUserPackageOrdersInnerResponseBody
from ._describe_user_package_orders_inner_response import DescribeUserPackageOrdersInnerResponse
from ._describe_user_personal_code_inner_request import DescribeUserPersonalCodeInnerRequest
from ._describe_user_personal_code_inner_response_body import DescribeUserPersonalCodeInnerResponseBody
from ._describe_user_personal_code_inner_response import DescribeUserPersonalCodeInnerResponse
from ._describe_user_properties_request import DescribeUserPropertiesRequest
from ._describe_user_properties_response_body import DescribeUserPropertiesResponseBody
from ._describe_user_properties_response import DescribeUserPropertiesResponse
from ._describe_user_purchase_coupons_inner_request import DescribeUserPurchaseCouponsInnerRequest
from ._describe_user_purchase_coupons_inner_response_body import DescribeUserPurchaseCouponsInnerResponseBody
from ._describe_user_purchase_coupons_inner_response import DescribeUserPurchaseCouponsInnerResponse
from ._diamond_publish_inner_request import DiamondPublishInnerRequest
from ._diamond_publish_inner_response_body import DiamondPublishInnerResponseBody
from ._diamond_publish_inner_response import DiamondPublishInnerResponse
from ._get_available_bundle_ids_request import GetAvailableBundleIdsRequest
from ._get_available_bundle_ids_response_body import GetAvailableBundleIdsResponseBody
from ._get_available_bundle_ids_response import GetAvailableBundleIdsResponse
from ._get_channel_general_agent_request import GetChannelGeneralAgentRequest
from ._get_channel_general_agent_response_body import GetChannelGeneralAgentResponseBody
from ._get_channel_general_agent_response import GetChannelGeneralAgentResponse
from ._get_channel_transfer_desktops_request import GetChannelTransferDesktopsRequest
from ._get_channel_transfer_desktops_response_body import GetChannelTransferDesktopsResponseBody
from ._get_channel_transfer_desktops_response import GetChannelTransferDesktopsResponse
from ._init_user_coin_request import InitUserCoinRequest
from ._init_user_coin_response_body import InitUserCoinResponseBody
from ._init_user_coin_response import InitUserCoinResponse
from ._inner_add_image_request import InnerAddImageRequest
from ._inner_add_image_response_body import InnerAddImageResponseBody
from ._inner_add_image_response import InnerAddImageResponse
from ._inner_analyze_user_coupon_code_request import InnerAnalyzeUserCouponCodeRequest
from ._inner_analyze_user_coupon_code_response_body import InnerAnalyzeUserCouponCodeResponseBody
from ._inner_analyze_user_coupon_code_response import InnerAnalyzeUserCouponCodeResponse
from ._inner_bind_image_code_request import InnerBindImageCodeRequest
from ._inner_bind_image_code_response_body import InnerBindImageCodeResponseBody
from ._inner_bind_image_code_response import InnerBindImageCodeResponse
from ._inner_create_image_request import InnerCreateImageRequest
from ._inner_create_image_response_body import InnerCreateImageResponseBody
from ._inner_create_image_response import InnerCreateImageResponse
from ._inner_describe_available_mode_list_response_body import InnerDescribeAvailableModeListResponseBody
from ._inner_describe_available_mode_list_response import InnerDescribeAvailableModeListResponse
from ._inner_describe_community_images_request import InnerDescribeCommunityImagesRequest
from ._inner_describe_community_images_shrink_request import InnerDescribeCommunityImagesShrinkRequest
from ._inner_describe_community_images_response_body import InnerDescribeCommunityImagesResponseBody
from ._inner_describe_community_images_response import InnerDescribeCommunityImagesResponse
from ._inner_describe_desktop_local_info_request import InnerDescribeDesktopLocalInfoRequest
from ._inner_describe_desktop_local_info_shrink_request import InnerDescribeDesktopLocalInfoShrinkRequest
from ._inner_describe_desktop_local_info_response_body import InnerDescribeDesktopLocalInfoResponseBody
from ._inner_describe_desktop_local_info_response import InnerDescribeDesktopLocalInfoResponse
from ._inner_describe_image_codes_page_request import InnerDescribeImageCodesPageRequest
from ._inner_describe_image_codes_page_shrink_request import InnerDescribeImageCodesPageShrinkRequest
from ._inner_describe_image_codes_page_response_body import InnerDescribeImageCodesPageResponseBody
from ._inner_describe_image_codes_page_response import InnerDescribeImageCodesPageResponse
from ._inner_describe_image_detail_request import InnerDescribeImageDetailRequest
from ._inner_describe_image_detail_response_body import InnerDescribeImageDetailResponseBody
from ._inner_describe_image_detail_response import InnerDescribeImageDetailResponse
from ._inner_describe_images_request import InnerDescribeImagesRequest
from ._inner_describe_images_shrink_request import InnerDescribeImagesShrinkRequest
from ._inner_describe_images_response_body import InnerDescribeImagesResponseBody
from ._inner_describe_images_response import InnerDescribeImagesResponse
from ._inner_describe_product_list_request import InnerDescribeProductListRequest
from ._inner_describe_product_list_shrink_request import InnerDescribeProductListShrinkRequest
from ._inner_describe_product_list_response_body import InnerDescribeProductListResponseBody
from ._inner_describe_product_list_response import InnerDescribeProductListResponse
from ._inner_describe_team_desktop_count_request import InnerDescribeTeamDesktopCountRequest
from ._inner_describe_team_desktop_count_response_body import InnerDescribeTeamDesktopCountResponseBody
from ._inner_describe_team_desktop_count_response import InnerDescribeTeamDesktopCountResponse
from ._inner_describe_team_desktops_page_request import InnerDescribeTeamDesktopsPageRequest
from ._inner_describe_team_desktops_page_shrink_request import InnerDescribeTeamDesktopsPageShrinkRequest
from ._inner_describe_team_desktops_page_response_body import InnerDescribeTeamDesktopsPageResponseBody
from ._inner_describe_team_desktops_page_response import InnerDescribeTeamDesktopsPageResponse
from ._inner_modify_desktop_image_request import InnerModifyDesktopImageRequest
from ._inner_modify_desktop_image_response_body import InnerModifyDesktopImageResponseBody
from ._inner_modify_desktop_image_response import InnerModifyDesktopImageResponse
from ._inner_update_image_request import InnerUpdateImageRequest
from ._inner_update_image_shrink_request import InnerUpdateImageShrinkRequest
from ._inner_update_image_response_body import InnerUpdateImageResponseBody
from ._inner_update_image_response import InnerUpdateImageResponse
from ._modify_activity_request import ModifyActivityRequest
from ._modify_activity_response_body import ModifyActivityResponseBody
from ._modify_activity_response import ModifyActivityResponse
from ._modify_activity_product_config_request import ModifyActivityProductConfigRequest
from ._modify_activity_product_config_shrink_request import ModifyActivityProductConfigShrinkRequest
from ._modify_activity_product_config_response_body import ModifyActivityProductConfigResponseBody
from ._modify_activity_product_config_response import ModifyActivityProductConfigResponse
from ._modify_customer_activity_request import ModifyCustomerActivityRequest
from ._modify_customer_activity_response_body import ModifyCustomerActivityResponseBody
from ._modify_customer_activity_response import ModifyCustomerActivityResponse
from ._modify_desktop_cache_image_request import ModifyDesktopCacheImageRequest
from ._modify_desktop_cache_image_response_body import ModifyDesktopCacheImageResponseBody
from ._modify_desktop_cache_image_response import ModifyDesktopCacheImageResponse
from ._modify_desktop_install_app_request import ModifyDesktopInstallAppRequest
from ._modify_desktop_install_app_response_body import ModifyDesktopInstallAppResponseBody
from ._modify_desktop_install_app_response import ModifyDesktopInstallAppResponse
from ._off_cloud_app_inner_request import OffCloudAppInnerRequest
from ._off_cloud_app_inner_response_body import OffCloudAppInnerResponseBody
from ._off_cloud_app_inner_response import OffCloudAppInnerResponse
from ._offline_customer_activity_request import OfflineCustomerActivityRequest
from ._offline_customer_activity_response_body import OfflineCustomerActivityResponseBody
from ._offline_customer_activity_response import OfflineCustomerActivityResponse
from ._online_benefit_pool_request import OnlineBenefitPoolRequest
from ._online_benefit_pool_response_body import OnlineBenefitPoolResponseBody
from ._online_benefit_pool_response import OnlineBenefitPoolResponse
from ._online_cloud_app_inner_request import OnlineCloudAppInnerRequest
from ._online_cloud_app_inner_response_body import OnlineCloudAppInnerResponseBody
from ._online_cloud_app_inner_response import OnlineCloudAppInnerResponse
from ._online_customer_activity_request import OnlineCustomerActivityRequest
from ._online_customer_activity_response_body import OnlineCustomerActivityResponseBody
from ._online_customer_activity_response import OnlineCustomerActivityResponse
from ._record_activate_channel_desktop_request import RecordActivateChannelDesktopRequest
from ._record_activate_channel_desktop_response_body import RecordActivateChannelDesktopResponseBody
from ._record_activate_channel_desktop_response import RecordActivateChannelDesktopResponse
from ._refund_order_request import RefundOrderRequest
from ._refund_order_response_body import RefundOrderResponseBody
from ._refund_order_response import RefundOrderResponse
from ._refund_taobao_order_request import RefundTaobaoOrderRequest
from ._refund_taobao_order_response_body import RefundTaobaoOrderResponseBody
from ._refund_taobao_order_response import RefundTaobaoOrderResponse
from ._rewrite_taobao_order_telephone_number_request import RewriteTaobaoOrderTelephoneNumberRequest
from ._rewrite_taobao_order_telephone_number_response_body import RewriteTaobaoOrderTelephoneNumberResponseBody
from ._rewrite_taobao_order_telephone_number_response import RewriteTaobaoOrderTelephoneNumberResponse
from ._rollback_channel_transfer_desktops_request import RollbackChannelTransferDesktopsRequest
from ._rollback_channel_transfer_desktops_response_body import RollbackChannelTransferDesktopsResponseBody
from ._rollback_channel_transfer_desktops_response import RollbackChannelTransferDesktopsResponse
from ._rollback_order_package_number_request import RollbackOrderPackageNumberRequest
from ._rollback_order_package_number_response_body import RollbackOrderPackageNumberResponseBody
from ._rollback_order_package_number_response import RollbackOrderPackageNumberResponse
from ._send_activity_launcher_message_request import SendActivityLauncherMessageRequest
from ._send_activity_launcher_message_response_body import SendActivityLauncherMessageResponseBody
from ._send_activity_launcher_message_response import SendActivityLauncherMessageResponse
from ._send_launcher_message_request import SendLauncherMessageRequest
from ._send_launcher_message_response_body import SendLauncherMessageResponseBody
from ._send_launcher_message_response import SendLauncherMessageResponse
from ._subscribe_withhold_inner_request import SubscribeWithholdInnerRequest
from ._subscribe_withhold_inner_response_body import SubscribeWithholdInnerResponseBody
from ._subscribe_withhold_inner_response import SubscribeWithholdInnerResponse
from ._supply_benefit_pool_request import SupplyBenefitPoolRequest
from ._supply_benefit_pool_response_body import SupplyBenefitPoolResponseBody
from ._supply_benefit_pool_response import SupplyBenefitPoolResponse
from ._transfer_desktops_request import TransferDesktopsRequest
from ._transfer_desktops_response_body import TransferDesktopsResponseBody
from ._transfer_desktops_response import TransferDesktopsResponse
from ._update_channel_user_type_request import UpdateChannelUserTypeRequest
from ._update_channel_user_type_response_body import UpdateChannelUserTypeResponseBody
from ._update_channel_user_type_response import UpdateChannelUserTypeResponse
from ._update_checkin_activity_config_inner_request import UpdateCheckinActivityConfigInnerRequest
from ._update_checkin_activity_config_inner_response_body import UpdateCheckinActivityConfigInnerResponseBody
from ._update_checkin_activity_config_inner_response import UpdateCheckinActivityConfigInnerResponse
from ._update_desktop_real_expire_time_request import UpdateDesktopRealExpireTimeRequest
from ._update_desktop_real_expire_time_shrink_request import UpdateDesktopRealExpireTimeShrinkRequest
from ._update_desktop_real_expire_time_response_body import UpdateDesktopRealExpireTimeResponseBody
from ._update_desktop_real_expire_time_response import UpdateDesktopRealExpireTimeResponse
from ._update_order_package_number_request import UpdateOrderPackageNumberRequest
from ._update_order_package_number_response_body import UpdateOrderPackageNumberResponseBody
from ._update_order_package_number_response import UpdateOrderPackageNumberResponse
from ._update_order_pay_status_request import UpdateOrderPayStatusRequest
from ._update_order_pay_status_response_body import UpdateOrderPayStatusResponseBody
from ._update_order_pay_status_response import UpdateOrderPayStatusResponse
from ._update_order_status_request import UpdateOrderStatusRequest
from ._update_order_status_response_body import UpdateOrderStatusResponseBody
from ._update_order_status_response import UpdateOrderStatusResponse
from ._update_terminal_soft_request import UpdateTerminalSoftRequest
from ._update_terminal_soft_response_body import UpdateTerminalSoftResponseBody
from ._update_terminal_soft_response import UpdateTerminalSoftResponse
from ._use_business_rights_request import UseBusinessRightsRequest
from ._use_business_rights_shrink_request import UseBusinessRightsShrinkRequest
from ._use_business_rights_response_body import UseBusinessRightsResponseBody
from ._use_business_rights_response import UseBusinessRightsResponse
from ._verify_hardware_coupon_request import VerifyHardwareCouponRequest
from ._verify_hardware_coupon_response_body import VerifyHardwareCouponResponseBody
from ._verify_hardware_coupon_response import VerifyHardwareCouponResponse
from ._verify_redeem_code_request import VerifyRedeemCodeRequest
from ._verify_redeem_code_response_body import VerifyRedeemCodeResponseBody
from ._verify_redeem_code_response import VerifyRedeemCodeResponse
from ._verify_user_apply_coupon_request import VerifyUserApplyCouponRequest
from ._verify_user_apply_coupon_response_body import VerifyUserApplyCouponResponseBody
from ._verify_user_apply_coupon_response import VerifyUserApplyCouponResponse
from ._activate_resource_response_body import ActivateResourceResponseBodyData
from ._active_channel_redeem_code_response_body import ActiveChannelRedeemCodeResponseBodyData
from ._add_channel_user_response_body import AddChannelUserResponseBodyData
from ._add_core_hour_instance_response_body import AddCoreHourInstanceResponseBodyData
from ._cancel_business_rights_response_body import CancelBusinessRightsResponseBodyData
from ._cancel_package_order_response_body import CancelPackageOrderResponseBodyData
from ._check_coupon_code_rule_inner_response_body import CheckCouponCodeRuleInnerResponseBodyData
from ._check_quota_response_body import CheckQuotaResponseBodyQuotaInfo
from ._check_subscribe_first_discount_inner_request import CheckSubscribeFirstDiscountInnerRequestPackageBundlesJson
from ._check_subscribe_first_discount_inner_response_body import CheckSubscribeFirstDiscountInnerResponseBodyDataPackageBundles
from ._check_subscribe_first_discount_inner_response_body import CheckSubscribeFirstDiscountInnerResponseBodyData
from ._check_user_has_desktop_or_package_response_body import CheckUserHasDesktopOrPackageResponseBodyUserResourceOwnership
from ._count_desktop_group_by_bundle_id_and_sku_code_response_body import CountDesktopGroupByBundleIdAndSkuCodeResponseBodyData
from ._count_user_desktop_and_package_response_body import CountUserDesktopAndPackageResponseBodyResourceStatistics
from ._create_activity_product_config_request import CreateActivityProductConfigRequestActivityProductConfigInitResourceConfig
from ._create_activity_product_config_request import CreateActivityProductConfigRequestActivityProductConfig
from ._create_activity_product_config_response_body import CreateActivityProductConfigResponseBodyAddActivityProductConfigResponse
from ._create_and_activate_resource_response_body import CreateAndActivateResourceResponseBodyData
from ._create_benefit_response_body import CreateBenefitResponseBodyData
from ._create_benefit_pool_request import CreateBenefitPoolRequestApprovalParam
from ._create_benefit_pool_request import CreateBenefitPoolRequestBody
from ._create_benefit_pool_request import CreateBenefitPoolRequestChannelParam
from ._create_benefit_pool_response_body import CreateBenefitPoolResponseBodyData
from ._create_business_apply_response_body import CreateBusinessApplyResponseBodyDataBusinessRenewRightsDtoList
from ._create_business_apply_response_body import CreateBusinessApplyResponseBodyData
from ._create_coupon_code_rule_inner_response_body import CreateCouponCodeRuleInnerResponseBodyData
from ._create_customer_activity_response_body import CreateCustomerActivityResponseBodyData
from ._create_desktop_response_body import CreateDesktopResponseBodyDesktop
from ._create_desktop_cache_image_response_body import CreateDesktopCacheImageResponseBodyData
from ._create_hardware_coupons_response_body import CreateHardwareCouponsResponseBodyDataFailList
from ._create_hardware_coupons_response_body import CreateHardwareCouponsResponseBodyData
from ._create_order_response_body import CreateOrderResponseBodyData
from ._create_package_order_request import CreatePackageOrderRequestProductListJsonAutoActivateConfig
from ._create_package_order_request import CreatePackageOrderRequestProductListJson
from ._create_package_order_response_body import CreatePackageOrderResponseBodyDataOrderDetailList
from ._create_package_order_response_body import CreatePackageOrderResponseBodyData
from ._create_personal_users_coupon_response_body import CreatePersonalUsersCouponResponseBodyData
from ._create_redeem_codes_response_body import CreateRedeemCodesResponseBodyData
from ._create_resource_response_body import CreateResourceResponseBodyData
from ._create_standard_order_response_body import CreateStandardOrderResponseBodyData
from ._create_user_apply_coupons_response_body import CreateUserApplyCouponsResponseBodyData
from ._create_user_desktop_install_response_body import CreateUserDesktopInstallResponseBodyData
from ._delete_activity_product_config_response_body import DeleteActivityProductConfigResponseBodyDeleteActivityProductConfigResponse
from ._delete_desktop_from_dbresponse_body import DeleteDesktopFromDBResponseBodyData
from ._describe_activity_info_by_code_response_body import DescribeActivityInfoByCodeResponseBodyDataImageInfos
from ._describe_activity_info_by_code_response_body import DescribeActivityInfoByCodeResponseBodyData
from ._describe_activity_inner_response_body import DescribeActivityInnerResponseBodyData
from ._describe_activity_product_config_list_response_body import DescribeActivityProductConfigListResponseBodyDataActivityConfigInfoListInitResourceConfig
from ._describe_activity_product_config_list_response_body import DescribeActivityProductConfigListResponseBodyDataActivityConfigInfoList
from ._describe_activity_product_config_list_response_body import DescribeActivityProductConfigListResponseBodyData
from ._describe_alarm_notify_desktop_ids_response_body import DescribeAlarmNotifyDesktopIdsResponseBodyDataWillExpiringDesktopIds
from ._describe_alarm_notify_desktop_ids_response_body import DescribeAlarmNotifyDesktopIdsResponseBodyDataWillReleaseDesktopIds
from ._describe_alarm_notify_desktop_ids_response_body import DescribeAlarmNotifyDesktopIdsResponseBodyDataWillTimePackExhaustingDesktopIds
from ._describe_alarm_notify_desktop_ids_response_body import DescribeAlarmNotifyDesktopIdsResponseBodyData
from ._describe_benefit_pool_response_body import DescribeBenefitPoolResponseBodyDataBenefitInfos
from ._describe_benefit_pool_response_body import DescribeBenefitPoolResponseBodyData
from ._describe_benefit_pool_by_channel_orders_response_body import DescribeBenefitPoolByChannelOrdersResponseBodyDataBenefitInfos
from ._describe_benefit_pool_by_channel_orders_response_body import DescribeBenefitPoolByChannelOrdersResponseBodyData
from ._describe_bundle_response_body import DescribeBundleResponseBodyBundlesDesktopTypeAttribute
from ._describe_bundle_response_body import DescribeBundleResponseBodyBundlesDisks
from ._describe_bundle_response_body import DescribeBundleResponseBodyBundlesSkuItems
from ._describe_bundle_response_body import DescribeBundleResponseBodyBundles
from ._describe_business_rights_response_body import DescribeBusinessRightsResponseBodyDataPackageSku
from ._describe_business_rights_response_body import DescribeBusinessRightsResponseBodyDataProduction
from ._describe_business_rights_response_body import DescribeBusinessRightsResponseBodyData
from ._describe_channel_info_by_uid_response_body import DescribeChannelInfoByUidResponseBodyData
from ._describe_channel_orders_response_body import DescribeChannelOrdersResponseBodyData
from ._describe_channel_transfer_desktops_response_body import DescribeChannelTransferDesktopsResponseBodyDataDisks
from ._describe_channel_transfer_desktops_response_body import DescribeChannelTransferDesktopsResponseBodyData
from ._describe_cloud_app_infos_inner_response_body import DescribeCloudAppInfosInnerResponseBodyData
from ._describe_commodities_response_body import DescribeCommoditiesResponseBodyDataCommodityItem1
from ._describe_commodities_response_body import DescribeCommoditiesResponseBodyDataCommodityItem2
from ._describe_commodities_response_body import DescribeCommoditiesResponseBodyData
from ._describe_commodity_code_info_response_body import DescribeCommodityCodeInfoResponseBodyDataDescribeCommodityTypeDto
from ._describe_commodity_code_info_response_body import DescribeCommodityCodeInfoResponseBodyDataDescribePackageDto
from ._describe_commodity_code_info_response_body import DescribeCommodityCodeInfoResponseBodyDataDescribeSpecCodeDto
from ._describe_commodity_code_info_response_body import DescribeCommodityCodeInfoResponseBodyData
from ._describe_coupon_operate_sum_response_body import DescribeCouponOperateSumResponseBodyData
from ._describe_customer_activity_inner_response_body import DescribeCustomerActivityInnerResponseBodyDataBenefitInfo
from ._describe_customer_activity_inner_response_body import DescribeCustomerActivityInnerResponseBodyDataVerifyStepInfo
from ._describe_customer_activity_inner_response_body import DescribeCustomerActivityInnerResponseBodyData
from ._describe_desktop_by_team_id_and_condition_response_body import DescribeDesktopByTeamIdAndConditionResponseBodyDataRecords
from ._describe_desktop_by_team_id_and_condition_response_body import DescribeDesktopByTeamIdAndConditionResponseBodyData
from ._describe_desktop_image_cache_response_body import DescribeDesktopImageCacheResponseBodyData
from ._describe_desktop_install_by_desktop_response_body import DescribeDesktopInstallByDesktopResponseBodyData
from ._describe_desktop_install_by_task_response_body import DescribeDesktopInstallByTaskResponseBodyData
from ._describe_desktop_specs_response_body import DescribeDesktopSpecsResponseBodyDataDesktopTemplates
from ._describe_desktop_specs_response_body import DescribeDesktopSpecsResponseBodyData
from ._describe_desktops_response_body import DescribeDesktopsResponseBodyDataDisks
from ._describe_desktops_response_body import DescribeDesktopsResponseBodyData
from ._describe_discount_eligibility_response_body import DescribeDiscountEligibilityResponseBodyData
from ._describe_dynamic_resource_list_response_body import DescribeDynamicResourceListResponseBodyDataResourceList
from ._describe_dynamic_resource_list_response_body import DescribeDynamicResourceListResponseBodyData
from ._describe_event_data_summary_inner_response_body import DescribeEventDataSummaryInnerResponseBodyDataCodeSummaryInfos
from ._describe_event_data_summary_inner_response_body import DescribeEventDataSummaryInnerResponseBodyData
from ._describe_init_channel_user_info_response_body import DescribeInitChannelUserInfoResponseBodyData
from ._describe_inner_desktop_detail_response_body import DescribeInnerDesktopDetailResponseBodyDataDesktopSpec
from ._describe_inner_desktop_detail_response_body import DescribeInnerDesktopDetailResponseBodyDataDisks
from ._describe_inner_desktop_detail_response_body import DescribeInnerDesktopDetailResponseBodyDataPackageDuration
from ._describe_inner_desktop_detail_response_body import DescribeInnerDesktopDetailResponseBodyDataSessions
from ._describe_inner_desktop_detail_response_body import DescribeInnerDesktopDetailResponseBodyData
from ._describe_monthly_package_list_response_body import DescribeMonthlyPackageListResponseBodyMonthlyPackageListPackageBillingInfoList
from ._describe_monthly_package_list_response_body import DescribeMonthlyPackageListResponseBodyMonthlyPackageListRedeemCouponInfo
from ._describe_monthly_package_list_response_body import DescribeMonthlyPackageListResponseBodyMonthlyPackageList
from ._describe_multiple_prices_request import DescribeMultiplePricesRequestProductListJson
from ._describe_multiple_prices_response_body import DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListPrice
from ._describe_multiple_prices_response_body import DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProduct
from ._describe_multiple_prices_response_body import DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProductSku
from ._describe_multiple_prices_response_body import DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetail
from ._describe_multiple_prices_response_body import DescribeMultiplePricesResponseBodyPriceResponsePriceDetailList
from ._describe_multiple_prices_response_body import DescribeMultiplePricesResponseBodyPriceResponsePromotionList
from ._describe_multiple_prices_response_body import DescribeMultiplePricesResponseBodyPriceResponseRuleList
from ._describe_multiple_prices_response_body import DescribeMultiplePricesResponseBodyPriceResponseSummaryPrice
from ._describe_multiple_prices_response_body import DescribeMultiplePricesResponseBodyPriceResponse
from ._describe_order_money_response_body import DescribeOrderMoneyResponseBodyData
from ._describe_orders_response_body import DescribeOrdersResponseBodyData
from ._describe_original_prices_request import DescribeOriginalPricesRequestProductListJson
from ._describe_original_prices_response_body import DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListPrice
from ._describe_original_prices_response_body import DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProduct
from ._describe_original_prices_response_body import DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProductSku
from ._describe_original_prices_response_body import DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetail
from ._describe_original_prices_response_body import DescribeOriginalPricesResponseBodyPriceResponsePriceDetailList
from ._describe_original_prices_response_body import DescribeOriginalPricesResponseBodyPriceResponseSummaryPrice
from ._describe_original_prices_response_body import DescribeOriginalPricesResponseBodyPriceResponse
from ._describe_package_orders_response_body import DescribePackageOrdersResponseBodyPageOrderList
from ._describe_package_orders_response_body import DescribePackageOrdersResponseBodyPage
from ._describe_personal_activity_infos_response_body import DescribePersonalActivityInfosResponseBodyDataImageInfos
from ._describe_personal_activity_infos_response_body import DescribePersonalActivityInfosResponseBodyData
from ._describe_personal_code_relation_inner_response_body import DescribePersonalCodeRelationInnerResponseBodyData
from ._describe_personal_users_owned_benefits_inner_response_body import DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitListBenefitInfo
from ._describe_personal_users_owned_benefits_inner_response_body import DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitListCustomerActivityInfo
from ._describe_personal_users_owned_benefits_inner_response_body import DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitList
from ._describe_personal_users_owned_benefits_inner_response_body import DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataHistoryActivityList
from ._describe_personal_users_owned_benefits_inner_response_body import DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataHistoryBenefitList
from ._describe_personal_users_owned_benefits_inner_response_body import DescribePersonalUsersOwnedBenefitsInnerResponseBodyData
from ._describe_pick_coupon_user_list_response_body import DescribePickCouponUserListResponseBodyData
from ._describe_product_list_response_body import DescribeProductListResponseBodyProductListSkuList
from ._describe_product_list_response_body import DescribeProductListResponseBodyProductList
from ._describe_redeem_code_info_inner_response_body import DescribeRedeemCodeInfoInnerResponseBodyDataBenefitInfo
from ._describe_redeem_code_info_inner_response_body import DescribeRedeemCodeInfoInnerResponseBodyDataExtraCouponInfo
from ._describe_redeem_code_info_inner_response_body import DescribeRedeemCodeInfoInnerResponseBodyDataVerifyStepInfo
from ._describe_redeem_code_info_inner_response_body import DescribeRedeemCodeInfoInnerResponseBodyData
from ._describe_renew_product_list_response_body import DescribeRenewProductListResponseBodyData
from ._describe_route_area_city_info_response_body import DescribeRouteAreaCityInfoResponseBodyData
from ._describe_route_region_by_ip_response_body import DescribeRouteRegionByIpResponseBodyData
from ._describe_standard_orders_response_body import DescribeStandardOrdersResponseBodyDataDescribeOrdersLists
from ._describe_standard_orders_response_body import DescribeStandardOrdersResponseBodyData
from ._describe_standard_team_available_orders_response_body import DescribeStandardTeamAvailableOrdersResponseBodyData
from ._describe_subscribe_packages_inner_response_body import DescribeSubscribePackagesInnerResponseBodyDataInstanceInfos
from ._describe_subscribe_packages_inner_response_body import DescribeSubscribePackagesInnerResponseBodyData
from ._describe_tag_list_response_body import DescribeTagListResponseBodyData
from ._describe_tb_user_benefits_response_body import DescribeTbUserBenefitsResponseBodyData
from ._describe_team_info_by_code_response_body import DescribeTeamInfoByCodeResponseBodyData
from ._describe_team_info_by_ids_response_body import DescribeTeamInfoByIdsResponseBodyData
from ._describe_team_member_info_inner_response_body import DescribeTeamMemberInfoInnerResponseBodyData
from ._describe_teams_by_role_inner_response_body import DescribeTeamsByRoleInnerResponseBodyDataMemberInfo
from ._describe_teams_by_role_inner_response_body import DescribeTeamsByRoleInnerResponseBodyDataOwnerInfo
from ._describe_teams_by_role_inner_response_body import DescribeTeamsByRoleInnerResponseBodyDataPersonalInfo
from ._describe_teams_by_role_inner_response_body import DescribeTeamsByRoleInnerResponseBodyData
from ._describe_terminal_serial_number_benefit_response_body import DescribeTerminalSerialNumberBenefitResponseBodyData
from ._describe_terminal_serial_number_by_token_response_body import DescribeTerminalSerialNumberByTokenResponseBodyData
from ._describe_user_coin_info_response_body import DescribeUserCoinInfoResponseBodyData
from ._describe_user_desktop_info_response_body import DescribeUserDesktopInfoResponseBodyData
from ._describe_user_desktop_install_task_response_body import DescribeUserDesktopInstallTaskResponseBodyData
from ._describe_user_discount_inner_response_body import DescribeUserDiscountInnerResponseBodyData
from ._describe_user_package_orders_inner_response_body import DescribeUserPackageOrdersInnerResponseBodyData
from ._describe_user_personal_code_inner_response_body import DescribeUserPersonalCodeInnerResponseBodyDataCodeInfo
from ._describe_user_personal_code_inner_response_body import DescribeUserPersonalCodeInnerResponseBodyData
from ._describe_user_purchase_coupons_inner_response_body import DescribeUserPurchaseCouponsInnerResponseBodyDataCouponList
from ._describe_user_purchase_coupons_inner_response_body import DescribeUserPurchaseCouponsInnerResponseBodyData
from ._diamond_publish_inner_response_body import DiamondPublishInnerResponseBodyData
from ._get_channel_general_agent_response_body import GetChannelGeneralAgentResponseBodyData
from ._inner_analyze_user_coupon_code_response_body import InnerAnalyzeUserCouponCodeResponseBodyDataCodeInfo
from ._inner_analyze_user_coupon_code_response_body import InnerAnalyzeUserCouponCodeResponseBodyData
from ._inner_describe_available_mode_list_response_body import InnerDescribeAvailableModeListResponseBodyDesktopModeList
from ._inner_describe_community_images_response_body import InnerDescribeCommunityImagesResponseBodyData
from ._inner_describe_desktop_local_info_response_body import InnerDescribeDesktopLocalInfoResponseBodyData
from ._inner_describe_image_codes_page_response_body import InnerDescribeImageCodesPageResponseBodyDataImageCodeModels
from ._inner_describe_image_codes_page_response_body import InnerDescribeImageCodesPageResponseBodyData
from ._inner_describe_image_detail_response_body import InnerDescribeImageDetailResponseBodyData
from ._inner_describe_images_response_body import InnerDescribeImagesResponseBodyData
from ._inner_describe_product_list_response_body import InnerDescribeProductListResponseBodyProductListSkuList
from ._inner_describe_product_list_response_body import InnerDescribeProductListResponseBodyProductList
from ._inner_describe_team_desktop_count_response_body import InnerDescribeTeamDesktopCountResponseBodyData
from ._inner_describe_team_desktops_page_response_body import InnerDescribeTeamDesktopsPageResponseBodyDataDesktopsDurationList
from ._inner_describe_team_desktops_page_response_body import InnerDescribeTeamDesktopsPageResponseBodyDataDesktops
from ._inner_describe_team_desktops_page_response_body import InnerDescribeTeamDesktopsPageResponseBodyData
from ._modify_activity_product_config_request import ModifyActivityProductConfigRequestNewActivityConfigInfoInitResourceConfig
from ._modify_activity_product_config_request import ModifyActivityProductConfigRequestNewActivityConfigInfo
from ._modify_activity_product_config_response_body import ModifyActivityProductConfigResponseBodyUpdateActivityProductConfigResponse
from ._subscribe_withhold_inner_response_body import SubscribeWithholdInnerResponseBodyData
from ._verify_redeem_code_response_body import VerifyRedeemCodeResponseBodyData

__all__ = [
    AckRedeemCodeVerifyRequest,
    AckRedeemCodeVerifyResponseBody,
    AckRedeemCodeVerifyResponse,
    ActivateResourceRequest,
    ActivateResourceShrinkRequest,
    ActivateResourceResponseBody,
    ActivateResourceResponse,
    ActiveChannelRedeemCodeRequest,
    ActiveChannelRedeemCodeResponseBody,
    ActiveChannelRedeemCodeResponse,
    ActiveRedeemCouponRequest,
    ActiveRedeemCouponResponseBody,
    ActiveRedeemCouponResponse,
    AddChannelUserRequest,
    AddChannelUserResponseBody,
    AddChannelUserResponse,
    AddCollectionLogRequest,
    AddCollectionLogResponseBody,
    AddCollectionLogResponse,
    AddCoreHourRequest,
    AddCoreHourResponseBody,
    AddCoreHourResponse,
    AddCoreHourInstanceRequest,
    AddCoreHourInstanceResponseBody,
    AddCoreHourInstanceResponse,
    AddTagInfoRequest,
    AddTagInfoResponseBody,
    AddTagInfoResponse,
    AddUserTagRequest,
    AddUserTagResponseBody,
    AddUserTagResponse,
    BatchGrantDeviceCouponInnerRequest,
    BatchGrantDeviceCouponInnerResponseBody,
    BatchGrantDeviceCouponInnerResponse,
    BatchGrantUserCouponRequest,
    BatchGrantUserCouponResponseBody,
    BatchGrantUserCouponResponse,
    BindPersonalHardwareTerminalBenefitRequest,
    BindPersonalHardwareTerminalBenefitShrinkRequest,
    BindPersonalHardwareTerminalBenefitResponseBody,
    BindPersonalHardwareTerminalBenefitResponse,
    CancelBusinessRightsRequest,
    CancelBusinessRightsResponseBody,
    CancelBusinessRightsResponse,
    CancelPackageOrderRequest,
    CancelPackageOrderResponseBody,
    CancelPackageOrderResponse,
    CheckCouponCodeRuleInnerRequest,
    CheckCouponCodeRuleInnerResponseBody,
    CheckCouponCodeRuleInnerResponse,
    CheckPersonalActivityUserRequest,
    CheckPersonalActivityUserResponseBody,
    CheckPersonalActivityUserResponse,
    CheckQuotaRequest,
    CheckQuotaResponseBody,
    CheckQuotaResponse,
    CheckSubscribeFirstDiscountInnerRequest,
    CheckSubscribeFirstDiscountInnerShrinkRequest,
    CheckSubscribeFirstDiscountInnerResponseBody,
    CheckSubscribeFirstDiscountInnerResponse,
    CheckUserContainTagRequest,
    CheckUserContainTagResponseBody,
    CheckUserContainTagResponse,
    CheckUserHasDesktopOrPackageRequest,
    CheckUserHasDesktopOrPackageResponseBody,
    CheckUserHasDesktopOrPackageResponse,
    ConsumePoolBenefitRequest,
    ConsumePoolBenefitResponseBody,
    ConsumePoolBenefitResponse,
    CountDesktopGroupByBundleIdAndSkuCodeRequest,
    CountDesktopGroupByBundleIdAndSkuCodeResponseBody,
    CountDesktopGroupByBundleIdAndSkuCodeResponse,
    CountUserDesktopAndPackageRequest,
    CountUserDesktopAndPackageResponseBody,
    CountUserDesktopAndPackageResponse,
    CreateActivityRequest,
    CreateActivityResponseBody,
    CreateActivityResponse,
    CreateActivityProductConfigRequest,
    CreateActivityProductConfigShrinkRequest,
    CreateActivityProductConfigResponseBody,
    CreateActivityProductConfigResponse,
    CreateAndActivateResourceRequest,
    CreateAndActivateResourceShrinkRequest,
    CreateAndActivateResourceResponseBody,
    CreateAndActivateResourceResponse,
    CreateBenefitRequest,
    CreateBenefitShrinkRequest,
    CreateBenefitResponseBody,
    CreateBenefitResponse,
    CreateBenefitPoolRequest,
    CreateBenefitPoolResponseBody,
    CreateBenefitPoolResponse,
    CreateBenefitPoolByOrderRequest,
    CreateBenefitPoolByOrderResponseBody,
    CreateBenefitPoolByOrderResponse,
    CreateBenefitTeamTransferRequest,
    CreateBenefitTeamTransferResponseBody,
    CreateBenefitTeamTransferResponse,
    CreateBusinessApplyRequest,
    CreateBusinessApplyShrinkRequest,
    CreateBusinessApplyResponseBody,
    CreateBusinessApplyResponse,
    CreateCloudAppInnerRequest,
    CreateCloudAppInnerResponseBody,
    CreateCloudAppInnerResponse,
    CreateCouponCodeRuleInnerRequest,
    CreateCouponCodeRuleInnerResponseBody,
    CreateCouponCodeRuleInnerResponse,
    CreateCustomerActivityRequest,
    CreateCustomerActivityResponseBody,
    CreateCustomerActivityResponse,
    CreateDesktopRequest,
    CreateDesktopResponseBody,
    CreateDesktopResponse,
    CreateDesktopCacheImageRequest,
    CreateDesktopCacheImageResponseBody,
    CreateDesktopCacheImageResponse,
    CreateDesktopInstallAppRequest,
    CreateDesktopInstallAppResponseBody,
    CreateDesktopInstallAppResponse,
    CreateHardwareCouponsRequest,
    CreateHardwareCouponsResponseBody,
    CreateHardwareCouponsResponse,
    CreateHardwareInfoRequest,
    CreateHardwareInfoResponseBody,
    CreateHardwareInfoResponse,
    CreateOperationSceneRequest,
    CreateOperationSceneResponseBody,
    CreateOperationSceneResponse,
    CreateOrderRequest,
    CreateOrderResponseBody,
    CreateOrderResponse,
    CreatePackageOrderRequest,
    CreatePackageOrderShrinkRequest,
    CreatePackageOrderResponseBody,
    CreatePackageOrderResponse,
    CreatePersonalUsersCouponRequest,
    CreatePersonalUsersCouponResponseBody,
    CreatePersonalUsersCouponResponse,
    CreateRedeemCodesRequest,
    CreateRedeemCodesResponseBody,
    CreateRedeemCodesResponse,
    CreateResourceRequest,
    CreateResourceShrinkRequest,
    CreateResourceResponseBody,
    CreateResourceResponse,
    CreateResourceTransferRequest,
    CreateResourceTransferResponseBody,
    CreateResourceTransferResponse,
    CreateStandardOrderRequest,
    CreateStandardOrderResponseBody,
    CreateStandardOrderResponse,
    CreateUserApplyCouponsRequest,
    CreateUserApplyCouponsResponseBody,
    CreateUserApplyCouponsResponse,
    CreateUserApplyInfoRequest,
    CreateUserApplyInfoResponseBody,
    CreateUserApplyInfoResponse,
    CreateUserDesktopInstallRequest,
    CreateUserDesktopInstallResponseBody,
    CreateUserDesktopInstallResponse,
    DeleteActivityProductConfigRequest,
    DeleteActivityProductConfigShrinkRequest,
    DeleteActivityProductConfigResponseBody,
    DeleteActivityProductConfigResponse,
    DeleteDesktopFromDBRequest,
    DeleteDesktopFromDBResponseBody,
    DeleteDesktopFromDBResponse,
    DeleteUserTagRequest,
    DeleteUserTagResponseBody,
    DeleteUserTagResponse,
    DescribeActivityInfoByCodeRequest,
    DescribeActivityInfoByCodeResponseBody,
    DescribeActivityInfoByCodeResponse,
    DescribeActivityInnerRequest,
    DescribeActivityInnerResponseBody,
    DescribeActivityInnerResponse,
    DescribeActivityProductConfigListRequest,
    DescribeActivityProductConfigListShrinkRequest,
    DescribeActivityProductConfigListResponseBody,
    DescribeActivityProductConfigListResponse,
    DescribeAlarmNotifyDesktopIdsRequest,
    DescribeAlarmNotifyDesktopIdsResponseBody,
    DescribeAlarmNotifyDesktopIdsResponse,
    DescribeBenefitPoolRequest,
    DescribeBenefitPoolResponseBody,
    DescribeBenefitPoolResponse,
    DescribeBenefitPoolByChannelOrdersRequest,
    DescribeBenefitPoolByChannelOrdersResponseBody,
    DescribeBenefitPoolByChannelOrdersResponse,
    DescribeBundleRequest,
    DescribeBundleResponseBody,
    DescribeBundleResponse,
    DescribeBusinessRightsRequest,
    DescribeBusinessRightsShrinkRequest,
    DescribeBusinessRightsResponseBody,
    DescribeBusinessRightsResponse,
    DescribeChannelIdByFiltersRequest,
    DescribeChannelIdByFiltersResponseBody,
    DescribeChannelIdByFiltersResponse,
    DescribeChannelInfoByUidRequest,
    DescribeChannelInfoByUidResponseBody,
    DescribeChannelInfoByUidResponse,
    DescribeChannelOrdersRequest,
    DescribeChannelOrdersShrinkRequest,
    DescribeChannelOrdersResponseBody,
    DescribeChannelOrdersResponse,
    DescribeChannelTransferDesktopsRequest,
    DescribeChannelTransferDesktopsResponseBody,
    DescribeChannelTransferDesktopsResponse,
    DescribeCloudAppInfosInnerRequest,
    DescribeCloudAppInfosInnerShrinkRequest,
    DescribeCloudAppInfosInnerResponseBody,
    DescribeCloudAppInfosInnerResponse,
    DescribeCommoditiesRequest,
    DescribeCommoditiesResponseBody,
    DescribeCommoditiesResponse,
    DescribeCommodityCodeInfoRequest,
    DescribeCommodityCodeInfoResponseBody,
    DescribeCommodityCodeInfoResponse,
    DescribeCouponOperateSumRequest,
    DescribeCouponOperateSumResponseBody,
    DescribeCouponOperateSumResponse,
    DescribeCustomerActivityConfigByKeyRequest,
    DescribeCustomerActivityConfigByKeyResponseBody,
    DescribeCustomerActivityConfigByKeyResponse,
    DescribeCustomerActivityInnerRequest,
    DescribeCustomerActivityInnerResponseBody,
    DescribeCustomerActivityInnerResponse,
    DescribeDesktopByTeamIdAndConditionRequest,
    DescribeDesktopByTeamIdAndConditionResponseBody,
    DescribeDesktopByTeamIdAndConditionResponse,
    DescribeDesktopImageCacheRequest,
    DescribeDesktopImageCacheResponseBody,
    DescribeDesktopImageCacheResponse,
    DescribeDesktopInstallByDesktopRequest,
    DescribeDesktopInstallByDesktopShrinkRequest,
    DescribeDesktopInstallByDesktopResponseBody,
    DescribeDesktopInstallByDesktopResponse,
    DescribeDesktopInstallByTaskRequest,
    DescribeDesktopInstallByTaskShrinkRequest,
    DescribeDesktopInstallByTaskResponseBody,
    DescribeDesktopInstallByTaskResponse,
    DescribeDesktopSpecsRequest,
    DescribeDesktopSpecsResponseBody,
    DescribeDesktopSpecsResponse,
    DescribeDesktopsRequest,
    DescribeDesktopsResponseBody,
    DescribeDesktopsResponse,
    DescribeDiscountEligibilityRequest,
    DescribeDiscountEligibilityResponseBody,
    DescribeDiscountEligibilityResponse,
    DescribeDynamicResourceListRequest,
    DescribeDynamicResourceListShrinkRequest,
    DescribeDynamicResourceListResponseBody,
    DescribeDynamicResourceListResponse,
    DescribeEventDataSummaryInnerRequest,
    DescribeEventDataSummaryInnerShrinkRequest,
    DescribeEventDataSummaryInnerResponseBody,
    DescribeEventDataSummaryInnerResponse,
    DescribeInitChannelUserInfoRequest,
    DescribeInitChannelUserInfoResponseBody,
    DescribeInitChannelUserInfoResponse,
    DescribeInnerDesktopDetailRequest,
    DescribeInnerDesktopDetailResponseBody,
    DescribeInnerDesktopDetailResponse,
    DescribeMonthlyPackageListRequest,
    DescribeMonthlyPackageListShrinkRequest,
    DescribeMonthlyPackageListResponseBody,
    DescribeMonthlyPackageListResponse,
    DescribeMultiplePricesRequest,
    DescribeMultiplePricesShrinkRequest,
    DescribeMultiplePricesResponseBody,
    DescribeMultiplePricesResponse,
    DescribeOrderMoneyRequest,
    DescribeOrderMoneyResponseBody,
    DescribeOrderMoneyResponse,
    DescribeOrdersRequest,
    DescribeOrdersResponseBody,
    DescribeOrdersResponse,
    DescribeOriginalPricesRequest,
    DescribeOriginalPricesShrinkRequest,
    DescribeOriginalPricesResponseBody,
    DescribeOriginalPricesResponse,
    DescribePackageOrdersRequest,
    DescribePackageOrdersShrinkRequest,
    DescribePackageOrdersResponseBody,
    DescribePackageOrdersResponse,
    DescribePersonalActivityInfosRequest,
    DescribePersonalActivityInfosResponseBody,
    DescribePersonalActivityInfosResponse,
    DescribePersonalCodeRelationInnerRequest,
    DescribePersonalCodeRelationInnerResponseBody,
    DescribePersonalCodeRelationInnerResponse,
    DescribePersonalUsersOwnedBenefitsInnerRequest,
    DescribePersonalUsersOwnedBenefitsInnerResponseBody,
    DescribePersonalUsersOwnedBenefitsInnerResponse,
    DescribePickCouponUserListRequest,
    DescribePickCouponUserListResponseBody,
    DescribePickCouponUserListResponse,
    DescribeProductListRequest,
    DescribeProductListShrinkRequest,
    DescribeProductListResponseBody,
    DescribeProductListResponse,
    DescribeRedeemCodeInfoInnerRequest,
    DescribeRedeemCodeInfoInnerResponseBody,
    DescribeRedeemCodeInfoInnerResponse,
    DescribeRenewProductListRequest,
    DescribeRenewProductListResponseBody,
    DescribeRenewProductListResponse,
    DescribeRouteAreaRequest,
    DescribeRouteAreaResponseBody,
    DescribeRouteAreaResponse,
    DescribeRouteAreaCityInfoRequest,
    DescribeRouteAreaCityInfoResponseBody,
    DescribeRouteAreaCityInfoResponse,
    DescribeRouteRegionByIpRequest,
    DescribeRouteRegionByIpResponseBody,
    DescribeRouteRegionByIpResponse,
    DescribeStandardOrdersRequest,
    DescribeStandardOrdersResponseBody,
    DescribeStandardOrdersResponse,
    DescribeStandardTeamAvailableOrdersRequest,
    DescribeStandardTeamAvailableOrdersResponseBody,
    DescribeStandardTeamAvailableOrdersResponse,
    DescribeSubscribePackagesInnerRequest,
    DescribeSubscribePackagesInnerShrinkRequest,
    DescribeSubscribePackagesInnerResponseBody,
    DescribeSubscribePackagesInnerResponse,
    DescribeTagListResponseBody,
    DescribeTagListResponse,
    DescribeTbUserBenefitsRequest,
    DescribeTbUserBenefitsResponseBody,
    DescribeTbUserBenefitsResponse,
    DescribeTeamInfoByCodeRequest,
    DescribeTeamInfoByCodeResponseBody,
    DescribeTeamInfoByCodeResponse,
    DescribeTeamInfoByIdsRequest,
    DescribeTeamInfoByIdsResponseBody,
    DescribeTeamInfoByIdsResponse,
    DescribeTeamMemberInfoInnerRequest,
    DescribeTeamMemberInfoInnerShrinkRequest,
    DescribeTeamMemberInfoInnerResponseBody,
    DescribeTeamMemberInfoInnerResponse,
    DescribeTeamsByRoleInnerRequest,
    DescribeTeamsByRoleInnerResponseBody,
    DescribeTeamsByRoleInnerResponse,
    DescribeTerminalSerialNumberBenefitRequest,
    DescribeTerminalSerialNumberBenefitResponseBody,
    DescribeTerminalSerialNumberBenefitResponse,
    DescribeTerminalSerialNumberByTokenRequest,
    DescribeTerminalSerialNumberByTokenResponseBody,
    DescribeTerminalSerialNumberByTokenResponse,
    DescribeUserCoinInfoRequest,
    DescribeUserCoinInfoShrinkRequest,
    DescribeUserCoinInfoResponseBody,
    DescribeUserCoinInfoResponse,
    DescribeUserCoinInnerRequest,
    DescribeUserCoinInnerResponseBody,
    DescribeUserCoinInnerResponse,
    DescribeUserDesktopIdsRequest,
    DescribeUserDesktopIdsResponseBody,
    DescribeUserDesktopIdsResponse,
    DescribeUserDesktopInfoRequest,
    DescribeUserDesktopInfoResponseBody,
    DescribeUserDesktopInfoResponse,
    DescribeUserDesktopInstallTaskRequest,
    DescribeUserDesktopInstallTaskResponseBody,
    DescribeUserDesktopInstallTaskResponse,
    DescribeUserDiscountInnerRequest,
    DescribeUserDiscountInnerResponseBody,
    DescribeUserDiscountInnerResponse,
    DescribeUserPackageOrdersInnerRequest,
    DescribeUserPackageOrdersInnerShrinkRequest,
    DescribeUserPackageOrdersInnerResponseBody,
    DescribeUserPackageOrdersInnerResponse,
    DescribeUserPersonalCodeInnerRequest,
    DescribeUserPersonalCodeInnerResponseBody,
    DescribeUserPersonalCodeInnerResponse,
    DescribeUserPropertiesRequest,
    DescribeUserPropertiesResponseBody,
    DescribeUserPropertiesResponse,
    DescribeUserPurchaseCouponsInnerRequest,
    DescribeUserPurchaseCouponsInnerResponseBody,
    DescribeUserPurchaseCouponsInnerResponse,
    DiamondPublishInnerRequest,
    DiamondPublishInnerResponseBody,
    DiamondPublishInnerResponse,
    GetAvailableBundleIdsRequest,
    GetAvailableBundleIdsResponseBody,
    GetAvailableBundleIdsResponse,
    GetChannelGeneralAgentRequest,
    GetChannelGeneralAgentResponseBody,
    GetChannelGeneralAgentResponse,
    GetChannelTransferDesktopsRequest,
    GetChannelTransferDesktopsResponseBody,
    GetChannelTransferDesktopsResponse,
    InitUserCoinRequest,
    InitUserCoinResponseBody,
    InitUserCoinResponse,
    InnerAddImageRequest,
    InnerAddImageResponseBody,
    InnerAddImageResponse,
    InnerAnalyzeUserCouponCodeRequest,
    InnerAnalyzeUserCouponCodeResponseBody,
    InnerAnalyzeUserCouponCodeResponse,
    InnerBindImageCodeRequest,
    InnerBindImageCodeResponseBody,
    InnerBindImageCodeResponse,
    InnerCreateImageRequest,
    InnerCreateImageResponseBody,
    InnerCreateImageResponse,
    InnerDescribeAvailableModeListResponseBody,
    InnerDescribeAvailableModeListResponse,
    InnerDescribeCommunityImagesRequest,
    InnerDescribeCommunityImagesShrinkRequest,
    InnerDescribeCommunityImagesResponseBody,
    InnerDescribeCommunityImagesResponse,
    InnerDescribeDesktopLocalInfoRequest,
    InnerDescribeDesktopLocalInfoShrinkRequest,
    InnerDescribeDesktopLocalInfoResponseBody,
    InnerDescribeDesktopLocalInfoResponse,
    InnerDescribeImageCodesPageRequest,
    InnerDescribeImageCodesPageShrinkRequest,
    InnerDescribeImageCodesPageResponseBody,
    InnerDescribeImageCodesPageResponse,
    InnerDescribeImageDetailRequest,
    InnerDescribeImageDetailResponseBody,
    InnerDescribeImageDetailResponse,
    InnerDescribeImagesRequest,
    InnerDescribeImagesShrinkRequest,
    InnerDescribeImagesResponseBody,
    InnerDescribeImagesResponse,
    InnerDescribeProductListRequest,
    InnerDescribeProductListShrinkRequest,
    InnerDescribeProductListResponseBody,
    InnerDescribeProductListResponse,
    InnerDescribeTeamDesktopCountRequest,
    InnerDescribeTeamDesktopCountResponseBody,
    InnerDescribeTeamDesktopCountResponse,
    InnerDescribeTeamDesktopsPageRequest,
    InnerDescribeTeamDesktopsPageShrinkRequest,
    InnerDescribeTeamDesktopsPageResponseBody,
    InnerDescribeTeamDesktopsPageResponse,
    InnerModifyDesktopImageRequest,
    InnerModifyDesktopImageResponseBody,
    InnerModifyDesktopImageResponse,
    InnerUpdateImageRequest,
    InnerUpdateImageShrinkRequest,
    InnerUpdateImageResponseBody,
    InnerUpdateImageResponse,
    ModifyActivityRequest,
    ModifyActivityResponseBody,
    ModifyActivityResponse,
    ModifyActivityProductConfigRequest,
    ModifyActivityProductConfigShrinkRequest,
    ModifyActivityProductConfigResponseBody,
    ModifyActivityProductConfigResponse,
    ModifyCustomerActivityRequest,
    ModifyCustomerActivityResponseBody,
    ModifyCustomerActivityResponse,
    ModifyDesktopCacheImageRequest,
    ModifyDesktopCacheImageResponseBody,
    ModifyDesktopCacheImageResponse,
    ModifyDesktopInstallAppRequest,
    ModifyDesktopInstallAppResponseBody,
    ModifyDesktopInstallAppResponse,
    OffCloudAppInnerRequest,
    OffCloudAppInnerResponseBody,
    OffCloudAppInnerResponse,
    OfflineCustomerActivityRequest,
    OfflineCustomerActivityResponseBody,
    OfflineCustomerActivityResponse,
    OnlineBenefitPoolRequest,
    OnlineBenefitPoolResponseBody,
    OnlineBenefitPoolResponse,
    OnlineCloudAppInnerRequest,
    OnlineCloudAppInnerResponseBody,
    OnlineCloudAppInnerResponse,
    OnlineCustomerActivityRequest,
    OnlineCustomerActivityResponseBody,
    OnlineCustomerActivityResponse,
    RecordActivateChannelDesktopRequest,
    RecordActivateChannelDesktopResponseBody,
    RecordActivateChannelDesktopResponse,
    RefundOrderRequest,
    RefundOrderResponseBody,
    RefundOrderResponse,
    RefundTaobaoOrderRequest,
    RefundTaobaoOrderResponseBody,
    RefundTaobaoOrderResponse,
    RewriteTaobaoOrderTelephoneNumberRequest,
    RewriteTaobaoOrderTelephoneNumberResponseBody,
    RewriteTaobaoOrderTelephoneNumberResponse,
    RollbackChannelTransferDesktopsRequest,
    RollbackChannelTransferDesktopsResponseBody,
    RollbackChannelTransferDesktopsResponse,
    RollbackOrderPackageNumberRequest,
    RollbackOrderPackageNumberResponseBody,
    RollbackOrderPackageNumberResponse,
    SendActivityLauncherMessageRequest,
    SendActivityLauncherMessageResponseBody,
    SendActivityLauncherMessageResponse,
    SendLauncherMessageRequest,
    SendLauncherMessageResponseBody,
    SendLauncherMessageResponse,
    SubscribeWithholdInnerRequest,
    SubscribeWithholdInnerResponseBody,
    SubscribeWithholdInnerResponse,
    SupplyBenefitPoolRequest,
    SupplyBenefitPoolResponseBody,
    SupplyBenefitPoolResponse,
    TransferDesktopsRequest,
    TransferDesktopsResponseBody,
    TransferDesktopsResponse,
    UpdateChannelUserTypeRequest,
    UpdateChannelUserTypeResponseBody,
    UpdateChannelUserTypeResponse,
    UpdateCheckinActivityConfigInnerRequest,
    UpdateCheckinActivityConfigInnerResponseBody,
    UpdateCheckinActivityConfigInnerResponse,
    UpdateDesktopRealExpireTimeRequest,
    UpdateDesktopRealExpireTimeShrinkRequest,
    UpdateDesktopRealExpireTimeResponseBody,
    UpdateDesktopRealExpireTimeResponse,
    UpdateOrderPackageNumberRequest,
    UpdateOrderPackageNumberResponseBody,
    UpdateOrderPackageNumberResponse,
    UpdateOrderPayStatusRequest,
    UpdateOrderPayStatusResponseBody,
    UpdateOrderPayStatusResponse,
    UpdateOrderStatusRequest,
    UpdateOrderStatusResponseBody,
    UpdateOrderStatusResponse,
    UpdateTerminalSoftRequest,
    UpdateTerminalSoftResponseBody,
    UpdateTerminalSoftResponse,
    UseBusinessRightsRequest,
    UseBusinessRightsShrinkRequest,
    UseBusinessRightsResponseBody,
    UseBusinessRightsResponse,
    VerifyHardwareCouponRequest,
    VerifyHardwareCouponResponseBody,
    VerifyHardwareCouponResponse,
    VerifyRedeemCodeRequest,
    VerifyRedeemCodeResponseBody,
    VerifyRedeemCodeResponse,
    VerifyUserApplyCouponRequest,
    VerifyUserApplyCouponResponseBody,
    VerifyUserApplyCouponResponse,
    ActivateResourceResponseBodyData,
    ActiveChannelRedeemCodeResponseBodyData,
    AddChannelUserResponseBodyData,
    AddCoreHourInstanceResponseBodyData,
    CancelBusinessRightsResponseBodyData,
    CancelPackageOrderResponseBodyData,
    CheckCouponCodeRuleInnerResponseBodyData,
    CheckQuotaResponseBodyQuotaInfo,
    CheckSubscribeFirstDiscountInnerRequestPackageBundlesJson,
    CheckSubscribeFirstDiscountInnerResponseBodyDataPackageBundles,
    CheckSubscribeFirstDiscountInnerResponseBodyData,
    CheckUserHasDesktopOrPackageResponseBodyUserResourceOwnership,
    CountDesktopGroupByBundleIdAndSkuCodeResponseBodyData,
    CountUserDesktopAndPackageResponseBodyResourceStatistics,
    CreateActivityProductConfigRequestActivityProductConfigInitResourceConfig,
    CreateActivityProductConfigRequestActivityProductConfig,
    CreateActivityProductConfigResponseBodyAddActivityProductConfigResponse,
    CreateAndActivateResourceResponseBodyData,
    CreateBenefitResponseBodyData,
    CreateBenefitPoolRequestApprovalParam,
    CreateBenefitPoolRequestBody,
    CreateBenefitPoolRequestChannelParam,
    CreateBenefitPoolResponseBodyData,
    CreateBusinessApplyResponseBodyDataBusinessRenewRightsDtoList,
    CreateBusinessApplyResponseBodyData,
    CreateCouponCodeRuleInnerResponseBodyData,
    CreateCustomerActivityResponseBodyData,
    CreateDesktopResponseBodyDesktop,
    CreateDesktopCacheImageResponseBodyData,
    CreateHardwareCouponsResponseBodyDataFailList,
    CreateHardwareCouponsResponseBodyData,
    CreateOrderResponseBodyData,
    CreatePackageOrderRequestProductListJsonAutoActivateConfig,
    CreatePackageOrderRequestProductListJson,
    CreatePackageOrderResponseBodyDataOrderDetailList,
    CreatePackageOrderResponseBodyData,
    CreatePersonalUsersCouponResponseBodyData,
    CreateRedeemCodesResponseBodyData,
    CreateResourceResponseBodyData,
    CreateStandardOrderResponseBodyData,
    CreateUserApplyCouponsResponseBodyData,
    CreateUserDesktopInstallResponseBodyData,
    DeleteActivityProductConfigResponseBodyDeleteActivityProductConfigResponse,
    DeleteDesktopFromDBResponseBodyData,
    DescribeActivityInfoByCodeResponseBodyDataImageInfos,
    DescribeActivityInfoByCodeResponseBodyData,
    DescribeActivityInnerResponseBodyData,
    DescribeActivityProductConfigListResponseBodyDataActivityConfigInfoListInitResourceConfig,
    DescribeActivityProductConfigListResponseBodyDataActivityConfigInfoList,
    DescribeActivityProductConfigListResponseBodyData,
    DescribeAlarmNotifyDesktopIdsResponseBodyDataWillExpiringDesktopIds,
    DescribeAlarmNotifyDesktopIdsResponseBodyDataWillReleaseDesktopIds,
    DescribeAlarmNotifyDesktopIdsResponseBodyDataWillTimePackExhaustingDesktopIds,
    DescribeAlarmNotifyDesktopIdsResponseBodyData,
    DescribeBenefitPoolResponseBodyDataBenefitInfos,
    DescribeBenefitPoolResponseBodyData,
    DescribeBenefitPoolByChannelOrdersResponseBodyDataBenefitInfos,
    DescribeBenefitPoolByChannelOrdersResponseBodyData,
    DescribeBundleResponseBodyBundlesDesktopTypeAttribute,
    DescribeBundleResponseBodyBundlesDisks,
    DescribeBundleResponseBodyBundlesSkuItems,
    DescribeBundleResponseBodyBundles,
    DescribeBusinessRightsResponseBodyDataPackageSku,
    DescribeBusinessRightsResponseBodyDataProduction,
    DescribeBusinessRightsResponseBodyData,
    DescribeChannelInfoByUidResponseBodyData,
    DescribeChannelOrdersResponseBodyData,
    DescribeChannelTransferDesktopsResponseBodyDataDisks,
    DescribeChannelTransferDesktopsResponseBodyData,
    DescribeCloudAppInfosInnerResponseBodyData,
    DescribeCommoditiesResponseBodyDataCommodityItem1,
    DescribeCommoditiesResponseBodyDataCommodityItem2,
    DescribeCommoditiesResponseBodyData,
    DescribeCommodityCodeInfoResponseBodyDataDescribeCommodityTypeDto,
    DescribeCommodityCodeInfoResponseBodyDataDescribePackageDto,
    DescribeCommodityCodeInfoResponseBodyDataDescribeSpecCodeDto,
    DescribeCommodityCodeInfoResponseBodyData,
    DescribeCouponOperateSumResponseBodyData,
    DescribeCustomerActivityInnerResponseBodyDataBenefitInfo,
    DescribeCustomerActivityInnerResponseBodyDataVerifyStepInfo,
    DescribeCustomerActivityInnerResponseBodyData,
    DescribeDesktopByTeamIdAndConditionResponseBodyDataRecords,
    DescribeDesktopByTeamIdAndConditionResponseBodyData,
    DescribeDesktopImageCacheResponseBodyData,
    DescribeDesktopInstallByDesktopResponseBodyData,
    DescribeDesktopInstallByTaskResponseBodyData,
    DescribeDesktopSpecsResponseBodyDataDesktopTemplates,
    DescribeDesktopSpecsResponseBodyData,
    DescribeDesktopsResponseBodyDataDisks,
    DescribeDesktopsResponseBodyData,
    DescribeDiscountEligibilityResponseBodyData,
    DescribeDynamicResourceListResponseBodyDataResourceList,
    DescribeDynamicResourceListResponseBodyData,
    DescribeEventDataSummaryInnerResponseBodyDataCodeSummaryInfos,
    DescribeEventDataSummaryInnerResponseBodyData,
    DescribeInitChannelUserInfoResponseBodyData,
    DescribeInnerDesktopDetailResponseBodyDataDesktopSpec,
    DescribeInnerDesktopDetailResponseBodyDataDisks,
    DescribeInnerDesktopDetailResponseBodyDataPackageDuration,
    DescribeInnerDesktopDetailResponseBodyDataSessions,
    DescribeInnerDesktopDetailResponseBodyData,
    DescribeMonthlyPackageListResponseBodyMonthlyPackageListPackageBillingInfoList,
    DescribeMonthlyPackageListResponseBodyMonthlyPackageListRedeemCouponInfo,
    DescribeMonthlyPackageListResponseBodyMonthlyPackageList,
    DescribeMultiplePricesRequestProductListJson,
    DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListPrice,
    DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProduct,
    DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProductSku,
    DescribeMultiplePricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetail,
    DescribeMultiplePricesResponseBodyPriceResponsePriceDetailList,
    DescribeMultiplePricesResponseBodyPriceResponsePromotionList,
    DescribeMultiplePricesResponseBodyPriceResponseRuleList,
    DescribeMultiplePricesResponseBodyPriceResponseSummaryPrice,
    DescribeMultiplePricesResponseBodyPriceResponse,
    DescribeOrderMoneyResponseBodyData,
    DescribeOrdersResponseBodyData,
    DescribeOriginalPricesRequestProductListJson,
    DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListPrice,
    DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProduct,
    DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetailProductSku,
    DescribeOriginalPricesResponseBodyPriceResponsePriceDetailListProductAndSkuDetail,
    DescribeOriginalPricesResponseBodyPriceResponsePriceDetailList,
    DescribeOriginalPricesResponseBodyPriceResponseSummaryPrice,
    DescribeOriginalPricesResponseBodyPriceResponse,
    DescribePackageOrdersResponseBodyPageOrderList,
    DescribePackageOrdersResponseBodyPage,
    DescribePersonalActivityInfosResponseBodyDataImageInfos,
    DescribePersonalActivityInfosResponseBodyData,
    DescribePersonalCodeRelationInnerResponseBodyData,
    DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitListBenefitInfo,
    DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitListCustomerActivityInfo,
    DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataBenefitList,
    DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataHistoryActivityList,
    DescribePersonalUsersOwnedBenefitsInnerResponseBodyDataHistoryBenefitList,
    DescribePersonalUsersOwnedBenefitsInnerResponseBodyData,
    DescribePickCouponUserListResponseBodyData,
    DescribeProductListResponseBodyProductListSkuList,
    DescribeProductListResponseBodyProductList,
    DescribeRedeemCodeInfoInnerResponseBodyDataBenefitInfo,
    DescribeRedeemCodeInfoInnerResponseBodyDataExtraCouponInfo,
    DescribeRedeemCodeInfoInnerResponseBodyDataVerifyStepInfo,
    DescribeRedeemCodeInfoInnerResponseBodyData,
    DescribeRenewProductListResponseBodyData,
    DescribeRouteAreaCityInfoResponseBodyData,
    DescribeRouteRegionByIpResponseBodyData,
    DescribeStandardOrdersResponseBodyDataDescribeOrdersLists,
    DescribeStandardOrdersResponseBodyData,
    DescribeStandardTeamAvailableOrdersResponseBodyData,
    DescribeSubscribePackagesInnerResponseBodyDataInstanceInfos,
    DescribeSubscribePackagesInnerResponseBodyData,
    DescribeTagListResponseBodyData,
    DescribeTbUserBenefitsResponseBodyData,
    DescribeTeamInfoByCodeResponseBodyData,
    DescribeTeamInfoByIdsResponseBodyData,
    DescribeTeamMemberInfoInnerResponseBodyData,
    DescribeTeamsByRoleInnerResponseBodyDataMemberInfo,
    DescribeTeamsByRoleInnerResponseBodyDataOwnerInfo,
    DescribeTeamsByRoleInnerResponseBodyDataPersonalInfo,
    DescribeTeamsByRoleInnerResponseBodyData,
    DescribeTerminalSerialNumberBenefitResponseBodyData,
    DescribeTerminalSerialNumberByTokenResponseBodyData,
    DescribeUserCoinInfoResponseBodyData,
    DescribeUserDesktopInfoResponseBodyData,
    DescribeUserDesktopInstallTaskResponseBodyData,
    DescribeUserDiscountInnerResponseBodyData,
    DescribeUserPackageOrdersInnerResponseBodyData,
    DescribeUserPersonalCodeInnerResponseBodyDataCodeInfo,
    DescribeUserPersonalCodeInnerResponseBodyData,
    DescribeUserPurchaseCouponsInnerResponseBodyDataCouponList,
    DescribeUserPurchaseCouponsInnerResponseBodyData,
    DiamondPublishInnerResponseBodyData,
    GetChannelGeneralAgentResponseBodyData,
    InnerAnalyzeUserCouponCodeResponseBodyDataCodeInfo,
    InnerAnalyzeUserCouponCodeResponseBodyData,
    InnerDescribeAvailableModeListResponseBodyDesktopModeList,
    InnerDescribeCommunityImagesResponseBodyData,
    InnerDescribeDesktopLocalInfoResponseBodyData,
    InnerDescribeImageCodesPageResponseBodyDataImageCodeModels,
    InnerDescribeImageCodesPageResponseBodyData,
    InnerDescribeImageDetailResponseBodyData,
    InnerDescribeImagesResponseBodyData,
    InnerDescribeProductListResponseBodyProductListSkuList,
    InnerDescribeProductListResponseBodyProductList,
    InnerDescribeTeamDesktopCountResponseBodyData,
    InnerDescribeTeamDesktopsPageResponseBodyDataDesktopsDurationList,
    InnerDescribeTeamDesktopsPageResponseBodyDataDesktops,
    InnerDescribeTeamDesktopsPageResponseBodyData,
    ModifyActivityProductConfigRequestNewActivityConfigInfoInitResourceConfig,
    ModifyActivityProductConfigRequestNewActivityConfigInfo,
    ModifyActivityProductConfigResponseBodyUpdateActivityProductConfigResponse,
    SubscribeWithholdInnerResponseBodyData,
    VerifyRedeemCodeResponseBodyData
]
