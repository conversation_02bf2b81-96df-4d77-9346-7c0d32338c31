# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribePackageOrdersResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        message: str = None,
        page: main_models.DescribePackageOrdersResponseBodyPage = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.message = message
        self.page = page
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.page:
            self.page.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.message is not None:
            result['Message'] = self.message

        if self.page is not None:
            result['Page'] = self.page.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('Page') is not None:
            temp_model = main_models.DescribePackageOrdersResponseBodyPage()
            self.page = temp_model.from_map(m.get('Page'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribePackageOrdersResponseBodyPage(DaraModel):
    def __init__(
        self,
        current_page: int = None,
        order_list: List[main_models.DescribePackageOrdersResponseBodyPageOrderList] = None,
        page_size: int = None,
        total_count: int = None,
    ):
        self.current_page = current_page
        self.order_list = order_list
        self.page_size = page_size
        self.total_count = total_count

    def validate(self):
        if self.order_list:
            for v1 in self.order_list:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.current_page is not None:
            result['CurrentPage'] = self.current_page

        result['OrderList'] = []
        if self.order_list is not None:
            for k1 in self.order_list:
                result['OrderList'].append(k1.to_map() if k1 else None)

        if self.page_size is not None:
            result['PageSize'] = self.page_size

        if self.total_count is not None:
            result['TotalCount'] = self.total_count

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CurrentPage') is not None:
            self.current_page = m.get('CurrentPage')

        self.order_list = []
        if m.get('OrderList') is not None:
            for k1 in m.get('OrderList'):
                temp_model = main_models.DescribePackageOrdersResponseBodyPageOrderList()
                self.order_list.append(temp_model.from_map(k1))

        if m.get('PageSize') is not None:
            self.page_size = m.get('PageSize')

        if m.get('TotalCount') is not None:
            self.total_count = m.get('TotalCount')

        return self

class DescribePackageOrdersResponseBodyPageOrderList(DaraModel):
    def __init__(
        self,
        amount: int = None,
        creator_ali_uid: int = None,
        creator_end_user_id: str = None,
        extend_info: str = None,
        gmt_canceled: str = None,
        gmt_create: str = None,
        gmt_paid: str = None,
        instance_id: str = None,
        is_first_buy: bool = None,
        is_subscribe: bool = None,
        order_id: str = None,
        order_status: str = None,
        order_type: str = None,
        product_code: str = None,
        product_info: str = None,
        product_sku_code: str = None,
        product_sku_info: str = None,
        product_type: str = None,
        trade_price: str = None,
    ):
        self.amount = amount
        self.creator_ali_uid = creator_ali_uid
        self.creator_end_user_id = creator_end_user_id
        self.extend_info = extend_info
        # Use the UTC time format: yyyy-MM-ddTHH:mm:ssZ
        self.gmt_canceled = gmt_canceled
        # Use the UTC time format: yyyy-MM-ddTHH:mm:ssZ
        self.gmt_create = gmt_create
        # Use the UTC time format: yyyy-MM-ddTHH:mm:ssZ
        self.gmt_paid = gmt_paid
        self.instance_id = instance_id
        self.is_first_buy = is_first_buy
        self.is_subscribe = is_subscribe
        self.order_id = order_id
        self.order_status = order_status
        self.order_type = order_type
        self.product_code = product_code
        self.product_info = product_info
        self.product_sku_code = product_sku_code
        self.product_sku_info = product_sku_info
        self.product_type = product_type
        self.trade_price = trade_price

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.amount is not None:
            result['Amount'] = self.amount

        if self.creator_ali_uid is not None:
            result['CreatorAliUid'] = self.creator_ali_uid

        if self.creator_end_user_id is not None:
            result['CreatorEndUserId'] = self.creator_end_user_id

        if self.extend_info is not None:
            result['ExtendInfo'] = self.extend_info

        if self.gmt_canceled is not None:
            result['GmtCanceled'] = self.gmt_canceled

        if self.gmt_create is not None:
            result['GmtCreate'] = self.gmt_create

        if self.gmt_paid is not None:
            result['GmtPaid'] = self.gmt_paid

        if self.instance_id is not None:
            result['InstanceId'] = self.instance_id

        if self.is_first_buy is not None:
            result['IsFirstBuy'] = self.is_first_buy

        if self.is_subscribe is not None:
            result['IsSubscribe'] = self.is_subscribe

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.order_status is not None:
            result['OrderStatus'] = self.order_status

        if self.order_type is not None:
            result['OrderType'] = self.order_type

        if self.product_code is not None:
            result['ProductCode'] = self.product_code

        if self.product_info is not None:
            result['ProductInfo'] = self.product_info

        if self.product_sku_code is not None:
            result['ProductSkuCode'] = self.product_sku_code

        if self.product_sku_info is not None:
            result['ProductSkuInfo'] = self.product_sku_info

        if self.product_type is not None:
            result['ProductType'] = self.product_type

        if self.trade_price is not None:
            result['TradePrice'] = self.trade_price

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Amount') is not None:
            self.amount = m.get('Amount')

        if m.get('CreatorAliUid') is not None:
            self.creator_ali_uid = m.get('CreatorAliUid')

        if m.get('CreatorEndUserId') is not None:
            self.creator_end_user_id = m.get('CreatorEndUserId')

        if m.get('ExtendInfo') is not None:
            self.extend_info = m.get('ExtendInfo')

        if m.get('GmtCanceled') is not None:
            self.gmt_canceled = m.get('GmtCanceled')

        if m.get('GmtCreate') is not None:
            self.gmt_create = m.get('GmtCreate')

        if m.get('GmtPaid') is not None:
            self.gmt_paid = m.get('GmtPaid')

        if m.get('InstanceId') is not None:
            self.instance_id = m.get('InstanceId')

        if m.get('IsFirstBuy') is not None:
            self.is_first_buy = m.get('IsFirstBuy')

        if m.get('IsSubscribe') is not None:
            self.is_subscribe = m.get('IsSubscribe')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('OrderStatus') is not None:
            self.order_status = m.get('OrderStatus')

        if m.get('OrderType') is not None:
            self.order_type = m.get('OrderType')

        if m.get('ProductCode') is not None:
            self.product_code = m.get('ProductCode')

        if m.get('ProductInfo') is not None:
            self.product_info = m.get('ProductInfo')

        if m.get('ProductSkuCode') is not None:
            self.product_sku_code = m.get('ProductSkuCode')

        if m.get('ProductSkuInfo') is not None:
            self.product_sku_info = m.get('ProductSkuInfo')

        if m.get('ProductType') is not None:
            self.product_type = m.get('ProductType')

        if m.get('TradePrice') is not None:
            self.trade_price = m.get('TradePrice')

        return self

