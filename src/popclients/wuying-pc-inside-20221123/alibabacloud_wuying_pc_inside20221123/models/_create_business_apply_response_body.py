# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class CreateBusinessApplyResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.CreateBusinessApplyResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.CreateBusinessApplyResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class CreateBusinessApplyResponseBodyData(DaraModel):
    def __init__(
        self,
        apply_id: str = None,
        business_renew_rights_dto_list: List[main_models.CreateBusinessApplyResponseBodyDataBusinessRenewRightsDtoList] = None,
    ):
        self.apply_id = apply_id
        self.business_renew_rights_dto_list = business_renew_rights_dto_list

    def validate(self):
        if self.business_renew_rights_dto_list:
            for v1 in self.business_renew_rights_dto_list:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.apply_id is not None:
            result['ApplyId'] = self.apply_id

        result['BusinessRenewRightsDtoList'] = []
        if self.business_renew_rights_dto_list is not None:
            for k1 in self.business_renew_rights_dto_list:
                result['BusinessRenewRightsDtoList'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ApplyId') is not None:
            self.apply_id = m.get('ApplyId')

        self.business_renew_rights_dto_list = []
        if m.get('BusinessRenewRightsDtoList') is not None:
            for k1 in m.get('BusinessRenewRightsDtoList'):
                temp_model = main_models.CreateBusinessApplyResponseBodyDataBusinessRenewRightsDtoList()
                self.business_renew_rights_dto_list.append(temp_model.from_map(k1))

        return self

class CreateBusinessApplyResponseBodyDataBusinessRenewRightsDtoList(DaraModel):
    def __init__(
        self,
        classroom_id: str = None,
        desktop_id: str = None,
        error_code: str = None,
        error_message: str = None,
        rights_id: str = None,
        status: str = None,
    ):
        self.classroom_id = classroom_id
        self.desktop_id = desktop_id
        self.error_code = error_code
        self.error_message = error_message
        self.rights_id = rights_id
        self.status = status

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.classroom_id is not None:
            result['ClassroomId'] = self.classroom_id

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.error_code is not None:
            result['ErrorCode'] = self.error_code

        if self.error_message is not None:
            result['ErrorMessage'] = self.error_message

        if self.rights_id is not None:
            result['RightsId'] = self.rights_id

        if self.status is not None:
            result['Status'] = self.status

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ClassroomId') is not None:
            self.classroom_id = m.get('ClassroomId')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('ErrorCode') is not None:
            self.error_code = m.get('ErrorCode')

        if m.get('ErrorMessage') is not None:
            self.error_message = m.get('ErrorMessage')

        if m.get('RightsId') is not None:
            self.rights_id = m.get('RightsId')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        return self

