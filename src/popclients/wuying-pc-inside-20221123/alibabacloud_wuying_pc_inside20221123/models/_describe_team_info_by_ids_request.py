# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeTeamInfoByIdsRequest(DaraModel):
    def __init__(
        self,
        team_ids: str = None,
    ):
        self.team_ids = team_ids

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.team_ids is not None:
            result['TeamIds'] = self.team_ids

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('TeamIds') is not None:
            self.team_ids = m.get('TeamIds')

        return self

