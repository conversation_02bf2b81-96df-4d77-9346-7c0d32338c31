# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeChannelOrdersResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.DescribeChannelOrdersResponseBodyData] = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.DescribeChannelOrdersResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeChannelOrdersResponseBodyData(DaraModel):
    def __init__(
        self,
        amount: int = None,
        available_amount: int = None,
        bandwidth: int = None,
        commodity_code: str = None,
        commodity_name: str = None,
        cpu_core_count: int = None,
        create_order_time: str = None,
        duration_time: int = None,
        gpu_count: int = None,
        memory_size: int = None,
        order_id: str = None,
        order_number: str = None,
        os_type: str = None,
        package_code: str = None,
        package_period: str = None,
        parent_id: str = None,
        pay_number: str = None,
        pay_time: str = None,
        period: int = None,
        period_unit: str = None,
        remark: str = None,
        spec_code: str = None,
        specification: str = None,
        status: str = None,
        system_disk_size: int = None,
        user_disk_size: int = None,
    ):
        self.amount = amount
        self.available_amount = available_amount
        self.bandwidth = bandwidth
        self.commodity_code = commodity_code
        self.commodity_name = commodity_name
        self.cpu_core_count = cpu_core_count
        self.create_order_time = create_order_time
        self.duration_time = duration_time
        self.gpu_count = gpu_count
        self.memory_size = memory_size
        self.order_id = order_id
        self.order_number = order_number
        self.os_type = os_type
        self.package_code = package_code
        self.package_period = package_period
        self.parent_id = parent_id
        self.pay_number = pay_number
        self.pay_time = pay_time
        self.period = period
        self.period_unit = period_unit
        self.remark = remark
        self.spec_code = spec_code
        self.specification = specification
        self.status = status
        self.system_disk_size = system_disk_size
        self.user_disk_size = user_disk_size

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.amount is not None:
            result['Amount'] = self.amount

        if self.available_amount is not None:
            result['AvailableAmount'] = self.available_amount

        if self.bandwidth is not None:
            result['Bandwidth'] = self.bandwidth

        if self.commodity_code is not None:
            result['CommodityCode'] = self.commodity_code

        if self.commodity_name is not None:
            result['CommodityName'] = self.commodity_name

        if self.cpu_core_count is not None:
            result['CpuCoreCount'] = self.cpu_core_count

        if self.create_order_time is not None:
            result['CreateOrderTime'] = self.create_order_time

        if self.duration_time is not None:
            result['DurationTime'] = self.duration_time

        if self.gpu_count is not None:
            result['GpuCount'] = self.gpu_count

        if self.memory_size is not None:
            result['MemorySize'] = self.memory_size

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.order_number is not None:
            result['OrderNumber'] = self.order_number

        if self.os_type is not None:
            result['OsType'] = self.os_type

        if self.package_code is not None:
            result['PackageCode'] = self.package_code

        if self.package_period is not None:
            result['PackagePeriod'] = self.package_period

        if self.parent_id is not None:
            result['ParentId'] = self.parent_id

        if self.pay_number is not None:
            result['PayNumber'] = self.pay_number

        if self.pay_time is not None:
            result['PayTime'] = self.pay_time

        if self.period is not None:
            result['Period'] = self.period

        if self.period_unit is not None:
            result['PeriodUnit'] = self.period_unit

        if self.remark is not None:
            result['Remark'] = self.remark

        if self.spec_code is not None:
            result['SpecCode'] = self.spec_code

        if self.specification is not None:
            result['Specification'] = self.specification

        if self.status is not None:
            result['Status'] = self.status

        if self.system_disk_size is not None:
            result['SystemDiskSize'] = self.system_disk_size

        if self.user_disk_size is not None:
            result['UserDiskSize'] = self.user_disk_size

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Amount') is not None:
            self.amount = m.get('Amount')

        if m.get('AvailableAmount') is not None:
            self.available_amount = m.get('AvailableAmount')

        if m.get('Bandwidth') is not None:
            self.bandwidth = m.get('Bandwidth')

        if m.get('CommodityCode') is not None:
            self.commodity_code = m.get('CommodityCode')

        if m.get('CommodityName') is not None:
            self.commodity_name = m.get('CommodityName')

        if m.get('CpuCoreCount') is not None:
            self.cpu_core_count = m.get('CpuCoreCount')

        if m.get('CreateOrderTime') is not None:
            self.create_order_time = m.get('CreateOrderTime')

        if m.get('DurationTime') is not None:
            self.duration_time = m.get('DurationTime')

        if m.get('GpuCount') is not None:
            self.gpu_count = m.get('GpuCount')

        if m.get('MemorySize') is not None:
            self.memory_size = m.get('MemorySize')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('OrderNumber') is not None:
            self.order_number = m.get('OrderNumber')

        if m.get('OsType') is not None:
            self.os_type = m.get('OsType')

        if m.get('PackageCode') is not None:
            self.package_code = m.get('PackageCode')

        if m.get('PackagePeriod') is not None:
            self.package_period = m.get('PackagePeriod')

        if m.get('ParentId') is not None:
            self.parent_id = m.get('ParentId')

        if m.get('PayNumber') is not None:
            self.pay_number = m.get('PayNumber')

        if m.get('PayTime') is not None:
            self.pay_time = m.get('PayTime')

        if m.get('Period') is not None:
            self.period = m.get('Period')

        if m.get('PeriodUnit') is not None:
            self.period_unit = m.get('PeriodUnit')

        if m.get('Remark') is not None:
            self.remark = m.get('Remark')

        if m.get('SpecCode') is not None:
            self.spec_code = m.get('SpecCode')

        if m.get('Specification') is not None:
            self.specification = m.get('Specification')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        if m.get('SystemDiskSize') is not None:
            self.system_disk_size = m.get('SystemDiskSize')

        if m.get('UserDiskSize') is not None:
            self.user_disk_size = m.get('UserDiskSize')

        return self

