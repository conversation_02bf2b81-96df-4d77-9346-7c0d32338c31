# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class UpdateOrderStatusRequest(DaraModel):
    def __init__(
        self,
        cur_order_status: str = None,
        expire_time_stamp: int = None,
        order_id: str = None,
        target_order_status: str = None,
    ):
        self.cur_order_status = cur_order_status
        self.expire_time_stamp = expire_time_stamp
        self.order_id = order_id
        self.target_order_status = target_order_status

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.cur_order_status is not None:
            result['CurOrderStatus'] = self.cur_order_status

        if self.expire_time_stamp is not None:
            result['ExpireTimeStamp'] = self.expire_time_stamp

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.target_order_status is not None:
            result['TargetOrderStatus'] = self.target_order_status

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CurOrderStatus') is not None:
            self.cur_order_status = m.get('CurOrderStatus')

        if m.get('ExpireTimeStamp') is not None:
            self.expire_time_stamp = m.get('ExpireTimeStamp')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('TargetOrderStatus') is not None:
            self.target_order_status = m.get('TargetOrderStatus')

        return self

