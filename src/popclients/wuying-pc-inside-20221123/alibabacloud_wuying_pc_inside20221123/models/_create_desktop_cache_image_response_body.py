# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 


class CreateDesktopCacheImageResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.CreateDesktopCacheImageResponseBodyData = None,
        request_id: str = None,
    ):
        self.code = code
        self.data = data
        self.request_id = request_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.request_id is not None:
            result['requestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.CreateDesktopCacheImageResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('requestId') is not None:
            self.request_id = m.get('requestId')

        return self

class CreateDesktopCacheImageResponseBodyData(DaraModel):
    def __init__(
        self,
        cache_id: str = None,
    ):
        self.cache_id = cache_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.cache_id is not None:
            result['CacheId'] = self.cache_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CacheId') is not None:
            self.cache_id = m.get('CacheId')

        return self

