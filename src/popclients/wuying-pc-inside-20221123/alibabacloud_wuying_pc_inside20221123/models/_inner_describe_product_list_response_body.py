# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class InnerDescribeProductListResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        message: str = None,
        product_list: List[main_models.InnerDescribeProductListResponseBodyProductList] = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.message = message
        self.product_list = product_list
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.product_list:
            for v1 in self.product_list:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.message is not None:
            result['Message'] = self.message

        result['ProductList'] = []
        if self.product_list is not None:
            for k1 in self.product_list:
                result['ProductList'].append(k1.to_map() if k1 else None)

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        self.product_list = []
        if m.get('ProductList') is not None:
            for k1 in m.get('ProductList'):
                temp_model = main_models.InnerDescribeProductListResponseBodyProductList()
                self.product_list.append(temp_model.from_map(k1))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class InnerDescribeProductListResponseBodyProductList(DaraModel):
    def __init__(
        self,
        attribute: str = None,
        product_code: str = None,
        product_desc: str = None,
        product_name: str = None,
        product_type: str = None,
        sku_list: List[main_models.InnerDescribeProductListResponseBodyProductListSkuList] = None,
    ):
        self.attribute = attribute
        self.product_code = product_code
        self.product_desc = product_desc
        self.product_name = product_name
        self.product_type = product_type
        self.sku_list = sku_list

    def validate(self):
        if self.sku_list:
            for v1 in self.sku_list:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.attribute is not None:
            result['Attribute'] = self.attribute

        if self.product_code is not None:
            result['ProductCode'] = self.product_code

        if self.product_desc is not None:
            result['ProductDesc'] = self.product_desc

        if self.product_name is not None:
            result['ProductName'] = self.product_name

        if self.product_type is not None:
            result['ProductType'] = self.product_type

        result['SkuList'] = []
        if self.sku_list is not None:
            for k1 in self.sku_list:
                result['SkuList'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Attribute') is not None:
            self.attribute = m.get('Attribute')

        if m.get('ProductCode') is not None:
            self.product_code = m.get('ProductCode')

        if m.get('ProductDesc') is not None:
            self.product_desc = m.get('ProductDesc')

        if m.get('ProductName') is not None:
            self.product_name = m.get('ProductName')

        if m.get('ProductType') is not None:
            self.product_type = m.get('ProductType')

        self.sku_list = []
        if m.get('SkuList') is not None:
            for k1 in m.get('SkuList'):
                temp_model = main_models.InnerDescribeProductListResponseBodyProductListSkuList()
                self.sku_list.append(temp_model.from_map(k1))

        return self

class InnerDescribeProductListResponseBodyProductListSkuList(DaraModel):
    def __init__(
        self,
        attribute: str = None,
        sku_code: str = None,
        sku_desc: str = None,
        sku_name: str = None,
    ):
        self.attribute = attribute
        self.sku_code = sku_code
        self.sku_desc = sku_desc
        self.sku_name = sku_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.attribute is not None:
            result['Attribute'] = self.attribute

        if self.sku_code is not None:
            result['SkuCode'] = self.sku_code

        if self.sku_desc is not None:
            result['SkuDesc'] = self.sku_desc

        if self.sku_name is not None:
            result['SkuName'] = self.sku_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Attribute') is not None:
            self.attribute = m.get('Attribute')

        if m.get('SkuCode') is not None:
            self.sku_code = m.get('SkuCode')

        if m.get('SkuDesc') is not None:
            self.sku_desc = m.get('SkuDesc')

        if m.get('SkuName') is not None:
            self.sku_name = m.get('SkuName')

        return self

