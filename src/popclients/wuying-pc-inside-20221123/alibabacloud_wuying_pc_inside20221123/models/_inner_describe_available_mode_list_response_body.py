# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class InnerDescribeAvailableModeListResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        desktop_mode_list: List[main_models.InnerDescribeAvailableModeListResponseBodyDesktopModeList] = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.desktop_mode_list = desktop_mode_list
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.desktop_mode_list:
            for v1 in self.desktop_mode_list:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['DesktopModeList'] = []
        if self.desktop_mode_list is not None:
            for k1 in self.desktop_mode_list:
                result['DesktopModeList'].append(k1.to_map() if k1 else None)

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.desktop_mode_list = []
        if m.get('DesktopModeList') is not None:
            for k1 in m.get('DesktopModeList'):
                temp_model = main_models.InnerDescribeAvailableModeListResponseBodyDesktopModeList()
                self.desktop_mode_list.append(temp_model.from_map(k1))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class InnerDescribeAvailableModeListResponseBodyDesktopModeList(DaraModel):
    def __init__(
        self,
        core_packages_per_hour: str = None,
        desktop_type: str = None,
        is_default: bool = None,
        is_gpu_mode: bool = None,
        mode_description: str = None,
        mode_id: str = None,
        mode_name: str = None,
    ):
        self.core_packages_per_hour = core_packages_per_hour
        self.desktop_type = desktop_type
        self.is_default = is_default
        self.is_gpu_mode = is_gpu_mode
        self.mode_description = mode_description
        self.mode_id = mode_id
        self.mode_name = mode_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.core_packages_per_hour is not None:
            result['CorePackagesPerHour'] = self.core_packages_per_hour

        if self.desktop_type is not None:
            result['DesktopType'] = self.desktop_type

        if self.is_default is not None:
            result['IsDefault'] = self.is_default

        if self.is_gpu_mode is not None:
            result['IsGpuMode'] = self.is_gpu_mode

        if self.mode_description is not None:
            result['ModeDescription'] = self.mode_description

        if self.mode_id is not None:
            result['ModeId'] = self.mode_id

        if self.mode_name is not None:
            result['ModeName'] = self.mode_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CorePackagesPerHour') is not None:
            self.core_packages_per_hour = m.get('CorePackagesPerHour')

        if m.get('DesktopType') is not None:
            self.desktop_type = m.get('DesktopType')

        if m.get('IsDefault') is not None:
            self.is_default = m.get('IsDefault')

        if m.get('IsGpuMode') is not None:
            self.is_gpu_mode = m.get('IsGpuMode')

        if m.get('ModeDescription') is not None:
            self.mode_description = m.get('ModeDescription')

        if m.get('ModeId') is not None:
            self.mode_id = m.get('ModeId')

        if m.get('ModeName') is not None:
            self.mode_name = m.get('ModeName')

        return self

