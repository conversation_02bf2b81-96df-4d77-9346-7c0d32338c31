# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CancelPackageOrderRequest(DaraModel):
    def __init__(
        self,
        end_user_id: str = None,
        login_ali_uid: int = None,
        order_id: str = None,
    ):
        # This parameter is required.
        self.end_user_id = end_user_id
        # This parameter is required.
        self.login_ali_uid = login_ali_uid
        # This parameter is required.
        self.order_id = order_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.login_ali_uid is not None:
            result['LoginAliUid'] = self.login_ali_uid

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('LoginAliUid') is not None:
            self.login_ali_uid = m.get('LoginAliUid')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        return self

