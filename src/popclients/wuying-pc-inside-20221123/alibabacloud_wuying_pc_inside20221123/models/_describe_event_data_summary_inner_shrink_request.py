# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeEventDataSummaryInnerShrinkRequest(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        code_type: str = None,
        event_codes_shrink: str = None,
        source: str = None,
    ):
        self.ali_uid = ali_uid
        self.code_type = code_type
        self.event_codes_shrink = event_codes_shrink
        self.source = source

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.code_type is not None:
            result['CodeType'] = self.code_type

        if self.event_codes_shrink is not None:
            result['EventCodes'] = self.event_codes_shrink

        if self.source is not None:
            result['Source'] = self.source

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('CodeType') is not None:
            self.code_type = m.get('CodeType')

        if m.get('EventCodes') is not None:
            self.event_codes_shrink = m.get('EventCodes')

        if m.get('Source') is not None:
            self.source = m.get('Source')

        return self

