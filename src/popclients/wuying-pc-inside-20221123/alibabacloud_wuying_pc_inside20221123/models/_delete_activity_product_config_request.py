# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from typing import List


class DeleteActivityProductConfigRequest(DaraModel):
    def __init__(
        self,
        config_code_list: List[str] = None,
    ):
        # This parameter is required.
        self.config_code_list = config_code_list

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.config_code_list is not None:
            result['ConfigCodeList'] = self.config_code_list

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ConfigCodeList') is not None:
            self.config_code_list = m.get('ConfigCodeList')

        return self

