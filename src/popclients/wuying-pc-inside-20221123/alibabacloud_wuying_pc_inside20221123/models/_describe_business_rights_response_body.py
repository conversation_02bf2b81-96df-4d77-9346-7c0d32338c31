# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeBusinessRightsResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.DescribeBusinessRightsResponseBodyData] = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.DescribeBusinessRightsResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeBusinessRightsResponseBodyData(DaraModel):
    def __init__(
        self,
        apply_order_id: str = None,
        create_time: int = None,
        expire_time: int = None,
        instance_id: str = None,
        package_sku: main_models.DescribeBusinessRightsResponseBodyDataPackageSku = None,
        production: main_models.DescribeBusinessRightsResponseBodyDataProduction = None,
        right_id: str = None,
        status: str = None,
        type: str = None,
        use_time: int = None,
    ):
        self.apply_order_id = apply_order_id
        self.create_time = create_time
        self.expire_time = expire_time
        self.instance_id = instance_id
        self.package_sku = package_sku
        self.production = production
        self.right_id = right_id
        self.status = status
        self.type = type
        self.use_time = use_time

    def validate(self):
        if self.package_sku:
            self.package_sku.validate()
        if self.production:
            self.production.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.apply_order_id is not None:
            result['ApplyOrderId'] = self.apply_order_id

        if self.create_time is not None:
            result['CreateTime'] = self.create_time

        if self.expire_time is not None:
            result['ExpireTime'] = self.expire_time

        if self.instance_id is not None:
            result['InstanceId'] = self.instance_id

        if self.package_sku is not None:
            result['PackageSku'] = self.package_sku.to_map()

        if self.production is not None:
            result['Production'] = self.production.to_map()

        if self.right_id is not None:
            result['RightId'] = self.right_id

        if self.status is not None:
            result['Status'] = self.status

        if self.type is not None:
            result['Type'] = self.type

        if self.use_time is not None:
            result['UseTime'] = self.use_time

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ApplyOrderId') is not None:
            self.apply_order_id = m.get('ApplyOrderId')

        if m.get('CreateTime') is not None:
            self.create_time = m.get('CreateTime')

        if m.get('ExpireTime') is not None:
            self.expire_time = m.get('ExpireTime')

        if m.get('InstanceId') is not None:
            self.instance_id = m.get('InstanceId')

        if m.get('PackageSku') is not None:
            temp_model = main_models.DescribeBusinessRightsResponseBodyDataPackageSku()
            self.package_sku = temp_model.from_map(m.get('PackageSku'))

        if m.get('Production') is not None:
            temp_model = main_models.DescribeBusinessRightsResponseBodyDataProduction()
            self.production = temp_model.from_map(m.get('Production'))

        if m.get('RightId') is not None:
            self.right_id = m.get('RightId')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        if m.get('UseTime') is not None:
            self.use_time = m.get('UseTime')

        return self

class DescribeBusinessRightsResponseBodyDataProduction(DaraModel):
    def __init__(
        self,
        bandwidth: int = None,
        cpu_core_count: int = None,
        gpu_count: int = None,
        image_url: str = None,
        memory_size: int = None,
        name: str = None,
        os_type: str = None,
        spec_code: str = None,
        system_disk_size: int = None,
        user_disk_size: int = None,
    ):
        self.bandwidth = bandwidth
        self.cpu_core_count = cpu_core_count
        self.gpu_count = gpu_count
        self.image_url = image_url
        self.memory_size = memory_size
        self.name = name
        self.os_type = os_type
        self.spec_code = spec_code
        self.system_disk_size = system_disk_size
        self.user_disk_size = user_disk_size

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.bandwidth is not None:
            result['Bandwidth'] = self.bandwidth

        if self.cpu_core_count is not None:
            result['CpuCoreCount'] = self.cpu_core_count

        if self.gpu_count is not None:
            result['GpuCount'] = self.gpu_count

        if self.image_url is not None:
            result['ImageUrl'] = self.image_url

        if self.memory_size is not None:
            result['MemorySize'] = self.memory_size

        if self.name is not None:
            result['Name'] = self.name

        if self.os_type is not None:
            result['OsType'] = self.os_type

        if self.spec_code is not None:
            result['SpecCode'] = self.spec_code

        if self.system_disk_size is not None:
            result['SystemDiskSize'] = self.system_disk_size

        if self.user_disk_size is not None:
            result['UserDiskSize'] = self.user_disk_size

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Bandwidth') is not None:
            self.bandwidth = m.get('Bandwidth')

        if m.get('CpuCoreCount') is not None:
            self.cpu_core_count = m.get('CpuCoreCount')

        if m.get('GpuCount') is not None:
            self.gpu_count = m.get('GpuCount')

        if m.get('ImageUrl') is not None:
            self.image_url = m.get('ImageUrl')

        if m.get('MemorySize') is not None:
            self.memory_size = m.get('MemorySize')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('OsType') is not None:
            self.os_type = m.get('OsType')

        if m.get('SpecCode') is not None:
            self.spec_code = m.get('SpecCode')

        if m.get('SystemDiskSize') is not None:
            self.system_disk_size = m.get('SystemDiskSize')

        if m.get('UserDiskSize') is not None:
            self.user_disk_size = m.get('UserDiskSize')

        return self

class DescribeBusinessRightsResponseBodyDataPackageSku(DaraModel):
    def __init__(
        self,
        code: str = None,
        name: str = None,
        period: str = None,
        period_unit: str = None,
    ):
        self.code = code
        self.name = name
        self.period = period
        self.period_unit = period_unit

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.name is not None:
            result['Name'] = self.name

        if self.period is not None:
            result['Period'] = self.period

        if self.period_unit is not None:
            result['PeriodUnit'] = self.period_unit

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('Period') is not None:
            self.period = m.get('Period')

        if m.get('PeriodUnit') is not None:
            self.period_unit = m.get('PeriodUnit')

        return self

