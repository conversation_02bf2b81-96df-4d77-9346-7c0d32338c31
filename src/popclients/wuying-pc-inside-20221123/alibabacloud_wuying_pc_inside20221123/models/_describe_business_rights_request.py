# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from typing import List


class DescribeBusinessRightsRequest(DaraModel):
    def __init__(
        self,
        right_ids: List[str] = None,
        type: str = None,
    ):
        self.right_ids = right_ids
        self.type = type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.right_ids is not None:
            result['RightIds'] = self.right_ids

        if self.type is not None:
            result['Type'] = self.type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('RightIds') is not None:
            self.right_ids = m.get('RightIds')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        return self

