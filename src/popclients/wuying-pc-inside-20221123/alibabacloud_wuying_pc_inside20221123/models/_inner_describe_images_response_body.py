# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class InnerDescribeImagesResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.InnerDescribeImagesResponseBodyData] = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.InnerDescribeImagesResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class InnerDescribeImagesResponseBodyData(DaraModel):
    def __init__(
        self,
        auth_time: str = None,
        create_user_id: str = None,
        data_disk_size: int = None,
        description: str = None,
        gmt_created: str = None,
        image_id: str = None,
        image_scope: str = None,
        image_source: str = None,
        image_type: str = None,
        is_gpu: bool = None,
        name: str = None,
        os_type: str = None,
        permission: str = None,
        progress: str = None,
        receiver_type: str = None,
        share_codes: List[str] = None,
        shared: bool = None,
        shared_by: str = None,
        source_desktop_type: str = None,
        status: str = None,
        system_disk_size: int = None,
        validation_code: str = None,
    ):
        self.auth_time = auth_time
        self.create_user_id = create_user_id
        self.data_disk_size = data_disk_size
        self.description = description
        self.gmt_created = gmt_created
        self.image_id = image_id
        self.image_scope = image_scope
        self.image_source = image_source
        self.image_type = image_type
        self.is_gpu = is_gpu
        self.name = name
        self.os_type = os_type
        self.permission = permission
        self.progress = progress
        self.receiver_type = receiver_type
        self.share_codes = share_codes
        self.shared = shared
        self.shared_by = shared_by
        self.source_desktop_type = source_desktop_type
        self.status = status
        self.system_disk_size = system_disk_size
        self.validation_code = validation_code

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.auth_time is not None:
            result['AuthTime'] = self.auth_time

        if self.create_user_id is not None:
            result['CreateUserId'] = self.create_user_id

        if self.data_disk_size is not None:
            result['DataDiskSize'] = self.data_disk_size

        if self.description is not None:
            result['Description'] = self.description

        if self.gmt_created is not None:
            result['GmtCreated'] = self.gmt_created

        if self.image_id is not None:
            result['ImageId'] = self.image_id

        if self.image_scope is not None:
            result['ImageScope'] = self.image_scope

        if self.image_source is not None:
            result['ImageSource'] = self.image_source

        if self.image_type is not None:
            result['ImageType'] = self.image_type

        if self.is_gpu is not None:
            result['IsGpu'] = self.is_gpu

        if self.name is not None:
            result['Name'] = self.name

        if self.os_type is not None:
            result['OsType'] = self.os_type

        if self.permission is not None:
            result['Permission'] = self.permission

        if self.progress is not None:
            result['Progress'] = self.progress

        if self.receiver_type is not None:
            result['ReceiverType'] = self.receiver_type

        if self.share_codes is not None:
            result['ShareCodes'] = self.share_codes

        if self.shared is not None:
            result['Shared'] = self.shared

        if self.shared_by is not None:
            result['SharedBy'] = self.shared_by

        if self.source_desktop_type is not None:
            result['SourceDesktopType'] = self.source_desktop_type

        if self.status is not None:
            result['Status'] = self.status

        if self.system_disk_size is not None:
            result['SystemDiskSize'] = self.system_disk_size

        if self.validation_code is not None:
            result['ValidationCode'] = self.validation_code

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AuthTime') is not None:
            self.auth_time = m.get('AuthTime')

        if m.get('CreateUserId') is not None:
            self.create_user_id = m.get('CreateUserId')

        if m.get('DataDiskSize') is not None:
            self.data_disk_size = m.get('DataDiskSize')

        if m.get('Description') is not None:
            self.description = m.get('Description')

        if m.get('GmtCreated') is not None:
            self.gmt_created = m.get('GmtCreated')

        if m.get('ImageId') is not None:
            self.image_id = m.get('ImageId')

        if m.get('ImageScope') is not None:
            self.image_scope = m.get('ImageScope')

        if m.get('ImageSource') is not None:
            self.image_source = m.get('ImageSource')

        if m.get('ImageType') is not None:
            self.image_type = m.get('ImageType')

        if m.get('IsGpu') is not None:
            self.is_gpu = m.get('IsGpu')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('OsType') is not None:
            self.os_type = m.get('OsType')

        if m.get('Permission') is not None:
            self.permission = m.get('Permission')

        if m.get('Progress') is not None:
            self.progress = m.get('Progress')

        if m.get('ReceiverType') is not None:
            self.receiver_type = m.get('ReceiverType')

        if m.get('ShareCodes') is not None:
            self.share_codes = m.get('ShareCodes')

        if m.get('Shared') is not None:
            self.shared = m.get('Shared')

        if m.get('SharedBy') is not None:
            self.shared_by = m.get('SharedBy')

        if m.get('SourceDesktopType') is not None:
            self.source_desktop_type = m.get('SourceDesktopType')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        if m.get('SystemDiskSize') is not None:
            self.system_disk_size = m.get('SystemDiskSize')

        if m.get('ValidationCode') is not None:
            self.validation_code = m.get('ValidationCode')

        return self

