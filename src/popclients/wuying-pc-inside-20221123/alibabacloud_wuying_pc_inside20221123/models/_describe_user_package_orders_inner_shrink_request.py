# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeUserPackageOrdersInnerShrinkRequest(DaraModel):
    def __init__(
        self,
        is_subscribe: bool = None,
        login_ali_uid: int = None,
        order_status: str = None,
        order_status_list_json_shrink: str = None,
        order_type_list_json_shrink: str = None,
        product_type_list_json_shrink: str = None,
        start_date: str = None,
    ):
        self.is_subscribe = is_subscribe
        self.login_ali_uid = login_ali_uid
        self.order_status = order_status
        self.order_status_list_json_shrink = order_status_list_json_shrink
        self.order_type_list_json_shrink = order_type_list_json_shrink
        self.product_type_list_json_shrink = product_type_list_json_shrink
        self.start_date = start_date

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.is_subscribe is not None:
            result['IsSubscribe'] = self.is_subscribe

        if self.login_ali_uid is not None:
            result['LoginAliUid'] = self.login_ali_uid

        if self.order_status is not None:
            result['OrderStatus'] = self.order_status

        if self.order_status_list_json_shrink is not None:
            result['OrderStatusListJson'] = self.order_status_list_json_shrink

        if self.order_type_list_json_shrink is not None:
            result['OrderTypeListJson'] = self.order_type_list_json_shrink

        if self.product_type_list_json_shrink is not None:
            result['ProductTypeListJson'] = self.product_type_list_json_shrink

        if self.start_date is not None:
            result['StartDate'] = self.start_date

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('IsSubscribe') is not None:
            self.is_subscribe = m.get('IsSubscribe')

        if m.get('LoginAliUid') is not None:
            self.login_ali_uid = m.get('LoginAliUid')

        if m.get('OrderStatus') is not None:
            self.order_status = m.get('OrderStatus')

        if m.get('OrderStatusListJson') is not None:
            self.order_status_list_json_shrink = m.get('OrderStatusListJson')

        if m.get('OrderTypeListJson') is not None:
            self.order_type_list_json_shrink = m.get('OrderTypeListJson')

        if m.get('ProductTypeListJson') is not None:
            self.product_type_list_json_shrink = m.get('ProductTypeListJson')

        if m.get('StartDate') is not None:
            self.start_date = m.get('StartDate')

        return self

