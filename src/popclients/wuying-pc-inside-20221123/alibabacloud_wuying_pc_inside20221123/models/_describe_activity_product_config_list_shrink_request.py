# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeActivityProductConfigListShrinkRequest(DaraModel):
    def __init__(
        self,
        activity_product_code_list_json_shrink: str = None,
    ):
        self.activity_product_code_list_json_shrink = activity_product_code_list_json_shrink

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity_product_code_list_json_shrink is not None:
            result['ActivityProductCodeListJson'] = self.activity_product_code_list_json_shrink

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivityProductCodeListJson') is not None:
            self.activity_product_code_list_json_shrink = m.get('ActivityProductCodeListJson')

        return self

