# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribePersonalUsersOwnedBenefitsInnerRequest(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        client_type: str = None,
        customer_activity_id_list: str = None,
        wy_id: str = None,
    ):
        self.ali_uid = ali_uid
        self.client_type = client_type
        self.customer_activity_id_list = customer_activity_id_list
        self.wy_id = wy_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['aliUid'] = self.ali_uid

        if self.client_type is not None:
            result['clientType'] = self.client_type

        if self.customer_activity_id_list is not None:
            result['customerActivityIdList'] = self.customer_activity_id_list

        if self.wy_id is not None:
            result['wyId'] = self.wy_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('aliUid') is not None:
            self.ali_uid = m.get('aliUid')

        if m.get('clientType') is not None:
            self.client_type = m.get('clientType')

        if m.get('customerActivityIdList') is not None:
            self.customer_activity_id_list = m.get('customerActivityIdList')

        if m.get('wyId') is not None:
            self.wy_id = m.get('wyId')

        return self

