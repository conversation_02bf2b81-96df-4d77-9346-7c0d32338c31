# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 


class DescribeOrderMoneyResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeOrderMoneyResponseBodyData = None,
        http_status_code: int = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.http_status_code = http_status_code
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.http_status_code is not None:
            result['HttpStatusCode'] = self.http_status_code

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeOrderMoneyResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('HttpStatusCode') is not None:
            self.http_status_code = m.get('HttpStatusCode')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeOrderMoneyResponseBodyData(DaraModel):
    def __init__(
        self,
        discount_money: float = None,
        original_money: float = None,
        pay_money: float = None,
    ):
        self.discount_money = discount_money
        self.original_money = original_money
        self.pay_money = pay_money

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.discount_money is not None:
            result['DiscountMoney'] = self.discount_money

        if self.original_money is not None:
            result['OriginalMoney'] = self.original_money

        if self.pay_money is not None:
            result['PayMoney'] = self.pay_money

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DiscountMoney') is not None:
            self.discount_money = m.get('DiscountMoney')

        if m.get('OriginalMoney') is not None:
            self.original_money = m.get('OriginalMoney')

        if m.get('PayMoney') is not None:
            self.pay_money = m.get('PayMoney')

        return self

