# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeEventDataSummaryInnerResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeEventDataSummaryInnerResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeEventDataSummaryInnerResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeEventDataSummaryInnerResponseBodyData(DaraModel):
    def __init__(
        self,
        code_summary_infos: List[main_models.DescribeEventDataSummaryInnerResponseBodyDataCodeSummaryInfos] = None,
    ):
        self.code_summary_infos = code_summary_infos

    def validate(self):
        if self.code_summary_infos:
            for v1 in self.code_summary_infos:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['CodeSummaryInfos'] = []
        if self.code_summary_infos is not None:
            for k1 in self.code_summary_infos:
                result['CodeSummaryInfos'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.code_summary_infos = []
        if m.get('CodeSummaryInfos') is not None:
            for k1 in m.get('CodeSummaryInfos'):
                temp_model = main_models.DescribeEventDataSummaryInnerResponseBodyDataCodeSummaryInfos()
                self.code_summary_infos.append(temp_model.from_map(k1))

        return self

class DescribeEventDataSummaryInnerResponseBodyDataCodeSummaryInfos(DaraModel):
    def __init__(
        self,
        code_type: str = None,
        event_code: str = None,
        user_count: int = None,
        visit_count: int = None,
    ):
        self.code_type = code_type
        self.event_code = event_code
        self.user_count = user_count
        self.visit_count = visit_count

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code_type is not None:
            result['CodeType'] = self.code_type

        if self.event_code is not None:
            result['EventCode'] = self.event_code

        if self.user_count is not None:
            result['UserCount'] = self.user_count

        if self.visit_count is not None:
            result['VisitCount'] = self.visit_count

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CodeType') is not None:
            self.code_type = m.get('CodeType')

        if m.get('EventCode') is not None:
            self.event_code = m.get('EventCode')

        if m.get('UserCount') is not None:
            self.user_count = m.get('UserCount')

        if m.get('VisitCount') is not None:
            self.visit_count = m.get('VisitCount')

        return self

