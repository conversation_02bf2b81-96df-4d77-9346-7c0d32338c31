# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeStandardOrdersRequest(DaraModel):
    def __init__(
        self,
        label: str = None,
        page: int = None,
        page_size: int = None,
        query_aliyun_uid: int = None,
        team_ids: str = None,
    ):
        self.label = label
        self.page = page
        self.page_size = page_size
        # This parameter is required.
        self.query_aliyun_uid = query_aliyun_uid
        self.team_ids = team_ids

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.label is not None:
            result['Label'] = self.label

        if self.page is not None:
            result['Page'] = self.page

        if self.page_size is not None:
            result['PageSize'] = self.page_size

        if self.query_aliyun_uid is not None:
            result['QueryAliyunUid'] = self.query_aliyun_uid

        if self.team_ids is not None:
            result['TeamIds'] = self.team_ids

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Label') is not None:
            self.label = m.get('Label')

        if m.get('Page') is not None:
            self.page = m.get('Page')

        if m.get('PageSize') is not None:
            self.page_size = m.get('PageSize')

        if m.get('QueryAliyunUid') is not None:
            self.query_aliyun_uid = m.get('QueryAliyunUid')

        if m.get('TeamIds') is not None:
            self.team_ids = m.get('TeamIds')

        return self

