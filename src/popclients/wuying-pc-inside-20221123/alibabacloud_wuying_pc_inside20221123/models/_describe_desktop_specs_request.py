# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeDesktopSpecsRequest(DaraModel):
    def __init__(
        self,
        specs_type: str = None,
    ):
        self.specs_type = specs_type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.specs_type is not None:
            result['SpecsType'] = self.specs_type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('SpecsType') is not None:
            self.specs_type = m.get('SpecsType')

        return self

