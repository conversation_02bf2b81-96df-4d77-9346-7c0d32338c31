# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from typing import List


class DescribeActivityProductConfigListRequest(DaraModel):
    def __init__(
        self,
        activity_product_code_list_json: List[str] = None,
    ):
        self.activity_product_code_list_json = activity_product_code_list_json

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity_product_code_list_json is not None:
            result['ActivityProductCodeListJson'] = self.activity_product_code_list_json

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivityProductCodeListJson') is not None:
            self.activity_product_code_list_json = m.get('ActivityProductCodeListJson')

        return self

