# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 


class InnerDescribeTeamDesktopCountResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.InnerDescribeTeamDesktopCountResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.InnerDescribeTeamDesktopCountResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class InnerDescribeTeamDesktopCountResponseBodyData(DaraModel):
    def __init__(
        self,
        assigned_count: int = None,
        inactivated_count: int = None,
        recycled_count: int = None,
        team_id: str = None,
        total_count: int = None,
    ):
        self.assigned_count = assigned_count
        self.inactivated_count = inactivated_count
        self.recycled_count = recycled_count
        self.team_id = team_id
        self.total_count = total_count

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.assigned_count is not None:
            result['AssignedCount'] = self.assigned_count

        if self.inactivated_count is not None:
            result['InactivatedCount'] = self.inactivated_count

        if self.recycled_count is not None:
            result['RecycledCount'] = self.recycled_count

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        if self.total_count is not None:
            result['TotalCount'] = self.total_count

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AssignedCount') is not None:
            self.assigned_count = m.get('AssignedCount')

        if m.get('InactivatedCount') is not None:
            self.inactivated_count = m.get('InactivatedCount')

        if m.get('RecycledCount') is not None:
            self.recycled_count = m.get('RecycledCount')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        if m.get('TotalCount') is not None:
            self.total_count = m.get('TotalCount')

        return self

