# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class CreateBenefitPoolRequest(DaraModel):
    def __init__(
        self,
        approval_param: main_models.CreateBenefitPoolRequestApprovalParam = None,
        body: List[main_models.CreateBenefitPoolRequestBody] = None,
        channel_param: main_models.CreateBenefitPoolRequestChannelParam = None,
        description: str = None,
        expire_time: str = None,
        name: str = None,
    ):
        self.approval_param = approval_param
        self.body = body
        self.channel_param = channel_param
        self.description = description
        self.expire_time = expire_time
        self.name = name

    def validate(self):
        if self.approval_param:
            self.approval_param.validate()
        if self.body:
            for v1 in self.body:
                 if v1:
                    v1.validate()
        if self.channel_param:
            self.channel_param.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.approval_param is not None:
            result['approvalParam'] = self.approval_param.to_map()

        result['body'] = []
        if self.body is not None:
            for k1 in self.body:
                result['body'].append(k1.to_map() if k1 else None)

        if self.channel_param is not None:
            result['channelParam'] = self.channel_param.to_map()

        if self.description is not None:
            result['description'] = self.description

        if self.expire_time is not None:
            result['expireTime'] = self.expire_time

        if self.name is not None:
            result['name'] = self.name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('approvalParam') is not None:
            temp_model = main_models.CreateBenefitPoolRequestApprovalParam()
            self.approval_param = temp_model.from_map(m.get('approvalParam'))

        self.body = []
        if m.get('body') is not None:
            for k1 in m.get('body'):
                temp_model = main_models.CreateBenefitPoolRequestBody()
                self.body.append(temp_model.from_map(k1))

        if m.get('channelParam') is not None:
            temp_model = main_models.CreateBenefitPoolRequestChannelParam()
            self.channel_param = temp_model.from_map(m.get('channelParam'))

        if m.get('description') is not None:
            self.description = m.get('description')

        if m.get('expireTime') is not None:
            self.expire_time = m.get('expireTime')

        if m.get('name') is not None:
            self.name = m.get('name')

        return self

class CreateBenefitPoolRequestChannelParam(DaraModel):
    def __init__(
        self,
        channel_id: str = None,
    ):
        self.channel_id = channel_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.channel_id is not None:
            result['channelId'] = self.channel_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('channelId') is not None:
            self.channel_id = m.get('channelId')

        return self

class CreateBenefitPoolRequestBody(DaraModel):
    def __init__(
        self,
        amount: int = None,
        benefit_id: str = None,
    ):
        self.amount = amount
        self.benefit_id = benefit_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.amount is not None:
            result['amount'] = self.amount

        if self.benefit_id is not None:
            result['benefitId'] = self.benefit_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('amount') is not None:
            self.amount = m.get('amount')

        if m.get('benefitId') is not None:
            self.benefit_id = m.get('benefitId')

        return self

class CreateBenefitPoolRequestApprovalParam(DaraModel):
    def __init__(
        self,
        approver: str = None,
        operator: str = None,
        proposer: str = None,
    ):
        self.approver = approver
        self.operator = operator
        self.proposer = proposer

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.approver is not None:
            result['approver'] = self.approver

        if self.operator is not None:
            result['operator'] = self.operator

        if self.proposer is not None:
            result['proposer'] = self.proposer

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('approver') is not None:
            self.approver = m.get('approver')

        if m.get('operator') is not None:
            self.operator = m.get('operator')

        if m.get('proposer') is not None:
            self.proposer = m.get('proposer')

        return self

