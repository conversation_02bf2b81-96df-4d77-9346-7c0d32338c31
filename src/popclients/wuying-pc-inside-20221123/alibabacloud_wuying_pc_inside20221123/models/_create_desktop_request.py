# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateDesktopRequest(DaraModel):
    def __init__(
        self,
        amount: int = None,
        channel_id: str = None,
        charge_type: str = None,
        create_user_id: str = None,
        desktop_id: str = None,
        desktop_label: str = None,
        desktop_name: str = None,
        desktop_region_id: str = None,
        desktop_spec_code: str = None,
        desktop_type: str = None,
        distribution_id: str = None,
        end_user_id: str = None,
        expire_time: str = None,
        login_ali_uid: int = None,
        order_id: str = None,
        origin_desktop_city: str = None,
        period: int = None,
        period_unit: str = None,
        use_duration: int = None,
        user_id: str = None,
    ):
        self.amount = amount
        self.channel_id = channel_id
        self.charge_type = charge_type
        self.create_user_id = create_user_id
        self.desktop_id = desktop_id
        self.desktop_label = desktop_label
        self.desktop_name = desktop_name
        self.desktop_region_id = desktop_region_id
        self.desktop_spec_code = desktop_spec_code
        self.desktop_type = desktop_type
        self.distribution_id = distribution_id
        self.end_user_id = end_user_id
        self.expire_time = expire_time
        self.login_ali_uid = login_ali_uid
        self.order_id = order_id
        self.origin_desktop_city = origin_desktop_city
        self.period = period
        self.period_unit = period_unit
        self.use_duration = use_duration
        self.user_id = user_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.amount is not None:
            result['Amount'] = self.amount

        if self.channel_id is not None:
            result['ChannelId'] = self.channel_id

        if self.charge_type is not None:
            result['ChargeType'] = self.charge_type

        if self.create_user_id is not None:
            result['CreateUserId'] = self.create_user_id

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.desktop_label is not None:
            result['DesktopLabel'] = self.desktop_label

        if self.desktop_name is not None:
            result['DesktopName'] = self.desktop_name

        if self.desktop_region_id is not None:
            result['DesktopRegionId'] = self.desktop_region_id

        if self.desktop_spec_code is not None:
            result['DesktopSpecCode'] = self.desktop_spec_code

        if self.desktop_type is not None:
            result['DesktopType'] = self.desktop_type

        if self.distribution_id is not None:
            result['DistributionId'] = self.distribution_id

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.expire_time is not None:
            result['ExpireTime'] = self.expire_time

        if self.login_ali_uid is not None:
            result['LoginAliUid'] = self.login_ali_uid

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.origin_desktop_city is not None:
            result['OriginDesktopCity'] = self.origin_desktop_city

        if self.period is not None:
            result['Period'] = self.period

        if self.period_unit is not None:
            result['PeriodUnit'] = self.period_unit

        if self.use_duration is not None:
            result['UseDuration'] = self.use_duration

        if self.user_id is not None:
            result['UserId'] = self.user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Amount') is not None:
            self.amount = m.get('Amount')

        if m.get('ChannelId') is not None:
            self.channel_id = m.get('ChannelId')

        if m.get('ChargeType') is not None:
            self.charge_type = m.get('ChargeType')

        if m.get('CreateUserId') is not None:
            self.create_user_id = m.get('CreateUserId')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('DesktopLabel') is not None:
            self.desktop_label = m.get('DesktopLabel')

        if m.get('DesktopName') is not None:
            self.desktop_name = m.get('DesktopName')

        if m.get('DesktopRegionId') is not None:
            self.desktop_region_id = m.get('DesktopRegionId')

        if m.get('DesktopSpecCode') is not None:
            self.desktop_spec_code = m.get('DesktopSpecCode')

        if m.get('DesktopType') is not None:
            self.desktop_type = m.get('DesktopType')

        if m.get('DistributionId') is not None:
            self.distribution_id = m.get('DistributionId')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('ExpireTime') is not None:
            self.expire_time = m.get('ExpireTime')

        if m.get('LoginAliUid') is not None:
            self.login_ali_uid = m.get('LoginAliUid')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('OriginDesktopCity') is not None:
            self.origin_desktop_city = m.get('OriginDesktopCity')

        if m.get('Period') is not None:
            self.period = m.get('Period')

        if m.get('PeriodUnit') is not None:
            self.period_unit = m.get('PeriodUnit')

        if m.get('UseDuration') is not None:
            self.use_duration = m.get('UseDuration')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        return self

