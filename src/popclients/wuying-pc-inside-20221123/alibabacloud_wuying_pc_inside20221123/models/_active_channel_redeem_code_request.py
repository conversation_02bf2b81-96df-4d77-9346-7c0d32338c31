# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class ActiveChannelRedeemCodeRequest(DaraModel):
    def __init__(
        self,
        active_action: str = None,
        active_type: str = None,
        city_name: str = None,
        desktop_id: str = None,
        desktop_name: str = None,
        redeem_code: str = None,
        team_id: str = None,
        user_id: str = None,
    ):
        self.active_action = active_action
        self.active_type = active_type
        self.city_name = city_name
        self.desktop_id = desktop_id
        self.desktop_name = desktop_name
        self.redeem_code = redeem_code
        self.team_id = team_id
        self.user_id = user_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.active_action is not None:
            result['ActiveAction'] = self.active_action

        if self.active_type is not None:
            result['ActiveType'] = self.active_type

        if self.city_name is not None:
            result['CityName'] = self.city_name

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.desktop_name is not None:
            result['DesktopName'] = self.desktop_name

        if self.redeem_code is not None:
            result['RedeemCode'] = self.redeem_code

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        if self.user_id is not None:
            result['UserId'] = self.user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActiveAction') is not None:
            self.active_action = m.get('ActiveAction')

        if m.get('ActiveType') is not None:
            self.active_type = m.get('ActiveType')

        if m.get('CityName') is not None:
            self.city_name = m.get('CityName')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('DesktopName') is not None:
            self.desktop_name = m.get('DesktopName')

        if m.get('RedeemCode') is not None:
            self.redeem_code = m.get('RedeemCode')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        return self

