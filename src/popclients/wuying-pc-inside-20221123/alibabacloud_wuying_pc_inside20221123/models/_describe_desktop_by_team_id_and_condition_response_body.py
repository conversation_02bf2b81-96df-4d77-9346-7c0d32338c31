# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeDesktopByTeamIdAndConditionResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.DescribeDesktopByTeamIdAndConditionResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.DescribeDesktopByTeamIdAndConditionResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeDesktopByTeamIdAndConditionResponseBodyData(DaraModel):
    def __init__(
        self,
        current: int = None,
        records: List[main_models.DescribeDesktopByTeamIdAndConditionResponseBodyDataRecords] = None,
        size: int = None,
        total: int = None,
    ):
        self.current = current
        self.records = records
        self.size = size
        self.total = total

    def validate(self):
        if self.records:
            for v1 in self.records:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.current is not None:
            result['Current'] = self.current

        result['Records'] = []
        if self.records is not None:
            for k1 in self.records:
                result['Records'].append(k1.to_map() if k1 else None)

        if self.size is not None:
            result['Size'] = self.size

        if self.total is not None:
            result['Total'] = self.total

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Current') is not None:
            self.current = m.get('Current')

        self.records = []
        if m.get('Records') is not None:
            for k1 in m.get('Records'):
                temp_model = main_models.DescribeDesktopByTeamIdAndConditionResponseBodyDataRecords()
                self.records.append(temp_model.from_map(k1))

        if m.get('Size') is not None:
            self.size = m.get('Size')

        if m.get('Total') is not None:
            self.total = m.get('Total')

        return self

class DescribeDesktopByTeamIdAndConditionResponseBodyDataRecords(DaraModel):
    def __init__(
        self,
        activate_status: str = None,
        ali_uid: str = None,
        bundle_id: str = None,
        bundle_sku_code: str = None,
        desktop_center_id: str = None,
        desktop_label: str = None,
        desktop_pkg_id: str = None,
        desktop_region_id: str = None,
        desktop_type: str = None,
        gmt_created: str = None,
        order_id: str = None,
        team_id: str = None,
    ):
        self.activate_status = activate_status
        self.ali_uid = ali_uid
        self.bundle_id = bundle_id
        self.bundle_sku_code = bundle_sku_code
        self.desktop_center_id = desktop_center_id
        self.desktop_label = desktop_label
        self.desktop_pkg_id = desktop_pkg_id
        self.desktop_region_id = desktop_region_id
        self.desktop_type = desktop_type
        self.gmt_created = gmt_created
        self.order_id = order_id
        self.team_id = team_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activate_status is not None:
            result['ActivateStatus'] = self.activate_status

        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.bundle_id is not None:
            result['BundleId'] = self.bundle_id

        if self.bundle_sku_code is not None:
            result['BundleSkuCode'] = self.bundle_sku_code

        if self.desktop_center_id is not None:
            result['DesktopCenterId'] = self.desktop_center_id

        if self.desktop_label is not None:
            result['DesktopLabel'] = self.desktop_label

        if self.desktop_pkg_id is not None:
            result['DesktopPkgId'] = self.desktop_pkg_id

        if self.desktop_region_id is not None:
            result['DesktopRegionId'] = self.desktop_region_id

        if self.desktop_type is not None:
            result['DesktopType'] = self.desktop_type

        if self.gmt_created is not None:
            result['GmtCreated'] = self.gmt_created

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivateStatus') is not None:
            self.activate_status = m.get('ActivateStatus')

        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('BundleId') is not None:
            self.bundle_id = m.get('BundleId')

        if m.get('BundleSkuCode') is not None:
            self.bundle_sku_code = m.get('BundleSkuCode')

        if m.get('DesktopCenterId') is not None:
            self.desktop_center_id = m.get('DesktopCenterId')

        if m.get('DesktopLabel') is not None:
            self.desktop_label = m.get('DesktopLabel')

        if m.get('DesktopPkgId') is not None:
            self.desktop_pkg_id = m.get('DesktopPkgId')

        if m.get('DesktopRegionId') is not None:
            self.desktop_region_id = m.get('DesktopRegionId')

        if m.get('DesktopType') is not None:
            self.desktop_type = m.get('DesktopType')

        if m.get('GmtCreated') is not None:
            self.gmt_created = m.get('GmtCreated')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        return self

