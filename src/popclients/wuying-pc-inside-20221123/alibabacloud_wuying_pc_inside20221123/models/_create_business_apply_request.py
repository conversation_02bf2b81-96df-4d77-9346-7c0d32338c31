# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from typing import List


class CreateBusinessApplyRequest(DaraModel):
    def __init__(
        self,
        aliyun_uid: int = None,
        apply_action: str = None,
        classroom_id: str = None,
        desktop_ids: List[str] = None,
        message: str = None,
        renew_package_code: str = None,
        type: str = None,
    ):
        self.aliyun_uid = aliyun_uid
        self.apply_action = apply_action
        self.classroom_id = classroom_id
        self.desktop_ids = desktop_ids
        self.message = message
        self.renew_package_code = renew_package_code
        self.type = type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.aliyun_uid is not None:
            result['AliyunUid'] = self.aliyun_uid

        if self.apply_action is not None:
            result['ApplyAction'] = self.apply_action

        if self.classroom_id is not None:
            result['ClassroomId'] = self.classroom_id

        if self.desktop_ids is not None:
            result['DesktopIds'] = self.desktop_ids

        if self.message is not None:
            result['Message'] = self.message

        if self.renew_package_code is not None:
            result['RenewPackageCode'] = self.renew_package_code

        if self.type is not None:
            result['Type'] = self.type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliyunUid') is not None:
            self.aliyun_uid = m.get('AliyunUid')

        if m.get('ApplyAction') is not None:
            self.apply_action = m.get('ApplyAction')

        if m.get('ClassroomId') is not None:
            self.classroom_id = m.get('ClassroomId')

        if m.get('DesktopIds') is not None:
            self.desktop_ids = m.get('DesktopIds')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RenewPackageCode') is not None:
            self.renew_package_code = m.get('RenewPackageCode')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        return self

