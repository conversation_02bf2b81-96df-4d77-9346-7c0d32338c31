# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeDesktopsResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.DescribeDesktopsResponseBodyData] = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.DescribeDesktopsResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class DescribeDesktopsResponseBodyData(DaraModel):
    def __init__(
        self,
        activate_status: str = None,
        assignment_status: str = None,
        change_type: str = None,
        charge_label: str = None,
        charge_name: str = None,
        connection_status: str = None,
        cpu: int = None,
        create_id: str = None,
        create_user_id: str = None,
        deletion_time: str = None,
        desktop_center_id: str = None,
        desktop_id: str = None,
        desktop_name: str = None,
        desktop_spec_code: str = None,
        desktop_status: str = None,
        disks: List[main_models.DescribeDesktopsResponseBodyDataDisks] = None,
        management_flag: str = None,
        memory: int = None,
        order_id: str = None,
        region_id: str = None,
        spec_name: str = None,
        user_id: str = None,
    ):
        self.activate_status = activate_status
        self.assignment_status = assignment_status
        self.change_type = change_type
        self.charge_label = charge_label
        self.charge_name = charge_name
        self.connection_status = connection_status
        self.cpu = cpu
        self.create_id = create_id
        self.create_user_id = create_user_id
        self.deletion_time = deletion_time
        self.desktop_center_id = desktop_center_id
        self.desktop_id = desktop_id
        self.desktop_name = desktop_name
        self.desktop_spec_code = desktop_spec_code
        self.desktop_status = desktop_status
        self.disks = disks
        self.management_flag = management_flag
        self.memory = memory
        self.order_id = order_id
        self.region_id = region_id
        self.spec_name = spec_name
        self.user_id = user_id

    def validate(self):
        if self.disks:
            for v1 in self.disks:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activate_status is not None:
            result['ActivateStatus'] = self.activate_status

        if self.assignment_status is not None:
            result['AssignmentStatus'] = self.assignment_status

        if self.change_type is not None:
            result['ChangeType'] = self.change_type

        if self.charge_label is not None:
            result['ChargeLabel'] = self.charge_label

        if self.charge_name is not None:
            result['ChargeName'] = self.charge_name

        if self.connection_status is not None:
            result['ConnectionStatus'] = self.connection_status

        if self.cpu is not None:
            result['Cpu'] = self.cpu

        if self.create_id is not None:
            result['CreateId'] = self.create_id

        if self.create_user_id is not None:
            result['CreateUserId'] = self.create_user_id

        if self.deletion_time is not None:
            result['DeletionTime'] = self.deletion_time

        if self.desktop_center_id is not None:
            result['DesktopCenterId'] = self.desktop_center_id

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.desktop_name is not None:
            result['DesktopName'] = self.desktop_name

        if self.desktop_spec_code is not None:
            result['DesktopSpecCode'] = self.desktop_spec_code

        if self.desktop_status is not None:
            result['DesktopStatus'] = self.desktop_status

        result['Disks'] = []
        if self.disks is not None:
            for k1 in self.disks:
                result['Disks'].append(k1.to_map() if k1 else None)

        if self.management_flag is not None:
            result['ManagementFlag'] = self.management_flag

        if self.memory is not None:
            result['Memory'] = self.memory

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.region_id is not None:
            result['RegionId'] = self.region_id

        if self.spec_name is not None:
            result['SpecName'] = self.spec_name

        if self.user_id is not None:
            result['UserId'] = self.user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivateStatus') is not None:
            self.activate_status = m.get('ActivateStatus')

        if m.get('AssignmentStatus') is not None:
            self.assignment_status = m.get('AssignmentStatus')

        if m.get('ChangeType') is not None:
            self.change_type = m.get('ChangeType')

        if m.get('ChargeLabel') is not None:
            self.charge_label = m.get('ChargeLabel')

        if m.get('ChargeName') is not None:
            self.charge_name = m.get('ChargeName')

        if m.get('ConnectionStatus') is not None:
            self.connection_status = m.get('ConnectionStatus')

        if m.get('Cpu') is not None:
            self.cpu = m.get('Cpu')

        if m.get('CreateId') is not None:
            self.create_id = m.get('CreateId')

        if m.get('CreateUserId') is not None:
            self.create_user_id = m.get('CreateUserId')

        if m.get('DeletionTime') is not None:
            self.deletion_time = m.get('DeletionTime')

        if m.get('DesktopCenterId') is not None:
            self.desktop_center_id = m.get('DesktopCenterId')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('DesktopName') is not None:
            self.desktop_name = m.get('DesktopName')

        if m.get('DesktopSpecCode') is not None:
            self.desktop_spec_code = m.get('DesktopSpecCode')

        if m.get('DesktopStatus') is not None:
            self.desktop_status = m.get('DesktopStatus')

        self.disks = []
        if m.get('Disks') is not None:
            for k1 in m.get('Disks'):
                temp_model = main_models.DescribeDesktopsResponseBodyDataDisks()
                self.disks.append(temp_model.from_map(k1))

        if m.get('ManagementFlag') is not None:
            self.management_flag = m.get('ManagementFlag')

        if m.get('Memory') is not None:
            self.memory = m.get('Memory')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('RegionId') is not None:
            self.region_id = m.get('RegionId')

        if m.get('SpecName') is not None:
            self.spec_name = m.get('SpecName')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        return self

class DescribeDesktopsResponseBodyDataDisks(DaraModel):
    def __init__(
        self,
        disk_id: str = None,
        disk_size: int = None,
        disk_type: str = None,
    ):
        self.disk_id = disk_id
        self.disk_size = disk_size
        self.disk_type = disk_type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.disk_id is not None:
            result['DiskId'] = self.disk_id

        if self.disk_size is not None:
            result['DiskSize'] = self.disk_size

        if self.disk_type is not None:
            result['DiskType'] = self.disk_type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DiskId') is not None:
            self.disk_id = m.get('DiskId')

        if m.get('DiskSize') is not None:
            self.disk_size = m.get('DiskSize')

        if m.get('DiskType') is not None:
            self.disk_type = m.get('DiskType')

        return self

