# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class ModifyCustomerActivityRequest(DaraModel):
    def __init__(
        self,
        activity_id: str = None,
        button_text: str = None,
        corner_marker: str = None,
        description: str = None,
        end_time: str = None,
        fixed_content_json_str: str = None,
        hyper_link: str = None,
        mini_app_hyper_link: str = None,
        name: str = None,
        public_visibility: str = None,
        start_time: str = None,
        status: str = None,
        sub_title: str = None,
        title: str = None,
    ):
        self.activity_id = activity_id
        self.button_text = button_text
        self.corner_marker = corner_marker
        self.description = description
        self.end_time = end_time
        self.fixed_content_json_str = fixed_content_json_str
        self.hyper_link = hyper_link
        self.mini_app_hyper_link = mini_app_hyper_link
        self.name = name
        self.public_visibility = public_visibility
        self.start_time = start_time
        self.status = status
        self.sub_title = sub_title
        self.title = title

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity_id is not None:
            result['ActivityId'] = self.activity_id

        if self.button_text is not None:
            result['ButtonText'] = self.button_text

        if self.corner_marker is not None:
            result['CornerMarker'] = self.corner_marker

        if self.description is not None:
            result['Description'] = self.description

        if self.end_time is not None:
            result['EndTime'] = self.end_time

        if self.fixed_content_json_str is not None:
            result['FixedContentJsonStr'] = self.fixed_content_json_str

        if self.hyper_link is not None:
            result['HyperLink'] = self.hyper_link

        if self.mini_app_hyper_link is not None:
            result['MiniAppHyperLink'] = self.mini_app_hyper_link

        if self.name is not None:
            result['Name'] = self.name

        if self.public_visibility is not None:
            result['PublicVisibility'] = self.public_visibility

        if self.start_time is not None:
            result['StartTime'] = self.start_time

        if self.status is not None:
            result['Status'] = self.status

        if self.sub_title is not None:
            result['SubTitle'] = self.sub_title

        if self.title is not None:
            result['Title'] = self.title

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivityId') is not None:
            self.activity_id = m.get('ActivityId')

        if m.get('ButtonText') is not None:
            self.button_text = m.get('ButtonText')

        if m.get('CornerMarker') is not None:
            self.corner_marker = m.get('CornerMarker')

        if m.get('Description') is not None:
            self.description = m.get('Description')

        if m.get('EndTime') is not None:
            self.end_time = m.get('EndTime')

        if m.get('FixedContentJsonStr') is not None:
            self.fixed_content_json_str = m.get('FixedContentJsonStr')

        if m.get('HyperLink') is not None:
            self.hyper_link = m.get('HyperLink')

        if m.get('MiniAppHyperLink') is not None:
            self.mini_app_hyper_link = m.get('MiniAppHyperLink')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('PublicVisibility') is not None:
            self.public_visibility = m.get('PublicVisibility')

        if m.get('StartTime') is not None:
            self.start_time = m.get('StartTime')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        if m.get('SubTitle') is not None:
            self.sub_title = m.get('SubTitle')

        if m.get('Title') is not None:
            self.title = m.get('Title')

        return self

