# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class TransferDesktopsRequest(DaraModel):
    def __init__(
        self,
        amount: int = None,
        order_id: str = None,
        receiver_type: str = None,
        source_channel_id: str = None,
        target_city_name: str = None,
        target_desktop_name: str = None,
        target_personal_ali_uid: int = None,
        target_personal_end_user_id: str = None,
        target_team_id: str = None,
    ):
        self.amount = amount
        self.order_id = order_id
        self.receiver_type = receiver_type
        self.source_channel_id = source_channel_id
        self.target_city_name = target_city_name
        self.target_desktop_name = target_desktop_name
        self.target_personal_ali_uid = target_personal_ali_uid
        self.target_personal_end_user_id = target_personal_end_user_id
        self.target_team_id = target_team_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.amount is not None:
            result['Amount'] = self.amount

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.receiver_type is not None:
            result['ReceiverType'] = self.receiver_type

        if self.source_channel_id is not None:
            result['SourceChannelId'] = self.source_channel_id

        if self.target_city_name is not None:
            result['TargetCityName'] = self.target_city_name

        if self.target_desktop_name is not None:
            result['TargetDesktopName'] = self.target_desktop_name

        if self.target_personal_ali_uid is not None:
            result['TargetPersonalAliUid'] = self.target_personal_ali_uid

        if self.target_personal_end_user_id is not None:
            result['TargetPersonalEndUserId'] = self.target_personal_end_user_id

        if self.target_team_id is not None:
            result['TargetTeamId'] = self.target_team_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Amount') is not None:
            self.amount = m.get('Amount')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('ReceiverType') is not None:
            self.receiver_type = m.get('ReceiverType')

        if m.get('SourceChannelId') is not None:
            self.source_channel_id = m.get('SourceChannelId')

        if m.get('TargetCityName') is not None:
            self.target_city_name = m.get('TargetCityName')

        if m.get('TargetDesktopName') is not None:
            self.target_desktop_name = m.get('TargetDesktopName')

        if m.get('TargetPersonalAliUid') is not None:
            self.target_personal_ali_uid = m.get('TargetPersonalAliUid')

        if m.get('TargetPersonalEndUserId') is not None:
            self.target_personal_end_user_id = m.get('TargetPersonalEndUserId')

        if m.get('TargetTeamId') is not None:
            self.target_team_id = m.get('TargetTeamId')

        return self

