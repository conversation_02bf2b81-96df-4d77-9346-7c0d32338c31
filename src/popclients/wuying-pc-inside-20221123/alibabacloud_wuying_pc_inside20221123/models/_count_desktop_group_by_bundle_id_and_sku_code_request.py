# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CountDesktopGroupByBundleIdAndSkuCodeRequest(DaraModel):
    def __init__(
        self,
        activate_status_condition: str = None,
        bundle_id_condition: str = None,
        bundle_sku_code_condition: str = None,
        desktop_type_condition: str = None,
        team_id: str = None,
    ):
        self.activate_status_condition = activate_status_condition
        self.bundle_id_condition = bundle_id_condition
        self.bundle_sku_code_condition = bundle_sku_code_condition
        self.desktop_type_condition = desktop_type_condition
        # This parameter is required.
        self.team_id = team_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activate_status_condition is not None:
            result['ActivateStatusCondition'] = self.activate_status_condition

        if self.bundle_id_condition is not None:
            result['BundleIdCondition'] = self.bundle_id_condition

        if self.bundle_sku_code_condition is not None:
            result['BundleSkuCodeCondition'] = self.bundle_sku_code_condition

        if self.desktop_type_condition is not None:
            result['DesktopTypeCondition'] = self.desktop_type_condition

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ActivateStatusCondition') is not None:
            self.activate_status_condition = m.get('ActivateStatusCondition')

        if m.get('BundleIdCondition') is not None:
            self.bundle_id_condition = m.get('BundleIdCondition')

        if m.get('BundleSkuCodeCondition') is not None:
            self.bundle_sku_code_condition = m.get('BundleSkuCodeCondition')

        if m.get('DesktopTypeCondition') is not None:
            self.desktop_type_condition = m.get('DesktopTypeCondition')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        return self

