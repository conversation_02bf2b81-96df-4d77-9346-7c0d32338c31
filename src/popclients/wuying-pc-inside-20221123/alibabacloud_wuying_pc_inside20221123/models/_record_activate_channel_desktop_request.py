# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class RecordActivateChannelDesktopRequest(DaraModel):
    def __init__(
        self,
        center_instance_ids: str = None,
        desktop_ids: str = None,
        order_id: str = None,
        package_amount: int = None,
        personal_aliyun_uid: str = None,
        record_desktop_action: str = None,
        team_id: str = None,
    ):
        self.center_instance_ids = center_instance_ids
        self.desktop_ids = desktop_ids
        # This parameter is required.
        self.order_id = order_id
        self.package_amount = package_amount
        self.personal_aliyun_uid = personal_aliyun_uid
        self.record_desktop_action = record_desktop_action
        self.team_id = team_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.center_instance_ids is not None:
            result['CenterInstanceIds'] = self.center_instance_ids

        if self.desktop_ids is not None:
            result['DesktopIds'] = self.desktop_ids

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.package_amount is not None:
            result['PackageAmount'] = self.package_amount

        if self.personal_aliyun_uid is not None:
            result['PersonalAliyunUid'] = self.personal_aliyun_uid

        if self.record_desktop_action is not None:
            result['RecordDesktopAction'] = self.record_desktop_action

        if self.team_id is not None:
            result['TeamId'] = self.team_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CenterInstanceIds') is not None:
            self.center_instance_ids = m.get('CenterInstanceIds')

        if m.get('DesktopIds') is not None:
            self.desktop_ids = m.get('DesktopIds')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('PackageAmount') is not None:
            self.package_amount = m.get('PackageAmount')

        if m.get('PersonalAliyunUid') is not None:
            self.personal_aliyun_uid = m.get('PersonalAliyunUid')

        if m.get('RecordDesktopAction') is not None:
            self.record_desktop_action = m.get('RecordDesktopAction')

        if m.get('TeamId') is not None:
            self.team_id = m.get('TeamId')

        return self

