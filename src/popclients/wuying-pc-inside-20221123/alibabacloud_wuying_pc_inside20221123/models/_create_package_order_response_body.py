# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class CreatePackageOrderResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: main_models.CreatePackageOrderResponseBodyData = None,
        message: str = None,
        request_id: str = None,
        trace_id: str = None,
    ):
        self.code = code
        self.data = data
        self.message = message
        self.request_id = request_id
        self.trace_id = trace_id

    def validate(self):
        if self.data:
            self.data.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.data is not None:
            result['Data'] = self.data.to_map()

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.trace_id is not None:
            result['TraceId'] = self.trace_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Data') is not None:
            temp_model = main_models.CreatePackageOrderResponseBodyData()
            self.data = temp_model.from_map(m.get('Data'))

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TraceId') is not None:
            self.trace_id = m.get('TraceId')

        return self

class CreatePackageOrderResponseBodyData(DaraModel):
    def __init__(
        self,
        order_detail_list: List[main_models.CreatePackageOrderResponseBodyDataOrderDetailList] = None,
        order_id: str = None,
        pay_link: str = None,
        resource_id: str = None,
    ):
        self.order_detail_list = order_detail_list
        self.order_id = order_id
        self.pay_link = pay_link
        self.resource_id = resource_id

    def validate(self):
        if self.order_detail_list:
            for v1 in self.order_detail_list:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['OrderDetailList'] = []
        if self.order_detail_list is not None:
            for k1 in self.order_detail_list:
                result['OrderDetailList'].append(k1.to_map() if k1 else None)

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.pay_link is not None:
            result['PayLink'] = self.pay_link

        if self.resource_id is not None:
            result['ResourceId'] = self.resource_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.order_detail_list = []
        if m.get('OrderDetailList') is not None:
            for k1 in m.get('OrderDetailList'):
                temp_model = main_models.CreatePackageOrderResponseBodyDataOrderDetailList()
                self.order_detail_list.append(temp_model.from_map(k1))

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('PayLink') is not None:
            self.pay_link = m.get('PayLink')

        if m.get('ResourceId') is not None:
            self.resource_id = m.get('ResourceId')

        return self

class CreatePackageOrderResponseBodyDataOrderDetailList(DaraModel):
    def __init__(
        self,
        instance_ids: List[str] = None,
        order_id: int = None,
    ):
        self.instance_ids = instance_ids
        self.order_id = order_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.instance_ids is not None:
            result['InstanceIds'] = self.instance_ids

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('InstanceIds') is not None:
            self.instance_ids = m.get('InstanceIds')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        return self

