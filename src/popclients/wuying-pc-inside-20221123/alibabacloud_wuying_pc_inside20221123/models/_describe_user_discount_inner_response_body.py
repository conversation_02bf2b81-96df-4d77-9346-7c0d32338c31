# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribeUserDiscountInnerResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.DescribeUserDiscountInnerResponseBodyData] = None,
        err_code: str = None,
        err_msg: str = None,
        request_id: str = None,
    ):
        self.code = code
        self.data = data
        self.err_code = err_code
        self.err_msg = err_msg
        self.request_id = request_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['code'] = self.code

        result['data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['data'].append(k1.to_map() if k1 else None)

        if self.err_code is not None:
            result['errCode'] = self.err_code

        if self.err_msg is not None:
            result['errMsg'] = self.err_msg

        if self.request_id is not None:
            result['requestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('code') is not None:
            self.code = m.get('code')

        self.data = []
        if m.get('data') is not None:
            for k1 in m.get('data'):
                temp_model = main_models.DescribeUserDiscountInnerResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('errCode') is not None:
            self.err_code = m.get('errCode')

        if m.get('errMsg') is not None:
            self.err_msg = m.get('errMsg')

        if m.get('requestId') is not None:
            self.request_id = m.get('requestId')

        return self

class DescribeUserDiscountInnerResponseBodyData(DaraModel):
    def __init__(
        self,
        activity_end_time: str = None,
        activity_id: str = None,
        activity_user_discount_countdown_seconds: int = None,
        activity_user_discount_end_time: str = None,
        benefit_content: str = None,
    ):
        self.activity_end_time = activity_end_time
        self.activity_id = activity_id
        self.activity_user_discount_countdown_seconds = activity_user_discount_countdown_seconds
        self.activity_user_discount_end_time = activity_user_discount_end_time
        self.benefit_content = benefit_content

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.activity_end_time is not None:
            result['activityEndTime'] = self.activity_end_time

        if self.activity_id is not None:
            result['activityId'] = self.activity_id

        if self.activity_user_discount_countdown_seconds is not None:
            result['activityUserDiscountCountdownSeconds'] = self.activity_user_discount_countdown_seconds

        if self.activity_user_discount_end_time is not None:
            result['activityUserDiscountEndTime'] = self.activity_user_discount_end_time

        if self.benefit_content is not None:
            result['benefitContent'] = self.benefit_content

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('activityEndTime') is not None:
            self.activity_end_time = m.get('activityEndTime')

        if m.get('activityId') is not None:
            self.activity_id = m.get('activityId')

        if m.get('activityUserDiscountCountdownSeconds') is not None:
            self.activity_user_discount_countdown_seconds = m.get('activityUserDiscountCountdownSeconds')

        if m.get('activityUserDiscountEndTime') is not None:
            self.activity_user_discount_end_time = m.get('activityUserDiscountEndTime')

        if m.get('benefitContent') is not None:
            self.benefit_content = m.get('benefitContent')

        return self

