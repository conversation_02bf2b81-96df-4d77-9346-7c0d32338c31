# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from typing import List


class InnerDescribeImageCodesPageRequest(DaraModel):
    def __init__(
        self,
        current_page: int = None,
        image_id_conditions: List[str] = None,
        is_asc: bool = None,
        is_enable: bool = None,
        operator_id_conditions: List[str] = None,
        order_by: str = None,
        page_size: int = None,
        share_code_conditions: List[str] = None,
    ):
        self.current_page = current_page
        self.image_id_conditions = image_id_conditions
        self.is_asc = is_asc
        self.is_enable = is_enable
        self.operator_id_conditions = operator_id_conditions
        self.order_by = order_by
        self.page_size = page_size
        self.share_code_conditions = share_code_conditions

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.current_page is not None:
            result['CurrentPage'] = self.current_page

        if self.image_id_conditions is not None:
            result['ImageIdConditions'] = self.image_id_conditions

        if self.is_asc is not None:
            result['IsAsc'] = self.is_asc

        if self.is_enable is not None:
            result['IsEnable'] = self.is_enable

        if self.operator_id_conditions is not None:
            result['OperatorIdConditions'] = self.operator_id_conditions

        if self.order_by is not None:
            result['OrderBy'] = self.order_by

        if self.page_size is not None:
            result['PageSize'] = self.page_size

        if self.share_code_conditions is not None:
            result['ShareCodeConditions'] = self.share_code_conditions

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CurrentPage') is not None:
            self.current_page = m.get('CurrentPage')

        if m.get('ImageIdConditions') is not None:
            self.image_id_conditions = m.get('ImageIdConditions')

        if m.get('IsAsc') is not None:
            self.is_asc = m.get('IsAsc')

        if m.get('IsEnable') is not None:
            self.is_enable = m.get('IsEnable')

        if m.get('OperatorIdConditions') is not None:
            self.operator_id_conditions = m.get('OperatorIdConditions')

        if m.get('OrderBy') is not None:
            self.order_by = m.get('OrderBy')

        if m.get('PageSize') is not None:
            self.page_size = m.get('PageSize')

        if m.get('ShareCodeConditions') is not None:
            self.share_code_conditions = m.get('ShareCodeConditions')

        return self

