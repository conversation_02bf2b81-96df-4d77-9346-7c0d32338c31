# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class UpdateDesktopRealExpireTimeShrinkRequest(DaraModel):
    def __init__(
        self,
        desktop_ids_shrink: str = None,
        is_deleted: bool = None,
    ):
        self.desktop_ids_shrink = desktop_ids_shrink
        self.is_deleted = is_deleted

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.desktop_ids_shrink is not None:
            result['DesktopIds'] = self.desktop_ids_shrink

        if self.is_deleted is not None:
            result['IsDeleted'] = self.is_deleted

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DesktopIds') is not None:
            self.desktop_ids_shrink = m.get('DesktopIds')

        if m.get('IsDeleted') is not None:
            self.is_deleted = m.get('IsDeleted')

        return self

