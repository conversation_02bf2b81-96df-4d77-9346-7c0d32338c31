# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 


class ModifyActivityProductConfigRequest(DaraModel):
    def __init__(
        self,
        config_code: str = None,
        new_activity_config_info: main_models.ModifyActivityProductConfigRequestNewActivityConfigInfo = None,
    ):
        # This parameter is required.
        self.config_code = config_code
        self.new_activity_config_info = new_activity_config_info

    def validate(self):
        if self.new_activity_config_info:
            self.new_activity_config_info.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.config_code is not None:
            result['ConfigCode'] = self.config_code

        if self.new_activity_config_info is not None:
            result['NewActivityConfigInfo'] = self.new_activity_config_info.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ConfigCode') is not None:
            self.config_code = m.get('ConfigCode')

        if m.get('NewActivityConfigInfo') is not None:
            temp_model = main_models.ModifyActivityProductConfigRequestNewActivityConfigInfo()
            self.new_activity_config_info = temp_model.from_map(m.get('NewActivityConfigInfo'))

        return self

class ModifyActivityProductConfigRequestNewActivityConfigInfo(DaraModel):
    def __init__(
        self,
        init_resource_config: main_models.ModifyActivityProductConfigRequestNewActivityConfigInfoInitResourceConfig = None,
        is_auto_activate: bool = None,
        lx_promotion_id: str = None,
        prm_activity_product_config_code: str = None,
        product_code: str = None,
        product_sku_code: str = None,
        product_type: str = None,
        purchase_mode: str = None,
    ):
        self.init_resource_config = init_resource_config
        self.is_auto_activate = is_auto_activate
        self.lx_promotion_id = lx_promotion_id
        self.prm_activity_product_config_code = prm_activity_product_config_code
        self.product_code = product_code
        self.product_sku_code = product_sku_code
        self.product_type = product_type
        self.purchase_mode = purchase_mode

    def validate(self):
        if self.init_resource_config:
            self.init_resource_config.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.init_resource_config is not None:
            result['InitResourceConfig'] = self.init_resource_config.to_map()

        if self.is_auto_activate is not None:
            result['IsAutoActivate'] = self.is_auto_activate

        if self.lx_promotion_id is not None:
            result['LxPromotionId'] = self.lx_promotion_id

        if self.prm_activity_product_config_code is not None:
            result['PrmActivityProductConfigCode'] = self.prm_activity_product_config_code

        if self.product_code is not None:
            result['ProductCode'] = self.product_code

        if self.product_sku_code is not None:
            result['ProductSkuCode'] = self.product_sku_code

        if self.product_type is not None:
            result['ProductType'] = self.product_type

        if self.purchase_mode is not None:
            result['PurchaseMode'] = self.purchase_mode

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('InitResourceConfig') is not None:
            temp_model = main_models.ModifyActivityProductConfigRequestNewActivityConfigInfoInitResourceConfig()
            self.init_resource_config = temp_model.from_map(m.get('InitResourceConfig'))

        if m.get('IsAutoActivate') is not None:
            self.is_auto_activate = m.get('IsAutoActivate')

        if m.get('LxPromotionId') is not None:
            self.lx_promotion_id = m.get('LxPromotionId')

        if m.get('PrmActivityProductConfigCode') is not None:
            self.prm_activity_product_config_code = m.get('PrmActivityProductConfigCode')

        if m.get('ProductCode') is not None:
            self.product_code = m.get('ProductCode')

        if m.get('ProductSkuCode') is not None:
            self.product_sku_code = m.get('ProductSkuCode')

        if m.get('ProductType') is not None:
            self.product_type = m.get('ProductType')

        if m.get('PurchaseMode') is not None:
            self.purchase_mode = m.get('PurchaseMode')

        return self

class ModifyActivityProductConfigRequestNewActivityConfigInfoInitResourceConfig(DaraModel):
    def __init__(
        self,
        app_id_list: str = None,
        city_name: str = None,
        enable_default_mode: str = None,
        end_user_id: str = None,
        image_id: str = None,
        model_id: str = None,
        user_defined_desktop_name: str = None,
        user_expected_region_id: str = None,
        user_ip: str = None,
    ):
        self.app_id_list = app_id_list
        self.city_name = city_name
        self.enable_default_mode = enable_default_mode
        self.end_user_id = end_user_id
        # This parameter is required.
        self.image_id = image_id
        # This parameter is required.
        self.model_id = model_id
        self.user_defined_desktop_name = user_defined_desktop_name
        self.user_expected_region_id = user_expected_region_id
        self.user_ip = user_ip

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.app_id_list is not None:
            result['AppIdList'] = self.app_id_list

        if self.city_name is not None:
            result['CityName'] = self.city_name

        if self.enable_default_mode is not None:
            result['EnableDefaultMode'] = self.enable_default_mode

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.image_id is not None:
            result['ImageId'] = self.image_id

        if self.model_id is not None:
            result['ModelId'] = self.model_id

        if self.user_defined_desktop_name is not None:
            result['UserDefinedDesktopName'] = self.user_defined_desktop_name

        if self.user_expected_region_id is not None:
            result['UserExpectedRegionId'] = self.user_expected_region_id

        if self.user_ip is not None:
            result['UserIp'] = self.user_ip

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AppIdList') is not None:
            self.app_id_list = m.get('AppIdList')

        if m.get('CityName') is not None:
            self.city_name = m.get('CityName')

        if m.get('EnableDefaultMode') is not None:
            self.enable_default_mode = m.get('EnableDefaultMode')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('ImageId') is not None:
            self.image_id = m.get('ImageId')

        if m.get('ModelId') is not None:
            self.model_id = m.get('ModelId')

        if m.get('UserDefinedDesktopName') is not None:
            self.user_defined_desktop_name = m.get('UserDefinedDesktopName')

        if m.get('UserExpectedRegionId') is not None:
            self.user_expected_region_id = m.get('UserExpectedRegionId')

        if m.get('UserIp') is not None:
            self.user_ip = m.get('UserIp')

        return self

