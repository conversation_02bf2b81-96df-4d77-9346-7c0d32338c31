# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_wuying_pc_inside20221123 import models as main_models 
from typing import List


class DescribePickCouponUserListResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        data: List[main_models.DescribePickCouponUserListResponseBodyData] = None,
        err_code: str = None,
        err_msg: str = None,
        request_id: str = None,
    ):
        self.code = code
        self.data = data
        self.err_code = err_code
        self.err_msg = err_msg
        self.request_id = request_id

    def validate(self):
        if self.data:
            for v1 in self.data:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        result['Data'] = []
        if self.data is not None:
            for k1 in self.data:
                result['Data'].append(k1.to_map() if k1 else None)

        if self.err_code is not None:
            result['ErrCode'] = self.err_code

        if self.err_msg is not None:
            result['ErrMsg'] = self.err_msg

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        self.data = []
        if m.get('Data') is not None:
            for k1 in m.get('Data'):
                temp_model = main_models.DescribePickCouponUserListResponseBodyData()
                self.data.append(temp_model.from_map(k1))

        if m.get('ErrCode') is not None:
            self.err_code = m.get('ErrCode')

        if m.get('ErrMsg') is not None:
            self.err_msg = m.get('ErrMsg')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class DescribePickCouponUserListResponseBodyData(DaraModel):
    def __init__(
        self,
        certificated_type: str = None,
        coupon_template_id: str = None,
        create_time_stamp: int = None,
        relation_id: str = None,
        user_id: str = None,
    ):
        self.certificated_type = certificated_type
        self.coupon_template_id = coupon_template_id
        self.create_time_stamp = create_time_stamp
        self.relation_id = relation_id
        self.user_id = user_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.certificated_type is not None:
            result['CertificatedType'] = self.certificated_type

        if self.coupon_template_id is not None:
            result['CouponTemplateId'] = self.coupon_template_id

        if self.create_time_stamp is not None:
            result['CreateTimeStamp'] = self.create_time_stamp

        if self.relation_id is not None:
            result['RelationId'] = self.relation_id

        if self.user_id is not None:
            result['UserId'] = self.user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CertificatedType') is not None:
            self.certificated_type = m.get('CertificatedType')

        if m.get('CouponTemplateId') is not None:
            self.coupon_template_id = m.get('CouponTemplateId')

        if m.get('CreateTimeStamp') is not None:
            self.create_time_stamp = m.get('CreateTimeStamp')

        if m.get('RelationId') is not None:
            self.relation_id = m.get('RelationId')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        return self

