# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class AddCoreHourRequest(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        core_hour_amount: int = None,
        period: int = None,
        period_unit: str = None,
        source: str = None,
    ):
        self.ali_uid = ali_uid
        self.core_hour_amount = core_hour_amount
        self.period = period
        self.period_unit = period_unit
        self.source = source

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.core_hour_amount is not None:
            result['CoreHourAmount'] = self.core_hour_amount

        if self.period is not None:
            result['Period'] = self.period

        if self.period_unit is not None:
            result['PeriodUnit'] = self.period_unit

        if self.source is not None:
            result['Source'] = self.source

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('CoreHourAmount') is not None:
            self.core_hour_amount = m.get('CoreHourAmount')

        if m.get('Period') is not None:
            self.period = m.get('Period')

        if m.get('PeriodUnit') is not None:
            self.period_unit = m.get('PeriodUnit')

        if m.get('Source') is not None:
            self.source = m.get('Source')

        return self

