#!/usr/bin/env python3
"""
AppStreamInnerClient 使用示例
展示如何使用AppStream内部服务客户端获取STS Token
"""

import asyncio
from typing import Optional
from loguru import logger

from .appstream_inner_client import (
    AppStreamInnerClient,
    AppStreamInnerClientError,
    get_appstream_inner_client
)


class AppStreamTokenService:
    """AppStream Token服务封装类"""
    
    def __init__(
        self,
        access_key_id: str,
        access_key_secret: str,
        endpoint: Optional[str] = None
    ):
        """
        初始化AppStream Token服务
        
        Args:
            access_key_id: 阿里云访问密钥ID
            access_key_secret: 阿里云访问密钥Secret
            endpoint: 服务端点
        """
        self.client = AppStreamInnerClient(
            access_key_id=access_key_id,
            access_key_secret=access_key_secret,
            endpoint=endpoint
        )
    
    def get_user_sts_token(
        self,
        end_user_id: str,
        external_user_id: Optional[str] = None,
        account_type: str = "SIMPLE",
        auto_create_user: bool = True,
        policy: Optional[str] = None,
        user_ali_uid: Optional[int] = None
    ) -> dict:
        """
        获取用户STS Token
        
        Args:
            end_user_id: 终端用户ID
            external_user_id: 外部用户ID
            account_type: 账户类型，默认为SIMPLE
            auto_create_user: 是否自动创建用户，默认为True
            policy: 策略
            user_ali_uid: 用户阿里云UID
            
        Returns:
            dict: STS Token信息
        """
        try:
            logger.info(f"获取用户STS Token: end_user_id={end_user_id}")
            
            response = self.client.get_sts_token(
                account_type=account_type,
                auto_create_user=auto_create_user,
                end_user_id=end_user_id,
                external_user_id=external_user_id,
                policy=policy,
                user_ali_uid=user_ali_uid
            )
            
            if response.body and response.body.token:
                token_info = {
                    "request_id": response.body.request_id,
                    "ali_uid": response.body.token.ali_uid,
                    "end_user_id": response.body.token.end_user_id,
                    "session_id": response.body.token.session_id,
                    "sts_token": response.body.token.sts_token,
                    "wy_id": response.body.token.wy_id
                }
                
                logger.success(f"获取STS Token成功: session_id={token_info['session_id']}")
                return token_info
            else:
                logger.error("STS Token响应为空")
                return {}
                
        except AppStreamInnerClientError as e:
            logger.error(f"获取STS Token失败: {e}")
            raise
        except Exception as e:
            logger.error(f"获取STS Token异常: {e}")
            raise AppStreamInnerClientError(f"获取STS Token异常: {e}") from e
    
    async def get_user_sts_token_async(
        self,
        end_user_id: str,
        external_user_id: Optional[str] = None,
        account_type: str = "SIMPLE",
        auto_create_user: bool = True,
        policy: Optional[str] = None,
        user_ali_uid: Optional[int] = None
    ) -> dict:
        """
        异步获取用户STS Token
        
        Args:
            end_user_id: 终端用户ID
            external_user_id: 外部用户ID
            account_type: 账户类型，默认为SIMPLE
            auto_create_user: 是否自动创建用户，默认为True
            policy: 策略
            user_ali_uid: 用户阿里云UID
            
        Returns:
            dict: STS Token信息
        """
        try:
            logger.info(f"异步获取用户STS Token: end_user_id={end_user_id}")
            
            response = await self.client.get_sts_token_async(
                account_type=account_type,
                auto_create_user=auto_create_user,
                end_user_id=end_user_id,
                external_user_id=external_user_id,
                policy=policy,
                user_ali_uid=user_ali_uid
            )
            
            if response.body and response.body.token:
                token_info = {
                    "request_id": response.body.request_id,
                    "ali_uid": response.body.token.ali_uid,
                    "end_user_id": response.body.token.end_user_id,
                    "session_id": response.body.token.session_id,
                    "sts_token": response.body.token.sts_token,
                    "wy_id": response.body.token.wy_id
                }
                
                logger.success(f"异步获取STS Token成功: session_id={token_info['session_id']}")
                return token_info
            else:
                logger.error("异步STS Token响应为空")
                return {}
                
        except AppStreamInnerClientError as e:
            logger.error(f"异步获取STS Token失败: {e}")
            raise
        except Exception as e:
            logger.error(f"异步获取STS Token异常: {e}")
            raise AppStreamInnerClientError(f"异步获取STS Token异常: {e}") from e


def example_basic_usage():
    """基本使用示例"""
    logger.info("=== AppStream内部客户端基本使用示例 ===")
    
    try:
        # 创建客户端
        client = AppStreamInnerClient(
            access_key_id="your_access_key_id",
            access_key_secret="your_access_key_secret",
            endpoint="appstream-center-inner.aliyuncs.com"
        )
        
        logger.info("客户端创建成功")
        logger.info(f"客户端信息: {client.get_client_info()}")
        
        # 注意：这里只是示例，实际调用需要有效的凭证和网络连接
        # response = client.get_sts_token(
        #     end_user_id="test_user_001",
        #     account_type="SIMPLE",
        #     auto_create_user=True
        # )
        
        logger.info("基本使用示例完成（未实际调用API）")
        
    except Exception as e:
        logger.error(f"基本使用示例失败: {e}")


def example_singleton_usage():
    """单例模式使用示例"""
    logger.info("=== AppStream内部客户端单例模式使用示例 ===")
    
    try:
        # 使用单例模式获取客户端
        client = get_appstream_inner_client(
            access_key_id="your_access_key_id",
            access_key_secret="your_access_key_secret"
        )
        
        logger.info("单例客户端获取成功")
        
        # 再次获取，应该返回同一个实例
        client2 = get_appstream_inner_client()
        
        assert client is client2
        logger.info("单例模式验证成功")
        
    except Exception as e:
        logger.error(f"单例模式使用示例失败: {e}")


def example_service_wrapper():
    """服务封装使用示例"""
    logger.info("=== AppStream Token服务封装使用示例 ===")
    
    try:
        # 创建Token服务
        token_service = AppStreamTokenService(
            access_key_id="your_access_key_id",
            access_key_secret="your_access_key_secret"
        )
        
        logger.info("Token服务创建成功")
        
        # 注意：这里只是示例，实际调用需要有效的凭证和网络连接
        # token_info = token_service.get_user_sts_token(
        #     end_user_id="test_user_001",
        #     external_user_id="external_001"
        # )
        # 
        # logger.info(f"获取到STS Token: {token_info}")
        
        logger.info("服务封装使用示例完成（未实际调用API）")
        
    except Exception as e:
        logger.error(f"服务封装使用示例失败: {e}")


async def example_async_usage():
    """异步使用示例"""
    logger.info("=== AppStream内部客户端异步使用示例 ===")
    
    try:
        # 创建Token服务
        token_service = AppStreamTokenService(
            access_key_id="your_access_key_id",
            access_key_secret="your_access_key_secret"
        )
        
        logger.info("异步Token服务创建成功")
        
        # 注意：这里只是示例，实际调用需要有效的凭证和网络连接
        # token_info = await token_service.get_user_sts_token_async(
        #     end_user_id="test_user_001",
        #     external_user_id="external_001"
        # )
        # 
        # logger.info(f"异步获取到STS Token: {token_info}")
        
        logger.info("异步使用示例完成（未实际调用API）")
        
    except Exception as e:
        logger.error(f"异步使用示例失败: {e}")


def main():
    """主函数"""
    logger.info("开始AppStream内部客户端使用示例...")
    
    # 基本使用示例
    example_basic_usage()
    
    # 单例模式使用示例
    example_singleton_usage()
    
    # 服务封装使用示例
    example_service_wrapper()
    
    # 异步使用示例
    asyncio.run(example_async_usage())
    
    logger.success("所有使用示例完成！")


if __name__ == "__main__":
    main()
