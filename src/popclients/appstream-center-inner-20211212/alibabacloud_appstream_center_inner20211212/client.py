# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from alibabacloud_tea_openapi.client import Client as OpenApiClient 
from alibabacloud_tea_openapi import utils_models as open_api_util_models 
from darabonba.core import <PERSON><PERSON><PERSON><PERSON> as Dar<PERSON><PERSON><PERSON> 
from alibabacloud_tea_openapi.utils import Utils 
from alibabacloud_appstream_center_inner20211212 import models as main_models 
from darabonba.runtime import RuntimeOptions 
from typing import Dict


"""
"""
class Client(OpenApiClient):

    def __init__(
        self,
        config: open_api_util_models.Config,
    ):
        super().__init__(config)
        self._endpoint_rule = ''
        self.check_config(config)
        self._endpoint = self.get_endpoint('appstream-center-inner', self._region_id, self._endpoint_rule, self._network, self._suffix, self._endpoint_map, self._endpoint)

    def get_endpoint(
        self,
        product_id: str,
        region_id: str,
        endpoint_rule: str,
        network: str,
        suffix: str,
        endpoint_map: Dict[str, str],
        endpoint: str,
    ) -> str:
        if not DaraCore.is_null(endpoint):
            return endpoint
        if not DaraCore.is_null(endpoint_map) and not DaraCore.is_null(endpoint_map.get(region_id)):
            return endpoint_map.get(region_id)
        return Utils.get_endpoint_rules(product_id, region_id, endpoint_rule, network, suffix)

    def app_auth_login_get_access_token_with_options(
        self,
        request: main_models.AppAuthLoginGetAccessTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.AppAuthLoginGetAccessTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.app_key):
            query['AppKey'] = request.app_key
        if not DaraCore.is_null(request.auth_code):
            query['AuthCode'] = request.auth_code
        if not DaraCore.is_null(request.client_id):
            query['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_secret):
            query['ClientSecret'] = request.client_secret
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'AppAuthLoginGetAccessToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.AppAuthLoginGetAccessTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def app_auth_login_get_access_token_with_options_async(
        self,
        request: main_models.AppAuthLoginGetAccessTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.AppAuthLoginGetAccessTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.app_key):
            query['AppKey'] = request.app_key
        if not DaraCore.is_null(request.auth_code):
            query['AuthCode'] = request.auth_code
        if not DaraCore.is_null(request.client_id):
            query['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_secret):
            query['ClientSecret'] = request.client_secret
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'AppAuthLoginGetAccessToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.AppAuthLoginGetAccessTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def app_auth_login_get_access_token(
        self,
        request: main_models.AppAuthLoginGetAccessTokenRequest,
    ) -> main_models.AppAuthLoginGetAccessTokenResponse:
        runtime = RuntimeOptions()
        return self.app_auth_login_get_access_token_with_options(request, runtime)

    async def app_auth_login_get_access_token_async(
        self,
        request: main_models.AppAuthLoginGetAccessTokenRequest,
    ) -> main_models.AppAuthLoginGetAccessTokenResponse:
        runtime = RuntimeOptions()
        return await self.app_auth_login_get_access_token_with_options_async(request, runtime)

    def app_auth_login_get_app_info_with_options(
        self,
        request: main_models.AppAuthLoginGetAppInfoRequest,
        runtime: RuntimeOptions,
    ) -> main_models.AppAuthLoginGetAppInfoResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.app_key):
            query['AppKey'] = request.app_key
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'AppAuthLoginGetAppInfo',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.AppAuthLoginGetAppInfoResponse(),
            self.call_api(params, req, runtime)
        )

    async def app_auth_login_get_app_info_with_options_async(
        self,
        request: main_models.AppAuthLoginGetAppInfoRequest,
        runtime: RuntimeOptions,
    ) -> main_models.AppAuthLoginGetAppInfoResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.app_key):
            query['AppKey'] = request.app_key
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'AppAuthLoginGetAppInfo',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.AppAuthLoginGetAppInfoResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def app_auth_login_get_app_info(
        self,
        request: main_models.AppAuthLoginGetAppInfoRequest,
    ) -> main_models.AppAuthLoginGetAppInfoResponse:
        runtime = RuntimeOptions()
        return self.app_auth_login_get_app_info_with_options(request, runtime)

    async def app_auth_login_get_app_info_async(
        self,
        request: main_models.AppAuthLoginGetAppInfoRequest,
    ) -> main_models.AppAuthLoginGetAppInfoResponse:
        runtime = RuntimeOptions()
        return await self.app_auth_login_get_app_info_with_options_async(request, runtime)

    def app_auth_login_get_transient_token_with_options(
        self,
        request: main_models.AppAuthLoginGetTransientTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.AppAuthLoginGetTransientTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.account_type):
            query['AccountType'] = request.account_type
        if not DaraCore.is_null(request.domain_name):
            query['DomainName'] = request.domain_name
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.instance_id):
            query['InstanceId'] = request.instance_id
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.wy_id):
            query['WyId'] = request.wy_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'AppAuthLoginGetTransientToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.AppAuthLoginGetTransientTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def app_auth_login_get_transient_token_with_options_async(
        self,
        request: main_models.AppAuthLoginGetTransientTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.AppAuthLoginGetTransientTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.account_type):
            query['AccountType'] = request.account_type
        if not DaraCore.is_null(request.domain_name):
            query['DomainName'] = request.domain_name
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.instance_id):
            query['InstanceId'] = request.instance_id
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.wy_id):
            query['WyId'] = request.wy_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'AppAuthLoginGetTransientToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.AppAuthLoginGetTransientTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def app_auth_login_get_transient_token(
        self,
        request: main_models.AppAuthLoginGetTransientTokenRequest,
    ) -> main_models.AppAuthLoginGetTransientTokenResponse:
        runtime = RuntimeOptions()
        return self.app_auth_login_get_transient_token_with_options(request, runtime)

    async def app_auth_login_get_transient_token_async(
        self,
        request: main_models.AppAuthLoginGetTransientTokenRequest,
    ) -> main_models.AppAuthLoginGetTransientTokenResponse:
        runtime = RuntimeOptions()
        return await self.app_auth_login_get_transient_token_with_options_async(request, runtime)

    def app_auth_login_get_user_info_with_options(
        self,
        request: main_models.AppAuthLoginGetUserInfoRequest,
        runtime: RuntimeOptions,
    ) -> main_models.AppAuthLoginGetUserInfoResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.access_token):
            query['AccessToken'] = request.access_token
        if not DaraCore.is_null(request.app_key):
            query['AppKey'] = request.app_key
        if not DaraCore.is_null(request.client_id):
            query['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_secret):
            query['ClientSecret'] = request.client_secret
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'AppAuthLoginGetUserInfo',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.AppAuthLoginGetUserInfoResponse(),
            self.call_api(params, req, runtime)
        )

    async def app_auth_login_get_user_info_with_options_async(
        self,
        request: main_models.AppAuthLoginGetUserInfoRequest,
        runtime: RuntimeOptions,
    ) -> main_models.AppAuthLoginGetUserInfoResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.access_token):
            query['AccessToken'] = request.access_token
        if not DaraCore.is_null(request.app_key):
            query['AppKey'] = request.app_key
        if not DaraCore.is_null(request.client_id):
            query['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_secret):
            query['ClientSecret'] = request.client_secret
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'AppAuthLoginGetUserInfo',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.AppAuthLoginGetUserInfoResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def app_auth_login_get_user_info(
        self,
        request: main_models.AppAuthLoginGetUserInfoRequest,
    ) -> main_models.AppAuthLoginGetUserInfoResponse:
        runtime = RuntimeOptions()
        return self.app_auth_login_get_user_info_with_options(request, runtime)

    async def app_auth_login_get_user_info_async(
        self,
        request: main_models.AppAuthLoginGetUserInfoRequest,
    ) -> main_models.AppAuthLoginGetUserInfoResponse:
        runtime = RuntimeOptions()
        return await self.app_auth_login_get_user_info_with_options_async(request, runtime)

    def app_auth_login_login_by_auth_code_with_options(
        self,
        request: main_models.AppAuthLoginLoginByAuthCodeRequest,
        runtime: RuntimeOptions,
    ) -> main_models.AppAuthLoginLoginByAuthCodeResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.auth_code):
            query['AuthCode'] = request.auth_code
        if not DaraCore.is_null(request.client_id):
            query['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_secret):
            query['ClientSecret'] = request.client_secret
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'AppAuthLoginLoginByAuthCode',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.AppAuthLoginLoginByAuthCodeResponse(),
            self.call_api(params, req, runtime)
        )

    async def app_auth_login_login_by_auth_code_with_options_async(
        self,
        request: main_models.AppAuthLoginLoginByAuthCodeRequest,
        runtime: RuntimeOptions,
    ) -> main_models.AppAuthLoginLoginByAuthCodeResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.auth_code):
            query['AuthCode'] = request.auth_code
        if not DaraCore.is_null(request.client_id):
            query['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_secret):
            query['ClientSecret'] = request.client_secret
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'AppAuthLoginLoginByAuthCode',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.AppAuthLoginLoginByAuthCodeResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def app_auth_login_login_by_auth_code(
        self,
        request: main_models.AppAuthLoginLoginByAuthCodeRequest,
    ) -> main_models.AppAuthLoginLoginByAuthCodeResponse:
        runtime = RuntimeOptions()
        return self.app_auth_login_login_by_auth_code_with_options(request, runtime)

    async def app_auth_login_login_by_auth_code_async(
        self,
        request: main_models.AppAuthLoginLoginByAuthCodeRequest,
    ) -> main_models.AppAuthLoginLoginByAuthCodeResponse:
        runtime = RuntimeOptions()
        return await self.app_auth_login_login_by_auth_code_with_options_async(request, runtime)

    def change_password_with_options(
        self,
        request: main_models.ChangePasswordRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ChangePasswordResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.client_id):
            query['ClientId'] = request.client_id
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.login_identifier):
            query['LoginIdentifier'] = request.login_identifier
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.new_password):
            query['NewPassword'] = request.new_password
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.old_password):
            query['OldPassword'] = request.old_password
        if not DaraCore.is_null(request.region_id):
            query['RegionId'] = request.region_id
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'ChangePassword',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ChangePasswordResponse(),
            self.call_api(params, req, runtime)
        )

    async def change_password_with_options_async(
        self,
        request: main_models.ChangePasswordRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ChangePasswordResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.client_id):
            query['ClientId'] = request.client_id
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.login_identifier):
            query['LoginIdentifier'] = request.login_identifier
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.new_password):
            query['NewPassword'] = request.new_password
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.old_password):
            query['OldPassword'] = request.old_password
        if not DaraCore.is_null(request.region_id):
            query['RegionId'] = request.region_id
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'ChangePassword',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ChangePasswordResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def change_password(
        self,
        request: main_models.ChangePasswordRequest,
    ) -> main_models.ChangePasswordResponse:
        runtime = RuntimeOptions()
        return self.change_password_with_options(request, runtime)

    async def change_password_async(
        self,
        request: main_models.ChangePasswordRequest,
    ) -> main_models.ChangePasswordResponse:
        runtime = RuntimeOptions()
        return await self.change_password_with_options_async(request, runtime)

    def client_user_logout_with_options(
        self,
        request: main_models.ClientUserLogoutRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ClientUserLogoutResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.client_id):
            query['ClientId'] = request.client_id
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'ClientUserLogout',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ClientUserLogoutResponse(),
            self.call_api(params, req, runtime)
        )

    async def client_user_logout_with_options_async(
        self,
        request: main_models.ClientUserLogoutRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ClientUserLogoutResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.client_id):
            query['ClientId'] = request.client_id
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'ClientUserLogout',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ClientUserLogoutResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def client_user_logout(
        self,
        request: main_models.ClientUserLogoutRequest,
    ) -> main_models.ClientUserLogoutResponse:
        runtime = RuntimeOptions()
        return self.client_user_logout_with_options(request, runtime)

    async def client_user_logout_async(
        self,
        request: main_models.ClientUserLogoutRequest,
    ) -> main_models.ClientUserLogoutResponse:
        runtime = RuntimeOptions()
        return await self.client_user_logout_with_options_async(request, runtime)

    def create_sso_user_session_with_options(
        self,
        request: main_models.CreateSsoUserSessionRequest,
        runtime: RuntimeOptions,
    ) -> main_models.CreateSsoUserSessionResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.client_type):
            query['ClientType'] = request.client_type
        if not DaraCore.is_null(request.client_uuid):
            query['ClientUuid'] = request.client_uuid
        if not DaraCore.is_null(request.external_mapping_id):
            query['ExternalMappingId'] = request.external_mapping_id
        if not DaraCore.is_null(request.external_user_id):
            query['ExternalUserId'] = request.external_user_id
        if not DaraCore.is_null(request.jump_token):
            query['JumpToken'] = request.jump_token
        if not DaraCore.is_null(request.login_region_id):
            query['LoginRegionId'] = request.login_region_id
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.network_type):
            query['NetworkType'] = request.network_type
        if not DaraCore.is_null(request.refresh_token):
            query['RefreshToken'] = request.refresh_token
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        body = {}
        if not DaraCore.is_null(request.access_token):
            body['AccessToken'] = request.access_token
        if not DaraCore.is_null(request.end_user_id):
            body['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.idp_id):
            body['IdpId'] = request.idp_id
        if not DaraCore.is_null(request.office_site_id):
            body['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.sso_token):
            body['SsoToken'] = request.sso_token
        if not DaraCore.is_null(request.token_expire_date):
            body['TokenExpireDate'] = request.token_expire_date
        if not DaraCore.is_null(request.user_ali_uid):
            body['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query),
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'CreateSsoUserSession',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.CreateSsoUserSessionResponse(),
            self.call_api(params, req, runtime)
        )

    async def create_sso_user_session_with_options_async(
        self,
        request: main_models.CreateSsoUserSessionRequest,
        runtime: RuntimeOptions,
    ) -> main_models.CreateSsoUserSessionResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.client_type):
            query['ClientType'] = request.client_type
        if not DaraCore.is_null(request.client_uuid):
            query['ClientUuid'] = request.client_uuid
        if not DaraCore.is_null(request.external_mapping_id):
            query['ExternalMappingId'] = request.external_mapping_id
        if not DaraCore.is_null(request.external_user_id):
            query['ExternalUserId'] = request.external_user_id
        if not DaraCore.is_null(request.jump_token):
            query['JumpToken'] = request.jump_token
        if not DaraCore.is_null(request.login_region_id):
            query['LoginRegionId'] = request.login_region_id
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.network_type):
            query['NetworkType'] = request.network_type
        if not DaraCore.is_null(request.refresh_token):
            query['RefreshToken'] = request.refresh_token
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        body = {}
        if not DaraCore.is_null(request.access_token):
            body['AccessToken'] = request.access_token
        if not DaraCore.is_null(request.end_user_id):
            body['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.idp_id):
            body['IdpId'] = request.idp_id
        if not DaraCore.is_null(request.office_site_id):
            body['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.sso_token):
            body['SsoToken'] = request.sso_token
        if not DaraCore.is_null(request.token_expire_date):
            body['TokenExpireDate'] = request.token_expire_date
        if not DaraCore.is_null(request.user_ali_uid):
            body['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query),
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'CreateSsoUserSession',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.CreateSsoUserSessionResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def create_sso_user_session(
        self,
        request: main_models.CreateSsoUserSessionRequest,
    ) -> main_models.CreateSsoUserSessionResponse:
        runtime = RuntimeOptions()
        return self.create_sso_user_session_with_options(request, runtime)

    async def create_sso_user_session_async(
        self,
        request: main_models.CreateSsoUserSessionRequest,
    ) -> main_models.CreateSsoUserSessionResponse:
        runtime = RuntimeOptions()
        return await self.create_sso_user_session_with_options_async(request, runtime)

    def describe_session_config_with_options(
        self,
        request: main_models.DescribeSessionConfigRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeSessionConfigResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeSessionConfig',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeSessionConfigResponse(),
            self.call_api(params, req, runtime)
        )

    async def describe_session_config_with_options_async(
        self,
        request: main_models.DescribeSessionConfigRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeSessionConfigResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeSessionConfig',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeSessionConfigResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def describe_session_config(
        self,
        request: main_models.DescribeSessionConfigRequest,
    ) -> main_models.DescribeSessionConfigResponse:
        runtime = RuntimeOptions()
        return self.describe_session_config_with_options(request, runtime)

    async def describe_session_config_async(
        self,
        request: main_models.DescribeSessionConfigRequest,
    ) -> main_models.DescribeSessionConfigResponse:
        runtime = RuntimeOptions()
        return await self.describe_session_config_with_options_async(request, runtime)

    def describe_tenant_alias_with_options(
        self,
        request: main_models.DescribeTenantAliasRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeTenantAliasResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeTenantAlias',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeTenantAliasResponse(),
            self.call_api(params, req, runtime)
        )

    async def describe_tenant_alias_with_options_async(
        self,
        request: main_models.DescribeTenantAliasRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeTenantAliasResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeTenantAlias',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeTenantAliasResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def describe_tenant_alias(
        self,
        request: main_models.DescribeTenantAliasRequest,
    ) -> main_models.DescribeTenantAliasResponse:
        runtime = RuntimeOptions()
        return self.describe_tenant_alias_with_options(request, runtime)

    async def describe_tenant_alias_async(
        self,
        request: main_models.DescribeTenantAliasRequest,
    ) -> main_models.DescribeTenantAliasResponse:
        runtime = RuntimeOptions()
        return await self.describe_tenant_alias_with_options_async(request, runtime)

    def describe_tenants_alias_with_options(
        self,
        request: main_models.DescribeTenantsAliasRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeTenantsAliasResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uids):
            query['UserAliUids'] = request.user_ali_uids
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeTenantsAlias',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeTenantsAliasResponse(),
            self.call_api(params, req, runtime)
        )

    async def describe_tenants_alias_with_options_async(
        self,
        request: main_models.DescribeTenantsAliasRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeTenantsAliasResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uids):
            query['UserAliUids'] = request.user_ali_uids
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeTenantsAlias',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeTenantsAliasResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def describe_tenants_alias(
        self,
        request: main_models.DescribeTenantsAliasRequest,
    ) -> main_models.DescribeTenantsAliasResponse:
        runtime = RuntimeOptions()
        return self.describe_tenants_alias_with_options(request, runtime)

    async def describe_tenants_alias_async(
        self,
        request: main_models.DescribeTenantsAliasRequest,
    ) -> main_models.DescribeTenantsAliasResponse:
        runtime = RuntimeOptions()
        return await self.describe_tenants_alias_with_options_async(request, runtime)

    def find_idp_list_by_ali_uid_with_options(
        self,
        request: main_models.FindIdpListByAliUidRequest,
        runtime: RuntimeOptions,
    ) -> main_models.FindIdpListByAliUidResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.user_ali_uid):
            body['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'FindIdpListByAliUid',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.FindIdpListByAliUidResponse(),
            self.call_api(params, req, runtime)
        )

    async def find_idp_list_by_ali_uid_with_options_async(
        self,
        request: main_models.FindIdpListByAliUidRequest,
        runtime: RuntimeOptions,
    ) -> main_models.FindIdpListByAliUidResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.user_ali_uid):
            body['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'FindIdpListByAliUid',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.FindIdpListByAliUidResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def find_idp_list_by_ali_uid(
        self,
        request: main_models.FindIdpListByAliUidRequest,
    ) -> main_models.FindIdpListByAliUidResponse:
        runtime = RuntimeOptions()
        return self.find_idp_list_by_ali_uid_with_options(request, runtime)

    async def find_idp_list_by_ali_uid_async(
        self,
        request: main_models.FindIdpListByAliUidRequest,
    ) -> main_models.FindIdpListByAliUidResponse:
        runtime = RuntimeOptions()
        return await self.find_idp_list_by_ali_uid_with_options_async(request, runtime)

    def get_ad_session_info_with_options(
        self,
        request: main_models.GetAdSessionInfoRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetAdSessionInfoResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetAdSessionInfo',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetAdSessionInfoResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_ad_session_info_with_options_async(
        self,
        request: main_models.GetAdSessionInfoRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetAdSessionInfoResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetAdSessionInfo',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetAdSessionInfoResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_ad_session_info(
        self,
        request: main_models.GetAdSessionInfoRequest,
    ) -> main_models.GetAdSessionInfoResponse:
        runtime = RuntimeOptions()
        return self.get_ad_session_info_with_options(request, runtime)

    async def get_ad_session_info_async(
        self,
        request: main_models.GetAdSessionInfoRequest,
    ) -> main_models.GetAdSessionInfoResponse:
        runtime = RuntimeOptions()
        return await self.get_ad_session_info_with_options_async(request, runtime)

    def get_auth_code_with_options(
        self,
        request: main_models.GetAuthCodeRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetAuthCodeResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.ali_uid):
            body['AliUid'] = request.ali_uid
        if not DaraCore.is_null(request.auto_create_user):
            body['AutoCreateUser'] = request.auto_create_user
        if not DaraCore.is_null(request.end_user_id):
            body['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.expiration):
            body['Expiration'] = request.expiration
        if not DaraCore.is_null(request.external_user_id):
            body['ExternalUserId'] = request.external_user_id
        if not DaraCore.is_null(request.max_consume_times):
            body['MaxConsumeTimes'] = request.max_consume_times
        if not DaraCore.is_null(request.policy):
            body['Policy'] = request.policy
        if not DaraCore.is_null(request.token_expiration):
            body['TokenExpiration'] = request.token_expiration
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'GetAuthCode',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetAuthCodeResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_auth_code_with_options_async(
        self,
        request: main_models.GetAuthCodeRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetAuthCodeResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.ali_uid):
            body['AliUid'] = request.ali_uid
        if not DaraCore.is_null(request.auto_create_user):
            body['AutoCreateUser'] = request.auto_create_user
        if not DaraCore.is_null(request.end_user_id):
            body['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.expiration):
            body['Expiration'] = request.expiration
        if not DaraCore.is_null(request.external_user_id):
            body['ExternalUserId'] = request.external_user_id
        if not DaraCore.is_null(request.max_consume_times):
            body['MaxConsumeTimes'] = request.max_consume_times
        if not DaraCore.is_null(request.policy):
            body['Policy'] = request.policy
        if not DaraCore.is_null(request.token_expiration):
            body['TokenExpiration'] = request.token_expiration
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'GetAuthCode',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetAuthCodeResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_auth_code(
        self,
        request: main_models.GetAuthCodeRequest,
    ) -> main_models.GetAuthCodeResponse:
        runtime = RuntimeOptions()
        return self.get_auth_code_with_options(request, runtime)

    async def get_auth_code_async(
        self,
        request: main_models.GetAuthCodeRequest,
    ) -> main_models.GetAuthCodeResponse:
        runtime = RuntimeOptions()
        return await self.get_auth_code_with_options_async(request, runtime)

    def get_center_service_routing_policy_with_options(
        self,
        runtime: RuntimeOptions,
    ) -> main_models.GetCenterServiceRoutingPolicyResponse:
        req = open_api_util_models.OpenApiRequest()
        params = open_api_util_models.Params(
            action = 'GetCenterServiceRoutingPolicy',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetCenterServiceRoutingPolicyResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_center_service_routing_policy_with_options_async(
        self,
        runtime: RuntimeOptions,
    ) -> main_models.GetCenterServiceRoutingPolicyResponse:
        req = open_api_util_models.OpenApiRequest()
        params = open_api_util_models.Params(
            action = 'GetCenterServiceRoutingPolicy',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetCenterServiceRoutingPolicyResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_center_service_routing_policy(self) -> main_models.GetCenterServiceRoutingPolicyResponse:
        runtime = RuntimeOptions()
        return self.get_center_service_routing_policy_with_options(runtime)

    async def get_center_service_routing_policy_async(self) -> main_models.GetCenterServiceRoutingPolicyResponse:
        runtime = RuntimeOptions()
        return await self.get_center_service_routing_policy_with_options_async(runtime)

    def get_dual_center_pop_region_config_with_options(
        self,
        request: main_models.GetDualCenterPopRegionConfigRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetDualCenterPopRegionConfigResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetDualCenterPopRegionConfig',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetDualCenterPopRegionConfigResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_dual_center_pop_region_config_with_options_async(
        self,
        request: main_models.GetDualCenterPopRegionConfigRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetDualCenterPopRegionConfigResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetDualCenterPopRegionConfig',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetDualCenterPopRegionConfigResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_dual_center_pop_region_config(
        self,
        request: main_models.GetDualCenterPopRegionConfigRequest,
    ) -> main_models.GetDualCenterPopRegionConfigResponse:
        runtime = RuntimeOptions()
        return self.get_dual_center_pop_region_config_with_options(request, runtime)

    async def get_dual_center_pop_region_config_async(
        self,
        request: main_models.GetDualCenterPopRegionConfigRequest,
    ) -> main_models.GetDualCenterPopRegionConfigResponse:
        runtime = RuntimeOptions()
        return await self.get_dual_center_pop_region_config_with_options_async(request, runtime)

    def get_external_login_token_with_options(
        self,
        tmp_req: main_models.GetExternalLoginTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetExternalLoginTokenResponse:
        tmp_req.validate()
        request = main_models.GetExternalLoginTokenShrinkRequest()
        Utils.convert(tmp_req, request)
        if not DaraCore.is_null(tmp_req.external_user_info):
            request.external_user_info_shrink = Utils.array_to_string_with_specified_style(tmp_req.external_user_info, 'ExternalUserInfo', 'json')
        body = {}
        if not DaraCore.is_null(request.client_channel):
            body['ClientChannel'] = request.client_channel
        if not DaraCore.is_null(request.client_id):
            body['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_ip):
            body['ClientIp'] = request.client_ip
        if not DaraCore.is_null(request.client_os):
            body['ClientOS'] = request.client_os
        if not DaraCore.is_null(request.client_version):
            body['ClientVersion'] = request.client_version
        if not DaraCore.is_null(request.external_client_id):
            body['ExternalClientId'] = request.external_client_id
        if not DaraCore.is_null(request.external_user_info_shrink):
            body['ExternalUserInfo'] = request.external_user_info_shrink
        if not DaraCore.is_null(request.user_ali_uid):
            body['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.uuid):
            body['Uuid'] = request.uuid
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'GetExternalLoginToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetExternalLoginTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_external_login_token_with_options_async(
        self,
        tmp_req: main_models.GetExternalLoginTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetExternalLoginTokenResponse:
        tmp_req.validate()
        request = main_models.GetExternalLoginTokenShrinkRequest()
        Utils.convert(tmp_req, request)
        if not DaraCore.is_null(tmp_req.external_user_info):
            request.external_user_info_shrink = Utils.array_to_string_with_specified_style(tmp_req.external_user_info, 'ExternalUserInfo', 'json')
        body = {}
        if not DaraCore.is_null(request.client_channel):
            body['ClientChannel'] = request.client_channel
        if not DaraCore.is_null(request.client_id):
            body['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_ip):
            body['ClientIp'] = request.client_ip
        if not DaraCore.is_null(request.client_os):
            body['ClientOS'] = request.client_os
        if not DaraCore.is_null(request.client_version):
            body['ClientVersion'] = request.client_version
        if not DaraCore.is_null(request.external_client_id):
            body['ExternalClientId'] = request.external_client_id
        if not DaraCore.is_null(request.external_user_info_shrink):
            body['ExternalUserInfo'] = request.external_user_info_shrink
        if not DaraCore.is_null(request.user_ali_uid):
            body['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.uuid):
            body['Uuid'] = request.uuid
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'GetExternalLoginToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetExternalLoginTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_external_login_token(
        self,
        request: main_models.GetExternalLoginTokenRequest,
    ) -> main_models.GetExternalLoginTokenResponse:
        runtime = RuntimeOptions()
        return self.get_external_login_token_with_options(request, runtime)

    async def get_external_login_token_async(
        self,
        request: main_models.GetExternalLoginTokenRequest,
    ) -> main_models.GetExternalLoginTokenResponse:
        runtime = RuntimeOptions()
        return await self.get_external_login_token_with_options_async(request, runtime)

    def get_idp_by_office_site_id_with_options(
        self,
        request: main_models.GetIdpByOfficeSiteIdRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetIdpByOfficeSiteIdResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.office_site_id):
            body['OfficeSiteId'] = request.office_site_id
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'GetIdpByOfficeSiteId',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetIdpByOfficeSiteIdResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_idp_by_office_site_id_with_options_async(
        self,
        request: main_models.GetIdpByOfficeSiteIdRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetIdpByOfficeSiteIdResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.office_site_id):
            body['OfficeSiteId'] = request.office_site_id
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'GetIdpByOfficeSiteId',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetIdpByOfficeSiteIdResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_idp_by_office_site_id(
        self,
        request: main_models.GetIdpByOfficeSiteIdRequest,
    ) -> main_models.GetIdpByOfficeSiteIdResponse:
        runtime = RuntimeOptions()
        return self.get_idp_by_office_site_id_with_options(request, runtime)

    async def get_idp_by_office_site_id_async(
        self,
        request: main_models.GetIdpByOfficeSiteIdRequest,
    ) -> main_models.GetIdpByOfficeSiteIdResponse:
        runtime = RuntimeOptions()
        return await self.get_idp_by_office_site_id_with_options_async(request, runtime)

    def get_idp_metadata_with_options(
        self,
        request: main_models.GetIdpMetadataRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetIdpMetadataResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.idp_id):
            body['IdpId'] = request.idp_id
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'GetIdpMetadata',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetIdpMetadataResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_idp_metadata_with_options_async(
        self,
        request: main_models.GetIdpMetadataRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetIdpMetadataResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.idp_id):
            body['IdpId'] = request.idp_id
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'GetIdpMetadata',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetIdpMetadataResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_idp_metadata(
        self,
        request: main_models.GetIdpMetadataRequest,
    ) -> main_models.GetIdpMetadataResponse:
        runtime = RuntimeOptions()
        return self.get_idp_metadata_with_options(request, runtime)

    async def get_idp_metadata_async(
        self,
        request: main_models.GetIdpMetadataRequest,
    ) -> main_models.GetIdpMetadataResponse:
        runtime = RuntimeOptions()
        return await self.get_idp_metadata_with_options_async(request, runtime)

    def get_or_registry_personal_user_by_aliuid_with_options(
        self,
        request: main_models.GetOrRegistryPersonalUserByAliuidRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetOrRegistryPersonalUserByAliuidResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetOrRegistryPersonalUserByAliuid',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetOrRegistryPersonalUserByAliuidResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_or_registry_personal_user_by_aliuid_with_options_async(
        self,
        request: main_models.GetOrRegistryPersonalUserByAliuidRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetOrRegistryPersonalUserByAliuidResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetOrRegistryPersonalUserByAliuid',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetOrRegistryPersonalUserByAliuidResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_or_registry_personal_user_by_aliuid(
        self,
        request: main_models.GetOrRegistryPersonalUserByAliuidRequest,
    ) -> main_models.GetOrRegistryPersonalUserByAliuidResponse:
        runtime = RuntimeOptions()
        return self.get_or_registry_personal_user_by_aliuid_with_options(request, runtime)

    async def get_or_registry_personal_user_by_aliuid_async(
        self,
        request: main_models.GetOrRegistryPersonalUserByAliuidRequest,
    ) -> main_models.GetOrRegistryPersonalUserByAliuidResponse:
        runtime = RuntimeOptions()
        return await self.get_or_registry_personal_user_by_aliuid_with_options_async(request, runtime)

    def get_sso_ticket_with_options(
        self,
        request: main_models.GetSsoTicketRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetSsoTicketResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.access_token):
            body['AccessToken'] = request.access_token
        if not DaraCore.is_null(request.ali_uid):
            body['AliUid'] = request.ali_uid
        if not DaraCore.is_null(request.idp_id):
            body['IdpId'] = request.idp_id
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'GetSsoTicket',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetSsoTicketResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_sso_ticket_with_options_async(
        self,
        request: main_models.GetSsoTicketRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetSsoTicketResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.access_token):
            body['AccessToken'] = request.access_token
        if not DaraCore.is_null(request.ali_uid):
            body['AliUid'] = request.ali_uid
        if not DaraCore.is_null(request.idp_id):
            body['IdpId'] = request.idp_id
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'GetSsoTicket',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetSsoTicketResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_sso_ticket(
        self,
        request: main_models.GetSsoTicketRequest,
    ) -> main_models.GetSsoTicketResponse:
        runtime = RuntimeOptions()
        return self.get_sso_ticket_with_options(request, runtime)

    async def get_sso_ticket_async(
        self,
        request: main_models.GetSsoTicketRequest,
    ) -> main_models.GetSsoTicketResponse:
        runtime = RuntimeOptions()
        return await self.get_sso_ticket_with_options_async(request, runtime)

    def get_sts_token_with_options(
        self,
        request: main_models.GetStsTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetStsTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.account_type):
            query['AccountType'] = request.account_type
        body = {}
        if not DaraCore.is_null(request.auto_create_user):
            body['AutoCreateUser'] = request.auto_create_user
        if not DaraCore.is_null(request.end_user_id):
            body['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.external_user_id):
            body['ExternalUserId'] = request.external_user_id
        if not DaraCore.is_null(request.policy):
            body['Policy'] = request.policy
        if not DaraCore.is_null(request.user_ali_uid):
            body['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query),
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'GetStsToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetStsTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_sts_token_with_options_async(
        self,
        request: main_models.GetStsTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetStsTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.account_type):
            query['AccountType'] = request.account_type
        body = {}
        if not DaraCore.is_null(request.auto_create_user):
            body['AutoCreateUser'] = request.auto_create_user
        if not DaraCore.is_null(request.end_user_id):
            body['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.external_user_id):
            body['ExternalUserId'] = request.external_user_id
        if not DaraCore.is_null(request.policy):
            body['Policy'] = request.policy
        if not DaraCore.is_null(request.user_ali_uid):
            body['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query),
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'GetStsToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetStsTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_sts_token(
        self,
        request: main_models.GetStsTokenRequest,
    ) -> main_models.GetStsTokenResponse:
        runtime = RuntimeOptions()
        return self.get_sts_token_with_options(request, runtime)

    async def get_sts_token_async(
        self,
        request: main_models.GetStsTokenRequest,
    ) -> main_models.GetStsTokenResponse:
        runtime = RuntimeOptions()
        return await self.get_sts_token_with_options_async(request, runtime)

    def get_tenant_password_strategy_with_options(
        self,
        request: main_models.GetTenantPasswordStrategyRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetTenantPasswordStrategyResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetTenantPasswordStrategy',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetTenantPasswordStrategyResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_tenant_password_strategy_with_options_async(
        self,
        request: main_models.GetTenantPasswordStrategyRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetTenantPasswordStrategyResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetTenantPasswordStrategy',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetTenantPasswordStrategyResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_tenant_password_strategy(
        self,
        request: main_models.GetTenantPasswordStrategyRequest,
    ) -> main_models.GetTenantPasswordStrategyResponse:
        runtime = RuntimeOptions()
        return self.get_tenant_password_strategy_with_options(request, runtime)

    async def get_tenant_password_strategy_async(
        self,
        request: main_models.GetTenantPasswordStrategyRequest,
    ) -> main_models.GetTenantPasswordStrategyResponse:
        runtime = RuntimeOptions()
        return await self.get_tenant_password_strategy_with_options_async(request, runtime)

    def inner_scan_qrcode_login_with_options(
        self,
        request: main_models.InnerScanQrcodeLoginRequest,
        runtime: RuntimeOptions,
    ) -> main_models.InnerScanQrcodeLoginResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.client_type):
            query['ClientType'] = request.client_type
        if not DaraCore.is_null(request.network_type):
            query['NetworkType'] = request.network_type
        if not DaraCore.is_null(request.original_client_ip):
            query['OriginalClientIp'] = request.original_client_ip
        if not DaraCore.is_null(request.user_session_info):
            query['UserSessionInfo'] = request.user_session_info
        if not DaraCore.is_null(request.keep_alive):
            query['keepAlive'] = request.keep_alive
        body = {}
        if not DaraCore.is_null(request.client_id):
            body['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_os):
            body['ClientOS'] = request.client_os
        if not DaraCore.is_null(request.client_version):
            body['ClientVersion'] = request.client_version
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query),
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'InnerScanQrcodeLogin',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.InnerScanQrcodeLoginResponse(),
            self.call_api(params, req, runtime)
        )

    async def inner_scan_qrcode_login_with_options_async(
        self,
        request: main_models.InnerScanQrcodeLoginRequest,
        runtime: RuntimeOptions,
    ) -> main_models.InnerScanQrcodeLoginResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.client_type):
            query['ClientType'] = request.client_type
        if not DaraCore.is_null(request.network_type):
            query['NetworkType'] = request.network_type
        if not DaraCore.is_null(request.original_client_ip):
            query['OriginalClientIp'] = request.original_client_ip
        if not DaraCore.is_null(request.user_session_info):
            query['UserSessionInfo'] = request.user_session_info
        if not DaraCore.is_null(request.keep_alive):
            query['keepAlive'] = request.keep_alive
        body = {}
        if not DaraCore.is_null(request.client_id):
            body['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_os):
            body['ClientOS'] = request.client_os
        if not DaraCore.is_null(request.client_version):
            body['ClientVersion'] = request.client_version
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query),
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'InnerScanQrcodeLogin',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.InnerScanQrcodeLoginResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def inner_scan_qrcode_login(
        self,
        request: main_models.InnerScanQrcodeLoginRequest,
    ) -> main_models.InnerScanQrcodeLoginResponse:
        runtime = RuntimeOptions()
        return self.inner_scan_qrcode_login_with_options(request, runtime)

    async def inner_scan_qrcode_login_async(
        self,
        request: main_models.InnerScanQrcodeLoginRequest,
    ) -> main_models.InnerScanQrcodeLoginResponse:
        runtime = RuntimeOptions()
        return await self.inner_scan_qrcode_login_with_options_async(request, runtime)

    def mark_login_token_as_gray_if_necessary_with_options(
        self,
        request: main_models.MarkLoginTokenAsGrayIfNecessaryRequest,
        runtime: RuntimeOptions,
    ) -> main_models.MarkLoginTokenAsGrayIfNecessaryResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.login_token):
            body['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.user_ali_uid):
            body['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'MarkLoginTokenAsGrayIfNecessary',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.MarkLoginTokenAsGrayIfNecessaryResponse(),
            self.call_api(params, req, runtime)
        )

    async def mark_login_token_as_gray_if_necessary_with_options_async(
        self,
        request: main_models.MarkLoginTokenAsGrayIfNecessaryRequest,
        runtime: RuntimeOptions,
    ) -> main_models.MarkLoginTokenAsGrayIfNecessaryResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.login_token):
            body['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.user_ali_uid):
            body['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'MarkLoginTokenAsGrayIfNecessary',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.MarkLoginTokenAsGrayIfNecessaryResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def mark_login_token_as_gray_if_necessary(
        self,
        request: main_models.MarkLoginTokenAsGrayIfNecessaryRequest,
    ) -> main_models.MarkLoginTokenAsGrayIfNecessaryResponse:
        runtime = RuntimeOptions()
        return self.mark_login_token_as_gray_if_necessary_with_options(request, runtime)

    async def mark_login_token_as_gray_if_necessary_async(
        self,
        request: main_models.MarkLoginTokenAsGrayIfNecessaryRequest,
    ) -> main_models.MarkLoginTokenAsGrayIfNecessaryResponse:
        runtime = RuntimeOptions()
        return await self.mark_login_token_as_gray_if_necessary_with_options_async(request, runtime)

    def mock_session_token_with_options(
        self,
        request: main_models.MockSessionTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.MockSessionTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.account_type):
            query['AccountType'] = request.account_type
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.instance_id):
            query['InstanceId'] = request.instance_id
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'MockSessionToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.MockSessionTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def mock_session_token_with_options_async(
        self,
        request: main_models.MockSessionTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.MockSessionTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.account_type):
            query['AccountType'] = request.account_type
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.instance_id):
            query['InstanceId'] = request.instance_id
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'MockSessionToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.MockSessionTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def mock_session_token(
        self,
        request: main_models.MockSessionTokenRequest,
    ) -> main_models.MockSessionTokenResponse:
        runtime = RuntimeOptions()
        return self.mock_session_token_with_options(request, runtime)

    async def mock_session_token_async(
        self,
        request: main_models.MockSessionTokenRequest,
    ) -> main_models.MockSessionTokenResponse:
        runtime = RuntimeOptions()
        return await self.mock_session_token_with_options_async(request, runtime)

    def modify_session_config_with_options(
        self,
        request: main_models.ModifySessionConfigRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ModifySessionConfigResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.keep_alive_token_valid_time):
            query['KeepAliveTokenValidTime'] = request.keep_alive_token_valid_time
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'ModifySessionConfig',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ModifySessionConfigResponse(),
            self.call_api(params, req, runtime)
        )

    async def modify_session_config_with_options_async(
        self,
        request: main_models.ModifySessionConfigRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ModifySessionConfigResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.keep_alive_token_valid_time):
            query['KeepAliveTokenValidTime'] = request.keep_alive_token_valid_time
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'ModifySessionConfig',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ModifySessionConfigResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def modify_session_config(
        self,
        request: main_models.ModifySessionConfigRequest,
    ) -> main_models.ModifySessionConfigResponse:
        runtime = RuntimeOptions()
        return self.modify_session_config_with_options(request, runtime)

    async def modify_session_config_async(
        self,
        request: main_models.ModifySessionConfigRequest,
    ) -> main_models.ModifySessionConfigResponse:
        runtime = RuntimeOptions()
        return await self.modify_session_config_with_options_async(request, runtime)

    def personal_create_sso_user_session_with_options(
        self,
        request: main_models.PersonalCreateSsoUserSessionRequest,
        runtime: RuntimeOptions,
    ) -> main_models.PersonalCreateSsoUserSessionResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.account_type):
            body['AccountType'] = request.account_type
        if not DaraCore.is_null(request.avatar_url):
            body['AvatarUrl'] = request.avatar_url
        if not DaraCore.is_null(request.external_id):
            body['ExternalId'] = request.external_id
        if not DaraCore.is_null(request.nick_name):
            body['NickName'] = request.nick_name
        if not DaraCore.is_null(request.user_ali_uid):
            body['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'PersonalCreateSsoUserSession',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.PersonalCreateSsoUserSessionResponse(),
            self.call_api(params, req, runtime)
        )

    async def personal_create_sso_user_session_with_options_async(
        self,
        request: main_models.PersonalCreateSsoUserSessionRequest,
        runtime: RuntimeOptions,
    ) -> main_models.PersonalCreateSsoUserSessionResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.account_type):
            body['AccountType'] = request.account_type
        if not DaraCore.is_null(request.avatar_url):
            body['AvatarUrl'] = request.avatar_url
        if not DaraCore.is_null(request.external_id):
            body['ExternalId'] = request.external_id
        if not DaraCore.is_null(request.nick_name):
            body['NickName'] = request.nick_name
        if not DaraCore.is_null(request.user_ali_uid):
            body['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'PersonalCreateSsoUserSession',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.PersonalCreateSsoUserSessionResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def personal_create_sso_user_session(
        self,
        request: main_models.PersonalCreateSsoUserSessionRequest,
    ) -> main_models.PersonalCreateSsoUserSessionResponse:
        runtime = RuntimeOptions()
        return self.personal_create_sso_user_session_with_options(request, runtime)

    async def personal_create_sso_user_session_async(
        self,
        request: main_models.PersonalCreateSsoUserSessionRequest,
    ) -> main_models.PersonalCreateSsoUserSessionResponse:
        runtime = RuntimeOptions()
        return await self.personal_create_sso_user_session_with_options_async(request, runtime)

    def refresh_login_token_with_options(
        self,
        request: main_models.RefreshLoginTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.RefreshLoginTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.client_id):
            query['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_type):
            query['ClientType'] = request.client_type
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.login_identifier):
            query['LoginIdentifier'] = request.login_identifier
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        if not DaraCore.is_null(request.uuid):
            query['Uuid'] = request.uuid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'RefreshLoginToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.RefreshLoginTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def refresh_login_token_with_options_async(
        self,
        request: main_models.RefreshLoginTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.RefreshLoginTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.client_id):
            query['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_type):
            query['ClientType'] = request.client_type
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.login_identifier):
            query['LoginIdentifier'] = request.login_identifier
        if not DaraCore.is_null(request.login_token):
            query['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.session_id):
            query['SessionId'] = request.session_id
        if not DaraCore.is_null(request.uuid):
            query['Uuid'] = request.uuid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'RefreshLoginToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.RefreshLoginTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def refresh_login_token(
        self,
        request: main_models.RefreshLoginTokenRequest,
    ) -> main_models.RefreshLoginTokenResponse:
        runtime = RuntimeOptions()
        return self.refresh_login_token_with_options(request, runtime)

    async def refresh_login_token_async(
        self,
        request: main_models.RefreshLoginTokenRequest,
    ) -> main_models.RefreshLoginTokenResponse:
        runtime = RuntimeOptions()
        return await self.refresh_login_token_with_options_async(request, runtime)

    def reset_password_with_options(
        self,
        request: main_models.ResetPasswordRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ResetPasswordResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.client_id):
            query['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_token):
            query['ClientToken'] = request.client_token
        if not DaraCore.is_null(request.email):
            query['Email'] = request.email
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.phone):
            query['Phone'] = request.phone
        if not DaraCore.is_null(request.product_type):
            query['ProductType'] = request.product_type
        if not DaraCore.is_null(request.region_id):
            query['RegionId'] = request.region_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'ResetPassword',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ResetPasswordResponse(),
            self.call_api(params, req, runtime)
        )

    async def reset_password_with_options_async(
        self,
        request: main_models.ResetPasswordRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ResetPasswordResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.client_id):
            query['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_token):
            query['ClientToken'] = request.client_token
        if not DaraCore.is_null(request.email):
            query['Email'] = request.email
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.office_site_id):
            query['OfficeSiteId'] = request.office_site_id
        if not DaraCore.is_null(request.phone):
            query['Phone'] = request.phone
        if not DaraCore.is_null(request.product_type):
            query['ProductType'] = request.product_type
        if not DaraCore.is_null(request.region_id):
            query['RegionId'] = request.region_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'ResetPassword',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ResetPasswordResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def reset_password(
        self,
        request: main_models.ResetPasswordRequest,
    ) -> main_models.ResetPasswordResponse:
        runtime = RuntimeOptions()
        return self.reset_password_with_options(request, runtime)

    async def reset_password_async(
        self,
        request: main_models.ResetPasswordRequest,
    ) -> main_models.ResetPasswordResponse:
        runtime = RuntimeOptions()
        return await self.reset_password_with_options_async(request, runtime)

    def routing_get_login_token_with_options(
        self,
        request: main_models.RoutingGetLoginTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.RoutingGetLoginTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.params):
            query['Params'] = request.params
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'RoutingGetLoginToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.RoutingGetLoginTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def routing_get_login_token_with_options_async(
        self,
        request: main_models.RoutingGetLoginTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.RoutingGetLoginTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.params):
            query['Params'] = request.params
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'RoutingGetLoginToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.RoutingGetLoginTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def routing_get_login_token(
        self,
        request: main_models.RoutingGetLoginTokenRequest,
    ) -> main_models.RoutingGetLoginTokenResponse:
        runtime = RuntimeOptions()
        return self.routing_get_login_token_with_options(request, runtime)

    async def routing_get_login_token_async(
        self,
        request: main_models.RoutingGetLoginTokenRequest,
    ) -> main_models.RoutingGetLoginTokenResponse:
        runtime = RuntimeOptions()
        return await self.routing_get_login_token_with_options_async(request, runtime)

    def student_create_sso_user_session_with_options(
        self,
        request: main_models.StudentCreateSsoUserSessionRequest,
        runtime: RuntimeOptions,
    ) -> main_models.StudentCreateSsoUserSessionResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        body = {}
        if not DaraCore.is_null(request.nick_name):
            body['NickName'] = request.nick_name
        if not DaraCore.is_null(request.user_ali_uid):
            body['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query),
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'StudentCreateSsoUserSession',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.StudentCreateSsoUserSessionResponse(),
            self.call_api(params, req, runtime)
        )

    async def student_create_sso_user_session_with_options_async(
        self,
        request: main_models.StudentCreateSsoUserSessionRequest,
        runtime: RuntimeOptions,
    ) -> main_models.StudentCreateSsoUserSessionResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        body = {}
        if not DaraCore.is_null(request.nick_name):
            body['NickName'] = request.nick_name
        if not DaraCore.is_null(request.user_ali_uid):
            body['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query),
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'StudentCreateSsoUserSession',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.StudentCreateSsoUserSessionResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def student_create_sso_user_session(
        self,
        request: main_models.StudentCreateSsoUserSessionRequest,
    ) -> main_models.StudentCreateSsoUserSessionResponse:
        runtime = RuntimeOptions()
        return self.student_create_sso_user_session_with_options(request, runtime)

    async def student_create_sso_user_session_async(
        self,
        request: main_models.StudentCreateSsoUserSessionRequest,
    ) -> main_models.StudentCreateSsoUserSessionResponse:
        runtime = RuntimeOptions()
        return await self.student_create_sso_user_session_with_options_async(request, runtime)

    def validate_external_login_token_with_options(
        self,
        request: main_models.ValidateExternalLoginTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ValidateExternalLoginTokenResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.client_channel):
            body['ClientChannel'] = request.client_channel
        if not DaraCore.is_null(request.client_id):
            body['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_ip):
            body['ClientIp'] = request.client_ip
        if not DaraCore.is_null(request.client_os):
            body['ClientOS'] = request.client_os
        if not DaraCore.is_null(request.client_version):
            body['ClientVersion'] = request.client_version
        if not DaraCore.is_null(request.external_client_id):
            body['ExternalClientId'] = request.external_client_id
        if not DaraCore.is_null(request.login_token):
            body['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.session_id):
            body['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'ValidateExternalLoginToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ValidateExternalLoginTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def validate_external_login_token_with_options_async(
        self,
        request: main_models.ValidateExternalLoginTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ValidateExternalLoginTokenResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.client_channel):
            body['ClientChannel'] = request.client_channel
        if not DaraCore.is_null(request.client_id):
            body['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_ip):
            body['ClientIp'] = request.client_ip
        if not DaraCore.is_null(request.client_os):
            body['ClientOS'] = request.client_os
        if not DaraCore.is_null(request.client_version):
            body['ClientVersion'] = request.client_version
        if not DaraCore.is_null(request.external_client_id):
            body['ExternalClientId'] = request.external_client_id
        if not DaraCore.is_null(request.login_token):
            body['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.session_id):
            body['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'ValidateExternalLoginToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ValidateExternalLoginTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def validate_external_login_token(
        self,
        request: main_models.ValidateExternalLoginTokenRequest,
    ) -> main_models.ValidateExternalLoginTokenResponse:
        runtime = RuntimeOptions()
        return self.validate_external_login_token_with_options(request, runtime)

    async def validate_external_login_token_async(
        self,
        request: main_models.ValidateExternalLoginTokenRequest,
    ) -> main_models.ValidateExternalLoginTokenResponse:
        runtime = RuntimeOptions()
        return await self.validate_external_login_token_with_options_async(request, runtime)

    def validate_login_token_with_options(
        self,
        request: main_models.ValidateLoginTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ValidateLoginTokenResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.client_channel):
            body['ClientChannel'] = request.client_channel
        if not DaraCore.is_null(request.client_id):
            body['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_ip):
            body['ClientIp'] = request.client_ip
        if not DaraCore.is_null(request.client_os):
            body['ClientOS'] = request.client_os
        if not DaraCore.is_null(request.client_version):
            body['ClientVersion'] = request.client_version
        if not DaraCore.is_null(request.external_client_id):
            body['ExternalClientId'] = request.external_client_id
        if not DaraCore.is_null(request.login_token):
            body['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.session_id):
            body['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'ValidateLoginToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ValidateLoginTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def validate_login_token_with_options_async(
        self,
        request: main_models.ValidateLoginTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ValidateLoginTokenResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.client_channel):
            body['ClientChannel'] = request.client_channel
        if not DaraCore.is_null(request.client_id):
            body['ClientId'] = request.client_id
        if not DaraCore.is_null(request.client_ip):
            body['ClientIp'] = request.client_ip
        if not DaraCore.is_null(request.client_os):
            body['ClientOS'] = request.client_os
        if not DaraCore.is_null(request.client_version):
            body['ClientVersion'] = request.client_version
        if not DaraCore.is_null(request.external_client_id):
            body['ExternalClientId'] = request.external_client_id
        if not DaraCore.is_null(request.login_token):
            body['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.session_id):
            body['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'ValidateLoginToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ValidateLoginTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def validate_login_token(
        self,
        request: main_models.ValidateLoginTokenRequest,
    ) -> main_models.ValidateLoginTokenResponse:
        runtime = RuntimeOptions()
        return self.validate_login_token_with_options(request, runtime)

    async def validate_login_token_async(
        self,
        request: main_models.ValidateLoginTokenRequest,
    ) -> main_models.ValidateLoginTokenResponse:
        runtime = RuntimeOptions()
        return await self.validate_login_token_with_options_async(request, runtime)

    def validate_ntlmchallenge_with_options(
        self,
        request: main_models.ValidateNTLMChallengeRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ValidateNTLMChallengeResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.auth_domain):
            body['AuthDomain'] = request.auth_domain
        if not DaraCore.is_null(request.auth_user):
            body['AuthUser'] = request.auth_user
        if not DaraCore.is_null(request.nt_response):
            body['NtResponse'] = request.nt_response
        if not DaraCore.is_null(request.server_challenge):
            body['ServerChallenge'] = request.server_challenge
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'ValidateNTLMChallenge',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ValidateNTLMChallengeResponse(),
            self.call_api(params, req, runtime)
        )

    async def validate_ntlmchallenge_with_options_async(
        self,
        request: main_models.ValidateNTLMChallengeRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ValidateNTLMChallengeResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.auth_domain):
            body['AuthDomain'] = request.auth_domain
        if not DaraCore.is_null(request.auth_user):
            body['AuthUser'] = request.auth_user
        if not DaraCore.is_null(request.nt_response):
            body['NtResponse'] = request.nt_response
        if not DaraCore.is_null(request.server_challenge):
            body['ServerChallenge'] = request.server_challenge
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'ValidateNTLMChallenge',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ValidateNTLMChallengeResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def validate_ntlmchallenge(
        self,
        request: main_models.ValidateNTLMChallengeRequest,
    ) -> main_models.ValidateNTLMChallengeResponse:
        runtime = RuntimeOptions()
        return self.validate_ntlmchallenge_with_options(request, runtime)

    async def validate_ntlmchallenge_async(
        self,
        request: main_models.ValidateNTLMChallengeRequest,
    ) -> main_models.ValidateNTLMChallengeResponse:
        runtime = RuntimeOptions()
        return await self.validate_ntlmchallenge_with_options_async(request, runtime)

    def validate_session_token_with_options(
        self,
        request: main_models.ValidateSessionTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ValidateSessionTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.session_token):
            query['SessionToken'] = request.session_token
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'ValidateSessionToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ValidateSessionTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def validate_session_token_with_options_async(
        self,
        request: main_models.ValidateSessionTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ValidateSessionTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.session_token):
            query['SessionToken'] = request.session_token
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'ValidateSessionToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ValidateSessionTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def validate_session_token(
        self,
        request: main_models.ValidateSessionTokenRequest,
    ) -> main_models.ValidateSessionTokenResponse:
        runtime = RuntimeOptions()
        return self.validate_session_token_with_options(request, runtime)

    async def validate_session_token_async(
        self,
        request: main_models.ValidateSessionTokenRequest,
    ) -> main_models.ValidateSessionTokenResponse:
        runtime = RuntimeOptions()
        return await self.validate_session_token_with_options_async(request, runtime)

    def validate_sts_token_with_options(
        self,
        request: main_models.ValidateStsTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ValidateStsTokenResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.session_id):
            body['SessionId'] = request.session_id
        if not DaraCore.is_null(request.sts_token):
            body['StsToken'] = request.sts_token
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'ValidateStsToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ValidateStsTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def validate_sts_token_with_options_async(
        self,
        request: main_models.ValidateStsTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ValidateStsTokenResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.session_id):
            body['SessionId'] = request.session_id
        if not DaraCore.is_null(request.sts_token):
            body['StsToken'] = request.sts_token
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'ValidateStsToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ValidateStsTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def validate_sts_token(
        self,
        request: main_models.ValidateStsTokenRequest,
    ) -> main_models.ValidateStsTokenResponse:
        runtime = RuntimeOptions()
        return self.validate_sts_token_with_options(request, runtime)

    async def validate_sts_token_async(
        self,
        request: main_models.ValidateStsTokenRequest,
    ) -> main_models.ValidateStsTokenResponse:
        runtime = RuntimeOptions()
        return await self.validate_sts_token_with_options_async(request, runtime)

    def validate_website_admin_login_token_with_options(
        self,
        request: main_models.ValidateWebsiteAdminLoginTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ValidateWebsiteAdminLoginTokenResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.login_token):
            body['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.session_id):
            body['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'ValidateWebsiteAdminLoginToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ValidateWebsiteAdminLoginTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def validate_website_admin_login_token_with_options_async(
        self,
        request: main_models.ValidateWebsiteAdminLoginTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ValidateWebsiteAdminLoginTokenResponse:
        request.validate()
        body = {}
        if not DaraCore.is_null(request.login_token):
            body['LoginToken'] = request.login_token
        if not DaraCore.is_null(request.session_id):
            body['SessionId'] = request.session_id
        req = open_api_util_models.OpenApiRequest(
            body = Utils.parse_to_map(body)
        )
        params = open_api_util_models.Params(
            action = 'ValidateWebsiteAdminLoginToken',
            version = '2021-12-12',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ValidateWebsiteAdminLoginTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def validate_website_admin_login_token(
        self,
        request: main_models.ValidateWebsiteAdminLoginTokenRequest,
    ) -> main_models.ValidateWebsiteAdminLoginTokenResponse:
        runtime = RuntimeOptions()
        return self.validate_website_admin_login_token_with_options(request, runtime)

    async def validate_website_admin_login_token_async(
        self,
        request: main_models.ValidateWebsiteAdminLoginTokenRequest,
    ) -> main_models.ValidateWebsiteAdminLoginTokenResponse:
        runtime = RuntimeOptions()
        return await self.validate_website_admin_login_token_with_options_async(request, runtime)
