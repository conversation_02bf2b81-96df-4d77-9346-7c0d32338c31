# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class AppAuthLoginGetUserInfoResponseBody(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        email: str = None,
        open_id: str = None,
        phone: str = None,
        profile: str = None,
        request_id: str = None,
        user_name: str = None,
    ):
        self.ali_uid = ali_uid
        self.email = email
        self.open_id = open_id
        self.phone = phone
        self.profile = profile
        self.request_id = request_id
        self.user_name = user_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.email is not None:
            result['Email'] = self.email

        if self.open_id is not None:
            result['OpenId'] = self.open_id

        if self.phone is not None:
            result['Phone'] = self.phone

        if self.profile is not None:
            result['Profile'] = self.profile

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.user_name is not None:
            result['UserName'] = self.user_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('Email') is not None:
            self.email = m.get('Email')

        if m.get('OpenId') is not None:
            self.open_id = m.get('OpenId')

        if m.get('Phone') is not None:
            self.phone = m.get('Phone')

        if m.get('Profile') is not None:
            self.profile = m.get('Profile')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('UserName') is not None:
            self.user_name = m.get('UserName')

        return self

