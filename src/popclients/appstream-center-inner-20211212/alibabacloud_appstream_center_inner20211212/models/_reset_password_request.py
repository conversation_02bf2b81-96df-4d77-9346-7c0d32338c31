# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class ResetPasswordRequest(DaraModel):
    def __init__(
        self,
        client_id: str = None,
        client_token: str = None,
        email: str = None,
        end_user_id: str = None,
        office_site_id: str = None,
        phone: str = None,
        product_type: str = None,
        region_id: str = None,
    ):
        # This parameter is required.
        self.client_id = client_id
        self.client_token = client_token
        self.email = email
        # This parameter is required.
        self.end_user_id = end_user_id
        self.office_site_id = office_site_id
        self.phone = phone
        self.product_type = product_type
        self.region_id = region_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.client_id is not None:
            result['ClientId'] = self.client_id

        if self.client_token is not None:
            result['ClientToken'] = self.client_token

        if self.email is not None:
            result['Email'] = self.email

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.office_site_id is not None:
            result['OfficeSiteId'] = self.office_site_id

        if self.phone is not None:
            result['Phone'] = self.phone

        if self.product_type is not None:
            result['ProductType'] = self.product_type

        if self.region_id is not None:
            result['RegionId'] = self.region_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ClientId') is not None:
            self.client_id = m.get('ClientId')

        if m.get('ClientToken') is not None:
            self.client_token = m.get('ClientToken')

        if m.get('Email') is not None:
            self.email = m.get('Email')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('OfficeSiteId') is not None:
            self.office_site_id = m.get('OfficeSiteId')

        if m.get('Phone') is not None:
            self.phone = m.get('Phone')

        if m.get('ProductType') is not None:
            self.product_type = m.get('ProductType')

        if m.get('RegionId') is not None:
            self.region_id = m.get('RegionId')

        return self

