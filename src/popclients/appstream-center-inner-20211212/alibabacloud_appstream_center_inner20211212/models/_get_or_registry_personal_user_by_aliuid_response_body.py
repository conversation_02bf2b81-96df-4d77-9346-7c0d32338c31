# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_appstream_center_inner20211212 import models as main_models 


class GetOrRegistryPersonalUserByAliuidResponseBody(DaraModel):
    def __init__(
        self,
        request_id: str = None,
        user: main_models.GetOrRegistryPersonalUserByAliuidResponseBodyUser = None,
    ):
        self.request_id = request_id
        self.user = user

    def validate(self):
        if self.user:
            self.user.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.user is not None:
            result['User'] = self.user.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('User') is not None:
            temp_model = main_models.GetOrRegistryPersonalUserByAliuidResponseBodyUser()
            self.user = temp_model.from_map(m.get('User'))

        return self

class GetOrRegistryPersonalUserByAliuidResponseBodyUser(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        is_newly_user: bool = None,
        phone: str = None,
        union_id: str = None,
        wy_id: str = None,
    ):
        self.ali_uid = ali_uid
        self.is_newly_user = is_newly_user
        self.phone = phone
        self.union_id = union_id
        self.wy_id = wy_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.is_newly_user is not None:
            result['IsNewlyUser'] = self.is_newly_user

        if self.phone is not None:
            result['Phone'] = self.phone

        if self.union_id is not None:
            result['UnionId'] = self.union_id

        if self.wy_id is not None:
            result['WyId'] = self.wy_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('IsNewlyUser') is not None:
            self.is_newly_user = m.get('IsNewlyUser')

        if m.get('Phone') is not None:
            self.phone = m.get('Phone')

        if m.get('UnionId') is not None:
            self.union_id = m.get('UnionId')

        if m.get('WyId') is not None:
            self.wy_id = m.get('WyId')

        return self

