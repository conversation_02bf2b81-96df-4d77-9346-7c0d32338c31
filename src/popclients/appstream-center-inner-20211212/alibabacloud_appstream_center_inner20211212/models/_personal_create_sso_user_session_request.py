# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class PersonalCreateSsoUserSessionRequest(DaraModel):
    def __init__(
        self,
        account_type: str = None,
        avatar_url: str = None,
        external_id: str = None,
        nick_name: str = None,
        user_ali_uid: int = None,
    ):
        self.account_type = account_type
        self.avatar_url = avatar_url
        # This parameter is required.
        self.external_id = external_id
        self.nick_name = nick_name
        # This parameter is required.
        self.user_ali_uid = user_ali_uid

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.account_type is not None:
            result['AccountType'] = self.account_type

        if self.avatar_url is not None:
            result['AvatarUrl'] = self.avatar_url

        if self.external_id is not None:
            result['ExternalId'] = self.external_id

        if self.nick_name is not None:
            result['NickName'] = self.nick_name

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccountType') is not None:
            self.account_type = m.get('AccountType')

        if m.get('AvatarUrl') is not None:
            self.avatar_url = m.get('AvatarUrl')

        if m.get('ExternalId') is not None:
            self.external_id = m.get('ExternalId')

        if m.get('NickName') is not None:
            self.nick_name = m.get('NickName')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        return self

