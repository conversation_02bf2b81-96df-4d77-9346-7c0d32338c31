# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class MockSessionTokenRequest(DaraModel):
    def __init__(
        self,
        account_type: str = None,
        end_user_id: str = None,
        instance_id: str = None,
        office_site_id: str = None,
    ):
        # This parameter is required.
        self.account_type = account_type
        self.end_user_id = end_user_id
        self.instance_id = instance_id
        self.office_site_id = office_site_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.account_type is not None:
            result['AccountType'] = self.account_type

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.instance_id is not None:
            result['InstanceId'] = self.instance_id

        if self.office_site_id is not None:
            result['OfficeSiteId'] = self.office_site_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccountType') is not None:
            self.account_type = m.get('AccountType')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('InstanceId') is not None:
            self.instance_id = m.get('InstanceId')

        if m.get('OfficeSiteId') is not None:
            self.office_site_id = m.get('OfficeSiteId')

        return self

