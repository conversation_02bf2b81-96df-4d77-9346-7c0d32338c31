# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_appstream_center_inner20211212 import models as main_models 


class ValidateStsTokenResponseBody(DaraModel):
    def __init__(
        self,
        login_token_model: main_models.ValidateStsTokenResponseBodyLoginTokenModel = None,
        request_id: str = None,
    ):
        self.login_token_model = login_token_model
        self.request_id = request_id

    def validate(self):
        if self.login_token_model:
            self.login_token_model.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.login_token_model is not None:
            result['LoginTokenModel'] = self.login_token_model.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('LoginTokenModel') is not None:
            temp_model = main_models.ValidateStsTokenResponseBodyLoginTokenModel()
            self.login_token_model = temp_model.from_map(m.get('LoginTokenModel'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class ValidateStsTokenResponseBodyLoginTokenModel(DaraModel):
    def __init__(
        self,
        account_type: str = None,
        ali_uid: int = None,
        end_user_id: str = None,
        permission_policy: str = None,
        wy_id: str = None,
    ):
        self.account_type = account_type
        self.ali_uid = ali_uid
        self.end_user_id = end_user_id
        self.permission_policy = permission_policy
        self.wy_id = wy_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.account_type is not None:
            result['AccountType'] = self.account_type

        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.permission_policy is not None:
            result['PermissionPolicy'] = self.permission_policy

        if self.wy_id is not None:
            result['WyId'] = self.wy_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccountType') is not None:
            self.account_type = m.get('AccountType')

        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('PermissionPolicy') is not None:
            self.permission_policy = m.get('PermissionPolicy')

        if m.get('WyId') is not None:
            self.wy_id = m.get('WyId')

        return self

