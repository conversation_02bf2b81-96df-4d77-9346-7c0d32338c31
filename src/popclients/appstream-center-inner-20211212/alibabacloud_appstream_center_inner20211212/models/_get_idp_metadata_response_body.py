# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from alibabacloud_appstream_center_inner20211212 import models as main_models 
from typing import List


class GetIdpMetadataResponseBody(DaraModel):
    def __init__(
        self,
        idp_metadata: main_models.GetIdpMetadataResponseBodyIdpMetadata = None,
        request_id: str = None,
    ):
        self.idp_metadata = idp_metadata
        self.request_id = request_id

    def validate(self):
        if self.idp_metadata:
            self.idp_metadata.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.idp_metadata is not None:
            result['IdpMetadata'] = self.idp_metadata.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('IdpMetadata') is not None:
            temp_model = main_models.GetIdpMetadataResponseBodyIdpMetadata()
            self.idp_metadata = temp_model.from_map(m.get('IdpMetadata'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class GetIdpMetadataResponseBodyIdpMetadata(DaraModel):
    def __init__(
        self,
        account_type: str = None,
        ali_uid: int = None,
        content: str = None,
        idp_id: str = None,
        idp_name: str = None,
        idp_protocol: str = None,
        idp_provider: str = None,
        login_mapping_field: str = None,
        mapping_field_in_db: str = None,
        oidcmetadata: main_models.GetIdpMetadataResponseBodyIdpMetadataOIDCMetadata = None,
        oauth_metadata: main_models.GetIdpMetadataResponseBodyIdpMetadataOauthMetadata = None,
        saml_metadata: main_models.GetIdpMetadataResponseBodyIdpMetadataSamlMetadata = None,
    ):
        self.account_type = account_type
        self.ali_uid = ali_uid
        self.content = content
        self.idp_id = idp_id
        self.idp_name = idp_name
        self.idp_protocol = idp_protocol
        self.idp_provider = idp_provider
        self.login_mapping_field = login_mapping_field
        self.mapping_field_in_db = mapping_field_in_db
        self.oidcmetadata = oidcmetadata
        self.oauth_metadata = oauth_metadata
        self.saml_metadata = saml_metadata

    def validate(self):
        if self.oidcmetadata:
            self.oidcmetadata.validate()
        if self.oauth_metadata:
            self.oauth_metadata.validate()
        if self.saml_metadata:
            self.saml_metadata.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.account_type is not None:
            result['AccountType'] = self.account_type

        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.content is not None:
            result['Content'] = self.content

        if self.idp_id is not None:
            result['IdpId'] = self.idp_id

        if self.idp_name is not None:
            result['IdpName'] = self.idp_name

        if self.idp_protocol is not None:
            result['IdpProtocol'] = self.idp_protocol

        if self.idp_provider is not None:
            result['IdpProvider'] = self.idp_provider

        if self.login_mapping_field is not None:
            result['LoginMappingField'] = self.login_mapping_field

        if self.mapping_field_in_db is not None:
            result['MappingFieldInDb'] = self.mapping_field_in_db

        if self.oidcmetadata is not None:
            result['OIDCMetadata'] = self.oidcmetadata.to_map()

        if self.oauth_metadata is not None:
            result['OauthMetadata'] = self.oauth_metadata.to_map()

        if self.saml_metadata is not None:
            result['SamlMetadata'] = self.saml_metadata.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccountType') is not None:
            self.account_type = m.get('AccountType')

        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('Content') is not None:
            self.content = m.get('Content')

        if m.get('IdpId') is not None:
            self.idp_id = m.get('IdpId')

        if m.get('IdpName') is not None:
            self.idp_name = m.get('IdpName')

        if m.get('IdpProtocol') is not None:
            self.idp_protocol = m.get('IdpProtocol')

        if m.get('IdpProvider') is not None:
            self.idp_provider = m.get('IdpProvider')

        if m.get('LoginMappingField') is not None:
            self.login_mapping_field = m.get('LoginMappingField')

        if m.get('MappingFieldInDb') is not None:
            self.mapping_field_in_db = m.get('MappingFieldInDb')

        if m.get('OIDCMetadata') is not None:
            temp_model = main_models.GetIdpMetadataResponseBodyIdpMetadataOIDCMetadata()
            self.oidcmetadata = temp_model.from_map(m.get('OIDCMetadata'))

        if m.get('OauthMetadata') is not None:
            temp_model = main_models.GetIdpMetadataResponseBodyIdpMetadataOauthMetadata()
            self.oauth_metadata = temp_model.from_map(m.get('OauthMetadata'))

        if m.get('SamlMetadata') is not None:
            temp_model = main_models.GetIdpMetadataResponseBodyIdpMetadataSamlMetadata()
            self.saml_metadata = temp_model.from_map(m.get('SamlMetadata'))

        return self

class GetIdpMetadataResponseBodyIdpMetadataSamlMetadata(DaraModel):
    def __init__(
        self,
        idp_encrypt_cert: str = None,
        idp_entity_id: str = None,
        idp_logout_location: str = None,
        idp_sign_cert: str = None,
        idp_sign_certs: List[str] = None,
        idp_sso_location: str = None,
        name: str = None,
        sp_acs_url: str = None,
        sp_entity_id: str = None,
        sp_sign_private_key: str = None,
    ):
        self.idp_encrypt_cert = idp_encrypt_cert
        self.idp_entity_id = idp_entity_id
        self.idp_logout_location = idp_logout_location
        self.idp_sign_cert = idp_sign_cert
        self.idp_sign_certs = idp_sign_certs
        self.idp_sso_location = idp_sso_location
        self.name = name
        self.sp_acs_url = sp_acs_url
        self.sp_entity_id = sp_entity_id
        self.sp_sign_private_key = sp_sign_private_key

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.idp_encrypt_cert is not None:
            result['IdpEncryptCert'] = self.idp_encrypt_cert

        if self.idp_entity_id is not None:
            result['IdpEntityId'] = self.idp_entity_id

        if self.idp_logout_location is not None:
            result['IdpLogoutLocation'] = self.idp_logout_location

        if self.idp_sign_cert is not None:
            result['IdpSignCert'] = self.idp_sign_cert

        if self.idp_sign_certs is not None:
            result['IdpSignCerts'] = self.idp_sign_certs

        if self.idp_sso_location is not None:
            result['IdpSsoLocation'] = self.idp_sso_location

        if self.name is not None:
            result['Name'] = self.name

        if self.sp_acs_url is not None:
            result['SpAcsUrl'] = self.sp_acs_url

        if self.sp_entity_id is not None:
            result['SpEntityId'] = self.sp_entity_id

        if self.sp_sign_private_key is not None:
            result['SpSignPrivateKey'] = self.sp_sign_private_key

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('IdpEncryptCert') is not None:
            self.idp_encrypt_cert = m.get('IdpEncryptCert')

        if m.get('IdpEntityId') is not None:
            self.idp_entity_id = m.get('IdpEntityId')

        if m.get('IdpLogoutLocation') is not None:
            self.idp_logout_location = m.get('IdpLogoutLocation')

        if m.get('IdpSignCert') is not None:
            self.idp_sign_cert = m.get('IdpSignCert')

        if m.get('IdpSignCerts') is not None:
            self.idp_sign_certs = m.get('IdpSignCerts')

        if m.get('IdpSsoLocation') is not None:
            self.idp_sso_location = m.get('IdpSsoLocation')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('SpAcsUrl') is not None:
            self.sp_acs_url = m.get('SpAcsUrl')

        if m.get('SpEntityId') is not None:
            self.sp_entity_id = m.get('SpEntityId')

        if m.get('SpSignPrivateKey') is not None:
            self.sp_sign_private_key = m.get('SpSignPrivateKey')

        return self

class GetIdpMetadataResponseBodyIdpMetadataOauthMetadata(DaraModel):
    def __init__(
        self,
        access_token_endpoint: str = None,
        app_code: str = None,
        authority_endpoint: str = None,
        client_id: str = None,
        client_secret: str = None,
        idp_host_name: str = None,
        idp_login_cookies: str = None,
        idp_provider: str = None,
        login_endpoint: str = None,
        logout_endpoint: str = None,
        name: str = None,
        sign_public_key_endpoint: str = None,
        sso_ticket_endpoint: str = None,
        sso_ticket_key: str = None,
        token_endpoint: str = None,
    ):
        self.access_token_endpoint = access_token_endpoint
        self.app_code = app_code
        self.authority_endpoint = authority_endpoint
        self.client_id = client_id
        self.client_secret = client_secret
        self.idp_host_name = idp_host_name
        self.idp_login_cookies = idp_login_cookies
        self.idp_provider = idp_provider
        self.login_endpoint = login_endpoint
        self.logout_endpoint = logout_endpoint
        self.name = name
        self.sign_public_key_endpoint = sign_public_key_endpoint
        self.sso_ticket_endpoint = sso_ticket_endpoint
        self.sso_ticket_key = sso_ticket_key
        self.token_endpoint = token_endpoint

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.access_token_endpoint is not None:
            result['AccessTokenEndpoint'] = self.access_token_endpoint

        if self.app_code is not None:
            result['AppCode'] = self.app_code

        if self.authority_endpoint is not None:
            result['AuthorityEndpoint'] = self.authority_endpoint

        if self.client_id is not None:
            result['ClientId'] = self.client_id

        if self.client_secret is not None:
            result['ClientSecret'] = self.client_secret

        if self.idp_host_name is not None:
            result['IdpHostName'] = self.idp_host_name

        if self.idp_login_cookies is not None:
            result['IdpLoginCookies'] = self.idp_login_cookies

        if self.idp_provider is not None:
            result['IdpProvider'] = self.idp_provider

        if self.login_endpoint is not None:
            result['LoginEndpoint'] = self.login_endpoint

        if self.logout_endpoint is not None:
            result['LogoutEndpoint'] = self.logout_endpoint

        if self.name is not None:
            result['Name'] = self.name

        if self.sign_public_key_endpoint is not None:
            result['SignPublicKeyEndpoint'] = self.sign_public_key_endpoint

        if self.sso_ticket_endpoint is not None:
            result['SsoTicketEndpoint'] = self.sso_ticket_endpoint

        if self.sso_ticket_key is not None:
            result['SsoTicketKey'] = self.sso_ticket_key

        if self.token_endpoint is not None:
            result['TokenEndpoint'] = self.token_endpoint

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccessTokenEndpoint') is not None:
            self.access_token_endpoint = m.get('AccessTokenEndpoint')

        if m.get('AppCode') is not None:
            self.app_code = m.get('AppCode')

        if m.get('AuthorityEndpoint') is not None:
            self.authority_endpoint = m.get('AuthorityEndpoint')

        if m.get('ClientId') is not None:
            self.client_id = m.get('ClientId')

        if m.get('ClientSecret') is not None:
            self.client_secret = m.get('ClientSecret')

        if m.get('IdpHostName') is not None:
            self.idp_host_name = m.get('IdpHostName')

        if m.get('IdpLoginCookies') is not None:
            self.idp_login_cookies = m.get('IdpLoginCookies')

        if m.get('IdpProvider') is not None:
            self.idp_provider = m.get('IdpProvider')

        if m.get('LoginEndpoint') is not None:
            self.login_endpoint = m.get('LoginEndpoint')

        if m.get('LogoutEndpoint') is not None:
            self.logout_endpoint = m.get('LogoutEndpoint')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('SignPublicKeyEndpoint') is not None:
            self.sign_public_key_endpoint = m.get('SignPublicKeyEndpoint')

        if m.get('SsoTicketEndpoint') is not None:
            self.sso_ticket_endpoint = m.get('SsoTicketEndpoint')

        if m.get('SsoTicketKey') is not None:
            self.sso_ticket_key = m.get('SsoTicketKey')

        if m.get('TokenEndpoint') is not None:
            self.token_endpoint = m.get('TokenEndpoint')

        return self

class GetIdpMetadataResponseBodyIdpMetadataOIDCMetadata(DaraModel):
    def __init__(
        self,
        authority_endpoint: str = None,
        client_id: str = None,
        client_secret: str = None,
        scope: str = None,
        token_auth_method: str = None,
        url: str = None,
    ):
        self.authority_endpoint = authority_endpoint
        self.client_id = client_id
        self.client_secret = client_secret
        self.scope = scope
        self.token_auth_method = token_auth_method
        self.url = url

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.authority_endpoint is not None:
            result['AuthorityEndpoint'] = self.authority_endpoint

        if self.client_id is not None:
            result['ClientId'] = self.client_id

        if self.client_secret is not None:
            result['ClientSecret'] = self.client_secret

        if self.scope is not None:
            result['Scope'] = self.scope

        if self.token_auth_method is not None:
            result['TokenAuthMethod'] = self.token_auth_method

        if self.url is not None:
            result['Url'] = self.url

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AuthorityEndpoint') is not None:
            self.authority_endpoint = m.get('AuthorityEndpoint')

        if m.get('ClientId') is not None:
            self.client_id = m.get('ClientId')

        if m.get('ClientSecret') is not None:
            self.client_secret = m.get('ClientSecret')

        if m.get('Scope') is not None:
            self.scope = m.get('Scope')

        if m.get('TokenAuthMethod') is not None:
            self.token_auth_method = m.get('TokenAuthMethod')

        if m.get('Url') is not None:
            self.url = m.get('Url')

        return self

