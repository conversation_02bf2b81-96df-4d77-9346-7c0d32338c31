# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class AppAuthLoginGetAccessTokenResponseBody(DaraModel):
    def __init__(
        self,
        access_token: str = None,
        expires_at: int = None,
        expires_in: int = None,
        id_token: str = None,
        open_id: str = None,
        refresh_token: str = None,
        request_id: str = None,
        token_type: str = None,
    ):
        self.access_token = access_token
        self.expires_at = expires_at
        self.expires_in = expires_in
        self.id_token = id_token
        self.open_id = open_id
        self.refresh_token = refresh_token
        self.request_id = request_id
        self.token_type = token_type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.access_token is not None:
            result['AccessToken'] = self.access_token

        if self.expires_at is not None:
            result['ExpiresAt'] = self.expires_at

        if self.expires_in is not None:
            result['ExpiresIn'] = self.expires_in

        if self.id_token is not None:
            result['IdToken'] = self.id_token

        if self.open_id is not None:
            result['OpenId'] = self.open_id

        if self.refresh_token is not None:
            result['RefreshToken'] = self.refresh_token

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.token_type is not None:
            result['TokenType'] = self.token_type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccessToken') is not None:
            self.access_token = m.get('AccessToken')

        if m.get('ExpiresAt') is not None:
            self.expires_at = m.get('ExpiresAt')

        if m.get('ExpiresIn') is not None:
            self.expires_in = m.get('ExpiresIn')

        if m.get('IdToken') is not None:
            self.id_token = m.get('IdToken')

        if m.get('OpenId') is not None:
            self.open_id = m.get('OpenId')

        if m.get('RefreshToken') is not None:
            self.refresh_token = m.get('RefreshToken')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TokenType') is not None:
            self.token_type = m.get('TokenType')

        return self

