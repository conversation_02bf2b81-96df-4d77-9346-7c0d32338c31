# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_appstream_center_inner20211212 import models as main_models 
from typing import Dict


class ValidateLoginTokenResponseBody(DaraModel):
    def __init__(
        self,
        login_token_model: main_models.ValidateLoginTokenResponseBodyLoginTokenModel = None,
        request_id: str = None,
    ):
        self.login_token_model = login_token_model
        self.request_id = request_id

    def validate(self):
        if self.login_token_model:
            self.login_token_model.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.login_token_model is not None:
            result['LoginTokenModel'] = self.login_token_model.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('LoginTokenModel') is not None:
            temp_model = main_models.ValidateLoginTokenResponseBodyLoginTokenModel()
            self.login_token_model = temp_model.from_map(m.get('LoginTokenModel'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class ValidateLoginTokenResponseBodyLoginTokenModel(DaraModel):
    def __init__(
        self,
        account_type: str = None,
        ad_domain: str = None,
        ali_uid: int = None,
        biz_group_id: str = None,
        biz_type: int = None,
        end_user_id: str = None,
        extends_access_token: str = None,
        idp_id: str = None,
        is_newly_user: bool = None,
        is_personal: bool = None,
        login_method: str = None,
        nick_name: str = None,
        session_extras: Dict[str, str] = None,
        wy_id: str = None,
    ):
        self.account_type = account_type
        self.ad_domain = ad_domain
        self.ali_uid = ali_uid
        self.biz_group_id = biz_group_id
        self.biz_type = biz_type
        self.end_user_id = end_user_id
        self.extends_access_token = extends_access_token
        self.idp_id = idp_id
        self.is_newly_user = is_newly_user
        self.is_personal = is_personal
        self.login_method = login_method
        self.nick_name = nick_name
        self.session_extras = session_extras
        self.wy_id = wy_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.account_type is not None:
            result['AccountType'] = self.account_type

        if self.ad_domain is not None:
            result['AdDomain'] = self.ad_domain

        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.biz_group_id is not None:
            result['BizGroupId'] = self.biz_group_id

        if self.biz_type is not None:
            result['BizType'] = self.biz_type

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.extends_access_token is not None:
            result['ExtendsAccessToken'] = self.extends_access_token

        if self.idp_id is not None:
            result['IdpId'] = self.idp_id

        if self.is_newly_user is not None:
            result['IsNewlyUser'] = self.is_newly_user

        if self.is_personal is not None:
            result['IsPersonal'] = self.is_personal

        if self.login_method is not None:
            result['LoginMethod'] = self.login_method

        if self.nick_name is not None:
            result['NickName'] = self.nick_name

        if self.session_extras is not None:
            result['SessionExtras'] = self.session_extras

        if self.wy_id is not None:
            result['WyId'] = self.wy_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccountType') is not None:
            self.account_type = m.get('AccountType')

        if m.get('AdDomain') is not None:
            self.ad_domain = m.get('AdDomain')

        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('BizGroupId') is not None:
            self.biz_group_id = m.get('BizGroupId')

        if m.get('BizType') is not None:
            self.biz_type = m.get('BizType')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('ExtendsAccessToken') is not None:
            self.extends_access_token = m.get('ExtendsAccessToken')

        if m.get('IdpId') is not None:
            self.idp_id = m.get('IdpId')

        if m.get('IsNewlyUser') is not None:
            self.is_newly_user = m.get('IsNewlyUser')

        if m.get('IsPersonal') is not None:
            self.is_personal = m.get('IsPersonal')

        if m.get('LoginMethod') is not None:
            self.login_method = m.get('LoginMethod')

        if m.get('NickName') is not None:
            self.nick_name = m.get('NickName')

        if m.get('SessionExtras') is not None:
            self.session_extras = m.get('SessionExtras')

        if m.get('WyId') is not None:
            self.wy_id = m.get('WyId')

        return self

