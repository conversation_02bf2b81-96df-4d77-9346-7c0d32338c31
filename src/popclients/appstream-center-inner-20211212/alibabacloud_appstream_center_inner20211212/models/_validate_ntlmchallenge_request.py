# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class ValidateNTLMChallengeRequest(DaraModel):
    def __init__(
        self,
        auth_domain: str = None,
        auth_user: str = None,
        nt_response: str = None,
        server_challenge: str = None,
    ):
        self.auth_domain = auth_domain
        self.auth_user = auth_user
        self.nt_response = nt_response
        self.server_challenge = server_challenge

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.auth_domain is not None:
            result['AuthDomain'] = self.auth_domain

        if self.auth_user is not None:
            result['AuthUser'] = self.auth_user

        if self.nt_response is not None:
            result['NtResponse'] = self.nt_response

        if self.server_challenge is not None:
            result['ServerChallenge'] = self.server_challenge

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AuthDomain') is not None:
            self.auth_domain = m.get('AuthDomain')

        if m.get('AuthUser') is not None:
            self.auth_user = m.get('AuthUser')

        if m.get('NtResponse') is not None:
            self.nt_response = m.get('NtResponse')

        if m.get('ServerChallenge') is not None:
            self.server_challenge = m.get('ServerChallenge')

        return self

