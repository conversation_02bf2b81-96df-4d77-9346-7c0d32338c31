# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_appstream_center_inner20211212 import models as main_models 
from typing import List


class ValidateWebsiteAdminLoginTokenResponseBody(DaraModel):
    def __init__(
        self,
        login_token_model: main_models.ValidateWebsiteAdminLoginTokenResponseBodyLoginTokenModel = None,
        request_id: str = None,
    ):
        self.login_token_model = login_token_model
        self.request_id = request_id

    def validate(self):
        if self.login_token_model:
            self.login_token_model.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.login_token_model is not None:
            result['LoginTokenModel'] = self.login_token_model.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('LoginTokenModel') is not None:
            temp_model = main_models.ValidateWebsiteAdminLoginTokenResponseBodyLoginTokenModel()
            self.login_token_model = temp_model.from_map(m.get('LoginTokenModel'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class ValidateWebsiteAdminLoginTokenResponseBodyLoginTokenModel(DaraModel):
    def __init__(
        self,
        avatar: str = None,
        channels: List[str] = None,
        nick_name: str = None,
        user_id: int = None,
        user_name: str = None,
    ):
        self.avatar = avatar
        self.channels = channels
        self.nick_name = nick_name
        self.user_id = user_id
        self.user_name = user_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.avatar is not None:
            result['Avatar'] = self.avatar

        if self.channels is not None:
            result['Channels'] = self.channels

        if self.nick_name is not None:
            result['NickName'] = self.nick_name

        if self.user_id is not None:
            result['UserId'] = self.user_id

        if self.user_name is not None:
            result['UserName'] = self.user_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Avatar') is not None:
            self.avatar = m.get('Avatar')

        if m.get('Channels') is not None:
            self.channels = m.get('Channels')

        if m.get('NickName') is not None:
            self.nick_name = m.get('NickName')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        if m.get('UserName') is not None:
            self.user_name = m.get('UserName')

        return self

