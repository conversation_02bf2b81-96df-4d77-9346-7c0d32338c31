# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_appstream_center_inner20211212 import models as main_models 
from typing import Dict, List


class ValidateSessionTokenResponseBody(DaraModel):
    def __init__(
        self,
        request_id: str = None,
        session_model: main_models.ValidateSessionTokenResponseBodySessionModel = None,
    ):
        self.request_id = request_id
        self.session_model = session_model

    def validate(self):
        if self.session_model:
            self.session_model.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.session_model is not None:
            result['SessionModel'] = self.session_model.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('SessionModel') is not None:
            temp_model = main_models.ValidateSessionTokenResponseBodySessionModel()
            self.session_model = temp_model.from_map(m.get('SessionModel'))

        return self

class ValidateSessionTokenResponseBodySessionModel(DaraModel):
    def __init__(
        self,
        account_channels: str = None,
        desktop_id: str = None,
        end_user_id: str = None,
        external_user_id: str = None,
        extras: Dict[str, str] = None,
        nick_name: str = None,
        office_site_id: str = None,
        orgs: List[main_models.ValidateSessionTokenResponseBodySessionModelOrgs] = None,
        tenant_id: int = None,
        type: str = None,
    ):
        self.account_channels = account_channels
        self.desktop_id = desktop_id
        self.end_user_id = end_user_id
        self.external_user_id = external_user_id
        self.extras = extras
        self.nick_name = nick_name
        self.office_site_id = office_site_id
        self.orgs = orgs
        self.tenant_id = tenant_id
        self.type = type

    def validate(self):
        if self.orgs:
            for v1 in self.orgs:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.account_channels is not None:
            result['AccountChannels'] = self.account_channels

        if self.desktop_id is not None:
            result['DesktopId'] = self.desktop_id

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.external_user_id is not None:
            result['ExternalUserId'] = self.external_user_id

        if self.extras is not None:
            result['Extras'] = self.extras

        if self.nick_name is not None:
            result['NickName'] = self.nick_name

        if self.office_site_id is not None:
            result['OfficeSiteId'] = self.office_site_id

        result['Orgs'] = []
        if self.orgs is not None:
            for k1 in self.orgs:
                result['Orgs'].append(k1.to_map() if k1 else None)

        if self.tenant_id is not None:
            result['TenantId'] = self.tenant_id

        if self.type is not None:
            result['Type'] = self.type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccountChannels') is not None:
            self.account_channels = m.get('AccountChannels')

        if m.get('DesktopId') is not None:
            self.desktop_id = m.get('DesktopId')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('ExternalUserId') is not None:
            self.external_user_id = m.get('ExternalUserId')

        if m.get('Extras') is not None:
            self.extras = m.get('Extras')

        if m.get('NickName') is not None:
            self.nick_name = m.get('NickName')

        if m.get('OfficeSiteId') is not None:
            self.office_site_id = m.get('OfficeSiteId')

        self.orgs = []
        if m.get('Orgs') is not None:
            for k1 in m.get('Orgs'):
                temp_model = main_models.ValidateSessionTokenResponseBodySessionModelOrgs()
                self.orgs.append(temp_model.from_map(k1))

        if m.get('TenantId') is not None:
            self.tenant_id = m.get('TenantId')

        if m.get('Type') is not None:
            self.type = m.get('Type')

        return self

class ValidateSessionTokenResponseBodySessionModelOrgs(DaraModel):
    def __init__(
        self,
        org_full_name: List[str] = None,
        org_id: str = None,
    ):
        self.org_full_name = org_full_name
        self.org_id = org_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.org_full_name is not None:
            result['OrgFullName'] = self.org_full_name

        if self.org_id is not None:
            result['OrgId'] = self.org_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('OrgFullName') is not None:
            self.org_full_name = m.get('OrgFullName')

        if m.get('OrgId') is not None:
            self.org_id = m.get('OrgId')

        return self

