# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class AppAuthLoginGetTransientTokenRequest(DaraModel):
    def __init__(
        self,
        account_type: str = None,
        domain_name: str = None,
        end_user_id: str = None,
        instance_id: str = None,
        user_ali_uid: int = None,
        wy_id: str = None,
    ):
        self.account_type = account_type
        self.domain_name = domain_name
        self.end_user_id = end_user_id
        self.instance_id = instance_id
        self.user_ali_uid = user_ali_uid
        self.wy_id = wy_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.account_type is not None:
            result['AccountType'] = self.account_type

        if self.domain_name is not None:
            result['DomainName'] = self.domain_name

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.instance_id is not None:
            result['InstanceId'] = self.instance_id

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        if self.wy_id is not None:
            result['WyId'] = self.wy_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccountType') is not None:
            self.account_type = m.get('AccountType')

        if m.get('DomainName') is not None:
            self.domain_name = m.get('DomainName')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('InstanceId') is not None:
            self.instance_id = m.get('InstanceId')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        if m.get('WyId') is not None:
            self.wy_id = m.get('WyId')

        return self

