# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class StudentCreateSsoUserSessionRequest(DaraModel):
    def __init__(
        self,
        end_user_id: str = None,
        nick_name: str = None,
        user_ali_uid: int = None,
    ):
        self.end_user_id = end_user_id
        self.nick_name = nick_name
        # This parameter is required.
        self.user_ali_uid = user_ali_uid

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.nick_name is not None:
            result['NickName'] = self.nick_name

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('NickName') is not None:
            self.nick_name = m.get('NickName')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        return self

