# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class GetAuthCodeRequest(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        auto_create_user: bool = None,
        end_user_id: str = None,
        expiration: int = None,
        external_user_id: str = None,
        max_consume_times: int = None,
        policy: str = None,
        token_expiration: int = None,
    ):
        self.ali_uid = ali_uid
        self.auto_create_user = auto_create_user
        self.end_user_id = end_user_id
        self.expiration = expiration
        self.external_user_id = external_user_id
        self.max_consume_times = max_consume_times
        self.policy = policy
        self.token_expiration = token_expiration

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.auto_create_user is not None:
            result['AutoCreateUser'] = self.auto_create_user

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.expiration is not None:
            result['Expiration'] = self.expiration

        if self.external_user_id is not None:
            result['ExternalUserId'] = self.external_user_id

        if self.max_consume_times is not None:
            result['MaxConsumeTimes'] = self.max_consume_times

        if self.policy is not None:
            result['Policy'] = self.policy

        if self.token_expiration is not None:
            result['TokenExpiration'] = self.token_expiration

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('AutoCreateUser') is not None:
            self.auto_create_user = m.get('AutoCreateUser')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('Expiration') is not None:
            self.expiration = m.get('Expiration')

        if m.get('ExternalUserId') is not None:
            self.external_user_id = m.get('ExternalUserId')

        if m.get('MaxConsumeTimes') is not None:
            self.max_consume_times = m.get('MaxConsumeTimes')

        if m.get('Policy') is not None:
            self.policy = m.get('Policy')

        if m.get('TokenExpiration') is not None:
            self.token_expiration = m.get('TokenExpiration')

        return self

