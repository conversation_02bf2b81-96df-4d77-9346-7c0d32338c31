# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_appstream_center_inner20211212 import models as main_models 


class GetExternalLoginTokenRequest(DaraModel):
    def __init__(
        self,
        client_channel: str = None,
        client_id: str = None,
        client_ip: str = None,
        client_os: str = None,
        client_version: str = None,
        external_client_id: str = None,
        external_user_info: main_models.GetExternalLoginTokenRequestExternalUserInfo = None,
        user_ali_uid: int = None,
        uuid: str = None,
    ):
        self.client_channel = client_channel
        self.client_id = client_id
        self.client_ip = client_ip
        self.client_os = client_os
        self.client_version = client_version
        self.external_client_id = external_client_id
        self.external_user_info = external_user_info
        self.user_ali_uid = user_ali_uid
        self.uuid = uuid

    def validate(self):
        if self.external_user_info:
            self.external_user_info.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.client_channel is not None:
            result['ClientChannel'] = self.client_channel

        if self.client_id is not None:
            result['ClientId'] = self.client_id

        if self.client_ip is not None:
            result['ClientIp'] = self.client_ip

        if self.client_os is not None:
            result['ClientOS'] = self.client_os

        if self.client_version is not None:
            result['ClientVersion'] = self.client_version

        if self.external_client_id is not None:
            result['ExternalClientId'] = self.external_client_id

        if self.external_user_info is not None:
            result['ExternalUserInfo'] = self.external_user_info.to_map()

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        if self.uuid is not None:
            result['Uuid'] = self.uuid

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ClientChannel') is not None:
            self.client_channel = m.get('ClientChannel')

        if m.get('ClientId') is not None:
            self.client_id = m.get('ClientId')

        if m.get('ClientIp') is not None:
            self.client_ip = m.get('ClientIp')

        if m.get('ClientOS') is not None:
            self.client_os = m.get('ClientOS')

        if m.get('ClientVersion') is not None:
            self.client_version = m.get('ClientVersion')

        if m.get('ExternalClientId') is not None:
            self.external_client_id = m.get('ExternalClientId')

        if m.get('ExternalUserInfo') is not None:
            temp_model = main_models.GetExternalLoginTokenRequestExternalUserInfo()
            self.external_user_info = temp_model.from_map(m.get('ExternalUserInfo'))

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        if m.get('Uuid') is not None:
            self.uuid = m.get('Uuid')

        return self

class GetExternalLoginTokenRequestExternalUserInfo(DaraModel):
    def __init__(
        self,
        account_type: str = None,
        external_email: str = None,
        external_id: str = None,
        external_name: str = None,
        external_organization_id: str = None,
        external_phone: str = None,
    ):
        self.account_type = account_type
        self.external_email = external_email
        self.external_id = external_id
        self.external_name = external_name
        self.external_organization_id = external_organization_id
        self.external_phone = external_phone

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.account_type is not None:
            result['AccountType'] = self.account_type

        if self.external_email is not None:
            result['ExternalEmail'] = self.external_email

        if self.external_id is not None:
            result['ExternalId'] = self.external_id

        if self.external_name is not None:
            result['ExternalName'] = self.external_name

        if self.external_organization_id is not None:
            result['ExternalOrganizationId'] = self.external_organization_id

        if self.external_phone is not None:
            result['ExternalPhone'] = self.external_phone

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccountType') is not None:
            self.account_type = m.get('AccountType')

        if m.get('ExternalEmail') is not None:
            self.external_email = m.get('ExternalEmail')

        if m.get('ExternalId') is not None:
            self.external_id = m.get('ExternalId')

        if m.get('ExternalName') is not None:
            self.external_name = m.get('ExternalName')

        if m.get('ExternalOrganizationId') is not None:
            self.external_organization_id = m.get('ExternalOrganizationId')

        if m.get('ExternalPhone') is not None:
            self.external_phone = m.get('ExternalPhone')

        return self

