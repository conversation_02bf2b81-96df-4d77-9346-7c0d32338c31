# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from typing import List


class GetTenantPasswordStrategyResponseBody(DaraModel):
    def __init__(
        self,
        request_id: str = None,
        tenant_alternative_chars: List[str] = None,
        tenant_password_length: int = None,
    ):
        self.request_id = request_id
        self.tenant_alternative_chars = tenant_alternative_chars
        self.tenant_password_length = tenant_password_length

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.tenant_alternative_chars is not None:
            result['TenantAlternativeChars'] = self.tenant_alternative_chars

        if self.tenant_password_length is not None:
            result['TenantPasswordLength'] = self.tenant_password_length

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TenantAlternativeChars') is not None:
            self.tenant_alternative_chars = m.get('TenantAlternativeChars')

        if m.get('TenantPasswordLength') is not None:
            self.tenant_password_length = m.get('TenantPasswordLength')

        return self

