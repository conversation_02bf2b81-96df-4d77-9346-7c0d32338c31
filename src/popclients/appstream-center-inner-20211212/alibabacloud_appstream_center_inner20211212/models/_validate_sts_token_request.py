# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class ValidateStsTokenRequest(DaraModel):
    def __init__(
        self,
        session_id: str = None,
        sts_token: str = None,
    ):
        self.session_id = session_id
        self.sts_token = sts_token

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.session_id is not None:
            result['SessionId'] = self.session_id

        if self.sts_token is not None:
            result['StsToken'] = self.sts_token

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('SessionId') is not None:
            self.session_id = m.get('SessionId')

        if m.get('StsToken') is not None:
            self.sts_token = m.get('StsToken')

        return self

