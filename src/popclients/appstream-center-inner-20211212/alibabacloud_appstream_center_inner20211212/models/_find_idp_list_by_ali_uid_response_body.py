# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_appstream_center_inner20211212 import models as main_models 
from typing import List


class FindIdpListByAliUidResponseBody(DaraModel):
    def __init__(
        self,
        idp_detail_models: List[main_models.FindIdpListByAliUidResponseBodyIdpDetailModels] = None,
        request_id: str = None,
    ):
        self.idp_detail_models = idp_detail_models
        self.request_id = request_id

    def validate(self):
        if self.idp_detail_models:
            for v1 in self.idp_detail_models:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['IdpDetailModels'] = []
        if self.idp_detail_models is not None:
            for k1 in self.idp_detail_models:
                result['IdpDetailModels'].append(k1.to_map() if k1 else None)

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.idp_detail_models = []
        if m.get('IdpDetailModels') is not None:
            for k1 in m.get('IdpDetailModels'):
                temp_model = main_models.FindIdpListByAliUidResponseBodyIdpDetailModels()
                self.idp_detail_models.append(temp_model.from_map(k1))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class FindIdpListByAliUidResponseBodyIdpDetailModels(DaraModel):
    def __init__(
        self,
        idp_id: str = None,
        idp_name: str = None,
        idp_protocol: str = None,
    ):
        self.idp_id = idp_id
        self.idp_name = idp_name
        self.idp_protocol = idp_protocol

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.idp_id is not None:
            result['IdpId'] = self.idp_id

        if self.idp_name is not None:
            result['IdpName'] = self.idp_name

        if self.idp_protocol is not None:
            result['IdpProtocol'] = self.idp_protocol

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('IdpId') is not None:
            self.idp_id = m.get('IdpId')

        if m.get('IdpName') is not None:
            self.idp_name = m.get('IdpName')

        if m.get('IdpProtocol') is not None:
            self.idp_protocol = m.get('IdpProtocol')

        return self

