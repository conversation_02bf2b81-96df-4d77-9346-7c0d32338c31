# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_appstream_center_inner20211212 import models as main_models 
from typing import List


class DescribeTenantAliasResponseBody(DaraModel):
    def __init__(
        self,
        request_id: str = None,
        tenant_alias_info: main_models.DescribeTenantAliasResponseBodyTenantAliasInfo = None,
    ):
        self.request_id = request_id
        self.tenant_alias_info = tenant_alias_info

    def validate(self):
        if self.tenant_alias_info:
            self.tenant_alias_info.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.tenant_alias_info is not None:
            result['TenantAliasInfo'] = self.tenant_alias_info.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('TenantAliasInfo') is not None:
            temp_model = main_models.DescribeTenantAliasResponseBodyTenantAliasInfo()
            self.tenant_alias_info = temp_model.from_map(m.get('TenantAliasInfo'))

        return self



class DescribeTenantAliasResponseBodyTenantAliasInfo(DaraModel):
    def __init__(
        self,
        is_fast_login: bool = None,
        is_mfa_enabled: bool = None,
        is_open: bool = None,
        is_sso_enabled: bool = None,
        keep_alive_token_valid_time: int = None,
        login_token_valid_time: int = None,
        need_verify_login_risk: bool = None,
        need_verify_zero_device: bool = None,
        need_wy_account: bool = None,
        notify_status: str = None,
        phone_login_enable: int = None,
        send_login_logout_message: bool = None,
        support_client_type: List[str] = None,
        tenant_alias: str = None,
        time_duration_minutes: int = None,
        window_display_mode: str = None,
    ):
        self.is_fast_login = is_fast_login
        self.is_mfa_enabled = is_mfa_enabled
        self.is_open = is_open
        self.is_sso_enabled = is_sso_enabled
        self.keep_alive_token_valid_time = keep_alive_token_valid_time
        self.login_token_valid_time = login_token_valid_time
        self.need_verify_login_risk = need_verify_login_risk
        self.need_verify_zero_device = need_verify_zero_device
        self.need_wy_account = need_wy_account
        self.notify_status = notify_status
        self.phone_login_enable = phone_login_enable
        self.send_login_logout_message = send_login_logout_message
        self.support_client_type = support_client_type
        self.tenant_alias = tenant_alias
        self.time_duration_minutes = time_duration_minutes
        self.window_display_mode = window_display_mode

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.is_fast_login is not None:
            result['IsFastLogin'] = self.is_fast_login

        if self.is_mfa_enabled is not None:
            result['IsMfaEnabled'] = self.is_mfa_enabled

        if self.is_open is not None:
            result['IsOpen'] = self.is_open

        if self.is_sso_enabled is not None:
            result['IsSsoEnabled'] = self.is_sso_enabled

        if self.keep_alive_token_valid_time is not None:
            result['KeepAliveTokenValidTime'] = self.keep_alive_token_valid_time

        if self.login_token_valid_time is not None:
            result['LoginTokenValidTime'] = self.login_token_valid_time

        if self.need_verify_login_risk is not None:
            result['NeedVerifyLoginRisk'] = self.need_verify_login_risk

        if self.need_verify_zero_device is not None:
            result['NeedVerifyZeroDevice'] = self.need_verify_zero_device

        if self.need_wy_account is not None:
            result['NeedWyAccount'] = self.need_wy_account

        if self.notify_status is not None:
            result['NotifyStatus'] = self.notify_status

        if self.phone_login_enable is not None:
            result['PhoneLoginEnable'] = self.phone_login_enable

        if self.send_login_logout_message is not None:
            result['SendLoginLogoutMessage'] = self.send_login_logout_message

        if self.support_client_type is not None:
            result['SupportClientType'] = self.support_client_type

        if self.tenant_alias is not None:
            result['TenantAlias'] = self.tenant_alias

        if self.time_duration_minutes is not None:
            result['TimeDurationMinutes'] = self.time_duration_minutes

        if self.window_display_mode is not None:
            result['WindowDisplayMode'] = self.window_display_mode

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('IsFastLogin') is not None:
            self.is_fast_login = m.get('IsFastLogin')

        if m.get('IsMfaEnabled') is not None:
            self.is_mfa_enabled = m.get('IsMfaEnabled')

        if m.get('IsOpen') is not None:
            self.is_open = m.get('IsOpen')

        if m.get('IsSsoEnabled') is not None:
            self.is_sso_enabled = m.get('IsSsoEnabled')

        if m.get('KeepAliveTokenValidTime') is not None:
            self.keep_alive_token_valid_time = m.get('KeepAliveTokenValidTime')

        if m.get('LoginTokenValidTime') is not None:
            self.login_token_valid_time = m.get('LoginTokenValidTime')

        if m.get('NeedVerifyLoginRisk') is not None:
            self.need_verify_login_risk = m.get('NeedVerifyLoginRisk')

        if m.get('NeedVerifyZeroDevice') is not None:
            self.need_verify_zero_device = m.get('NeedVerifyZeroDevice')

        if m.get('NeedWyAccount') is not None:
            self.need_wy_account = m.get('NeedWyAccount')

        if m.get('NotifyStatus') is not None:
            self.notify_status = m.get('NotifyStatus')

        if m.get('PhoneLoginEnable') is not None:
            self.phone_login_enable = m.get('PhoneLoginEnable')

        if m.get('SendLoginLogoutMessage') is not None:
            self.send_login_logout_message = m.get('SendLoginLogoutMessage')

        if m.get('SupportClientType') is not None:
            self.support_client_type = m.get('SupportClientType')

        if m.get('TenantAlias') is not None:
            self.tenant_alias = m.get('TenantAlias')

        if m.get('TimeDurationMinutes') is not None:
            self.time_duration_minutes = m.get('TimeDurationMinutes')

        if m.get('WindowDisplayMode') is not None:
            self.window_display_mode = m.get('WindowDisplayMode')

        return self

