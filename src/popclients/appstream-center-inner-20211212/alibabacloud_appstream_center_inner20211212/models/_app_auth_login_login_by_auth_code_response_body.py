# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class AppAuthLoginLoginByAuthCodeResponseBody(DaraModel):
    def __init__(
        self,
        biz_type: str = None,
        email: str = None,
        end_user_id: str = None,
        login_token: str = None,
        nick_name: str = None,
        phone: str = None,
        request_id: str = None,
        session_id: str = None,
        tenant_id: int = None,
        wy_id: str = None,
    ):
        self.biz_type = biz_type
        self.email = email
        self.end_user_id = end_user_id
        self.login_token = login_token
        self.nick_name = nick_name
        self.phone = phone
        self.request_id = request_id
        self.session_id = session_id
        self.tenant_id = tenant_id
        self.wy_id = wy_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.biz_type is not None:
            result['BizType'] = self.biz_type

        if self.email is not None:
            result['Email'] = self.email

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.login_token is not None:
            result['LoginToken'] = self.login_token

        if self.nick_name is not None:
            result['NickName'] = self.nick_name

        if self.phone is not None:
            result['Phone'] = self.phone

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.session_id is not None:
            result['SessionId'] = self.session_id

        if self.tenant_id is not None:
            result['TenantId'] = self.tenant_id

        if self.wy_id is not None:
            result['WyId'] = self.wy_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('BizType') is not None:
            self.biz_type = m.get('BizType')

        if m.get('Email') is not None:
            self.email = m.get('Email')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('LoginToken') is not None:
            self.login_token = m.get('LoginToken')

        if m.get('NickName') is not None:
            self.nick_name = m.get('NickName')

        if m.get('Phone') is not None:
            self.phone = m.get('Phone')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('SessionId') is not None:
            self.session_id = m.get('SessionId')

        if m.get('TenantId') is not None:
            self.tenant_id = m.get('TenantId')

        if m.get('WyId') is not None:
            self.wy_id = m.get('WyId')

        return self

