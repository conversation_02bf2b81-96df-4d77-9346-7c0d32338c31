# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_appstream_center_inner20211212 import models as main_models 


class GetIdpByOfficeSiteIdResponseBody(DaraModel):
    def __init__(
        self,
        idp_info: main_models.GetIdpByOfficeSiteIdResponseBodyIdpInfo = None,
        request_id: str = None,
    ):
        self.idp_info = idp_info
        self.request_id = request_id

    def validate(self):
        if self.idp_info:
            self.idp_info.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.idp_info is not None:
            result['IdpInfo'] = self.idp_info.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('IdpInfo') is not None:
            temp_model = main_models.GetIdpByOfficeSiteIdResponseBodyIdpInfo()
            self.idp_info = temp_model.from_map(m.get('IdpInfo'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class GetIdpByOfficeSiteIdResponseBodyIdpInfo(DaraModel):
    def __init__(
        self,
        idp_id: str = None,
        idp_name: str = None,
        idp_protocol: str = None,
        ali_uid: int = None,
        status: str = None,
    ):
        self.idp_id = idp_id
        self.idp_name = idp_name
        self.idp_protocol = idp_protocol
        self.ali_uid = ali_uid
        self.status = status

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.idp_id is not None:
            result['IdpId'] = self.idp_id

        if self.idp_name is not None:
            result['IdpName'] = self.idp_name

        if self.idp_protocol is not None:
            result['IdpProtocol'] = self.idp_protocol

        if self.ali_uid is not None:
            result['aliUid'] = self.ali_uid

        if self.status is not None:
            result['status'] = self.status

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('IdpId') is not None:
            self.idp_id = m.get('IdpId')

        if m.get('IdpName') is not None:
            self.idp_name = m.get('IdpName')

        if m.get('IdpProtocol') is not None:
            self.idp_protocol = m.get('IdpProtocol')

        if m.get('aliUid') is not None:
            self.ali_uid = m.get('aliUid')

        if m.get('status') is not None:
            self.status = m.get('status')

        return self

