# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_appstream_center_inner20211212 import models as main_models 


class GetSsoTicketResponseBody(DaraModel):
    def __init__(
        self,
        get_sso_ticket_model: main_models.GetSsoTicketResponseBodyGetSsoTicketModel = None,
        request_id: str = None,
    ):
        self.get_sso_ticket_model = get_sso_ticket_model
        self.request_id = request_id

    def validate(self):
        if self.get_sso_ticket_model:
            self.get_sso_ticket_model.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.get_sso_ticket_model is not None:
            result['GetSsoTicketModel'] = self.get_sso_ticket_model.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('GetSsoTicketModel') is not None:
            temp_model = main_models.GetSsoTicketResponseBodyGetSsoTicketModel()
            self.get_sso_ticket_model = temp_model.from_map(m.get('GetSsoTicketModel'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class GetSsoTicketResponseBodyGetSsoTicketModel(DaraModel):
    def __init__(
        self,
        sso_ticket: str = None,
        sso_ticket_key: str = None,
    ):
        self.sso_ticket = sso_ticket
        self.sso_ticket_key = sso_ticket_key

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.sso_ticket is not None:
            result['SsoTicket'] = self.sso_ticket

        if self.sso_ticket_key is not None:
            result['SsoTicketKey'] = self.sso_ticket_key

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('SsoTicket') is not None:
            self.sso_ticket = m.get('SsoTicket')

        if m.get('SsoTicketKey') is not None:
            self.sso_ticket_key = m.get('SsoTicketKey')

        return self

