# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import Dara<PERSON>ode<PERSON> 
from alibabacloud_appstream_center_inner20211212 import models as main_models 


class StudentCreateSsoUserSessionResponseBody(DaraModel):
    def __init__(
        self,
        personal_sso_session_token: main_models.StudentCreateSsoUserSessionResponseBodyPersonalSsoSessionToken = None,
        request_id: str = None,
    ):
        self.personal_sso_session_token = personal_sso_session_token
        self.request_id = request_id

    def validate(self):
        if self.personal_sso_session_token:
            self.personal_sso_session_token.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.personal_sso_session_token is not None:
            result['PersonalSsoSessionToken'] = self.personal_sso_session_token.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('PersonalSsoSessionToken') is not None:
            temp_model = main_models.StudentCreateSsoUserSessionResponseBodyPersonalSsoSessionToken()
            self.personal_sso_session_token = temp_model.from_map(m.get('PersonalSsoSessionToken'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class StudentCreateSsoUserSessionResponseBodyPersonalSsoSessionToken(DaraModel):
    def __init__(
        self,
        sso_session_id: str = None,
        sso_session_token: str = None,
        next_stage: str = None,
    ):
        self.sso_session_id = sso_session_id
        self.sso_session_token = sso_session_token
        self.next_stage = next_stage

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.sso_session_id is not None:
            result['SsoSessionId'] = self.sso_session_id

        if self.sso_session_token is not None:
            result['SsoSessionToken'] = self.sso_session_token

        if self.next_stage is not None:
            result['nextStage'] = self.next_stage

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('SsoSessionId') is not None:
            self.sso_session_id = m.get('SsoSessionId')

        if m.get('SsoSessionToken') is not None:
            self.sso_session_token = m.get('SsoSessionToken')

        if m.get('nextStage') is not None:
            self.next_stage = m.get('nextStage')

        return self

