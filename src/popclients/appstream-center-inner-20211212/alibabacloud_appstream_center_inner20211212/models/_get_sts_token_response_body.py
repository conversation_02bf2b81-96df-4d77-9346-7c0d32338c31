# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_appstream_center_inner20211212 import models as main_models 


class GetStsTokenResponseBody(DaraModel):
    def __init__(
        self,
        request_id: str = None,
        token: main_models.GetStsTokenResponseBodyToken = None,
    ):
        self.request_id = request_id
        self.token = token

    def validate(self):
        if self.token:
            self.token.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.token is not None:
            result['Token'] = self.token.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('Token') is not None:
            temp_model = main_models.GetStsTokenResponseBodyToken()
            self.token = temp_model.from_map(m.get('Token'))

        return self

class GetStsTokenResponseBodyToken(DaraModel):
    def __init__(
        self,
        ali_uid: str = None,
        end_user_id: str = None,
        session_id: str = None,
        sts_token: str = None,
        wy_id: str = None,
    ):
        self.ali_uid = ali_uid
        self.end_user_id = end_user_id
        self.session_id = session_id
        self.sts_token = sts_token
        self.wy_id = wy_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.session_id is not None:
            result['SessionId'] = self.session_id

        if self.sts_token is not None:
            result['StsToken'] = self.sts_token

        if self.wy_id is not None:
            result['WyId'] = self.wy_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('SessionId') is not None:
            self.session_id = m.get('SessionId')

        if m.get('StsToken') is not None:
            self.sts_token = m.get('StsToken')

        if m.get('WyId') is not None:
            self.wy_id = m.get('WyId')

        return self

