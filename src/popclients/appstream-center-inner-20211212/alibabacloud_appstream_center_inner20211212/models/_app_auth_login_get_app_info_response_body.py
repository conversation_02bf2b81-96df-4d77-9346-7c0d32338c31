# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraMode<PERSON> 
from typing import List, Dict


class AppAuthLoginGetAppInfoResponseBody(DaraModel):
    def __init__(
        self,
        app_pub_jwks: List[Dict[str, str]] = None,
        authorization_endpoint: str = None,
        claims_supported: List[str] = None,
        code_challenge_methods_supported: List[str] = None,
        end_session_endpoint: str = None,
        grant_types_supported: List[str] = None,
        id_token_signing_alg_values_supported: List[str] = None,
        introspection_endpoint: str = None,
        issuer: str = None,
        jwks_uri: str = None,
        request_id: str = None,
        request_uri_parameter_supported: bool = None,
        response_modes_supported: List[str] = None,
        response_types_supported: List[str] = None,
        revocation_endpoint: str = None,
        scopes_supported: List[str] = None,
        subject_types_supported: List[str] = None,
        token_endpoint: str = None,
        token_endpoint_auth_methods_supported: List[str] = None,
        user_info_endpoint: str = None,
    ):
        self.app_pub_jwks = app_pub_jwks
        self.authorization_endpoint = authorization_endpoint
        self.claims_supported = claims_supported
        self.code_challenge_methods_supported = code_challenge_methods_supported
        self.end_session_endpoint = end_session_endpoint
        self.grant_types_supported = grant_types_supported
        self.id_token_signing_alg_values_supported = id_token_signing_alg_values_supported
        self.introspection_endpoint = introspection_endpoint
        self.issuer = issuer
        self.jwks_uri = jwks_uri
        self.request_id = request_id
        self.request_uri_parameter_supported = request_uri_parameter_supported
        self.response_modes_supported = response_modes_supported
        self.response_types_supported = response_types_supported
        self.revocation_endpoint = revocation_endpoint
        self.scopes_supported = scopes_supported
        self.subject_types_supported = subject_types_supported
        self.token_endpoint = token_endpoint
        self.token_endpoint_auth_methods_supported = token_endpoint_auth_methods_supported
        self.user_info_endpoint = user_info_endpoint

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.app_pub_jwks is not None:
            result['AppPubJwks'] = self.app_pub_jwks

        if self.authorization_endpoint is not None:
            result['AuthorizationEndpoint'] = self.authorization_endpoint

        if self.claims_supported is not None:
            result['ClaimsSupported'] = self.claims_supported

        if self.code_challenge_methods_supported is not None:
            result['CodeChallengeMethodsSupported'] = self.code_challenge_methods_supported

        if self.end_session_endpoint is not None:
            result['EndSessionEndpoint'] = self.end_session_endpoint

        if self.grant_types_supported is not None:
            result['GrantTypesSupported'] = self.grant_types_supported

        if self.id_token_signing_alg_values_supported is not None:
            result['IdTokenSigningAlgValuesSupported'] = self.id_token_signing_alg_values_supported

        if self.introspection_endpoint is not None:
            result['IntrospectionEndpoint'] = self.introspection_endpoint

        if self.issuer is not None:
            result['Issuer'] = self.issuer

        if self.jwks_uri is not None:
            result['JwksUri'] = self.jwks_uri

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.request_uri_parameter_supported is not None:
            result['RequestUriParameterSupported'] = self.request_uri_parameter_supported

        if self.response_modes_supported is not None:
            result['ResponseModesSupported'] = self.response_modes_supported

        if self.response_types_supported is not None:
            result['ResponseTypesSupported'] = self.response_types_supported

        if self.revocation_endpoint is not None:
            result['RevocationEndpoint'] = self.revocation_endpoint

        if self.scopes_supported is not None:
            result['ScopesSupported'] = self.scopes_supported

        if self.subject_types_supported is not None:
            result['SubjectTypesSupported'] = self.subject_types_supported

        if self.token_endpoint is not None:
            result['TokenEndpoint'] = self.token_endpoint

        if self.token_endpoint_auth_methods_supported is not None:
            result['TokenEndpointAuthMethodsSupported'] = self.token_endpoint_auth_methods_supported

        if self.user_info_endpoint is not None:
            result['UserInfoEndpoint'] = self.user_info_endpoint

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AppPubJwks') is not None:
            self.app_pub_jwks = m.get('AppPubJwks')

        if m.get('AuthorizationEndpoint') is not None:
            self.authorization_endpoint = m.get('AuthorizationEndpoint')

        if m.get('ClaimsSupported') is not None:
            self.claims_supported = m.get('ClaimsSupported')

        if m.get('CodeChallengeMethodsSupported') is not None:
            self.code_challenge_methods_supported = m.get('CodeChallengeMethodsSupported')

        if m.get('EndSessionEndpoint') is not None:
            self.end_session_endpoint = m.get('EndSessionEndpoint')

        if m.get('GrantTypesSupported') is not None:
            self.grant_types_supported = m.get('GrantTypesSupported')

        if m.get('IdTokenSigningAlgValuesSupported') is not None:
            self.id_token_signing_alg_values_supported = m.get('IdTokenSigningAlgValuesSupported')

        if m.get('IntrospectionEndpoint') is not None:
            self.introspection_endpoint = m.get('IntrospectionEndpoint')

        if m.get('Issuer') is not None:
            self.issuer = m.get('Issuer')

        if m.get('JwksUri') is not None:
            self.jwks_uri = m.get('JwksUri')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('RequestUriParameterSupported') is not None:
            self.request_uri_parameter_supported = m.get('RequestUriParameterSupported')

        if m.get('ResponseModesSupported') is not None:
            self.response_modes_supported = m.get('ResponseModesSupported')

        if m.get('ResponseTypesSupported') is not None:
            self.response_types_supported = m.get('ResponseTypesSupported')

        if m.get('RevocationEndpoint') is not None:
            self.revocation_endpoint = m.get('RevocationEndpoint')

        if m.get('ScopesSupported') is not None:
            self.scopes_supported = m.get('ScopesSupported')

        if m.get('SubjectTypesSupported') is not None:
            self.subject_types_supported = m.get('SubjectTypesSupported')

        if m.get('TokenEndpoint') is not None:
            self.token_endpoint = m.get('TokenEndpoint')

        if m.get('TokenEndpointAuthMethodsSupported') is not None:
            self.token_endpoint_auth_methods_supported = m.get('TokenEndpointAuthMethodsSupported')

        if m.get('UserInfoEndpoint') is not None:
            self.user_info_endpoint = m.get('UserInfoEndpoint')

        return self

