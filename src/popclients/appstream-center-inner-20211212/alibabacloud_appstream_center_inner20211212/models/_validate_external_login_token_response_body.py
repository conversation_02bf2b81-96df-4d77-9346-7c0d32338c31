# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_appstream_center_inner20211212 import models as main_models 


class ValidateExternalLoginTokenResponseBody(DaraModel):
    def __init__(
        self,
        login_token_model: main_models.ValidateExternalLoginTokenResponseBodyLoginTokenModel = None,
        request_id: str = None,
    ):
        self.login_token_model = login_token_model
        self.request_id = request_id

    def validate(self):
        if self.login_token_model:
            self.login_token_model.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.login_token_model is not None:
            result['LoginTokenModel'] = self.login_token_model.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('LoginTokenModel') is not None:
            temp_model = main_models.ValidateExternalLoginTokenResponseBodyLoginTokenModel()
            self.login_token_model = temp_model.from_map(m.get('LoginTokenModel'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class ValidateExternalLoginTokenResponseBodyLoginTokenModel(DaraModel):
    def __init__(
        self,
        account_type: str = None,
        ali_uid: int = None,
        end_user_id: str = None,
    ):
        self.account_type = account_type
        self.ali_uid = ali_uid
        self.end_user_id = end_user_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.account_type is not None:
            result['AccountType'] = self.account_type

        if self.ali_uid is not None:
            result['aliUid'] = self.ali_uid

        if self.end_user_id is not None:
            result['endUserId'] = self.end_user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccountType') is not None:
            self.account_type = m.get('AccountType')

        if m.get('aliUid') is not None:
            self.ali_uid = m.get('aliUid')

        if m.get('endUserId') is not None:
            self.end_user_id = m.get('endUserId')

        return self

