# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class ValidateLoginTokenRequest(DaraModel):
    def __init__(
        self,
        client_channel: str = None,
        client_id: str = None,
        client_ip: str = None,
        client_os: str = None,
        client_version: str = None,
        external_client_id: str = None,
        login_token: str = None,
        session_id: str = None,
    ):
        self.client_channel = client_channel
        self.client_id = client_id
        self.client_ip = client_ip
        self.client_os = client_os
        self.client_version = client_version
        self.external_client_id = external_client_id
        # This parameter is required.
        self.login_token = login_token
        # This parameter is required.
        self.session_id = session_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.client_channel is not None:
            result['ClientChannel'] = self.client_channel

        if self.client_id is not None:
            result['ClientId'] = self.client_id

        if self.client_ip is not None:
            result['ClientIp'] = self.client_ip

        if self.client_os is not None:
            result['ClientOS'] = self.client_os

        if self.client_version is not None:
            result['ClientVersion'] = self.client_version

        if self.external_client_id is not None:
            result['ExternalClientId'] = self.external_client_id

        if self.login_token is not None:
            result['LoginToken'] = self.login_token

        if self.session_id is not None:
            result['SessionId'] = self.session_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ClientChannel') is not None:
            self.client_channel = m.get('ClientChannel')

        if m.get('ClientId') is not None:
            self.client_id = m.get('ClientId')

        if m.get('ClientIp') is not None:
            self.client_ip = m.get('ClientIp')

        if m.get('ClientOS') is not None:
            self.client_os = m.get('ClientOS')

        if m.get('ClientVersion') is not None:
            self.client_version = m.get('ClientVersion')

        if m.get('ExternalClientId') is not None:
            self.external_client_id = m.get('ExternalClientId')

        if m.get('LoginToken') is not None:
            self.login_token = m.get('LoginToken')

        if m.get('SessionId') is not None:
            self.session_id = m.get('SessionId')

        return self

