# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateSsoUserSessionRequest(DaraModel):
    def __init__(
        self,
        access_token: str = None,
        client_type: str = None,
        client_uuid: str = None,
        end_user_id: str = None,
        external_mapping_id: str = None,
        external_user_id: str = None,
        idp_id: str = None,
        jump_token: str = None,
        login_region_id: str = None,
        login_token: str = None,
        network_type: str = None,
        office_site_id: str = None,
        refresh_token: str = None,
        session_id: str = None,
        sso_token: str = None,
        token_expire_date: int = None,
        user_ali_uid: int = None,
    ):
        self.access_token = access_token
        self.client_type = client_type
        self.client_uuid = client_uuid
        self.end_user_id = end_user_id
        self.external_mapping_id = external_mapping_id
        self.external_user_id = external_user_id
        self.idp_id = idp_id
        self.jump_token = jump_token
        self.login_region_id = login_region_id
        self.login_token = login_token
        self.network_type = network_type
        self.office_site_id = office_site_id
        self.refresh_token = refresh_token
        self.session_id = session_id
        self.sso_token = sso_token
        self.token_expire_date = token_expire_date
        self.user_ali_uid = user_ali_uid

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.access_token is not None:
            result['AccessToken'] = self.access_token

        if self.client_type is not None:
            result['ClientType'] = self.client_type

        if self.client_uuid is not None:
            result['ClientUuid'] = self.client_uuid

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.external_mapping_id is not None:
            result['ExternalMappingId'] = self.external_mapping_id

        if self.external_user_id is not None:
            result['ExternalUserId'] = self.external_user_id

        if self.idp_id is not None:
            result['IdpId'] = self.idp_id

        if self.jump_token is not None:
            result['JumpToken'] = self.jump_token

        if self.login_region_id is not None:
            result['LoginRegionId'] = self.login_region_id

        if self.login_token is not None:
            result['LoginToken'] = self.login_token

        if self.network_type is not None:
            result['NetworkType'] = self.network_type

        if self.office_site_id is not None:
            result['OfficeSiteId'] = self.office_site_id

        if self.refresh_token is not None:
            result['RefreshToken'] = self.refresh_token

        if self.session_id is not None:
            result['SessionId'] = self.session_id

        if self.sso_token is not None:
            result['SsoToken'] = self.sso_token

        if self.token_expire_date is not None:
            result['TokenExpireDate'] = self.token_expire_date

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccessToken') is not None:
            self.access_token = m.get('AccessToken')

        if m.get('ClientType') is not None:
            self.client_type = m.get('ClientType')

        if m.get('ClientUuid') is not None:
            self.client_uuid = m.get('ClientUuid')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('ExternalMappingId') is not None:
            self.external_mapping_id = m.get('ExternalMappingId')

        if m.get('ExternalUserId') is not None:
            self.external_user_id = m.get('ExternalUserId')

        if m.get('IdpId') is not None:
            self.idp_id = m.get('IdpId')

        if m.get('JumpToken') is not None:
            self.jump_token = m.get('JumpToken')

        if m.get('LoginRegionId') is not None:
            self.login_region_id = m.get('LoginRegionId')

        if m.get('LoginToken') is not None:
            self.login_token = m.get('LoginToken')

        if m.get('NetworkType') is not None:
            self.network_type = m.get('NetworkType')

        if m.get('OfficeSiteId') is not None:
            self.office_site_id = m.get('OfficeSiteId')

        if m.get('RefreshToken') is not None:
            self.refresh_token = m.get('RefreshToken')

        if m.get('SessionId') is not None:
            self.session_id = m.get('SessionId')

        if m.get('SsoToken') is not None:
            self.sso_token = m.get('SsoToken')

        if m.get('TokenExpireDate') is not None:
            self.token_expire_date = m.get('TokenExpireDate')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        return self

