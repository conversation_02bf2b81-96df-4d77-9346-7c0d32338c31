# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from typing import List


class DescribeTenantsAliasRequest(DaraModel):
    def __init__(
        self,
        user_ali_uids: List[int] = None,
    ):
        # This parameter is required.
        self.user_ali_uids = user_ali_uids

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.user_ali_uids is not None:
            result['UserAliUids'] = self.user_ali_uids

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('UserAliUids') is not None:
            self.user_ali_uids = m.get('UserAliUids')

        return self

