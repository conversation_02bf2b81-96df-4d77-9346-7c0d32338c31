# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class GetExternalLoginTokenShrinkRequest(DaraModel):
    def __init__(
        self,
        client_channel: str = None,
        client_id: str = None,
        client_ip: str = None,
        client_os: str = None,
        client_version: str = None,
        external_client_id: str = None,
        external_user_info_shrink: str = None,
        user_ali_uid: int = None,
        uuid: str = None,
    ):
        self.client_channel = client_channel
        self.client_id = client_id
        self.client_ip = client_ip
        self.client_os = client_os
        self.client_version = client_version
        self.external_client_id = external_client_id
        self.external_user_info_shrink = external_user_info_shrink
        self.user_ali_uid = user_ali_uid
        self.uuid = uuid

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.client_channel is not None:
            result['ClientChannel'] = self.client_channel

        if self.client_id is not None:
            result['ClientId'] = self.client_id

        if self.client_ip is not None:
            result['ClientIp'] = self.client_ip

        if self.client_os is not None:
            result['ClientOS'] = self.client_os

        if self.client_version is not None:
            result['ClientVersion'] = self.client_version

        if self.external_client_id is not None:
            result['ExternalClientId'] = self.external_client_id

        if self.external_user_info_shrink is not None:
            result['ExternalUserInfo'] = self.external_user_info_shrink

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        if self.uuid is not None:
            result['Uuid'] = self.uuid

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ClientChannel') is not None:
            self.client_channel = m.get('ClientChannel')

        if m.get('ClientId') is not None:
            self.client_id = m.get('ClientId')

        if m.get('ClientIp') is not None:
            self.client_ip = m.get('ClientIp')

        if m.get('ClientOS') is not None:
            self.client_os = m.get('ClientOS')

        if m.get('ClientVersion') is not None:
            self.client_version = m.get('ClientVersion')

        if m.get('ExternalClientId') is not None:
            self.external_client_id = m.get('ExternalClientId')

        if m.get('ExternalUserInfo') is not None:
            self.external_user_info_shrink = m.get('ExternalUserInfo')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        if m.get('Uuid') is not None:
            self.uuid = m.get('Uuid')

        return self

