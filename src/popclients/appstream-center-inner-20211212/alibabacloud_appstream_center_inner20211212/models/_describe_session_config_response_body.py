# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeSessionConfigResponseBody(DaraModel):
    def __init__(
        self,
        keep_alive_config_by_user: bool = None,
        keep_alive_token_valid_time: int = None,
        request_id: str = None,
    ):
        self.keep_alive_config_by_user = keep_alive_config_by_user
        self.keep_alive_token_valid_time = keep_alive_token_valid_time
        self.request_id = request_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.keep_alive_config_by_user is not None:
            result['KeepAliveConfigByUser'] = self.keep_alive_config_by_user

        if self.keep_alive_token_valid_time is not None:
            result['KeepAliveTokenValidTime'] = self.keep_alive_token_valid_time

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('KeepAliveConfigByUser') is not None:
            self.keep_alive_config_by_user = m.get('KeepAliveConfigByUser')

        if m.get('KeepAliveTokenValidTime') is not None:
            self.keep_alive_token_valid_time = m.get('KeepAliveTokenValidTime')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

