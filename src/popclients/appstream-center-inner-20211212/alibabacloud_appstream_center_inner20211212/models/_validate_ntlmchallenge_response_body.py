# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_appstream_center_inner20211212 import models as main_models 


class ValidateNTLMChallengeResponseBody(DaraModel):
    def __init__(
        self,
        ntlmchallenge_model: main_models.ValidateNTLMChallengeResponseBodyNTLMChallengeModel = None,
        request_id: str = None,
    ):
        self.ntlmchallenge_model = ntlmchallenge_model
        self.request_id = request_id

    def validate(self):
        if self.ntlmchallenge_model:
            self.ntlmchallenge_model.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ntlmchallenge_model is not None:
            result['NTLMChallengeModel'] = self.ntlmchallenge_model.to_map()

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('NTLMChallengeModel') is not None:
            temp_model = main_models.ValidateNTLMChallengeResponseBodyNTLMChallengeModel()
            self.ntlmchallenge_model = temp_model.from_map(m.get('NTLMChallengeModel'))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class ValidateNTLMChallengeResponseBodyNTLMChallengeModel(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        end_user_id: str = None,
        session_base_key: str = None,
        session_id: str = None,
        wy_id: str = None,
    ):
        self.ali_uid = ali_uid
        self.end_user_id = end_user_id
        self.session_base_key = session_base_key
        self.session_id = session_id
        self.wy_id = wy_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.session_base_key is not None:
            result['SessionBaseKey'] = self.session_base_key

        if self.session_id is not None:
            result['SessionId'] = self.session_id

        if self.wy_id is not None:
            result['WyId'] = self.wy_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('SessionBaseKey') is not None:
            self.session_base_key = m.get('SessionBaseKey')

        if m.get('SessionId') is not None:
            self.session_id = m.get('SessionId')

        if m.get('WyId') is not None:
            self.wy_id = m.get('WyId')

        return self

