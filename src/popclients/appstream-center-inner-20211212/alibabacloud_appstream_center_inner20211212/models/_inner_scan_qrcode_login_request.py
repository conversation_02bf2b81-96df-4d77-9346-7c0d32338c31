# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class InnerScanQrcodeLoginRequest(DaraModel):
    def __init__(
        self,
        client_id: str = None,
        client_os: str = None,
        client_type: str = None,
        client_version: str = None,
        network_type: str = None,
        original_client_ip: str = None,
        user_session_info: str = None,
        keep_alive: bool = None,
    ):
        self.client_id = client_id
        self.client_os = client_os
        self.client_type = client_type
        self.client_version = client_version
        self.network_type = network_type
        self.original_client_ip = original_client_ip
        self.user_session_info = user_session_info
        self.keep_alive = keep_alive

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.client_id is not None:
            result['ClientId'] = self.client_id

        if self.client_os is not None:
            result['ClientOS'] = self.client_os

        if self.client_type is not None:
            result['ClientType'] = self.client_type

        if self.client_version is not None:
            result['ClientVersion'] = self.client_version

        if self.network_type is not None:
            result['NetworkType'] = self.network_type

        if self.original_client_ip is not None:
            result['OriginalClientIp'] = self.original_client_ip

        if self.user_session_info is not None:
            result['UserSessionInfo'] = self.user_session_info

        if self.keep_alive is not None:
            result['keepAlive'] = self.keep_alive

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ClientId') is not None:
            self.client_id = m.get('ClientId')

        if m.get('ClientOS') is not None:
            self.client_os = m.get('ClientOS')

        if m.get('ClientType') is not None:
            self.client_type = m.get('ClientType')

        if m.get('ClientVersion') is not None:
            self.client_version = m.get('ClientVersion')

        if m.get('NetworkType') is not None:
            self.network_type = m.get('NetworkType')

        if m.get('OriginalClientIp') is not None:
            self.original_client_ip = m.get('OriginalClientIp')

        if m.get('UserSessionInfo') is not None:
            self.user_session_info = m.get('UserSessionInfo')

        if m.get('keepAlive') is not None:
            self.keep_alive = m.get('keepAlive')

        return self

