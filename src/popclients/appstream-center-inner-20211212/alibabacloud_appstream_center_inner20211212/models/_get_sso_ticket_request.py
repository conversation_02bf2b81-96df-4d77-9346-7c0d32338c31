# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class GetSsoTicketRequest(DaraModel):
    def __init__(
        self,
        access_token: str = None,
        ali_uid: str = None,
        idp_id: str = None,
    ):
        self.access_token = access_token
        self.ali_uid = ali_uid
        self.idp_id = idp_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.access_token is not None:
            result['AccessToken'] = self.access_token

        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.idp_id is not None:
            result['IdpId'] = self.idp_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccessToken') is not None:
            self.access_token = m.get('AccessToken')

        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('IdpId') is not None:
            self.idp_id = m.get('IdpId')

        return self

