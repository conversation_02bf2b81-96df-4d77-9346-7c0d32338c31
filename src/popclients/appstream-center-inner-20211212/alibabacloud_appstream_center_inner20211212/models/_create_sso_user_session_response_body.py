# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class CreateSsoUserSessionResponseBody(DaraModel):
    def __init__(
        self,
        ali_uid: str = None,
        alias: str = None,
        end_user_id: str = None,
        idp_id: str = None,
        login_scene: str = None,
        login_type: str = None,
        office_site_id: str = None,
        profile_region: str = None,
        request_id: str = None,
        sso_token: str = None,
    ):
        self.ali_uid = ali_uid
        self.alias = alias
        self.end_user_id = end_user_id
        self.idp_id = idp_id
        self.login_scene = login_scene
        self.login_type = login_type
        self.office_site_id = office_site_id
        self.profile_region = profile_region
        self.request_id = request_id
        self.sso_token = sso_token

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.alias is not None:
            result['Alias'] = self.alias

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.idp_id is not None:
            result['IdpId'] = self.idp_id

        if self.login_scene is not None:
            result['LoginScene'] = self.login_scene

        if self.login_type is not None:
            result['LoginType'] = self.login_type

        if self.office_site_id is not None:
            result['OfficeSiteId'] = self.office_site_id

        if self.profile_region is not None:
            result['ProfileRegion'] = self.profile_region

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.sso_token is not None:
            result['SsoToken'] = self.sso_token

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('Alias') is not None:
            self.alias = m.get('Alias')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('IdpId') is not None:
            self.idp_id = m.get('IdpId')

        if m.get('LoginScene') is not None:
            self.login_scene = m.get('LoginScene')

        if m.get('LoginType') is not None:
            self.login_type = m.get('LoginType')

        if m.get('OfficeSiteId') is not None:
            self.office_site_id = m.get('OfficeSiteId')

        if m.get('ProfileRegion') is not None:
            self.profile_region = m.get('ProfileRegion')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('SsoToken') is not None:
            self.sso_token = m.get('SsoToken')

        return self

