"""
消息模型
Message类的定义和相关方法
"""

import json
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field, field_validator, field_serializer
from enum import Enum


class Role(Enum):
    """消息角色枚举"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"
    
    @classmethod
    def from_string(cls, value: str) -> 'Role':
        """从字符串创建 Role 实例"""
        value = value.lower()
        for role in cls:
            if role.value == value:
                return role
        raise ValueError(f"Invalid role: {value}")
    
    def __eq__(self, other) -> bool:
        if isinstance(other, Role):
            return self.value == other.value
        elif isinstance(other, str):
            return self.value == other.lower()
        return False


class Function(BaseModel):
    """函数调用信息"""
    name: str
    arguments: str


class ToolCall(BaseModel):
    """工具调用信息"""
    id: str
    type: str = "function"
    function: Function


class Message(BaseModel):
    """消息类"""
    message_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    role: Role = Role.USER
    content: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    session_id: Optional[str] = None
    round_id: Optional[str] = None  # 原来的 trace_id 改为 round_id
    app_id: Optional[str] = None
    duration: Optional[float] = None
    tool_calls: Optional[List[ToolCall]] = None
    tool_call_id: Optional[str] = None
    name: Optional[str] = None
    
    @field_serializer('role')
    def serialize_role(self, value: Role) -> str:
        """输出时：Role枚举 → 字符串"""
        return value.value
    
    @field_serializer('tool_calls')
    def serialize_tool_calls(self, value: Optional[List[ToolCall]]) -> Optional[str]:
        """输出时：ToolCall列表 → JSON字符串"""
        if value is None:
            return None
        return json.dumps([tool_call.model_dump() for tool_call in value])
    
    @field_validator('role', mode='before')
    @classmethod
    def parse_role(cls, v):
        """输入时：字符串/其他 → Role枚举"""
        if v is None:
            return Role.USER
        if isinstance(v, Role):
            return v
        if isinstance(v, str):
            try:
                return Role.from_string(v)
            except ValueError:
                return Role.USER
        # 其他类型都返回默认值
        return Role.USER
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        """输出时：datetime → ISO格式字符串"""
        return value.isoformat() if value else None
    
    @field_validator('timestamp', mode='before')
    @classmethod
    def parse_timestamp(cls, v):
        """输入时：多种格式 → datetime"""
        if v is None:
            return datetime.now()
        if isinstance(v, datetime):
            return v
        if isinstance(v, (int, float)):
            return datetime.fromtimestamp(v)
        elif isinstance(v, str):
            try:
                return datetime.fromisoformat(v.replace('Z', '+00:00'))
            except ValueError:
                return datetime.now()
        return datetime.now()
    
    @field_validator('tool_calls', mode='before')
    @classmethod
    def parse_tool_calls(cls, v):
        """输入时：字符串/列表 → ToolCall对象列表"""
        if v is None:
            return None
        if isinstance(v, str):
            try:
                data = json.loads(v)
                if isinstance(data, list):
                    return [ToolCall.model_validate(item) for item in data]
                else:
                    return [ToolCall.model_validate(data)]
            except (json.JSONDecodeError, ValueError):
                return None
        elif isinstance(v, list):
            return [
                item if isinstance(item, ToolCall) 
                else ToolCall.model_validate(item) if isinstance(item, dict)
                else item
                for item in v
            ]
        return v
    
    @classmethod
    def user_message(cls, round_id: str, content: str, session_id: Optional[str] = None) -> "Message":
        """工厂方法：用户消息"""
        return cls(
            message_id=str(uuid.uuid4()),
            role=Role.USER,
            content=content,
            round_id=round_id,
            session_id=session_id
        )
    
    @classmethod
    def llm_message(cls, round_id: str, content: str, session_id: Optional[str] = None, **metadata) -> "Message":
        """工厂方法：LLM消息"""
        message = cls(
            message_id=str(uuid.uuid4()),
            role=Role.ASSISTANT,
            content=content,
            round_id=round_id,
            session_id=session_id
        )
        # 设置元数据
        for key, value in metadata.items():
            if hasattr(message, key):
                setattr(message, key, value)
        return message
    
    @classmethod
    def system_message(cls, round_id: str, content: str, session_id: Optional[str] = None) -> "Message":
        """工厂方法：系统消息"""
        return cls(
            message_id=str(uuid.uuid4()),
            role=Role.SYSTEM,
            content=content,
            round_id=round_id,
            session_id=session_id
        )
    
    @classmethod
    def tool_message(cls, round_id: str, content: str, name: str, tool_call_id: Optional[str] = None, session_id: Optional[str] = None) -> "Message":
        """工厂方法：工具消息"""
        return cls(
            message_id=str(uuid.uuid4()),
            role=Role.TOOL,
            content=content,
            round_id=round_id,
            session_id=session_id,
            name=name,
            tool_call_id=tool_call_id
        )
    
    def is_user_message(self) -> bool:
        """是否为用户消息"""
        return self.role == Role.USER
    
    def is_llm_message(self) -> bool:
        """是否为LLM消息"""
        return self.role == Role.ASSISTANT
    
    def is_system_message(self) -> bool:
        """是否为系统消息"""
        return self.role == Role.SYSTEM
        
    def is_tool_message(self) -> bool:
        """是否为工具消息"""
        return self.role == Role.TOOL 