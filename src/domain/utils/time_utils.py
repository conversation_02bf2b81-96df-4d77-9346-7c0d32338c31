#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间工具类
提供时间格式转换功能
"""

from datetime import datetime, timezone
from typing import Optional


class TimeUtils:
    """时间工具类"""

    @staticmethod
    def to_iso8601_utc(dt: datetime) -> str:
        """
        将 datetime 转换为 ISO 8601 格式的字符串，使用 UTC+0 时间

        Args:
            dt: datetime 对象

        Returns:
            str: ISO 8601 格式的字符串，如 "2017-12-08T22:40Z"

        Raises:
            ValueError: 如果输入的 dt 为 None
        """
        if dt is None:
            raise ValueError("datetime 对象不能为 None")

        # 如果 datetime 没有时区信息，假设为 UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)

        # 转换为 UTC 时间
        utc_dt = dt.astimezone(timezone.utc)

        # 格式化为 ISO 8601 格式，去掉微秒，使用 Z 表示 UTC
        return utc_dt.strftime("%Y-%m-%dT%H:%MZ")

    @staticmethod
    def from_iso8601_utc(iso_str: str) -> datetime:
        """
        将 ISO 8601 格式的字符串转换为 datetime 对象

        Args:
            iso_str: ISO 8601 格式的字符串，如 "2017-12-08T22:40Z"

        Returns:
            datetime: 对应的 datetime 对象（UTC 时区）

        Raises:
            ValueError: 如果字符串格式不正确
        """
        if not iso_str:
            raise ValueError("ISO 8601 字符串不能为空")

        try:
            # 解析 ISO 8601 格式的字符串
            dt = datetime.fromisoformat(iso_str.replace("Z", "+00:00"))
            return dt
        except ValueError as e:
            raise ValueError(f"无效的 ISO 8601 格式: {iso_str}") from e

    @staticmethod
    def now_iso8601_utc() -> str:
        """
        获取当前时间的 ISO 8601 格式字符串（UTC）

        Returns:
            str: 当前时间的 ISO 8601 格式字符串
        """
        return TimeUtils.to_iso8601_utc(datetime.now(timezone.utc))

    @staticmethod
    def format_datetime(dt: datetime, format_str: str = "%Y-%m-%dT%H:%MZ") -> str:
        """
        将 datetime 格式化为指定格式的字符串

        Args:
            dt: datetime 对象
            format_str: 格式字符串，默认为 ISO 8601 格式

        Returns:
            str: 格式化后的字符串
        """
        if dt is None:
            raise ValueError("datetime 对象不能为 None")

        # 如果 datetime 没有时区信息，假设为 UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)

        # 转换为 UTC 时间
        utc_dt = dt.astimezone(timezone.utc)

        return utc_dt.strftime(format_str)


# 创建全局实例，方便直接调用
time_utils = TimeUtils()
