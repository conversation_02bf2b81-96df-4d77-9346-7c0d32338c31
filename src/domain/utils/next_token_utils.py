from loguru import logger
from typing import Any, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime

# 使用 loguru logger


@dataclass
class PaginationModel:
    gmt: Optional[datetime]
    id: Optional[int]


class NextTokenUtils:
    SPLITTER = "_"

    @classmethod
    def encode(
        cls, id: Union[int, str], date: Union[datetime, int, float]
    ) -> Optional[str]:
        """
        编码: id, date -> next_token 字符串
        :param id: ID (int or str)
        :param date: datetime 或 时间戳（毫秒）
        :return: 编码字符串
        """
        if isinstance(date, datetime):
            timestamp = int(date.timestamp() * 1000)
        else:
            timestamp = int(date)
        next_token = cls.SPLITTER.join([str(id), str(timestamp)])
        logger.info(f"Begin to encode nextToken: {next_token}.")
        return next_token

    @classmethod
    def decode(cls, next_token: Optional[str]) -> PaginationModel:
        """
        解码: next_token 字符串 -> PaginationModel
        :param next_token: 编码字符串
        :return: PaginationModel(gmt, id)
        """
        gmt = None
        id_ = None
        if next_token:
            logger.info(f"Begin to decode nextToken: {next_token}.")
            parts = next_token.split(cls.SPLITTER)
            if len(parts) >= 2:
                try:
                    id_ = int(parts[0])
                    gmt = datetime.fromtimestamp(int(parts[1]) / 1000)
                except Exception as e:
                    logger.warning(f"Decode nextToken failed: {e}")
        return PaginationModel(gmt=gmt, id=id_)
