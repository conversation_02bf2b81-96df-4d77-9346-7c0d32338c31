"""
RocketMQ Group 动态管理工具
"""
import os
import socket
from loguru import logger
import uuid
from typing import Optional, Dict, Any
from alibabacloud_credentials.client import Client
from alibabacloud_credentials.models import Config
from src.shared.config.environments import env_manager


# 使用 loguru logger

# 尝试导入阿里云ONS依赖
ONS_AVAILABLE = False
try:
    from alibabacloud_rocketmq20220801.client import Client as RocketMQ20220801Client
    from alibabacloud_credentials.client import Client as CredentialClient
    from alibabacloud_tea_openapi import models as open_api_models
    from alibabacloud_rocketmq20220801 import models as rocket_mq20220801_models
    from alibabacloud_tea_util import models as util_models
    from alibabacloud_tea_util.client import Client as UtilClient
    ONS_AVAILABLE = True
except ImportError:
    logger.warning("未安装阿里云ONS依赖，动态Group创建将不可用")


class RocketMQGroupManager:
    """
    RocketMQ Group 动态管理器
    用于在服务启动时创建唯一的Consumer Group
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Group管理器
        
        Args:
            config: 配置字典，包含阿里云连接信息
        """
        self.config = config
        self.client: Optional[RocketMQ20220801Client] = None
        self._current_group_id: Optional[str] = None
        
        # 从配置中提取必要信息
        self.instance_id = config.get("instance_id")
        self.region = config.get("region", "cn-hangzhou")
        
        if not self.instance_id:
            raise ValueError("缺少instance_id配置")
    
    def _create_client(self) -> Optional[RocketMQ20220801Client]:
        """
        创建阿里云ONS客户端
        
        Returns:
            RocketMQ20220801Client: ONS客户端实例，失败时返回None
        """
        if not ONS_AVAILABLE:
            logger.error("阿里云ONS SDK未安装，无法创建客户端")
            return None
        
        try:
            # 从配置或环境变量读取凭据
            config = env_manager.get_config()
            access_key = config.mq_access_key or os.environ.get("ALIBABA_CLOUD_ACCESS_KEY_ID")
            secret_key = config.mq_secret_key or os.environ.get("ALIBABA_CLOUD_ACCESS_KEY_SECRET")
            if not access_key or not secret_key:
                logger.error("缺少阿里云凭据，请设置access_key和secret_key配置或环境变量")
                return None
            
            # 使用凭据初始化账号Client
            cred_config = Config(
                type='access_key',
                access_key_id=access_key,
                access_key_secret=secret_key,
            )
            credential = CredentialClient(config=cred_config)
            config = open_api_models.Config(credential=credential)
            
            # 设置endpoint rocketmq.cn-hangzhou.aliyuncs.com
            config.endpoint = f'rocketmq.{self.region}.aliyuncs.com'
            
            client = RocketMQ20220801Client(config)
            logger.info(f"阿里云ONS客户端创建成功，region: {self.region}")
            return client
            
        except Exception as e:
            logger.error(f"创建阿里云ONS客户端失败: {e}")
            return None
    
    def initialize_client(self) -> bool:
        """
        在主线程中初始化客户端（解决线程安全问题）
        
        Returns:
            bool: 初始化是否成功
        """
        if self.client:
            return True
            
        self.client = self._create_client()
        return self.client is not None
    
    def _get_local_ip(self) -> str:
        """
        获取本机IP地址
        
        Returns:
            str: 本机IP地址或机器标识符
        """
        try:
            # 直接通过hostname获取本机IP地址
            local_ip = socket.gethostbyname(socket.gethostname())
            logger.info(f"获取到本机IP地址: {local_ip}")
            return local_ip
        except Exception as e:
            logger.warning(f"通过hostname获取IP失败: {e}")
            
            try:
                # 尝试获取hostname作为机器标识符
                hostname = socket.gethostname()
                if hostname and hostname != 'localhost':
                    logger.info(f"使用hostname作为机器标识符: {hostname}")
                    return hostname.replace(".", "_").replace("-", "_")
            except Exception as hostname_error:
                logger.warning(f"获取hostname失败: {hostname_error}")
            
            # 如果都失败了，生成随机标识符
            random_id = str(uuid.uuid4())[:8].replace("-", "")
            logger.warning(f"无法获取机器标识符，使用随机标识符: {random_id}")
            return f"RANDOM_{random_id}"

    def generate_unique_group_id(self, prefix: str = "GID_ALPHA_BROADCAST_SL") -> str:
        """
        基于本机IP地址和随机UUID生成Group ID

        Args:
            prefix: Group ID前缀

        Returns:
            str: 基于IP和UUID的Group ID
        """
        # 获取本机IP地址并替换点号为下划线（Group ID不能包含点号）
        local_ip = self._get_local_ip()
        ip_suffix = local_ip.replace(".", "_")

        # 生成基础Group ID
        group_id = f"{prefix}_{ip_suffix}"

        # 在daily环境下添加5位UUID后缀
        current_env = env_manager.current_env.value
        if current_env == "daily":
            uuid_suffix = str(uuid.uuid4()).replace("-", "")[:5]
            group_id = f"{group_id}_{uuid_suffix}"
            logger.info(f"Daily环境，添加UUID后缀: {uuid_suffix}")

        logger.info(f"基于IP和UUID生成Group ID: {group_id} (环境: {current_env})")
        return group_id
    
    def create_consumer_group(self, group_id: Optional[str] = None) -> Optional[str]:
        """
        创建Consumer Group
        
        Args:
            group_id: 指定的Group ID，如果为None则自动生成
            
        Returns:
            str: 创建成功的Group ID，失败时返回None
        """
        if not ONS_AVAILABLE:
            logger.error("阿里云ONS SDK未安装，无法创建Consumer Group")
            return None
        
        # 检查客户端是否已初始化
        if not self.client:
            logger.error("ONS客户端未初始化，请先调用initialize_client()")
            return None
        
        # 生成或使用指定的Group ID
        if not group_id:
            group_id = self.generate_unique_group_id()
        
        try:
            # 创建Group请求
            policy = rocket_mq20220801_models.CreateConsumerGroupRequestConsumeRetryPolicy(fixed_interval_retry_time= 15, max_retry_times=1000, retry_policy="FixedRetryPolicy")
            create_request = rocket_mq20220801_models.CreateConsumerGroupRequest(delivery_order_type="Orderly", consume_retry_policy = policy)
            
            runtime = util_models.RuntimeOptions()
            
            # 调用创建API
            response = self.client.create_consumer_group(self.instance_id, group_id, create_request)
            
            logger.info(f"Consumer Group创建成功: {group_id}")
            self._current_group_id = group_id
            return group_id
            
        except Exception as error:
            # 检查是否是Group已存在的错误
            error_message = str(error)
            error_details = ""
            
            # 尝试从异常中提取详细错误信息
            if hasattr(error, 'data') and error.data:
                error_details = str(error.data)
            elif hasattr(error, 'body') and hasattr(error.body, 'message'):
                error_details = str(error.body.message)
            elif hasattr(error, 'message'):
                error_details = str(error.message)
            else:
                error_details = error_message
            
            # 检查各种表示已存在的错误码和消息
            group_exists_indicators = [
                "GROUP_ALREADY_EXIST",
                "already exists",
                "BIZ_SUBSCRIPTION_EXISTED",
                "specified consumerId has already exist"
            ]
            
            # 检查错误消息和详情
            is_group_exists = any(
                indicator.lower() in error_message.lower() or indicator.lower() in error_details.lower()
                for indicator in group_exists_indicators
            )
            
            if is_group_exists:
                logger.info(f"Consumer Group已存在，复用现有Group: {group_id}")
                logger.info(f"错误详情: {error_details}")
                self._current_group_id = group_id
                return group_id
            else:
                logger.error(f"创建Consumer Group失败: {error}")
                logger.error(f"错误详情: {error_details}")
                return None
    
    def get_current_group_id(self) -> Optional[str]:
        """
        获取当前使用的Group ID
        
        Returns:
            str: 当前Group ID
        """
        return self._current_group_id
    
    def create_and_get_group_id(self, auto_create: bool = True) -> str:
        """
        创建并获取Group ID的便捷方法
        
        Args:
            auto_create: 是否自动创建Group
            
        Returns:
            str: Group ID，如果创建失败则返回默认值
        """
        if auto_create:
            group_id = self.create_consumer_group()
            if group_id:
                return group_id
            else:
                logger.warning("动态创建Group失败，使用fallback Group ID")
        
        # 创建失败时的fallback逻辑：使用配置中的默认值或生成基于IP的ID
        fallback_group_id = self.config.get("group_id", self.generate_unique_group_id("GID_ALPHA_FALLBACK"))
        logger.info(f"使用fallback Group ID: {fallback_group_id}")
        return fallback_group_id
    
    def delete_consumer_group(self, group_id: Optional[str] = None) -> bool:
        """
        删除Consumer Group
        
        Args:
            group_id: 要删除的Group ID，如果为None则删除当前创建的Group
            
        Returns:
            bool: 删除是否成功
        """
        if not ONS_AVAILABLE:
            logger.error("阿里云ONS SDK未安装，无法删除Consumer Group")
            return False
        
        # 检查客户端是否已初始化
        if not self.client:
            logger.error("ONS客户端未初始化，请先调用initialize_client()")
            return False
        
        # 确定要删除的Group ID
        target_group_id = group_id or self._current_group_id
        if not target_group_id:
            logger.warning("没有指定要删除的Group ID")
            return False
        
        # 安全检查：只删除我们创建的Group（以GID_ALPHA开头的）
        if not target_group_id.startswith("GID_ALPHA"):
            logger.warning(f"拒绝删除非ALPHA系统创建的Group: {target_group_id}")
            return False
        
        try:
            runtime = util_models.RuntimeOptions()
            
            # 调用删除API
            #self.client.delete_consumer_group(self.instance_id, target_group_id)
            
            logger.info(f"Consumer Group删除成功: {target_group_id}")

            # 清除当前Group ID记录
            if target_group_id == self._current_group_id:
                self._current_group_id = None
                
            return True
            
        except Exception as error:
            # 检查是否是Group不存在的错误
            error_message = str(error)
            if "GROUP_NOT_EXIST" in error_message or "not exist" in error_message.lower():
                logger.warning(f"Consumer Group不存在（可能已被删除）: {target_group_id}")
                return True  # 认为删除成功
            else:
                logger.error(f"删除Consumer Group失败: {error}")
                if hasattr(error, 'data') and error.data:
                    logger.error(f"错误详情: {error.data}")
                return False