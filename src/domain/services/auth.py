# -*- coding: utf-8 -*-
"""
统一鉴权模块 - 简化导入
提供一站式的鉴权相关导入，避免复杂的import路径
"""

# 导入所有鉴权相关的类型和函数
from .auth_service import (
    # 核心服务和类型
    auth_service,
    AuthContext,
    AuthResult,
    PermissionParam,
    
    # 便捷函数
    get_current_user,
    require_auth,
    require_file_read_permission,
    require_file_write_permission,
    require_file_delete_permission,
    require_session_read_permission,
    require_session_write_permission,
    require_knowledge_base_read_permission,
    require_knowledge_base_write_permission,
    require_artifact_read_permission,
    require_knowledge_read_permission,
)

# 导入枚举和常量
from ...infrastructure.database.models.auth_models import (
    PermissionType,
    ResourceType,
    ShareType,
)

# 导入权限工具
from ...infrastructure.database.models.permission_utils import (
    Permissions,
    PermissionUtils,
    PermissionParam as PermissionParamUtil,
    normalize_permission,
)

# 重新导出所有内容，方便一次性导入
__all__ = [
    # 核心服务和类型
    'auth_service',
    'AuthContext',
    'AuthResult',
    'PermissionParam',
    
    # 枚举类型
    'PermissionType',
    'ResourceType',
    'ShareType',
    
    # 权限常量和工具
    'Permissions',
    'PermissionUtils',
    'normalize_permission',
    
    # 便捷函数
    'get_current_user',
    'require_auth',
    'require_file_read_permission',
    'require_file_write_permission',
    'require_file_delete_permission',
    'require_session_read_permission',
    'require_session_write_permission',
    'require_knowledge_base_read_permission',
    'require_knowledge_base_write_permission',
    'require_artifact_read_permission',
    'require_knowledge_read_permission',
]


# ==================== 便捷的权限检查函数 ====================

def check_permission(context: AuthContext, resource_type: str, resource_id: str, permission: PermissionParam):
    """便捷的权限检查函数"""
    return auth_service.check_resource_permission(context, resource_type, resource_id, permission)


def has_permission(user_permissions: list, required_permission: PermissionParam) -> bool:
    """检查用户是否拥有指定权限"""
    return PermissionUtils.has_permission(user_permissions, required_permission)


def has_any_permission(user_permissions: list, required_permissions: list) -> bool:
    """检查用户是否拥有任意一个指定权限"""
    return PermissionUtils.has_any_permission(user_permissions, required_permissions)


def has_all_permissions(user_permissions: list, required_permissions: list) -> bool:
    """检查用户是否拥有所有指定权限"""
    return PermissionUtils.has_all_permissions(user_permissions, required_permissions)


def is_owner_permission(permissions: list) -> bool:
    """检查是否为所有者权限"""
    return set(permissions) == set(Permissions.OWNER)


def is_public_permission(permissions: list) -> bool:
    """检查是否为公开权限"""
    return permissions == Permissions.PUBLIC


# 添加到导出列表
__all__.extend(['check_permission', 'has_permission', 'has_any_permission', 'has_all_permissions', 
                'is_owner_permission', 'is_public_permission', 'P', 'R'])