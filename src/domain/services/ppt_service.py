#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT业务服务
处理PPT相关的业务逻辑，包括认证、生成、保存和下载等功能
"""
import time
from typing import Optional
import uuid

from memory.events import ArtifactEvent, CustomEvent
from src.application.ppt_api_models import GetPPTAuthCodeResponse
from loguru import logger

from ...popclients.aippt_client import get_aippt_client
from src.infrastructure.memory.memory_sdk import MemorySDK
from src.infrastructure.redis.client import RedisClient


class PPTService:
    """PPT业务服务"""

    def __init__(self):
        self.aippt_client = None  # 延迟初始化
        self.memory = None  # 延迟初始化
        logger.info("[PPTService] PPT服务初始化完成")

    def _get_aippt_client(self):
        """获取AIPPT客户端实例"""
        if self.aippt_client is None:
            self.aippt_client = get_aippt_client()
        return self.aippt_client

    def _get_memory(self):
        """获取MemorySDK实例"""
        if self.memory is None:
            self.memory = MemorySDK()
        return self.memory

    def _get_aippt_token(self):
        """
        获取AIPPT token，优先从缓存获取，如果不存在则重新获取
        """
        logger.info(f"[PPTService] 获取AIPPT token")

        # 优先从缓存中获取token
        redis_client = RedisClient()
        token = redis_client.get(f"aippt_token")
        if token:
            logger.info(f"[PPTService] 从缓存中获取AIPPT token: {token}")
            return token

        # 如果缓存中没有token，则重新获取，并缓存
        logger.info(f"[PPTService] 从缓存中没有获取到AIPPT token，重新获取")
        client = self._get_aippt_client()
        response = client.get_aippt_token()
        redis_client.set(f"aippt_token", response.token, int(response.time_expire))

        return response.token

    def _poll_export_result(
        self, task_key: str, token: str, timeout: int = 60
    ) -> Optional[str]:
        """
        轮询获取导出结果

        Args:
            task_key: 导出任务标识
            token: 认证token
            timeout: 超时时间（秒），默认60秒

        Returns:
            Optional[str]: 下载链接，如果超时则返回None
        """
        logger.info(
            f"[PPTService] 开始轮询导出结果: task_key={task_key}, timeout={timeout}s"
        )

        start_time = time.time()
        poll_interval = 1  # 每秒查询一次

        while time.time() - start_time < timeout:
            try:
                client = self._get_aippt_client()
                result = client.get_ppt_export_result(task_key=task_key, token=token)
                download_url = result.get('download_url')
                message = result.get('message', '')

                if download_url:
                    elapsed_time = time.time() - start_time
                    logger.info(
                        f"[PPTService] 导出成功: task_key={task_key}, 耗时={elapsed_time:.1f}s"
                    )
                    return download_url

                elapsed_time = time.time() - start_time
                logger.info(
                    f"[PPTService] 导出进行中: task_key={task_key}, 状态={message}, 已等待={elapsed_time:.1f}s"
                )

                # 等待下次查询
                time.sleep(poll_interval)

            except Exception as e:
                logger.warning(
                    f"[PPTService] 查询导出结果失败: task_key={task_key}, error={str(e)}"
                )
                time.sleep(poll_interval)

        # 超时
        elapsed_time = time.time() - start_time
        logger.error(
            f"[PPTService] 导出超时: task_key={task_key}, 超时时间={elapsed_time:.1f}s"
        )
        return None

    def get_ppt_auth_code(
        self,
        ali_uid: int,
    ) -> GetPPTAuthCodeResponse:
        """
        获取PPT认证code

        Args:
            ali_uid: 用户ID

        Returns:
            GetPPTAuthCodeResponse: 认证码响应
        """
        logger.info(f"[PPTService] 获取PPT认证code: ali_uid={ali_uid}")

        client = self._get_aippt_client()
        response = client.get_aippt_auth_code(ali_uid=ali_uid)

        return response

    def bind_ppt_to_session(
        self,
        session_id: str,
        ppt_id: str,
    ):
        """
        绑定PPT到会话
        """
        logger.info(
            f"[PPTService] 绑定PPT到会话: session_id={session_id}, ppt_id={ppt_id}"
        )

        memory = self._get_memory()

        # 生成随机run_id
        run_id = str(uuid.uuid4())

        # 创建自定义事件
        memory.add_event(
            CustomEvent(
                session_id=session_id,
                run_id=run_id,
                name="ppt_id",
                content=ppt_id,
            ),
            session_id,
        )

    def get_ppt_thumbnail(
        self,
        ppt_id: str,
    ) -> str:
        """
        获取PPT缩略图
        """
        try:
            logger.info(f"[PPTService] 获取PPT缩略图: ppt_id={ppt_id}")

            client = self._get_aippt_client()

            # 获取token
            token = self._get_aippt_token()

            # 获取ppt详情
            ppt_info = client.get_ppt_info(ppt_id=ppt_id, token=token)
            logger.info(
                f"[PPTService] 获取PPT详情成功: {ppt_info.get('name', 'Unknown')}"
            )

            return ppt_info.get('cover_url', '')

        except Exception as e:
            logger.error(
                f"[PPTService] 获取PPT缩略图失败: ppt_id={ppt_id}, error={str(e)}"
            )
            raise PPTServiceError(f"获取PPT缩略图失败: {str(e)}") from e

    def save_ppt(
        self,
        ppt_id: str,
        session_id: str,
    ) -> str:
        """
        保存PPT

        Args:
            ppt_id: PPT作品ID
            session_id: 会话ID，用于更新制品服务

        Returns:
            str: 下载链接
        """
        try:
            logger.info(f"[PPTService] 开始保存PPT: ppt_id={ppt_id}")

            client = self._get_aippt_client()

            # 获取token
            token = self._get_aippt_token()

            # 获取ppt详情
            ppt_info = client.get_ppt_info(ppt_id=ppt_id, token=token)
            logger.info(
                f"[PPTService] 获取PPT详情成功: {ppt_info.get('name', 'Unknown')}"
            )

            # 导出ppt
            task_key = client.export_ppt(ppt_id=ppt_id, token=token)
            logger.info(f"[PPTService] 启动PPT导出: task_key={task_key}")

            # 轮询获取导出结果（最多1分钟，每秒查询一次）
            download_url = self._poll_export_result(task_key, token, timeout=60)

            if download_url:
                logger.info(
                    f"[PPTService] PPT保存成功: ppt_id={ppt_id}, download_url={download_url}"
                )

                # 更新制品
                try:
                    memory = self._get_memory()

                    # 生成随机run_id
                    run_id = str(uuid.uuid4())

                    # 创建制品事件
                    artifact_event = ArtifactEvent(
                        session_id=session_id,
                        run_id=run_id,
                        artifact_type="download_url",
                        file_type="ppt",
                        file_name=ppt_info.get('name', 'Unknown'),
                        content=download_url,
                        description="PPT作品下载链接",
                        is_process_file=False,
                    )

                    # 使用MemorySDK的通用add_event方法
                    memory.add_event(artifact_event, session_id)

                except Exception as e:
                    logger.warning(f"[PPTService] 更新制品服务失败: {str(e)}")

                return download_url
            else:
                logger.error(f"[PPTService] PPT保存失败，导出超时: ppt_id={ppt_id}")
                raise PPTServiceError(f"PPT导出超时，请稍后重试")

        except Exception as e:
            logger.error(f"[PPTService] 保存PPT失败: ppt_id={ppt_id}, error={str(e)}")
            raise PPTServiceError(f"保存PPT失败: {str(e)}") from e


class PPTServiceError(Exception):
    """PPT服务异常"""

    pass


# 全局单例实例
ppt_service = PPTService()
