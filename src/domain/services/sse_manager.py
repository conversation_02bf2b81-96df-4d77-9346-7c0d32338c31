"""
SSE流管理器
处理SSE连接管理、消息推送和心跳
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any, AsyncGenerator, Optional, List
from loguru import logger

from ..models import Session, Message


class SSEManager:
    """SSE流管理器 - 处理SSE连接和消息推送"""
    
    def __init__(self):
        self.sse_connections: Dict[str, asyncio.Queue] = {}  # session_id -> Queue
        self.heartbeat_tasks: Dict[str, asyncio.Task] = {}  # session_id -> Task
        self.session_loader = None  # 稍后由SessionManager注入
        self.pending_messages: Dict[str, List[Dict[str, Any]]] = {}  # 待推送消息缓存 session_id -> messages
        
    def set_session_loader(self, session_loader):
        """设置Session加载器引用"""
        self.session_loader = session_loader
    
    async def create_sse_stream(self, session_id: str, last_message_id: Optional[str] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """创建SSE流生成器 - session维度的连接"""
        logger.info(f"[SSEManager] 开始创建SSE流: session_id={session_id}")
        if last_message_id:
            logger.info(f"[SSEManager] 断线重连: last_message_id={last_message_id}")
        
        # 加载Session
        session = await self._load_session_for_sse(session_id)
        if not session:
            raise ValueError(f"Session不存在: {session_id}")
        
        # 创建消息队列 - 使用session作为key
        message_queue = self._create_message_queue(session_id)

        heartbeat_task = None
        try:
            # 如果是重连，从历史消息中推送缓存消息
            #if last_message_id:
                #await self._push_session_history_messages(session_id, last_message_id, message_queue)

            # 开始心跳
            heartbeat_task = asyncio.create_task(self._heartbeat_loop(message_queue))

            # 跟踪心跳任务 - 使用session作为key
            self.heartbeat_tasks[session_id] = heartbeat_task
            
            logger.info(f"[SSEManager] SSE流创建完成，开始消息事件循环: session_id={session_id}")
            
            # 监听消息
            async for event in self._message_event_loop(message_queue, session_id):
                yield event
        
        finally:
            # 取消心跳任务
            if heartbeat_task and not heartbeat_task.done():
                heartbeat_task.cancel()
                try:
                    await heartbeat_task
                except asyncio.CancelledError:
                    pass
                except Exception as e:
                    logger.error(f"[SSEManager] 等待心跳任务结束时出错: {e}")
            
            # 清理连接和任务记录
            self._cleanup_session_connection(session_id)
            self._cleanup_session_heartbeat_task(session_id)
            logger.info(f"[SSEManager] SSE流和HTTP连接已完全关闭: session_id={session_id}")
            logger.info(f"[SSEManager] 清理后剩余连接数={len(self.sse_connections)}, 剩余连接: {list(self.sse_connections.keys())}")
    
    async def _load_session_for_sse(self, session_id: str) -> Optional[Session]:
        """为SSE加载Session"""
        try:
            logger.info(f"[SSEManager] 从数据库加载Session: {session_id}")
            
            # 使用session_loader加载Session
            if self.session_loader:
                session = self.session_loader.load_session_from_db(session_id)
                if session:
                    # 设置事件监听
                    self.session_loader.setup_session_events(session)
                    logger.info(f"[SSEManager] 从数据库加载Session成功: {session_id}")
                    return session
            
            # 如果没有session_loader，直接从数据库加载
            from ...infrastructure.database.repositories.session_repository import session_db_service
            session_model = session_db_service.get_session_by_id(session_id)
            if not session_model:
                return None
            
            # 从数据库模型重建Session对象
            session = Session(
                session_id=session_model.session_id,
                ali_uid=session_model.ali_uid,
                agent_id=session_model.agent_id,
                title=session_model.title,
                status=session_model.status,
                gmt_create=session_model.gmt_create,
                gmt_modified=session_model.gmt_modified,
                metadata=session_model.meta_data or {}
            )
            
            return session
            
        except Exception as e:
            logger.error(f"[SSEManager] 加载Session失败: {e}")
            return None
    

    def _create_message_queue(self, session_id: str) -> asyncio.Queue:
        """创建session级别的消息队列"""
        message_queue = asyncio.Queue()
        self.sse_connections[session_id] = message_queue
        logger.info(f"[SSEManager] 创建SSE连接: session_id={session_id}, 当前连接数={len(self.sse_connections)}")
        logger.info(f"[SSEManager] 当前所有连接: {list(self.sse_connections.keys())}")
        return message_queue
    
    async def _message_event_loop(self, message_queue: asyncio.Queue, session_id: str) -> AsyncGenerator[str, None]:
        """消息事件循环"""
        while True:
            try:
                message = await asyncio.wait_for(message_queue.get(), timeout=30.0)

                # 检查是否是关闭信号
                if isinstance(message, dict) and (message.get("type") == "close" or message.get("type") == "done" ) :
                    logger.info(f"[SSEManager] 收到关闭信号，退出消息循环: session_id={session_id}")
                    break

                # 正常消息处理
                if isinstance(message, dict):
                    message = json.dumps(message)

                yield f"data: {message}\n\n"
            except asyncio.TimeoutError:
                logger.warning(f"[SSEManager] SSE心跳超时: session_id={session_id}")
                break
            except Exception as e:
                logger.error(f"[SSEManager] SSE异常: {e}")
                break
    
    async def _heartbeat_loop(self, message_queue: asyncio.Queue):
        """心跳循环"""
        try:
            while True:
                await asyncio.sleep(3)  # 3秒心跳
                heartbeat_data = json.dumps({"type": "heartbeat"})
                await message_queue.put(heartbeat_data)
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"[SSEManager] 心跳异常: {e}")
    
    # async def _push_session_history_messages(self, session_id: str, last_message_id: str, message_queue: asyncio.Queue):
    #     """从session历史消息中推送消息"""
    #     try:
    #         logger.info(f"[SSEManager] 开始从session历史消息推送: session_id={session_id}, last_message_id={last_message_id}")
    #
    #         # 获取会话历史
    #         session_history = self._get_session_history(session_id)
    #         if not session_history:
    #             logger.warning(f"[SSEManager] 无法获取会话历史: session_id={session_id}")
    #             return
    #
    #         # 收集所有round的消息，按时间排序
    #         all_messages = []
    #         for round_obj in session_history.rounds:
    #             all_messages.extend(round_obj.messages)
    #
    #         # 按时间排序
    #         all_messages.sort(key=lambda m: m.timestamp if m.timestamp else datetime.now())
    #
    #         # 查找last_message_id的位置，并获取后续消息
    #         messages_to_push = self._find_messages_after_last_id(all_messages, last_message_id)
    #
    #         logger.info(f"[SSEManager] 需要推送的session历史消息数量: {len(messages_to_push)}")
    #
    #         # 推送历史消息
    #         for message in messages_to_push:
    #             message_data = self._format_history_message_data(message)
    #
    #             await message_queue.put({
    #                 "type": "message",
    #                 "data": message_data
    #             })
    #             await asyncio.sleep(0.1)  # 避免消息过快
    #
    #         logger.info(f"[SSEManager] session历史消息推送完成: {len(messages_to_push)}条")
    #
    #     except Exception as e:
    #         logger.error(f"[SSEManager] 推送session历史消息失败: {e}")
    #
    # def _find_messages_after_last_id(self, messages: list, last_message_id: str) -> list:
    #     """查找last_message_id之后的消息"""
    #     messages_to_push = []
    #     found_last_message = False
    #
    #     for message in messages:
    #         if found_last_message:
    #             # 已经找到last_message_id，后续的消息都需要推送
    #             messages_to_push.append(message)
    #         elif message.message_id == last_message_id:
    #             # 找到了last_message_id，从下一个消息开始推送
    #             found_last_message = True
    #
    #     # 如果没有找到last_message_id，推送所有消息
    #     if not found_last_message:
    #         logger.warning(f"[SSEManager] 未找到last_message_id: {last_message_id}, 推送所有消息")
    #         messages_to_push = messages
    #
    #     return messages_to_push
    #
    # def _format_history_message_data(self, message: Message) -> Dict[str, Any]:
    #     """格式化历史消息数据"""
    #     return {
    #         "messageId": message.message_id,
    #         "role": message.role.value if hasattr(message.role, 'value') else str(message.role),
    #         "roundId": message.round_id,
    #         "content": message.content,
    #         "timestamp": message.timestamp.isoformat() if message.timestamp else datetime.now().isoformat(),
    #         "sessionId": message.session_id,
    #         "name": message.name,
    #         "toolCallId": message.tool_call_id,
    #         "appId": message.app_id,
    #         "duration": message.duration
    #     }
    
    def _get_session_history(self, session_id: str):
        """获取会话历史 - 这里需要调用session_manager的方法"""
        # 这个方法需要session_manager的引用，先留空实现
        # 在session_manager中会设置正确的实现
        return None
    

    
    def _cleanup_session_connection(self, session_id: str):
        """清理session级别的连接"""
        if session_id in self.sse_connections:
            del self.sse_connections[session_id]

    def _cleanup_session_heartbeat_task(self, session_id: str):
        """清理session级别的心跳任务记录"""
        if session_id in self.heartbeat_tasks:
            heartbeat_task = self.heartbeat_tasks[session_id]
            if heartbeat_task and not heartbeat_task.done():
                heartbeat_task.cancel()
                logger.debug(f"[SSEManager] 清理时取消心跳任务: session_id={session_id}")
            del self.heartbeat_tasks[session_id]
    
    def close_session_connection(self, session_id: str):
        """关闭Session的SSE连接"""
        try:
            logger.info(f"[SSEManager] 开始关闭Session连接: session_id={session_id}")

            # 取消心跳任务
            if session_id in self.heartbeat_tasks:
                heartbeat_task = self.heartbeat_tasks[session_id]
                if heartbeat_task and not heartbeat_task.done():
                    heartbeat_task.cancel()
                    logger.info(f"[SSEManager] 已取消心跳任务: session_id={session_id}")
                # 立即清理心跳任务记录
                del self.heartbeat_tasks[session_id]

            # 向session连接发送关闭信号
            if session_id in self.sse_connections:
                message_queue = self.sse_connections[session_id]
                try:
                    # 发送关闭信号，这会触发生成器函数结束并关闭HTTP连接
                    # 使用同步方式确保信号能够立即发送
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        asyncio.create_task(message_queue.put({
                            "type": "close",
                            "data": {"message": "Session completed"}
                        }))
                    else:
                        # 如果事件循环未运行，直接放入队列
                        message_queue.put_nowait({
                            "type": "close",
                            "data": {"message": "Session completed"}
                        })
                    logger.info(f"[SSEManager] 已向连接发送关闭信号: session_id={session_id}")
                except Exception as e:
                    logger.error(f"[SSEManager] 发送关闭信号失败: session_id={session_id}, error={e}")

                logger.info(f"[SSEManager] 已向Session连接发送关闭信号: session_id={session_id}")

        except Exception as e:
            logger.error(f"[SSEManager] 关闭Session连接失败: {e}")
    
    async def push_to_sse(self, session_id: str, round_id: str, data: Dict[str, Any], event_type: str = "message"):
        """推送到SSE连接"""
        try:
            logger.info(f"[SSEManager] 尝试推送消息: session_id={session_id}, event_type={event_type}")
            logger.info(f"[SSEManager] 当前连接数={len(self.sse_connections)}, 所有连接: {list(self.sse_connections.keys())}")
            
            message_data = json.dumps(data)

            # 推送到session级别的连接
            if session_id in self.sse_connections:
                message_queue = self.sse_connections[session_id]
                await message_queue.put(message_data)
                logger.info(f"[SSEManager] 推送到session连接成功: session_id={session_id}, event_type={event_type}")
                return

        except Exception as e:
            logger.error(f"[SSEManager] 推送SSE失败: session_id={session_id}, error={e}")
    
    def cleanup_expired_connections(self):
        """清理过期的SSE连接"""
        try:
            current_sse_sessions = list(self.sse_connections.keys())
            current_heartbeat_sessions = list(self.heartbeat_tasks.keys())
            
            if current_sse_sessions:
                logger.info(f"[SSEManager] 当前活跃SSE连接: {len(current_sse_sessions)}个")
            
            if current_heartbeat_sessions:
                logger.info(f"[SSEManager] 当前活跃心跳任务: {len(current_heartbeat_sessions)}个session")
                
            # 检查是否有孤立的心跳任务（有心跳任务但没有连接）
            orphaned_heartbeat_sessions = set(current_heartbeat_sessions) - set(current_sse_sessions)
            for session_id in orphaned_heartbeat_sessions:
                logger.warning(f"[SSEManager] 发现孤立的心跳任务，正在清理: session_id={session_id}")
                if session_id in self.heartbeat_tasks:
                    task = self.heartbeat_tasks[session_id]
                    if task and not task.done():
                        task.cancel()
                    del self.heartbeat_tasks[session_id]
                    
        except Exception as e:
            logger.error(f"[SSEManager] 清理SSE连接失败: {e}")
    
    # def set_session_history_getter(self, getter_func):
    #     """设置获取会话历史的函数"""
    #     self._get_session_history = getter_func