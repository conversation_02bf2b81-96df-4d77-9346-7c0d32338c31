"""
状态机
Session状态转换的管理
"""

from datetime import datetime
from loguru import logger

from ..models.enums import SessionStatus


class SessionStateMachine:
    """独立的状态机类 - 职责单一"""
    
    # 状态转换规则 - 简化版
    TRANSITIONS = {
        SessionStatus.CREATE: {SessionStatus.ACTIVE},  # 刚创建 -> 开始处理
        SessionStatus.ACTIVE: {SessionStatus.CLOSED},  # 正在处理 -> 处理完成
        SessionStatus.CLOSED: {SessionStatus.ACTIVE}   # 处理完成 -> 开始新的处理
    }
    
    @classmethod
    def can_transition(cls, from_status: SessionStatus, to_status: SessionStatus) -> bool:
        """检查状态转换是否合法"""
        return to_status in cls.TRANSITIONS.get(from_status, set())
    
    @classmethod
    def transition(cls, session: "Session", to_status: SessionStatus, reason: str = "") -> bool:
        """执行状态转换"""
        if not cls.can_transition(session.status, to_status):
            logger.warning(f"[StateMachine] 非法状态转换: {session.status} -> {to_status}")
            return False
        
        old_status = session.status
        session.status = to_status
        session.gmt_modified = datetime.now()
        
        # 触发状态变更事件
        session.emit('status_changed', session, old_status, to_status, reason)
        logger.info(f"[StateMachine] 状态转换: {session.session_id} {old_status} -> {to_status}")
        return True
    
    @classmethod
    def get_valid_transitions(cls, from_status: SessionStatus) -> set:
        """获取当前状态可以转换到的状态"""
        return cls.TRANSITIONS.get(from_status, set())
    
    @classmethod
    def is_final_state(cls, status: SessionStatus) -> bool:
        """判断是否为终态"""
        return len(cls.TRANSITIONS.get(status, set())) == 0 