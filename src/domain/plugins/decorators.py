"""
插件装饰器和注册机制
提供便捷的插件注册和生命周期绑定装饰器
"""

from typing import Set, Type, Callable, Any
from functools import wraps
from .interfaces import ILifecyclePlugin, LifecyclePhase, PluginPriority, BaseLifecyclePlugin


class PluginRegistry:
    """插件注册表"""
    
    def __init__(self):
        self._plugins: dict[str, ILifecyclePlugin] = {}
        self._phase_plugins: dict[LifecyclePhase, list[ILifecyclePlugin]] = {}
    
    def register(self, plugin: ILifecyclePlugin) -> None:
        """注册插件"""
        self._plugins[plugin.name] = plugin
        
        # 按阶段索引插件
        for phase in plugin.supported_phases:
            if phase not in self._phase_plugins:
                self._phase_plugins[phase] = []
            self._phase_plugins[phase].append(plugin)
            # 按优先级排序
            self._phase_plugins[phase].sort(key=lambda p: p.priority.value, reverse=True)
    
    def get_plugins_for_phase(self, phase: LifecyclePhase) -> list[ILifecyclePlugin]:
        """获取指定阶段的所有插件"""
        return self._phase_plugins.get(phase, [])
    
    def get_plugin(self, name: str) -> ILifecyclePlugin | None:
        """根据名称获取插件"""
        return self._plugins.get(name)
    
    def list_plugins(self) -> list[ILifecyclePlugin]:
        """列出所有已注册的插件"""
        return list(self._plugins.values())


# 全局插件注册表
plugin_registry = PluginRegistry()


def lifecycle_plugin(
    name: str,
    phases: Set[LifecyclePhase],
    priority: PluginPriority = PluginPriority.NORMAL,
    version: str = "1.0.0",
    auto_register: bool = True
):
    """生命周期插件装饰器
    
    Args:
        name: 插件名称
        phases: 支持的生命周期阶段
        priority: 插件优先级
        version: 插件版本
        auto_register: 是否自动注册到全局注册表
    """
    def decorator(cls: Type) -> Type[ILifecyclePlugin]:
        # 保存原始的__init__方法
        original_init = getattr(cls, '__init__', None)
        
        # 创建新的__init__方法
        def new_init(self, *args, **kwargs):
            # 首先调用BaseLifecyclePlugin的初始化
            BaseLifecyclePlugin.__init__(self, name, version, priority)
            # 然后调用原始类的初始化（如果存在且不是object.__init__）
            if original_init and original_init is not object.__init__:
                # 跳过self参数，因为BaseLifecyclePlugin已经处理了
                try:
                    original_init(self, *args, **kwargs)
                except TypeError:
                    # 如果原始__init__不接受额外参数，就只调用无参版本
                    pass
        
        # 添加必要的属性和方法
        cls.__init__ = new_init
        cls._plugin_name = name
        cls._plugin_version = version  
        cls._plugin_priority = priority
        cls._supported_phases = phases
        
        # 确保类继承自BaseLifecyclePlugin
        if not issubclass(cls, BaseLifecyclePlugin):
            # 动态创建继承BaseLifecyclePlugin的新类
            class PluginClass(BaseLifecyclePlugin, cls):
                def __init__(self, *args, **kwargs):
                    BaseLifecyclePlugin.__init__(self, name, version, priority)
                
                @property 
                def supported_phases(self) -> Set[LifecyclePhase]:
                    return phases
            
            PluginClass.__name__ = cls.__name__
            PluginClass.__qualname__ = cls.__qualname__
            result_cls = PluginClass
        else:
            result_cls = cls
        
        # 自动注册插件
        if auto_register:
            instance = result_cls()
            plugin_registry.register(instance)
        
        return result_cls
    
    return decorator


def phase_handler(phase: LifecyclePhase):
    """阶段处理器装饰器，用于标记处理特定阶段的方法"""
    def decorator(func: Callable) -> Callable:
        func._handles_phase = phase
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def requires_context(*required_fields: str):
    """上下文验证装饰器，确保上下文包含必要字段"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(self, context, *args, **kwargs):
            # 验证必要字段
            for field in required_fields:
                if not hasattr(context, field) or getattr(context, field) is None:
                    raise ValueError(f"插件 {self.name} 需要上下文字段: {field}")
            
            return await func(self, context, *args, **kwargs)
        
        return wrapper
    return decorator


def error_handler(fallback_return=False):
    """错误处理装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(self, context, *args, **kwargs):
            try:
                return await func(self, context, *args, **kwargs)
            except Exception as e:
                self.on_error(context, e)
                return fallback_return
        
        return wrapper
    return decorator


# 便捷的阶段装饰器
def on_create(priority: PluginPriority = PluginPriority.NORMAL):
    """Session创建时执行的插件装饰器"""
    return lifecycle_plugin(
        name="",  # 将从类名推断
        phases={LifecyclePhase.ON_CREATE},
        priority=priority
    )


def on_active(priority: PluginPriority = PluginPriority.NORMAL):
    """Session激活时执行的插件装饰器"""
    return lifecycle_plugin(
        name="",
        phases={LifecyclePhase.ON_ACTIVE},
        priority=priority
    )


def on_processing(priority: PluginPriority = PluginPriority.NORMAL):
    """消息处理时执行的插件装饰器"""
    return lifecycle_plugin(
        name="",
        phases={LifecyclePhase.ON_PROCESSING},
        priority=priority
    )


def on_disconnected(priority: PluginPriority = PluginPriority.NORMAL):
    """连接断开时执行的插件装饰器"""
    return lifecycle_plugin(
        name="",
        phases={LifecyclePhase.ON_DISCONNECTED},
        priority=priority
    )


def on_closed(priority: PluginPriority = PluginPriority.NORMAL):
    """Session关闭时执行的插件装饰器"""
    return lifecycle_plugin(
        name="",
        phases={LifecyclePhase.ON_CLOSED},
        priority=priority
    ) 