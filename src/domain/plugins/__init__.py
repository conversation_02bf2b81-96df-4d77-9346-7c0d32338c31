"""
领域层 - 插件系统

提供规范化的插件接口、装饰器和管理机制，支持会话生命周期的扩展。

主要组件：
- ILifecyclePlugin: 插件接口定义
- BaseLifecyclePlugin: 插件基类
- PluginManager: 插件管理器
- lifecycle_plugin: 插件装饰器
- PluginContext: 插件执行上下文
- LifecyclePhase: 生命周期阶段枚举

使用示例：
```python
from src.domain.plugins import lifecycle_plugin, LifecyclePhase, PluginPriority

@lifecycle_plugin(
    name="MyPlugin",
    phases={LifecyclePhase.ON_CREATE},
    priority=PluginPriority.HIGH
)
class MyPlugin(BaseLifecyclePlugin):
    @property
    def supported_phases(self):
        return {LifecyclePhase.ON_CREATE}
    
    async def execute(self, context):
        # 插件逻辑
        return True
```
"""

# 核心接口和基类
from .interfaces import (
    ILifecyclePlugin,
    BaseLifecyclePlugin,
    LifecyclePhase,
    PluginContext,
    PluginPriority,
    PluginResult
)

# 装饰器和注册机制
from .decorators import (
    lifecycle_plugin,
    plugin_registry,
    phase_handler,
    requires_context,
    error_handler,
    on_create,
    on_active,
    on_processing,
    on_disconnected,
    on_closed
)

# 插件管理器
from .manager import (
    PluginManager,
    PluginExecutionResult,
    plugin_manager
)

# 内置插件（自动注册）
from .plugins import (
    AuthenticationPlugin,
    SessionInitializationPlugin,
    HeartbeatPlugin,
    MessageProcessingPlugin,
    MemoryExtractionPlugin,
    CleanupPlugin
)

__all__ = [
    # 接口和基类
    "ILifecyclePlugin",
    "BaseLifecyclePlugin", 
    "LifecyclePhase",
    "PluginContext",
    "PluginPriority",
    "PluginResult",
    
    # 装饰器和注册
    "lifecycle_plugin",
    "plugin_registry",
    "phase_handler",
    "requires_context", 
    "error_handler",
    "on_create",
    "on_active",
    "on_processing",
    "on_disconnected",
    "on_closed",
    
    # 管理器
    "PluginManager",
    "PluginExecutionResult",
    "plugin_manager",
    
    # 内置插件
    "AuthenticationPlugin",
    "SessionInitializationPlugin",
    "HeartbeatPlugin",
    "MessageProcessingPlugin",
    "MemoryExtractionPlugin",
    "CleanupPlugin"
] 