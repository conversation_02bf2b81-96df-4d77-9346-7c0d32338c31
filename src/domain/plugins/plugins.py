"""
会话生命周期插件实现
使用规范化的插件接口和装饰器系统
"""

from typing import Set
from datetime import datetime
from loguru import logger

from .interfaces import BaseLifecyclePlugin, LifecyclePhase, PluginContext, PluginPriority
from .decorators import lifecycle_plugin, requires_context, error_handler


@lifecycle_plugin(
    name="AuthenticationPlugin",
    phases={LifecyclePhase.ON_CREATE},
    priority=PluginPriority.CRITICAL,
    version="1.0.0"
)
class AuthenticationPlugin(BaseLifecyclePlugin):
    """认证插件 - 在Session创建时执行认证检查"""
    
    @property
    def supported_phases(self) -> Set[LifecyclePhase]:
        return {LifecyclePhase.ON_CREATE}
    
    @error_handler(fallback_return=False)
    @requires_context("ali_uid", "agent_id")
    async def execute(self, context: PluginContext) -> bool:
        """执行认证逻辑"""
        logger.info(f"[{self.name}] 执行认证检查: session_id={context.session_id}")
        
        # Mock认证逻辑
        if context.ali_uid and context.agent_id:
            context.extra_data.update({
                "authenticated": True,
                "auth_time": datetime.now().isoformat(),
                "auth_method": "ali_uid_verification"
            })
            logger.info(f"[{self.name}] 认证成功: ali_uid={context.ali_uid}")
            return True
        else:
            logger.warning(f"[{self.name}] 认证失败: 缺少必要参数")
            return False


@lifecycle_plugin(
    name="SessionInitializationPlugin",
    phases={LifecyclePhase.ON_CREATE},
    priority=PluginPriority.HIGH,
    version="1.0.0"
)
class SessionInitializationPlugin(BaseLifecyclePlugin):
    """Session初始化插件"""
    
    @property
    def supported_phases(self) -> Set[LifecyclePhase]:
        return {LifecyclePhase.ON_CREATE}
    
    @error_handler(fallback_return=False)
    async def execute(self, context: PluginContext) -> bool:
        """执行Session初始化"""
        logger.info(f"[{self.name}] 初始化Session: session_id={context.session_id}")
        
        # 设置默认配置
        context.extra_data.update({
            "max_rounds": 100,
            "session_timeout": 3600,  # 1小时
            "created_by": "system",
            "initialization_time": datetime.now().isoformat(),
            "features": {
                "sse_streaming": True,
                "heartbeat_monitoring": True,
                "memory_extraction": True
            }
        })
        
        logger.info(f"[{self.name}] Session初始化完成")
        return True


@lifecycle_plugin(
    name="HeartbeatPlugin",
    phases={LifecyclePhase.ON_ACTIVE},
    priority=PluginPriority.NORMAL,
    version="1.0.0"
)
class HeartbeatPlugin(BaseLifecyclePlugin):
    """心跳监控插件 - 在Session激活时启动心跳监控"""
    
    @property
    def supported_phases(self) -> Set[LifecyclePhase]:
        return {LifecyclePhase.ON_ACTIVE}
    
    @error_handler(fallback_return=True)  # 心跳失败不影响主流程
    async def execute(self, context: PluginContext) -> bool:
        """启动心跳监控"""
        logger.info(f"[{self.name}] 启动心跳监控: session_id={context.session_id}")
        
        # 配置心跳参数
        heartbeat_config = {
            "enabled": True,
            "interval": 30,  # 30秒
            "timeout": 10,   # 10秒超时
            "max_missed": 3,  # 最多错过3次
            "start_time": datetime.now().isoformat()
        }
        
        context.extra_data["heartbeat"] = heartbeat_config
        
        logger.info(f"[{self.name}] 心跳监控配置完成: {heartbeat_config}")
        return True


@lifecycle_plugin(
    name="MessageProcessingPlugin",
    phases={LifecyclePhase.ON_PROCESSING, LifecyclePhase.ON_MESSAGE_RECEIVED},
    priority=PluginPriority.HIGH,
    version="1.0.0"
)
class MessageProcessingPlugin(BaseLifecyclePlugin):
    """消息处理插件 - 处理消息相关的生命周期事件"""
    
    @property
    def supported_phases(self) -> Set[LifecyclePhase]:
        return {LifecyclePhase.ON_PROCESSING, LifecyclePhase.ON_MESSAGE_RECEIVED}
    
    def can_handle(self, phase: LifecyclePhase, context: PluginContext) -> bool:
        """检查是否能处理该阶段"""
        base_check = super().can_handle(phase, context)
        
        # 对于消息相关的阶段，需要有message_id
        if phase == LifecyclePhase.ON_MESSAGE_RECEIVED:
            return base_check and context.message_id is not None
        
        return base_check
    
    @error_handler(fallback_return=True)
    async def execute(self, context: PluginContext) -> bool:
        """执行消息处理逻辑"""
        logger.info(f"[{self.name}] 处理消息事件: phase={context.phase.value}, session_id={context.session_id}")
        
        if context.phase == LifecyclePhase.ON_PROCESSING:
            # 处理中状态的逻辑
            context.extra_data.setdefault("processing_events", []).append({
                "timestamp": datetime.now().isoformat(),
                "round_id": context.round_id,
                "event": "processing_started"
            })
            
        elif context.phase == LifecyclePhase.ON_MESSAGE_RECEIVED:
            # 消息接收的逻辑
            context.extra_data.setdefault("message_events", []).append({
                "timestamp": datetime.now().isoformat(),
                "message_id": context.message_id,
                "round_id": context.round_id,
                "event": "message_received"
            })
        
        return True


@lifecycle_plugin(
    name="MemoryExtractionPlugin",
    phases={LifecyclePhase.ON_DISCONNECTED, LifecyclePhase.ON_CLOSED},
    priority=PluginPriority.NORMAL,
    version="1.0.0"
)
class MemoryExtractionPlugin(BaseLifecyclePlugin):
    """记忆提取插件 - 在断线或关闭时触发长期记忆提取"""
    
    @property
    def supported_phases(self) -> Set[LifecyclePhase]:
        return {LifecyclePhase.ON_DISCONNECTED, LifecyclePhase.ON_CLOSED}
    
    @error_handler(fallback_return=True)  # 记忆提取失败不影响主流程
    async def execute(self, context: PluginContext) -> bool:
        """执行记忆提取"""
        logger.info(f"[{self.name}] 触发记忆提取: phase={context.phase.value}, session_id={context.session_id}")
        
        # 获取会话统计信息
        round_count = len(context.extra_data.get("processing_events", []))
        message_count = len(context.extra_data.get("message_events", []))
        
        # 判断是否需要提取长期记忆
        should_extract = (
            round_count > 5 or  # 超过5轮对话
            message_count > 10 or  # 超过10条消息
            context.phase == LifecyclePhase.ON_CLOSED  # 正常关闭时总是提取
        )
        
        if should_extract:
            memory_data = {
                "session_id": context.session_id,
                "extraction_time": datetime.now().isoformat(),
                "extraction_reason": context.phase.value,
                "statistics": {
                    "round_count": round_count,
                    "message_count": message_count,
                    "session_duration": self._calculate_session_duration(context)
                },
                "extracted": True
            }
            
            logger.info(f"[{self.name}] 执行长期记忆提取: {memory_data}")
        else:
            memory_data = {
                "session_id": context.session_id,
                "extraction_time": datetime.now().isoformat(),
                "extraction_reason": "insufficient_data",
                "statistics": {
                    "round_count": round_count,
                    "message_count": message_count
                },
                "extracted": False
            }
            
            logger.info(f"[{self.name}] 跳过记忆提取: {memory_data}")
        
        context.extra_data["memory_extraction"] = memory_data
        return True
    
    def _calculate_session_duration(self, context: PluginContext) -> float:
        """计算会话持续时间"""
        auth_time_str = context.extra_data.get("auth_time")
        if not auth_time_str:
            return 0.0
        
        try:
            auth_time = datetime.fromisoformat(auth_time_str.replace('Z', '+00:00'))
            duration = (datetime.now() - auth_time).total_seconds()
            return duration
        except Exception:
            return 0.0


@lifecycle_plugin(
    name="CleanupPlugin",
    phases={LifecyclePhase.ON_CLOSED},
    priority=PluginPriority.LOW,
    version="1.0.0"
)
class CleanupPlugin(BaseLifecyclePlugin):
    """清理插件 - 在Session关闭时执行资源清理"""
    
    @property
    def supported_phases(self) -> Set[LifecyclePhase]:
        return {LifecyclePhase.ON_CLOSED}
    
    @error_handler(fallback_return=True)
    async def execute(self, context: PluginContext) -> bool:
        """执行清理逻辑"""
        logger.info(f"[{self.name}] 执行资源清理: session_id={context.session_id}")
        
        cleanup_tasks = []
        
        # 清理心跳监控
        if "heartbeat" in context.extra_data:
            cleanup_tasks.append("heartbeat_monitoring")
            
        # 清理临时数据
        temp_keys = [k for k in context.extra_data.keys() if k.startswith("temp_")]
        if temp_keys:
            cleanup_tasks.extend(temp_keys)
            
        # 记录清理信息
        cleanup_info = {
            "cleanup_time": datetime.now().isoformat(),
            "tasks_completed": cleanup_tasks,
            "session_final_state": {
                "authenticated": context.extra_data.get("authenticated", False),
                "rounds_completed": len(context.extra_data.get("processing_events", [])),
                "memory_extracted": context.extra_data.get("memory_extraction", {}).get("extracted", False)
            }
        }
        
        context.extra_data["cleanup"] = cleanup_info
        
        logger.info(f"[{self.name}] 资源清理完成: {cleanup_info}")
        return True 