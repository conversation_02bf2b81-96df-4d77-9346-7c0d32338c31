"""
插件系统接口定义
定义插件的标准接口和生命周期钩子
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, Any, Optional, Set
from dataclasses import dataclass
from datetime import datetime


class LifecyclePhase(Enum):
    """生命周期阶段枚举"""
    ON_CREATE = "on_create"
    ON_ACTIVE = "on_active"
    ON_PROCESSING = "on_processing"
    ON_DISCONNECTED = "on_disconnected"
    ON_CLOSED = "on_closed"
    ON_ROUND_START = "on_round_start"
    ON_ROUND_COMPLETE = "on_round_complete"
    ON_MESSAGE_RECEIVED = "on_message_received"


@dataclass
class PluginContext:
    """插件执行上下文"""
    session_id: str
    ali_uid: str
    agent_id: str
    phase: LifecyclePhase
    timestamp: datetime
    extra_data: Dict[str, Any]
    
    # 可选的Round和Message信息
    round_id: Optional[int] = None
    message_id: Optional[str] = None
    
    def __post_init__(self):
        if not self.extra_data:
            self.extra_data = {}


class PluginPriority(Enum):
    """插件优先级"""
    CRITICAL = 1000    # 关键插件，必须执行
    HIGH = 800        # 高优先级
    NORMAL = 500      # 普通优先级
    LOW = 200         # 低优先级
    OPTIONAL = 100    # 可选插件


class ILifecyclePlugin(ABC):
    """生命周期插件接口"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """插件名称"""
        pass
    
    @property
    @abstractmethod
    def version(self) -> str:
        """插件版本"""
        pass
    
    @property
    @abstractmethod
    def priority(self) -> PluginPriority:
        """插件优先级"""
        pass
    
    @property
    @abstractmethod
    def supported_phases(self) -> Set[LifecyclePhase]:
        """支持的生命周期阶段"""
        pass
    
    @abstractmethod
    def can_handle(self, phase: LifecyclePhase, context: PluginContext) -> bool:
        """判断是否能处理指定的生命周期阶段和上下文"""
        pass
    
    @abstractmethod
    async def execute(self, context: PluginContext) -> bool:
        """执行插件逻辑
        
        Returns:
            bool: 执行是否成功
        """
        pass
    
    def on_error(self, context: PluginContext, error: Exception) -> None:
        """错误处理回调"""
        pass
    
    def validate_context(self, context: PluginContext) -> bool:
        """验证上下文是否有效"""
        return (
            context.session_id and 
            context.ali_uid and 
            context.agent_id and
            context.phase in self.supported_phases
        )


class BaseLifecyclePlugin(ILifecyclePlugin):
    """生命周期插件基类，提供通用实现"""
    
    def __init__(self, name: str, version: str = "1.0.0", priority: PluginPriority = PluginPriority.NORMAL):
        self._name = name
        self._version = version
        self._priority = priority
    
    @property
    def name(self) -> str:
        return self._name
    
    @property
    def version(self) -> str:
        return self._version
    
    @property
    def priority(self) -> PluginPriority:
        return self._priority
    
    def can_handle(self, phase: LifecyclePhase, context: PluginContext) -> bool:
        """默认实现：检查阶段是否支持且上下文有效"""
        return (
            phase in self.supported_phases and 
            self.validate_context(context)
        )
    
    def on_error(self, context: PluginContext, error: Exception) -> None:
        """默认错误处理"""
        from loguru import logger
        logger.error(f"[{self.name}] 插件执行失败: {error}, context={context.session_id}")


class PluginResult:
    """插件执行结果"""
    
    def __init__(self, success: bool, message: str = "", data: Dict[str, Any] = None):
        self.success = success
        self.message = message
        self.data = data or {}
        self.timestamp = datetime.now()
    
    def __bool__(self) -> bool:
        return self.success 