"""
插件管理器
负责插件的执行、生命周期管理和错误处理
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger

from .interfaces import ILifecyclePlugin, LifecyclePhase, PluginContext, PluginPriority
from .decorators import plugin_registry


class PluginExecutionResult:
    """插件执行结果"""
    
    def __init__(self):
        self.total_plugins = 0
        self.successful_plugins = 0
        self.failed_plugins = 0
        self.results: Dict[str, bool] = {}
        self.errors: Dict[str, Exception] = {}
        self.execution_time = 0.0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_plugins == 0:
            return 1.0
        return self.successful_plugins / self.total_plugins
    
    @property
    def has_critical_failures(self) -> bool:
        """是否有关键插件失败"""
        return len(self.errors) > 0  # 简化实现，实际可以检查优先级


class PluginManager:
    """插件管理器"""
    
    def __init__(self, registry=None):
        self.registry = registry or plugin_registry
        self._execution_history: List[Dict[str, Any]] = []
    
    async def execute_phase(
        self, 
        phase: LifecyclePhase, 
        context: PluginContext,
        timeout: float = 30.0,
        stop_on_critical_failure: bool = True
    ) -> PluginExecutionResult:
        """执行指定生命周期阶段的所有插件
        
        Args:
            phase: 生命周期阶段
            context: 插件执行上下文
            timeout: 超时时间（秒）
            stop_on_critical_failure: 是否在关键插件失败时停止执行
        
        Returns:
            PluginExecutionResult: 执行结果
        """
        start_time = datetime.now()
        result = PluginExecutionResult()
        
        # 获取该阶段的所有插件
        plugins = self.registry.get_plugins_for_phase(phase)
        result.total_plugins = len(plugins)
        
        if not plugins:
            logger.debug(f"[PluginManager] 阶段 {phase.value} 没有注册的插件")
            return result
        
        logger.info(f"[PluginManager] 开始执行阶段 {phase.value}，共 {len(plugins)} 个插件")
        
        # 按优先级分组执行
        priority_groups = self._group_plugins_by_priority(plugins)
        
        for priority in sorted(priority_groups.keys(), key=lambda p: p.value, reverse=True):
            group_plugins = priority_groups[priority]
            logger.debug(f"[PluginManager] 执行优先级 {priority.name} 的 {len(group_plugins)} 个插件")
            
            # 并行执行同优先级的插件
            tasks = []
            for plugin in group_plugins:
                if plugin.can_handle(phase, context):
                    task = self._execute_plugin_with_timeout(plugin, context, timeout)
                    tasks.append((plugin.name, task))
            
            if tasks:
                # 等待所有同优先级插件完成
                results = await asyncio.gather(
                    *[task for _, task in tasks], 
                    return_exceptions=True
                )
                
                # 处理结果
                for (plugin_name, _), plugin_result in zip(tasks, results):
                    if isinstance(plugin_result, Exception):
                        result.failed_plugins += 1
                        result.results[plugin_name] = False
                        result.errors[plugin_name] = plugin_result
                        logger.error(f"[PluginManager] 插件 {plugin_name} 执行异常: {plugin_result}")
                        
                        # 检查是否需要停止执行
                        if stop_on_critical_failure and self._is_critical_plugin(plugin_name):
                            logger.error(f"[PluginManager] 关键插件 {plugin_name} 失败，停止执行")
                            break
                    else:
                        if plugin_result:
                            result.successful_plugins += 1
                            result.results[plugin_name] = True
                            logger.debug(f"[PluginManager] 插件 {plugin_name} 执行成功")
                        else:
                            result.failed_plugins += 1
                            result.results[plugin_name] = False
                            logger.warning(f"[PluginManager] 插件 {plugin_name} 执行失败")
        
        # 记录执行时间
        end_time = datetime.now()
        result.execution_time = (end_time - start_time).total_seconds()
        
        # 记录执行历史
        self._record_execution(phase, context, result, start_time, end_time)
        
        logger.info(
            f"[PluginManager] 阶段 {phase.value} 执行完成: "
            f"成功 {result.successful_plugins}/{result.total_plugins} "
            f"耗时 {result.execution_time:.3f}s"
        )
        
        return result
    
    async def _execute_plugin_with_timeout(
        self, 
        plugin: ILifecyclePlugin, 
        context: PluginContext, 
        timeout: float
    ) -> bool:
        """带超时的插件执行"""
        try:
            return await asyncio.wait_for(plugin.execute(context), timeout=timeout)
        except asyncio.TimeoutError:
            logger.error(f"[PluginManager] 插件 {plugin.name} 执行超时 ({timeout}s)")
            plugin.on_error(context, TimeoutError(f"插件执行超时: {timeout}s"))
            return False
        except Exception as e:
            logger.error(f"[PluginManager] 插件 {plugin.name} 执行异常: {e}")
            plugin.on_error(context, e)
            return False
    
    def _group_plugins_by_priority(
        self, 
        plugins: List[ILifecyclePlugin]
    ) -> Dict[PluginPriority, List[ILifecyclePlugin]]:
        """按优先级分组插件"""
        groups: Dict[PluginPriority, List[ILifecyclePlugin]] = {}
        
        for plugin in plugins:
            priority = plugin.priority
            if priority not in groups:
                groups[priority] = []
            groups[priority].append(plugin)
        
        return groups
    
    def _is_critical_plugin(self, plugin_name: str) -> bool:
        """判断是否为关键插件"""
        plugin = self.registry.get_plugin(plugin_name)
        return plugin and plugin.priority == PluginPriority.CRITICAL
    
    def _record_execution(
        self, 
        phase: LifecyclePhase, 
        context: PluginContext,
        result: PluginExecutionResult,
        start_time: datetime,
        end_time: datetime
    ):
        """记录执行历史"""
        record = {
            "phase": phase.value,
            "session_id": context.session_id,
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "execution_time": result.execution_time,
            "total_plugins": result.total_plugins,
            "successful_plugins": result.successful_plugins,
            "failed_plugins": result.failed_plugins,
            "success_rate": result.success_rate,
            "results": result.results,
            "errors": {name: str(error) for name, error in result.errors.items()}
        }
        
        self._execution_history.append(record)
        
        # 保持历史记录数量限制
        if len(self._execution_history) > 1000:
            self._execution_history = self._execution_history[-500:]
    
    def get_execution_history(
        self, 
        session_id: Optional[str] = None, 
        phase: Optional[LifecyclePhase] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取执行历史"""
        history = self._execution_history
        
        # 过滤条件
        if session_id:
            history = [h for h in history if h["session_id"] == session_id]
        
        if phase:
            history = [h for h in history if h["phase"] == phase.value]
        
        # 返回最近的记录
        return history[-limit:]
    
    def get_plugin_statistics(self) -> Dict[str, Any]:
        """获取插件统计信息"""
        if not self._execution_history:
            return {"total_executions": 0}
        
        total_executions = len(self._execution_history)
        total_plugins_executed = sum(h["total_plugins"] for h in self._execution_history)
        total_successful = sum(h["successful_plugins"] for h in self._execution_history)
        total_failed = sum(h["failed_plugins"] for h in self._execution_history)
        
        # 插件级别统计
        plugin_stats = {}
        for history in self._execution_history:
            for plugin_name, success in history["results"].items():
                if plugin_name not in plugin_stats:
                    plugin_stats[plugin_name] = {"executions": 0, "successes": 0, "failures": 0}
                
                plugin_stats[plugin_name]["executions"] += 1
                if success:
                    plugin_stats[plugin_name]["successes"] += 1
                else:
                    plugin_stats[plugin_name]["failures"] += 1
        
        # 计算成功率
        for stats in plugin_stats.values():
            if stats["executions"] > 0:
                stats["success_rate"] = stats["successes"] / stats["executions"]
            else:
                stats["success_rate"] = 0.0
        
        return {
            "total_executions": total_executions,
            "total_plugins_executed": total_plugins_executed,
            "total_successful": total_successful,
            "total_failed": total_failed,
            "overall_success_rate": total_successful / total_plugins_executed if total_plugins_executed > 0 else 0.0,
            "plugin_statistics": plugin_stats
        }
    
    def list_registered_plugins(self) -> List[Dict[str, Any]]:
        """列出所有已注册的插件信息"""
        plugins = self.registry.list_plugins()
        
        return [
            {
                "name": plugin.name,
                "version": plugin.version,
                "priority": plugin.priority.name,
                "supported_phases": [phase.value for phase in plugin.supported_phases]
            }
            for plugin in plugins
        ]


# 全局插件管理器实例
plugin_manager = PluginManager() 