#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公共参数依赖注入 - 通用方案
支持GET和POST请求，支持查询参数和请求头两种方式
"""
from typing import Optional
from fastapi import Query, Header, Depends
from pydantic import BaseModel
from loguru import logger


class CommonParams(BaseModel):
    """公共参数模型"""
    login_token: Optional[str] = None
    session_id: Optional[str] = None
    region_id: Optional[str] = None

    def to_dict(self) -> dict:
        return {
            "login_token": self.login_token,
            "session_id": self.session_id,
            "region_id": self.region_id
        }


def get_universal_common_params(
    # 查询参数方式
    loginToken: Optional[str] = Query(None, description="登录令牌"),
    sessionId: Optional[str] = Query(None, description="会话ID"),
    regionId: Optional[str] = Query(None, description="区域ID"),
    # 请求头方式
    login_token_header: Optional[str] = Header(None, alias="X-Login-Token", description="登录令牌"),
    session_id_header: Optional[str] = Header(None, alias="X-Session-Id", description="会话ID"),
    region_id_header: Optional[str] = Header(None, alias="X-Region-Id", description="区域ID")
) -> CommonParams:
    """
    通用公共参数获取（POST和GET都支持）
    优先级：Query参数 > Header参数

    使用方式：
    - GET请求：通过查询参数传递 ?loginToken=xxx&sessionId=yyy&regionId=zzz
    - POST请求：通过请求头传递 X-Login-Token, X-Session-Id, X-Region-Id
    - 也可以混合使用，查询参数优先级更高
    """

    # 记录接收到的参数
    if any([loginToken, sessionId, regionId, login_token_header, session_id_header, region_id_header]):
        logger.debug(f"[CommonParams] 接收公共参数: query(token={bool(loginToken)}, session={bool(sessionId)}, region={bool(regionId)}), header(token={bool(login_token_header)}, session={bool(session_id_header)}, region={bool(region_id_header)})")

    return CommonParams(
        login_token=loginToken or login_token_header,
        session_id=sessionId or session_id_header,
        region_id=regionId or region_id_header
    )


# 推荐的依赖注入别名
UniversalCommonParams = Depends(get_universal_common_params)
