"""
Pythonic API路由
使用简化的充血模型和会话管理器
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
import uuid
from fastapi import APIRouter, HTTPException, Request, Query, UploadFile, File, Form, Depends
from fastapi.responses import StreamingResponse, PlainTextResponse
from loguru import logger
import httpx
import traceback

from ....presentation.api.dependencies.api_common_utils import get_request_id_dependency, package_api_result, OK_STATUS, FAIL_STATUS
from loguru import logger

router = APIRouter(prefix="/api", tags=["session"])

@router.get("/sessions/package_rlt_sample")
async def health_check(request: Request):
    """返回结果包装"""
    try:
        #这个request_id要改为从Pop传入的request_id么
        request_id = get_request_id_dependency(request)
        has_error = request.query_params.get("has_error", "false")
        if has_error == "true":
            raise Exception("测试异常返回结果")
        data = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "pythonic-v2.0"
        }
        package_rlt = package_api_result(
            code="",
            data=data,
            message="",
            request_id=request_id,
            status=OK_STATUS,
            success=True
        )
        logger.info(f"[API] package_rlt_sample 健康检查成功: {package_rlt}")
        return package_rlt
    except Exception as e:
        logger.error(f"[API] 健康检查异常: {e}")
        error_message = traceback.format_exc()
        package_rlt = package_api_result(
            code=f"{str(e)}",
            data={},
            message=error_message,
            request_id=request_id,
            status=FAIL_STATUS,
            success=False
        )
        return package_rlt