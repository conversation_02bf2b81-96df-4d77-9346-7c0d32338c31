"""
会话相关API路由
专门处理会话的发送消息、流式接口、历史查询、管理等功能
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
import uuid
import json
from fastapi import APIRouter, HTTPException, Request, Query, UploadFile, File, Form, Depends
from fastapi.responses import StreamingResponse, PlainTextResponse
from loguru import logger
import httpx
import asyncio

from ....application.api_models import (
    SendMessageRequest, QueryHistoryResponse,
    SessionRenameRequest, SessionDeleteRequest,
    ResourceType, SessionResource,
    UpdateUserSettingRequest, UserSettingResponse,
    MessageFeedbackRequest, MessageFeedbackResponse
)
from ....domain.services.auth_service import AuthContext, require_auth
from ....domain.services.session_service import session_service, SessionListParams
from ....domain.services.knowledge_service import knowledgebase_service
from ....domain.services.user_service import user_service
from ....infrastructure.database.models.auth_models import PermissionType
from ....presentation.api.dependencies.common_params import CommonParams, UniversalCommonParams
from ....presentation.api.dependencies.api_common_utils import package_api_result, handle_exception, get_request_id_dependency

router = APIRouter(prefix="/api", tags=["session"])


async def _send_message_async(
    session_id: str,
    prompt: str,
    agent_id: str,
    desktop_id: Optional[str],
    auth_code: Optional[str],
    resources: Optional[List],
    context,
    request_id: str
):
    """
    异步执行消息发送任务

    Args:
        session_id: 会话ID
        prompt: 用户消息
        agent_id: 智能体ID
        desktop_id: 桌面ID
        auth_code: 认证码
        resources: 资源列表
        context: 认证上下文
        request_id: 请求ID
    """
    try:
        logger.info(f"[API] 开始异步发送消息: session_id={session_id}, request_id={request_id}")

        # 异步执行消息发送
        session_id_result, round_id = await session_service.send_message(
            session_id=session_id,
            prompt=prompt,
            agent_id=agent_id,
            desktop_id=desktop_id,
            auth_code=auth_code,
            resources=resources,
            context=context
        )

        logger.info(f"[API] 异步消息发送成功: session_id={session_id_result}, round_id={round_id}, request_id={request_id}")

    except Exception as e:
        logger.error(f"[API] 异步消息发送失败: session_id={session_id}, request_id={request_id}, error={e}")
        # 异步任务中的错误不影响接口响应，只记录日志


@router.post("/sessions/send")
async def send_message(
    request: SendMessageRequest,
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency)
):
    """发送消息到Agent（接口1：发送消息接口）"""
    try:
        # 异步获取或创建会话ID
        session_id = request.session_id
        if not request.session_id:
            # 异步创建新会话
            session = await session_service.get_or_create_session_domain_async(
                session_id=None,
                ali_uid=context.ali_uid,
                agent_id=request.agent_id,
                wy_id=context.wy_id
            )
            session_id = session.session_id
            logger.info(f"[API] 异步创建新会话: session_id={session_id}")
        else:
            # 异步验证现有会话是否存在和有权限访问
            try:
                await session_service.get_session_with_permission_check_async(
                    context=context,
                    session_id=request.session_id,
                    required_permission=PermissionType.WRITE
                )
                logger.info(f"[API] 异步验证现有会话: session_id={session_id}")
            except Exception as e:
                logger.error(f"[API] 异步会话验证失败: session_id={request.session_id}, error={e}")
                raise ValueError(f"会话不存在或无权限访问: {request.session_id}")

        # 返回会话ID给前端建连
        logger.info(f"[API] 立即返回会话ID: session_id={session_id}")

        # 异步发消息 - 使用 asyncio.create_task 并添加错误处理
        task = asyncio.create_task(_send_message_async(
            session_id=session_id,
            prompt=request.prompt,
            agent_id=request.agent_id,
            desktop_id=request.desktop_id,
            auth_code=request.auth_code,
            resources=request.resources,
            context=context,
            request_id=request_id
        ))
        # 添加任务完成回调，避免任务泄漏
        task.add_done_callback(lambda t: logger.debug(f"异步消息发送任务完成: {session_id}"))

        return package_api_result(
            data={
                "session_id": session_id
            },
            request_id=request_id
        )

    except Exception as e:
        logger.error(f"[API] 发送消息接口异常: {e}")
        return handle_exception(e, request_id)


@router.get("/sessions/stream")
async def stream_messages(
    session_id: str = Query(..., description="会话ID"),
    last_event_id: Optional[str] = Query(None, description="最后接收到的消息ID，用于断线重连"),
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency)
):
    """建立SSE流连接"""
    try:
        logger.info(f"[API] 建立SSE流: session_id={session_id}")

        async def event_generator():
            try:
                # 局部去重缓存 - 仅在本次连接中有效
                # sent_event_ids = set()
                # session_finished = False
                #
                # # 只有在last_event_id不为空时才获取历史消息（断点续传场景）
                # if last_event_id and last_event_id == "asdf":
                #     logger.info(f"[API] 检测到断点续传，开始获取历史消息: session_id={session_id}, last_event_id={last_event_id}")
                #
                #     # 流式获取历史消息
                #     async for event_data, is_session_finished, event_id in session_service.stream_history_messages(
                #         session_id=session_id,
                #         last_event_id=last_event_id,
                #         page_size=50
                #     ):
                #         # 记录已发送的历史消息ID（局部缓存）
                #         if event_id:
                #             sent_event_ids.add(event_id)
                #
                #         # 发送历史消息
                #         yield f"data: {json.dumps(event_data)}\n\n"
                #
                #         # 检查是否为会话完成事件
                #         if is_session_finished:
                #             session_finished = True
                #             logger.info(f"[API] 检测到会话完成，准备关闭SSE连接: session_id={session_id}")
                #             break
                #
                #     logger.info(f"[API] 历史消息发送完成: session_id={session_id}, 本次连接缓存了{len(sent_event_ids)}个事件ID")
                #
                #     # 如果会话已完成，关闭连接
                #     if session_finished:
                #         logger.info(f"[API] 会话已完成，SSE连接关闭: session_id={session_id}")
                #         return
                # else:
                #     logger.info(f"[API] 新连接，跳过历史消息，直接进入实时消息流: session_id={session_id}")

                # 创建SSE流连接用于接收新消息
                async for event in session_service.create_sse_stream(
                    session_id=session_id,
                    last_message_id=last_event_id
                ):
                    # 对实时消息进行去重处理（使用局部缓存）
                    try:
                        if event.startswith("data: "):
                            event_json_str = event[6:].strip()
                            if event_json_str and event_json_str != "\n":
                                event_data = json.loads(event_json_str)
                                event_id = event_data.get("event_id")

                                # 检查是否重复（仅与本次连接的历史消息去重）
                                # if event_id and event_id in sent_event_ids:
                                #     logger.debug(f"[API] 跳过重复的实时消息: event_id={event_id}")
                                #     continue

                                # 检查是否为会话完成事件
                                event_type = event_data.get("type")
                                if event_type in ["RUN_FINISHED", "RUN_ERROR"]:
                                    logger.info(f"[API] 实时消息中检测到会话完成事件: {event_type}")
                                    yield event  # 先发送完成事件
                                    logger.info(f"[API] 会话完成，SSE连接关闭: session_id={session_id}")
                                    return  # 然后关闭连接
                    except (json.JSONDecodeError, KeyError, AttributeError) as e:
                        logger.debug(f"[API] 实时消息解析失败，直接发送: {e}")

                    yield event

            except Exception as e:
                logger.error(f"[API] SSE流异常: {e}")
                # 发送错误事件
                yield f"event: error\n"
                yield f'data: {{"error": "{str(e)}"}}\n\n'
            finally:
                # 局部缓存会在函数结束时自动清理，无需手动清理
                logger.info(f"[API] SSE连接结束: session_id={session_id}")
        
        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
        
    except ValueError as e:
        logger.error(f"[API] SSE流创建失败: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"[API] SSE流异常: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/sessions/query")
async def query_session_history(
    session_id: str = Query(..., description="会话ID"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    next_token: Optional[str] = Query(None, description="下一页的令牌，用于分页"),
    kb_id: Optional[str] = Query(None, description="知识库id"),
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency)
):
    """获取会话历史记录"""
    try:
        logger.info(f"[API] 获取会话历史: session_id={session_id}, page_size={page_size}, next_token={next_token}, kb_id={kb_id}")

        # 异步调用session_service获取历史数据
        if kb_id:
            result: Dict[str, Any] = await asyncio.to_thread(
                session_service.get_history_messages,
                session_id=session_id,
                page_size=page_size,
                next_token=next_token,
                kb_id=kb_id
            )
            return package_api_result(
                code=200,
                success=True,
                next_token=result.get("nextToken"),
                data={"events": result.get("messages")}
            )
        # 异步使用鉴权后的用户信息，login_token 已经通过鉴权处理
        result = await asyncio.to_thread(
            session_service.get_session_history,
            session_id=session_id,
            page_size=page_size,
            next_token=next_token
        )

        return package_api_result(
            code=200,
            success=True,
            next_token=result.get("nextToken"),
            data={"events": result.get('data', [])}
        )
    except Exception as e:
        logger.error(f"[API] 查询会话历史异常: {e}")
        return handle_exception(e, request_id)


@router.get("/sessions/list")
async def list_sessions(
    # 鉴权参数：通过依赖注入自动处理 loginToken、sessionId、regionId
    current_user: AuthContext = Depends(require_auth),
    page_size: int = Query(20, ge=1, le=500, description="每页数量"),
    next_token: Optional[str] = Query(None, description="下一页的令牌，用于分页"),
    search_keyword: Optional[str] = Query(None, description="搜索关键词"),
    agent_id: Optional[str] = Query(None, description="按Agent ID过滤"),
    kb_id: Optional[str] = Query(None, description="知识库id，查询是否在该知识库中"),
    common_params: CommonParams = UniversalCommonParams,
    request_id: str = Depends(get_request_id_dependency)
):
    """
    获取用户会话列表

    通过鉴权参数自动识别用户身份，支持分页、排序、搜索和过滤功能
    """
    try:
        logger.info(f"[API] 获取会话列表: user={current_user.user_key}, page_size={page_size}, next_token={next_token}, search={search_keyword}")

        # 构建查询参数
        params = SessionListParams(
            page_size=page_size,
            next_token=next_token,
            search_keyword=search_keyword,
            agent_id=agent_id
        )

        # 异步调用业务服务
        result = await asyncio.to_thread(
            session_service.get_user_sessions,
            current_user, params, kb_id=kb_id
        )
        logger.info(f"[API] 异步业务服务调用成功: user={current_user.ali_uid}, 返回 {len(result.sessions)} 个会话")

        # 异步设置会话的知识库关系
        await asyncio.to_thread(
            session_service.set_sessions_kb_relationship,
            result.sessions, kb_id
        )

        result_dict = result.to_dict()

        return package_api_result(
            code=200,
            success=True,
            total_count=result.total_count,
            next_token=result.next_token,
            data=result_dict,
            request_id=request_id
        )

    except Exception as e:
        logger.error(f"[API] 获取会话列表失败: user={current_user.user_key}, error={e}")
        return handle_exception(e, request_id)


@router.post("/sessions/rename")
async def rename_session(
    request: SessionRenameRequest,
    current_user: AuthContext = Depends(require_auth),
    common_params: CommonParams = UniversalCommonParams,
    request_id: str = Depends(get_request_id_dependency)
):
    """
    重命名会话

    通过鉴权参数自动识别用户身份，只能重命名自己的会话
    """
    try:
        logger.info(f"[API] 重命名会话: user={current_user.user_key}, session_id={request.session_id}, new_title={request.new_title}")

        # 异步调用业务服务
        success = await asyncio.to_thread(
            session_service.rename_session,
            context=current_user,
            session_id=request.session_id,
            new_title=request.new_title
        )

        if not success:
            raise HTTPException(status_code=404, detail="会话不存在或无权限修改")

        logger.info(f"[API] 会话重命名成功: user={current_user.user_key}, session_id={request.session_id}")

        return package_api_result(
            data={
                "session_id": request.session_id,
                "new_title": request.new_title
            },
            request_id=request_id
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 重命名会话异常: user={current_user.user_key}, error={e}")
        return handle_exception(e, request_id)


@router.post("/sessions/delete")
async def delete_session(
    request: SessionDeleteRequest,
    current_user: AuthContext = Depends(require_auth),
    common_params: CommonParams = UniversalCommonParams,
    request_id: str = Depends(get_request_id_dependency)
):
    """
    删除会话

    通过鉴权参数自动识别用户身份，只能删除自己的会话
    执行软删除，会话状态变为已删除但数据仍保留
    """
    try:
        logger.info(f"[API] 删除会话: user={current_user.user_key}, session_id={request.session_id}")

        # 异步调用业务服务
        success = await asyncio.to_thread(
            session_service.delete_session,
            context=current_user,
            session_id=request.session_id
        )

        if not success:
            raise HTTPException(status_code=404, detail="会话不存在或无权限删除")

        logger.info(f"[API] 会话删除成功: user={current_user.user_key}, session_id={request.session_id}")

        return package_api_result(
            code=200,
            data={
                "session_id": request.session_id
            },
            request_id=request_id
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 删除会话异常: user={current_user.user_key}, error={e}")
        return handle_exception(e, request_id)


@router.get("/sessions/stream/mock")
async def stream_mock(
    session_id: str = Query(..., description="会话ID"),
    last_event_id: Optional[str] = Query(None, description="最后接收到的消息ID，用于断线重连")
):
    """Mock SSE流连接，返回预定义的事件数据"""
    try:
        logger.info("[API] 建立Mock SSE流连接")
        
        # 预定义的事件数据 - 使用简化版本避免JSON转义问题
        mock_events = [
            '{"eventid":"event-1","type":"RUN_STARTED","timestamp":1753164703441,"session_id":"session-test","ext_data":null,"run_id":"trace_test"}',
            '{"eventid":"event-2","type":"TEXT_MESSAGE_START","timestamp":1753164709367,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_1","role":"user"}',
            '{"eventid":"event-3","type":"TEXT_MESSAGE_CONTENT","timestamp":1753164709367,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_1","content":"用户: 你好,帮我搜一下中东的新闻"}',
            '{"eventid":"event-4","type":"TEXT_MESSAGE_END","timestamp":1753164709367,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_1"}',
            '{"eventid":"event-5","type":"TEXT_MESSAGE_START","timestamp":1753164709439,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_2","role":"assistant"}',
            '{"eventid":"event-6","type":"TOOL_CALL_ARGS","timestamp":1753164709439,"session_id":"session-test","ext_data":null,"run_id":"run_test","tool_call_id":"call_search","delta":"{\\"input\\":\\"中东最近新闻\\"}"}',
            '{"eventid":"event-7","type":"TEXT_MESSAGE_END","timestamp":1753164709439,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_2"}',
            '{"eventid":"event-8","type":"TEXT_MESSAGE_START","timestamp":1753164724530,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_3","role":"assistant"}',
            '{"eventid":"event-9","type":"TOOL_CALL_ARGS","timestamp":1753164724530,"session_id":"session-test","ext_data":null,"run_id":"run_test","tool_call_id":"call_search2","delta":"{\\"category\\":\\"news_center\\",\\"query\\":\\"中东新闻\\",\\"time_range\\":\\"OneWeek\\"}"}',
            '{"eventid":"event-10","type":"TEXT_MESSAGE_END","timestamp":1753164724530,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_3"}',
            '{"eventid":"event-11","type":"TEXT_MESSAGE_START","timestamp":1753164744072,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_4","role":"assistant"}',
            '{"eventid":"event-12","type":"TEXT_MESSAGE_CONTENT","timestamp":1753164744072,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_4","content":"助手: 我找到了一些关于中东地区的最新新闻..."}',
            '{"eventid":"event-13","type":"TEXT_MESSAGE_END","timestamp":1753164744072,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_4"}',
            '{"eventid":"event-14","type":"TOOL_CALL_RESULT","timestamp":1753164776397,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_5","tool_call_id":"call_search","tool_name":"web_search","content":"搜索结果: 找到中东相关新闻5条","role":"tool"}',
            '{"eventid":"event-15","type":"RUN_FINISHED","timestamp":1753167921723,"session_id":"session-test","ext_data":null,"run_id":"trace_test"}'
        ]
        
        async def mock_event_generator():
            for i, event_data in enumerate(mock_events):
                # 发送事件数据
                yield f"data: {event_data}\n\n"
                logger.debug(f"[API] 发送SSE事件 {i+1}/{len(mock_events)}")
                
                # 每个事件间隔0.5秒，模拟实时流
                await asyncio.sleep(0.5)
                
            logger.info("[API] Mock SSE流发送完成")

        return StreamingResponse(
            mock_event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
        
    except Exception as e:
        logger.error(f"[API] Mock SSE流异常: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/environments/list")
async def list_user_environments(
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency)
):
    """
    获取执行环境接口（接口14：获取执行环境接口）
    
    根据登录令牌获取用户的执行环境列表，包括云电脑和默认的AgentBAY环境
    """
    try:
        logger.info(f"[API] 获取用户执行环境: user={current_user.user_key}, ali_uid={current_user.ali_uid}")

        # 调用业务服务
        result = user_service.get_user_environments(current_user)

        logger.info(f"[API] 执行环境获取成功: user={current_user.user_key}, 环境数量={result.total}")

        return package_api_result(
            data=result.to_dict(),
            request_id=request_id
        )

    except Exception as e:
        logger.error(f"[API] 获取执行环境异常: user={current_user.user_key}, error={e}")
        return handle_exception(e, request_id)


@router.post("/settings/update")
async def update_user_settings(
    request: UpdateUserSettingRequest,
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency)
):
    try:
        logger.info(f"[API] 更新用户设置: user={context.user_key}, ali_uid={context.ali_uid}, "
                   f"desktop_id={request.desktop_id}, model={request.model}")

        # 调用业务服务更新用户设置
        setting_data = user_service.update_user_setting(
            context=context,
            desktop_id=request.desktop_id,
            model=request.model
        )

        logger.info(f"[API] 用户设置更新成功: user={context.user_key}")

        return package_api_result(
            code=200,
            data=setting_data.dict(),
            request_id=request_id
        )

    except Exception as e:
        logger.error(f"[API] 更新用户设置异常: user={context.user_key}, error={e}")
        return handle_exception(e, request_id)


@router.get("/settings/get")
async def get_user_settings(
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency)
):
    """
    获取用户设置接口
    
    根据登录令牌识别用户身份，返回用户当前设置、可用环境列表、可用模型列表和版本信息
    """
    try:
        logger.info(f"[API] 获取用户设置: user={context.user_key}, ali_uid={context.ali_uid}")

        # 调用业务服务获取用户设置
        setting_data = user_service.get_user_setting(context=context)

        logger.info(f"[API] 用户设置获取成功: user={context.user_key}")

        return package_api_result(
            data=setting_data.dict(),
            request_id=request_id
        )

    except Exception as e:
        logger.error(f"[API] 获取用户设置异常: user={context.user_key}, error={e}")
        return handle_exception(e, request_id)


@router.post("/sessions/message/feedback")
async def set_message_feedback(
    request: MessageFeedbackRequest,
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency)
):
    """
    设置消息反馈接口

    为指定的消息设置用户反馈（点赞/点踩）
    """
    try:
        from ....infrastructure.memory import memory_sdk

        logger.info(f"[API] 设置消息反馈: user={context.user_key}, session_id={request.session_id}, "
                   f"message_id={request.message_id}, feedback={request.feedback}")

        # 调用memory_sdk设置消息反馈
        success = memory_sdk.message_feedback(
            session_id=request.session_id,
            message_id=request.message_id,
            feedback=request.feedback
        )

        if success:
            logger.info(f"[API] 消息反馈设置成功: user={context.user_key}, session_id={request.session_id}, "
                       f"message_id={request.message_id}, feedback={request.feedback}")
        else:
            logger.warning(f"[API] 消息反馈设置失败: user={context.user_key}, session_id={request.session_id}, "
                          f"message_id={request.message_id}")

        return package_api_result(
            data={
                "session_id": request.session_id,
                "message_id": request.message_id,
                "feedback": request.feedback,
                "success": success
            },
            request_id=request_id
        )

    except Exception as e:
        logger.error(f"[API] 设置消息反馈异常: user={context.user_key}, error={e}")
        return handle_exception(e, request_id)

def _serialize_event_for_sse(event) -> Dict[str, Any]:
    """序列化Event对象为SSE数据，确保枚举值正确转换"""
    try:
        # 获取基础数据
        data = event.model_dump()

        # 手动处理枚举字段
        if hasattr(event, 'type') and event.type:
            data['type'] = event.type.value if hasattr(event.type, 'value') else str(event.type)

        # 处理其他可能的枚举字段
        if hasattr(event, 'role') and event.role:
            data['role'] = event.role.value if hasattr(event.role, 'value') else str(event.role)

        return data
    except Exception as e:
        logger.warning(f"[API] Event序列化失败，使用fallback: {e}")
        # 如果序列化失败，使用基本的字典构建
        return {
            'event_id': getattr(event, 'event_id', ''),
            'type': event.type.value if hasattr(event, 'type') and hasattr(event.type, 'value') else str(
                getattr(event, 'type', '')),
            'timestamp': getattr(event, 'timestamp', None),
            'session_id': getattr(event, 'session_id', ''),
            'run_id': getattr(event, 'run_id', ''),
            'content': getattr(event, 'content', ''),
            'role': getattr(event, 'role', None)
        }

