from pydantic import BaseModel, Field, validator
from typing import Optional, List, Any, Dict
from datetime import datetime

class BaseRequest(BaseModel):
    """基础请求模型"""
    
    class Config:
        # 允许使用别名
        allow_population_by_field_name = True
        # JSON编码器
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

class BaseListRequest(BaseRequest):
    """基础列表请求模型"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=100, description="每页数量")
    sort_by: Optional[str] = Field(None, description="排序字段")
    sort_order: Optional[str] = Field("desc", description="排序方向: asc, desc")
    
    @validator("sort_order")
    def validate_sort_order(cls, v):
        if v and v.lower() not in ["asc", "desc"]:
            raise ValueError("sort_order必须是asc或desc")
        return v.lower() if v else v

class SearchRequest(BaseListRequest):
    """搜索请求模型"""
    keyword: Optional[str] = Field(None, max_length=100, description="搜索关键词")
    filters: Optional[Dict[str, Any]] = Field(None, description="筛选条件")
    date_from: Optional[datetime] = Field(None, description="开始日期")
    date_to: Optional[datetime] = Field(None, description="结束日期")

# 用户相关请求模型
class UserCreateRequest(BaseRequest):
    """创建用户请求"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: str = Field(..., max_length=100, description="邮箱")
    nickname: Optional[str] = Field(None, max_length=50, description="昵称")
    
    @validator("email")
    def validate_email(cls, v):
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, v):
            raise ValueError("邮箱格式不正确")
        return v

class UserUpdateRequest(BaseRequest):
    """更新用户请求"""
    nickname: Optional[str] = Field(None, max_length=50, description="昵称")
    status: Optional[int] = Field(None, description="状态")

class UserListRequest(BaseListRequest):
    """用户列表请求"""
    status: Optional[int] = Field(None, description="状态筛选")
    search_type: Optional[str] = Field("username", description="搜索类型: username, email")
    
    @validator("search_type")
    def validate_search_type(cls, v):
        if v and v not in ["username", "email"]:
            raise ValueError("search_type必须是username或email")
        return v

# 日志相关请求模型
class LogCreateRequest(BaseRequest):
    """创建日志请求"""
    user_id: Optional[int] = Field(None, description="用户ID")
    action: str = Field(..., max_length=100, description="操作类型")
    content: Optional[str] = Field(None, description="日志内容")
    ip_address: Optional[str] = Field(None, max_length=45, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")

class LogListRequest(BaseListRequest):
    """日志列表请求"""
    user_id: Optional[int] = Field(None, description="用户ID筛选")
    action: Optional[str] = Field(None, description="操作类型筛选")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")

# 批量操作请求模型
class BatchRequest(BaseRequest):
    """批量操作请求"""
    ids: List[int] = Field(..., min_items=1, max_items=100, description="ID列表")
    action: str = Field(..., description="操作类型")
    params: Optional[Dict[str, Any]] = Field(None, description="操作参数")

# 文件上传请求模型
class FileUploadRequest(BaseRequest):
    """文件上传请求"""
    filename: str = Field(..., description="文件名")
    content_type: Optional[str] = Field(None, description="文件类型")
    size: Optional[int] = Field(None, description="文件大小")
    description: Optional[str] = Field(None, description="文件描述") 