from pydantic import BaseModel, Field
from typing import Optional, List, Any, Dict, Generic, TypeVar
from datetime import datetime

T = TypeVar('T')

class BaseResponse(BaseModel, Generic[T]):
    """基础响应模型"""
    success: bool = Field(True, description="请求是否成功")
    message: str = Field("操作成功", description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")

class BaseListResponse(BaseResponse[List[T]]):
    """基础列表响应模型"""
    pagination: Optional[Dict[str, Any]] = Field(None, description="分页信息")

class UserResponse(BaseModel):
    """用户响应模型"""
    id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="邮箱")
    nickname: Optional[str] = Field(None, description="昵称")
    status: int = Field(..., description="状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

class LogResponse(BaseModel):
    """日志响应模型"""
    id: int = Field(..., description="日志ID")
    user_id: Optional[int] = Field(None, description="用户ID")
    action: str = Field(..., description="操作类型")
    content: Optional[str] = Field(None, description="日志内容")
    ip_address: Optional[str] = Field(None, description="IP地址")
    created_at: datetime = Field(..., description="创建时间") 