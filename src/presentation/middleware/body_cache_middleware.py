#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
请求体缓存中间件
用于支持多次读取请求体，特别是为了支持从JSON body中提取认证参数
"""
import json
from typing import Callable, Optional, Dict, Any
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger


class BodyCacheMiddleware(BaseHTTPMiddleware):
    """
    请求体缓存中间件
    
    功能：
    1. 缓存POST请求的JSON body
    2. 支持多次读取请求体
    3. 将解析后的JSON数据存储在request.state中
    """
    
    def __init__(self, app):
        super().__init__(app)

    async def _log_request_details(
            self,
            request: Request
    ):
        """
        打印详细的 request 信息，用于调试

        Args:
            request: FastAPI Request 对象
        """
        try:
            # 构建详细信息字典
            details = {
                "method": request.method,
                "url": str(request.url),
                "path": request.url.path,
                "client_ip": request.client.host if request.client else 'Unknown'
            }

            # Query Parameters
            if request.query_params:
                query_params = {}
                for key, value in request.query_params.items():
                    # 对敏感信息进行脱敏
                    if key.lower() in ['password', 'token', 'secret', 'key']:
                        query_params[key] = '***'
                    else:
                        query_params[key] = value
                details["query_params"] = query_params
            else:
                details["query_params"] = {}

            # Headers
            if request.headers:
                headers = {}
                for key, value in request.headers.items():
                    # 对敏感信息进行脱敏
                    if key.lower() in ['authorization', 'cookie', 'x-api-key', 'x-auth-token']:
                        headers[key] = '***'
                    else:
                        headers[key] = value
                details["headers"] = headers
            else:
                details["headers"] = {}

            # Body (如果是 POST/PUT 等方法)
            if request.method in ['POST', 'PUT', 'PATCH']:
                try:
                    body = await request.body()
                    if body:
                        # 尝试解析为 JSON
                        try:
                            import json
                            body_json = json.loads(body.decode('utf-8'))
                            # 对敏感字段进行脱敏
                            if isinstance(body_json, dict):
                                sanitized_body = {}
                                for key, value in body_json.items():
                                    if key.lower() in ['password', 'token', 'secret', 'key', 'auth_code']:
                                        sanitized_body[key] = '***'
                                    else:
                                        sanitized_body[key] = value
                                details["body"] = sanitized_body
                            else:
                                details["body"] = body_json
                        except (json.JSONDecodeError, UnicodeDecodeError):
                            # 如果不是 JSON，显示前100个字符
                            body_str = body.decode('utf-8', errors='ignore')[:100]
                            details["body"] = f"{body_str}{'...' if len(body) > 100 else ''}"
                    else:
                        details["body"] = None
                except Exception as body_error:
                    details["body"] = f"无法读取: {body_error}"

            # Path Parameters
            if hasattr(request, 'path_params') and request.path_params:
                details["path_params"] = dict(request.path_params)
            else:
                details["path_params"] = {}

            # 将所有信息打印到一条日志中
            import json
            logger.info(f"[AuthService] Request详情: {json.dumps(details, ensure_ascii=False, separators=(',', ':'))}")

        except Exception as e:
            logger.error(f"[AuthService] 打印 request 详情时出错: {e}")

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        # 只处理POST请求且Content-Type为application/json的请求
        if request.method == "POST":
            await self._log_request_details(request)
        if (request.method == "POST" and 
            request.headers.get("content-type", "").startswith("application/json")):
            
            try:
                # 读取并缓存请求体
                body = await request.body()
                
                if body:
                    try:
                        # 解析JSON
                        json_data = json.loads(body.decode('utf-8'))
                        
                        # 将解析后的数据存储在request.state中
                        request.state.json_body = json_data
                        request.state.raw_body = body
                        
                        logger.debug(f"[BodyCacheMiddleware] 缓存请求体: path={request.url.path}, size={len(body)} bytes")
                        
                    except json.JSONDecodeError as e:
                        logger.warning(f"[BodyCacheMiddleware] JSON解析失败: {e}")
                        request.state.json_body = None
                        request.state.raw_body = body
                    except Exception as e:
                        logger.warning(f"[BodyCacheMiddleware] 处理请求体失败: {e}")
                        request.state.json_body = None
                        request.state.raw_body = body
                else:
                    request.state.json_body = None
                    request.state.raw_body = b""
                    
            except Exception as e:
                logger.error(f"[BodyCacheMiddleware] 读取请求体失败: {e}")
                request.state.json_body = None
                request.state.raw_body = b""
        else:
            # 非JSON POST请求，不缓存
            request.state.json_body = None
            request.state.raw_body = None
        
        # 继续处理请求
        response = await call_next(request)
        return response


def get_cached_json_body(request: Request) -> Optional[Dict[str, Any]]:
    """
    从request.state中获取缓存的JSON body
    
    Args:
        request: FastAPI Request对象
        
    Returns:
        Dict[str, Any]: 解析后的JSON数据，如果没有则返回None
    """
    return getattr(request.state, 'json_body', None)


def get_cached_raw_body(request: Request) -> Optional[bytes]:
    """
    从request.state中获取缓存的原始请求体
    
    Args:
        request: FastAPI Request对象
        
    Returns:
        bytes: 原始请求体数据，如果没有则返回None
    """
    return getattr(request.state, 'raw_body', None)


def extract_auth_params_from_json(json_data: Optional[Dict[str, Any]]) -> tuple[Optional[str], Optional[str], Optional[str]]:
    """
    从JSON数据中提取认证参数
    
    Args:
        json_data: 解析后的JSON数据
        
    Returns:
        tuple: (login_token, login_session_id, region_id)
    """
    if not json_data:
        return None, None, None
    
    login_token = json_data.get("loginToken")
    login_session_id = json_data.get("loginSessionId")
    region_id = json_data.get("regionId")
    
    return login_token, login_session_id, region_id

