"""
性能监控中间件
用于监控请求处理时间和并发情况
"""

import time
import asyncio
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger
import threading

class PerformanceMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""
    
    def __init__(self, app, log_slow_requests: bool = True, slow_threshold: float = 1.0):
        super().__init__(app)
        self.log_slow_requests = log_slow_requests
        self.slow_threshold = slow_threshold
        self._active_requests = 0
        self._lock = threading.Lock()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 跳过健康检查接口的性能日志
        if request.url.path in ["/status.taobao", "/", "/health"]:
            return await call_next(request)
        
        start_time = time.time()
        
        # 增加活跃请求计数
        with self._lock:
            self._active_requests += 1
            current_active = self._active_requests
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录性能信息
            if self.log_slow_requests and process_time > self.slow_threshold:
                logger.warning(
                    f"慢请求 | {request.method} {request.url.path} | "
                    f"耗时: {process_time:.3f}s | 并发: {current_active} | "
                    f"状态: {response.status_code}"
                )
            elif process_time > 0.1:  # 记录超过100ms的请求
                logger.info(
                    f"请求 | {request.method} {request.url.path} | "
                    f"耗时: {process_time:.3f}s | 并发: {current_active}"
                )
            
            # 添加性能头信息
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["X-Active-Requests"] = str(current_active)
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(
                f"请求异常 | {request.method} {request.url.path} | "
                f"耗时: {process_time:.3f}s | 并发: {current_active} | "
                f"错误: {str(e)}"
            )
            raise
        finally:
            # 减少活跃请求计数
            with self._lock:
                self._active_requests -= 1
    
    def get_active_requests(self) -> int:
        """获取当前活跃请求数"""
        with self._lock:
            return self._active_requests
