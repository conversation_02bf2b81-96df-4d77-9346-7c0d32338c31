# -*- coding: utf-8 -*-
"""
中间件模块 - 重定向到统一的认证服务
"""

# 从统一的认证服务导入认证功能
from ...domain.services.auth_service import (
    get_current_user,
    require_auth,
    require_file_read_permission,
    require_file_write_permission,
    require_session_read_permission,
    require_session_write_permission,
    require_artifact_read_permission,
    require_knowledge_read_permission,
    AuthContext,
    auth_service
)

# 全局认证中间件（如果需要的话）
from .global_auth_middleware import (
    GlobalAuthMiddleware,
    ConditionalAuthMiddleware,
    create_auth_middleware,
    create_conditional_auth_middleware
)

__all__ = [
    # 统一认证服务
    "get_current_user",
    "require_auth",
    "require_file_read_permission",
    "require_file_write_permission",
    "require_session_read_permission",
    "require_session_write_permission",
    "require_artifact_read_permission",
    "require_knowledge_read_permission",
    "AuthContext",
    "auth_service",

    # 全局认证中间件（如果需要）
    "GlobalAuthMiddleware",
    "ConditionalAuthMiddleware",
    "create_auth_middleware",
    "create_conditional_auth_middleware"
]
