# -*- coding: utf-8 -*-
"""
全局认证中间件 - 提供全局AOP认证能力
"""
import re
from typing import List, Pattern, Callable
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger

from ...domain.services.auth_service import auth_service
from ...infrastructure.database.models.auth_models import PermissionType


class GlobalAuthMiddleware(BaseHTTPMiddleware):
    """全局认证中间件"""
    
    def __init__(
        self, 
        app,
        protected_paths: List[str] = None,
        excluded_paths: List[str] = None,
        require_auth_by_default: bool = False
    ):
        """
        初始化全局认证中间件
        
        Args:
            app: FastAPI应用实例
            protected_paths: 需要认证的路径模式列表
            excluded_paths: 排除认证的路径模式列表
            require_auth_by_default: 是否默认要求认证
        """
        super().__init__(app)
        self.auth_service = auth_service
        
        # 编译路径模式
        self.protected_patterns = [re.compile(pattern) for pattern in (protected_paths or [])]
        self.excluded_patterns = [re.compile(pattern) for pattern in (excluded_paths or [])]
        self.require_auth_by_default = require_auth_by_default
        
        # 默认的受保护路径
        if not protected_paths and not require_auth_by_default:
            self.protected_patterns = [
                re.compile(r'^/api/user/.*'),
                re.compile(r'^/api/admin/.*'),
                re.compile(r'^/api/protected/.*'),
                re.compile(r'^/api/.*/private/.*'),
            ]
        
        # 默认的排除路径
        if not excluded_paths:
            self.excluded_patterns = [
                re.compile(r'^/api/auth/.*'),
                re.compile(r'^/api/public/.*'),
                re.compile(r'^/docs.*'),
                re.compile(r'^/redoc.*'),
                re.compile(r'^/openapi\.json'),
                re.compile(r'^/health.*'),
                re.compile(r'^/metrics.*'),
                re.compile(r'^/static/.*'),
            ]
    
    def _should_require_auth(self, path: str) -> bool:
        """判断路径是否需要认证"""
        # 首先检查排除列表
        for pattern in self.excluded_patterns:
            if pattern.match(path):
                return False
        
        # 如果默认要求认证，则检查是否在排除列表中
        if self.require_auth_by_default:
            return True
        
        # 检查是否在受保护列表中
        for pattern in self.protected_patterns:
            if pattern.match(path):
                return True
        
        return False
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        path = request.url.path
        method = request.method
        
        logger.debug(f"[GlobalAuthMiddleware] 处理请求: {method} {path}")
        
        # 判断是否需要认证
        if self._should_require_auth(path):
            try:
                # 执行认证
                current_user = await self.auth_service.get_current_user(request)
                
                if not current_user:
                    logger.warning(f"[GlobalAuthMiddleware] 认证失败: {method} {path}")
                    return JSONResponse(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        content={
                            "detail": "未认证用户，请提供有效的登录令牌",
                            "code": 401,
                            "path": path
                        },
                        headers={"WWW-Authenticate": "Bearer"}
                    )
                
                # 将用户信息存储到request.state中
                request.state.current_user = current_user
                logger.debug(f"[GlobalAuthMiddleware] 认证成功: {current_user.ali_uid}")
                
            except Exception as e:
                logger.error(f"[GlobalAuthMiddleware] 认证异常: {e}")
                return JSONResponse(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    content={
                        "detail": "认证服务异常",
                        "code": 500,
                        "path": path
                    }
                )
        else:
            logger.debug(f"[GlobalAuthMiddleware] 跳过认证: {method} {path}")
        
        # 继续处理请求
        response = await call_next(request)
        return response


class ConditionalAuthMiddleware(BaseHTTPMiddleware):
    """条件认证中间件 - 基于路径和方法的细粒度控制"""
    
    def __init__(self, app):
        super().__init__(app)
        self.auth_service = auth_service
        
        # 定义认证规则
        self.auth_rules = [
            # 规则格式: (path_pattern, methods, require_auth, permissions)
            (r'^/api/admin/.*', ['GET', 'POST', 'PUT', 'DELETE'], True, [PermissionType.ADMIN.value]),
            (r'^/api/user/profile.*', ['GET', 'PUT'], True, []),
            (r'^/api/user/settings.*', ['GET', 'POST'], True, []),
            (r'^/api/files/.*/download', ['GET'], True, [f'file:{PermissionType.READ.value}']),
            (r'^/api/files/.*/upload', ['POST'], True, [f'file:{PermissionType.WRITE.value}']),
            (r'^/api/sessions/.*/messages', ['GET'], True, [f'session:{PermissionType.READ.value}']),
            (r'^/api/sessions/.*/send', ['POST'], True, [f'session:{PermissionType.WRITE.value}']),
            (r'^/api/public/.*', ['GET'], False, []),
            (r'^/api/auth/.*', ['POST'], False, []),
        ]
    
    def _find_matching_rule(self, path: str, method: str):
        """找到匹配的认证规则"""
        for pattern, methods, require_auth, permissions in self.auth_rules:
            if re.match(pattern, path) and method in methods:
                return require_auth, permissions
        
        # 默认规则：API路径需要认证，其他不需要
        if path.startswith('/api/'):
            return True, []
        return False, []
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        path = request.url.path
        method = request.method
        
        # 找到匹配的规则
        require_auth, required_permissions = self._find_matching_rule(path, method)
        
        if require_auth:
            try:
                # 执行认证
                current_user = await self.auth_service.get_current_user(request)
                
                if not current_user:
                    return JSONResponse(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        content={
                            "detail": "未认证用户，请提供有效的登录令牌",
                            "code": 401,
                            "path": path
                        }
                    )
                
                # 检查权限
                if required_permissions:
                    user_permissions = getattr(current_user, 'permissions', [])
                    missing_permissions = [p for p in required_permissions if p not in user_permissions]
                    
                    if missing_permissions:
                        return JSONResponse(
                            status_code=status.HTTP_403_FORBIDDEN,
                            content={
                                "detail": f"缺少权限: {', '.join(missing_permissions)}",
                                "code": 403,
                                "path": path,
                                "required_permissions": required_permissions
                            }
                        )
                
                # 存储用户信息
                request.state.current_user = current_user
                logger.debug(f"[ConditionalAuthMiddleware] 认证成功: {method} {path}")
                
            except Exception as e:
                logger.error(f"[ConditionalAuthMiddleware] 认证异常: {e}")
                return JSONResponse(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    content={
                        "detail": "认证服务异常",
                        "code": 500
                    }
                )
        
        # 继续处理请求
        response = await call_next(request)
        return response


# 便捷的中间件配置函数
def create_auth_middleware(
    protected_paths: List[str] = None,
    excluded_paths: List[str] = None,
    require_auth_by_default: bool = False
) -> GlobalAuthMiddleware:
    """创建全局认证中间件"""
    def middleware_factory(app):
        return GlobalAuthMiddleware(
            app,
            protected_paths=protected_paths,
            excluded_paths=excluded_paths,
            require_auth_by_default=require_auth_by_default
        )
    return middleware_factory


def create_conditional_auth_middleware() -> ConditionalAuthMiddleware:
    """创建条件认证中间件"""
    def middleware_factory(app):
        return ConditionalAuthMiddleware(app)
    return middleware_factory
