# API V2 (基于当前实现)

## 数据结构

与磊子哥沟通明确，Memory模块存储和下发是block而不是stream形式的，每个大模型完整回复的block为一条message。一个Agent中可能包含多个大模型顺序回复，用Round这层抽象承接，所以抽象关系上是：

### Session -> Round -> Message

```plaintext
Session (会话)
├── Round 1 (第一轮对话)
│   ├── Message 1 (用户请求)
│   ├── Message 2 (LLM输出1)
│   ├── Message 3 (MCP调用Input/Output)
│   └── Message 4 (LLM输出2)
├── Round 2 (第二轮对话)
│   ├── Message 5 (用户请求)
│   └── Message 6 (LLM输出)
└── ...

```

### 数据结构定义

|  抽象  |  字段名  |  类型  |  描述  |
| --- | --- | --- | --- |
|  **Session**  |  session_id  |  string  |  会话唯一标识符  |
|  |  ali_uid  |  string  |  用户ID  |
|  |  agent_id  |  string  |  agentId  |
|  |  title  |  string  |  会话标题 （在会话中也会随流式下发，有特定事件）  |
|  |  status  |  enum  |  会话状态：create(刚创建,可接收消息)/active(正在处理,不可接收消息)/closed(处理完成,可接收消息)  |
|  |  gmt_create  |  timestamp  |  创建时间  |
|  |  gmt_modified  |  timestamp  |  最后更新时间  |
|  |  total_rounds  |  number  |  总对话轮数  |
|  |  metadata  |  object  |  会话元数据  |
|  **Round**  |  round_id  |  string  |  轮次标识（可以是数字或字符串）  |
|  |  session_id  |  string  |  所属会话ID  |
|  |  user_prompt  |  string  |  用户请求内容  |
|  |  status  |  enum  |  轮次状态：processing/completed/failed  |
|  |  start_time  |  timestamp  |  开始时间  |
|  |  end_time  |  timestamp  |  结束时间（可选）  |
|  |  duration  |  number  |  处理时长（秒）  |
|  |  metadata  |  object  |  轮次元数据  |
|  **Message**  |  message_id  |  string  |  消息唯一标识符 (UUID)  |
|  |  round_id  |  string  |  所属轮次ID  |
|  |  session_id  |  string  |  所属会话ID  |
|  |  role  |  enum  |  消息角色：user/assistant/system/tool  |
|  |  content  |  string  |  消息内容  |
|  |  timestamp  |  timestamp  |  消息时间戳  |
|  |  name  |  string  |  工具名称（仅tool角色）  |
|  |  tool_call_id  |  string  |  工具调用ID（可选）  |
|  |  app_id  |  string  |  应用ID（可选）  |
|  |  duration  |  number  |  处理时长（可选）  |

## 核心接口设计

### 接口列表

1. [发送消息接口](#1-发送消息接口) - `POST /api/sessions/send`
2. [会话接口（流式）](#2-会话接口流式) - `GET /api/sessions/stream`
3. [获取历史消息接口](#3-获取历史消息接口) - `GET /api/sessions/query`
4. [获取会话列表接口](#4-获取会话列表接口) - `GET /api/sessions/list`
5. [获取Agent列表接口](#5-获取agent列表接口) - `GET /api/agents/list`
6. [获取执行环境接口](#6-获取执行环境接口) - `GET /api/environments/list`
7. [重命名会话接口](#7-重命名会话接口) - `PUT /api/sessions/rename`
8. [删除会话接口](#8-删除会话接口) - `DELETE /api/sessions/delete`
9. [查询AI制品接口](#9-查询ai制品接口) - `GET /api/sessions/artifacts`
10. [健康检查接口](#10-健康检查接口) - `GET /api/sessions/health`
11. [Taobao健康检查接口](#11-taobao健康检查接口) - `GET /status.taobao`

### 1. 发送消息接口

|  项目  |  内容  |
| --- | --- |
|  **接口名称**  |  发送消息  |
|  **HTTP方法**  |  POST  |
|  **URL**  |  /api/sessions/send  |
|  **描述**  |  发送用户消息，创建新的对话轮次，返回session_id和round_id  |

#### 请求参数

|  参数名  |  类型  |  必填  |  描述  |
| --- | --- | --- | --- |
|  prompt  |  string  |  是  |  用户输入的消息内容  |
|  agent_id  |  string  |  是  |  Agent ID  |
|  ali_uid  |  string  |  是  |  用户ID，标识消息发送者  |
|  session_id  |  string  |  否  |  会话ID，为空则创建新会话  |
|  extra  |  object  |  否  |  额外信息  |

#### 请求示例

```json
{
  "prompt": "请详细分析这段代码的性能问题",
  "agent_id": "agent_gpt4",
  "ali_uid": "user_12345",
  "session_id": null,
  "extra": {}
}
```

#### 响应参数

|  参数名  |  类型  |  描述  |
| --- | --- | --- |
|  session_id  |  string  |  会话ID（新建或existing）  |
|  round_id  |  string  |  新创建的轮次ID  |

#### 响应示例

```json
{
  "session_id": "sess_123456",
  "round_id": "1"    
}
```

### 2. 会话接口（流式）

|  项目  |  内容  |
| --- | --- |
|  接口名称  |  建立消息流连接  |
|  HTTP方法  |  GET  |
|  URL  |  /api/sessions/stream  |
|  描述  |  建立SSE长连接，实时流推送session下的所有新消息  |

#### 请求参数

|  参数名  |  类型  |  必填  |  描述  |
| --- | --- | --- | --- |
|  session_id  |  string  |  是  |  会话ID  |
|  last_message_id  |  string  |  否  |  最后接收的消息ID，将以它为锚点继续流式输出，若为空则从session开头流式输出  |

#### SSE事件格式

|  事件类型  |  描述  |  数据格式  |
| --- | --- | --- |
|  message  |  新消息事件  |  Message对象  |
|  done  |  轮次完成事件  |  完成信息对象  |
|  error  |  错误事件  |  错误信息对象  |
|  heartbeat  |  心跳事件  |  时间戳对象  |
|  close  |  连接关闭事件  |  关闭信息对象  |

#### SSE响应示例

```plaintext
# 用户消息
data: {"messageId": "msg_001", "roundId": "5", "sessionId": "sess_123456", "role": "user", "content": "请详细分析这段代码的性能问题", "timestamp": "2024-01-15T10:31:25Z"}

# LLM助手回复
data: {"messageId": "msg_002", "roundId": "5", "sessionId": "sess_123456", "role": "assistant", "content": "好的，我来为您详细分析这段代码的性能问题。首先让我使用代码分析工具来检查...", "timestamp": "2024-01-15T10:31:26Z", "duration": 2.5}

# 工具调用消息
data: {"messageId": "msg_003", "roundId": "5", "sessionId": "sess_123456", "role": "tool", "content": "代码分析完成，发现3个性能瓶颈", "name": "code_analyzer", "toolCallId": "call_123", "timestamp": "2024-01-15T10:31:27Z"}

# LLM继续输出
data: {"messageId": "msg_004", "roundId": "5", "sessionId": "sess_123456", "role": "assistant", "content": "根据代码分析工具的结果，我发现了以下3个主要性能瓶颈：\n\n1. 循环效率问题：您的代码在循环中执行了大量重复计算\n2. 内存分配：频繁的对象创建导致GC压力\n3. 算法复杂度：当前算法时间复杂度为O(n²)，可以优化到O(n log n)", "timestamp": "2024-01-15T10:31:32Z", "duration": 14.6}

# 生成会话标题（工具消息）
data: {"messageId": "msg_005", "roundId": "5", "sessionId": "sess_123456", "role": "tool", "content": "JavaScript代码性能优化分析与建议", "name": "title", "timestamp": "2024-01-15T10:31:35Z"}

# 心跳事件
event: heartbeat
data: {"timestamp": "2024-01-15T10:31:40Z"}

# 处理完成事件
event: done
data: {"roundId": "5", "sessionId": "sess_123456", "status": "completed", "endTime": "2024-01-15T10:31:38Z"}

```

### 3. 获取历史消息接口

|  项目  |  内容  |
| --- | --- |
|  **接口名称**  |  获取历史消息  |
|  **HTTP方法**  |  GET  |
|  **URL**  |  /api/sessions/query  |
|  **描述**  |  获取会话的历史消息，支持分页和过滤  |

#### 请求参数

|  参数名  |  类型  |  必填  |  描述  |
| --- | --- | --- | --- |
|  session_id  |  string  |  是  |  会话ID  |
|  limit  |  number  |  否  |  默认20  |
|  before_round_id  |  string  |  否  |  获取指定round_id之前的消息，不传就返回最新的limit个对话round (用于向上分页)  |

#### 响应参数：

|  字段路径  |  类型  |  描述  |
| --- | --- | --- |
|  session_id  |  string  |  会话唯一标识符  |
|  ali_uid  |  string  |  用户ID  |
|  agent_id  |  string  |  智能体ID  |
|  title  |  string  |  会话标题  |
|  status  |  string  |  会话状态  |
|  gmt_create  |  timestamp  |  会话创建时间  |
|  gmt_modified  |  timestamp  |  会话最后更新时间  |
|  total_rounds  |  number  |  会话总轮次数  |
|  rounds  |  array  |  轮次数组（数据结构同"数据结构定义"中定义）  |
|  rounds[].roundId  |  string  |  轮次ID  |
|  rounds[].userPrompt  |  string  |  用户请求内容  |
|  rounds[].status  |  string  |  轮次状态  |
|  rounds[].startTime  |  timestamp  |  开始时间  |
|  rounds[].endTime  |  timestamp  |  结束时间（可选）  |
|  rounds[].duration  |  string  |  处理时长（可选）  |
|  rounds[].messages  |  array  |  消息数组  |
|  rounds[].messages[].messageId  |  string  |  消息ID  |
|  rounds[].messages[].roundId  |  string  |  轮次ID  |
|  rounds[].messages[].sessionId  |  string  |  会话ID  |
|  rounds[].messages[].role  |  string  |  消息角色  |
|  rounds[].messages[].content  |  string  |  消息内容  |
|  rounds[].messages[].timestamp  |  timestamp  |  消息时间戳  |
|  rounds[].messages[].name  |  string  |  工具名称（可选）  |
|  rounds[].messages[].toolCallId  |  string  |  工具调用ID（可选）  |
|  rounds[].messages[].appId  |  string  |  应用ID（可选）  |
|  rounds[].messages[].duration  |  number  |  处理时长（可选）  |

返回示例：

```json
{
  "session_id": "sess_123456",
  "ali_uid": "user_001",
  "agent_id": "agent_gpt4",
  "title": "JavaScript代码性能优化分析与建议",
  "status": "active",
  "gmt_create": "2024-01-15T09:00:00Z",
  "gmt_modified": "2024-01-15T10:31:45Z",
  "total_rounds": 2,
  "rounds": [
    {
      "roundId": "2",
      "userPrompt": "请详细分析这段代码的性能问题",
      "status": "processing",
      "startTime": "2024-01-15T10:31:25Z",
      "endTime": null,
      "duration": null,
      "messages": [
        {
          "messageId": "msg_456789",
          "roundId": "2",
          "content": "请详细分析这段代码的性能问题",
          "role": "user",
          "timestamp": "2024-01-15T10:31:25Z",
          "sessionId": "sess_123456",
          "name": null,
          "toolCallId": null,
          "appId": null,
          "duration": null
        },
        {
          "messageId": "msg_456790",
          "roundId": "2",
          "content": "好的，我来为您详细分析这段代码的性能问题。首先让我使用代码分析工具来检查...",
          "role": "assistant",
          "timestamp": "2024-01-15T10:31:26Z",
          "sessionId": "sess_123456",
          "name": null,
          "toolCallId": null,
          "appId": null,
          "duration": 5.2
        }
      ]
    },
    {
      "roundId": "1",
      "status": "completed",
      "userPrompt": "如何优化数据库查询性能？",
      "startTime": "2024-01-15T10:25:00Z",
      "endTime": "2024-01-15T10:25:45Z",
      "duration": "45.0",
      "messages": [
        {
          "roundId": "1",
          "content": "如何优化数据库查询性能？",
          "role": "user",
          "timestamp": "2024-01-15T10:25:00Z",
          "sessionId": "sess_123456",
          "name": null,
          "toolCallId": null,
          "appId": null,
          "duration": null
        }
      ]
    }
  ]
}
```

### 4. 获取会话列表接口

|  项目  |  内容  |
| --- | --- |
|  **接口名称**  |  获取会话列表  |
|  **HTTP方法**  |  GET  |
|  **URL**  |  /api/sessions/list  |
|  **描述**  |  分页获取会话列表，支持按用户和Agent过滤，按最新活动时间倒序返回  |

#### 请求参数

|  参数名  |  类型  |  必填  |  描述  |
| --- | --- | --- | --- |
|  limit  |  number  |  否  |  返回的会话数量限制，默认50  |
|  offset  |  number  |  否  |  分页偏移量，默认0  |
|  ali_uid  |  string  |  否  |  按用户ID过滤  |
|  agent_id  |  string  |  否  |  按Agent ID过滤  |

#### 响应参数

|  字段路径  |  类型  |  描述  |
| --- | --- | --- |
|  sessions  |  array  |  会话列表  |
|  total  |  number  |  总会话数  |
|  limit  |  number  |  当前限制数量  |
|  offset  |  number  |  当前偏移量  |
|  sessions[].sessionId  |  string  |  会话ID  |
|  sessions[].aliUid  |  string  |  用户ID  |
|  sessions[].agentId  |  string  |  智能体ID  |
|  sessions[].title  |  string  |  会话标题  |
|  sessions[].status  |  string  |  会话状态  |
|  sessions[].gmtCreate  |  timestamp  |  创建时间  |
|  sessions[].gmtModified  |  timestamp  |  最后更新时间  |
|  sessions[].metadata  |  object  |  会话元数据  |

#### 响应示例

```json
{
  "sessions": [
    {
      "sessionId": "sess_123456",
      "aliUid": "user_001",
      "agentId": "agent_gpt4",
      "title": "JavaScript代码性能优化分析与建议",
      "status": "active",
      "gmtCreate": "2024-01-15T09:00:00Z",
      "gmtModified": "2024-01-15T10:31:45Z",
      "metadata": {
        "created_by": "system",
        "max_rounds": 100,
        "timeout": 3600
      }
    },
    {
      "sessionId": "sess_789012",
      "aliUid": "user_001",
      "agentId": "agent_gpt4",
      "title": "数据库优化方案讨论",
      "status": "closed",
      "gmtCreate": "2024-01-14T16:20:00Z",
      "gmtModified": "2024-01-14T17:45:30Z",
      "metadata": {
        "created_by": "system",
        "max_rounds": 100,
        "timeout": 3600
      }
    }
  ],
  "total": 15,
  "limit": 50,
  "offset": 0
}
```

### 5. 获取Agent列表接口

|  项目  |  内容  |
| --- | --- |
|  **接口名称**  |  获取Agent列表  |
|  **HTTP方法**  |  GET  |
|  **URL**  |  /api/agents/list  |
|  **描述**  |  获取所有可用的Agent列表，用于前端展示可选的智能体  |

#### 请求参数

无需参数

#### 响应参数

|  字段路径  |  类型  |  描述  |
| --- | --- | --- |
|  agents  |  array  |  Agent列表  |
|  total  |  number  |  Agent总数  |
|  agents[].agentId  |  string  |  Agent唯一标识符  |
|  agents[].name  |  string  |  Agent名称  |
|  agents[].version  |  string  |  Agent版本  |
|  agents[].description  |  string  |  Agent描述  |
|  agents[].tags  |  array  |  Agent标签列表  |
|  agents[].mcpServers  |  array  |  MCP服务器列表  |

#### 响应示例

```json
{
  "agents": [
    {
      "agentId": "deeper",
      "name": "超级深度研究",
      "version": "1.0.0",
      "description": "基于搜索的深度研究助手",
      "tags": ["deep-research", "深度研究"],
      "mcpServers": ["file_system", "terminal", "browser"]
    },
    {
      "agentId": "customer_service",
      "name": "客户服务系统",
      "version": "1.0.0",
      "description": "多智能体客户服务系统",
      "tags": ["客服", "多智能体"],
      "mcpServers": ["file_system", "browser"]
    },
    {
      "agentId": "fin-research",
      "name": "Financial Researcher",
      "version": "1.0.0",
      "description": "金融研究工具",
      "tags": ["金融", "研究"],
      "mcpServers": ["file_system", "python", "browser"]
    },
    {
      "agentId": "deep-research",
      "name": "Deep Researcher",
      "version": "1.0.0",
      "description": "深度研究工具",
      "tags": ["互联网", "研究"],
      "mcpServers": ["file_system", "python", "browser"]
    },
    {
      "agentId": "manus",
      "name": "PC自动化助手",
      "version": "1.0.0",
      "description": "基于AI的PC自动化助手",
      "tags": ["自动化", "桌面"],
      "mcpServers": ["file_system", "terminal", "browser"]
    }
  ],
  "total": 5
}
```

### 6. 获取执行环境接口

|  项目  |  内容  |
| --- | --- |
|  **接口名称**  |  获取执行环境  |
|  **HTTP方法**  |  GET  |
|  **URL**  |  /api/environments/list  |
|  **描述**  |  根据登录令牌获取用户的执行环境列表，包括云电脑和默认的AgentBAY环境  |

#### 请求参数

|  参数名  |  类型  |  必填  |  描述  |
| --- | --- | --- | --- |
|  login_token  |  string  |  是  |  用户登录令牌  |

#### 响应参数

|  字段路径  |  类型  |  描述  |
| --- | --- | --- |
|  environments  |  array  |  执行环境列表  |
|  total  |  number  |  环境总数  |
|  environments[].name  |  string  |  环境名称  |
|  environments[].desktop_id  |  string  |  桌面ID  |

#### 响应示例

```json
{
  "environments": [
    {
      "name": "我的A290云电脑 (自动推荐)",
      "desktop_id": "desktop_a290_auto"
    },
    {
      "name": "我的A290云电脑 (优先)",
      "desktop_id": "desktop_a290_priority"
    },
    {
      "name": "我的A290云电脑",
      "desktop_id": "desktop_a290_normal"
    },
    {
      "name": "agentbay",
      "desktop_id": "agentbay"
    }
  ],
  "total": 4
}
```

#### 说明

- 接口会根据`login_token`获取用户可用的云电脑环境
- 在返回列表的最后会自动添加一个名称为"agentbay"，ID也为"agentbay"的默认环境
- 环境列表按优先级排序，自动推荐的环境排在前面

### 7. 重命名会话接口

|  项目  |  内容  |
| --- | --- |
|  **接口名称**  |  重命名会话  |
|  **HTTP方法**  |  PUT  |
|  **URL**  |  /api/sessions/rename  |
|  **描述**  |  重命名会话，仅允许重命名已关闭的会话  |

#### 请求参数

|  参数名  |  类型  |  必填  |  描述  |
| --- | --- | --- | --- |
|  session_id  |  string  |  是  |  会话ID  |
|  new_title  |  string  |  是  |  新的会话标题  |

#### 响应参数

|  字段路径  |  类型  |  描述  |
| --- | --- | --- |
|  session_id  |  string  |  会话ID  |
|  new_title  |  string  |  新的会话标题  |

#### 响应示例

```json
{
  "session_id": "sess_123456",
  "new_title": "优化后的代码性能分析"
}
```

#### 说明

- 只能重命名状态为`closed`的会话
- 如果会话状态为`active`或`create`，将返回错误
- 新标题不能为空，长度建议不超过100个字符
- 重命名成功后，会话的`gmt_modified`字段会自动更新

### 8. 删除会话接口

|  项目  |  内容  |
| --- | --- |
|  **接口名称**  |  删除会话  |
|  **HTTP方法**  |  DELETE  |
|  **URL**  |  /api/sessions/delete  |
|  **描述**  |  删除指定会话及其所有相关数据  |

#### 请求参数

|  参数名  |  类型  |  必填  |  描述  |
| --- | --- | --- | --- |
|  session_id  |  string  |  是  |  会话ID  |

#### 响应参数

|  字段路径  |  类型  |  描述  |
| --- | --- | --- |
|  session_id  |  string  |  已删除的会话ID  |

#### 响应示例

```json
{
  "session_id": "sess_123456"
}
```

#### 说明

- 删除会话会同时删除该会话下的所有轮次和消息
- 删除后无法通过接口1、2、3、4、5、6、7、9获取该会话的任何信息
- 删除操作是不可逆的
- 建议在删除前确认会话状态，避免误删正在进行的会话

### 9. 查询AI制品接口

|  项目  |  内容  |
| --- | --- |
|  **接口名称**  |  查询AI制品  |
|  **HTTP方法**  |  GET  |
|  **URL**  |  /api/sessions/artifacts  |
|  **描述**  |  查询会话下所有AI制品，包括结果制品文件和过程制品文件  |

#### 请求参数

|  参数名  |  类型  |  必填  |  描述  |
| --- | --- | --- | --- |
|  session_id  |  string  |  是  |  会话ID  |

#### 响应参数

|  字段路径  |  类型  |  描述  |
| --- | --- | --- |
|  session_id  |  string  |  会话ID  |
|  result_files  |  array  |  结果制品文件列表  |
|  process_files  |  array  |  过程制品文件列表  |
|  total_result_files  |  number  |  结果制品文件总数  |
|  total_process_files  |  number  |  过程制品文件总数  |
|  result_files[].file_name  |  string  |  文件名  |
|  result_files[].file_type  |  string  |  文件类型  |
|  result_files[].file_size  |  number  |  文件大小（字节）  |
|  result_files[].file_size_formatted  |  string  |  格式化文件大小  |
|  result_files[].created_time  |  timestamp  |  创建时间  |
|  result_files[].url  |  string  |  下载URL  |
|  process_files[].file_name  |  string  |  文件名  |
|  process_files[].file_type  |  string  |  文件类型  |
|  process_files[].file_size  |  number  |  文件大小（字节）  |
|  process_files[].file_size_formatted  |  string  |  格式化文件大小  |
|  process_files[].created_time  |  timestamp  |  创建时间  |
|  process_files[].url  |  string  |  下载URL  |

#### 响应示例

```json
{
  "session_id": "sess_123456",
  "result_files": [
    {
      "file_name": "生态环境保护是一个长期任务.ppt",
      "file_type": "ppt",
      "file_size": 2621440,
      "file_size_formatted": "2.5MB",
      "created_time": "2025-03-28T12:01:01Z",
      "url": "https://example.com/download/sess_123456/result/生态环境保护是一个长期任务.ppt"
      
    },
    {
      "file_name": "超越自我再创辉煌.html",
      "file_type": "html",
      "file_size": 524288,
      "file_size_formatted": "512KB",
      "created_time": "2025-03-28T12:01:01Z",
      "url": "https://example.com/download/sess_123456/result/超越自我再创辉煌.html"
      
    }
  ],
  "process_files": [
    {
      "file_name": "代码分析报告.txt",
      "file_type": "txt",
      "file_size": 10240,
      "file_size_formatted": "10KB",
      "created_time": "2025-03-28T12:00:30Z",
      "url": "https://example.com/download/sess_123456/process/代码分析报告.txt"
    },
    {
      "file_name": "性能测试数据.csv",
      "file_type": "csv",
      "file_size": 51200,
      "file_size_formatted": "50KB",
      "created_time": "2025-03-28T12:00:45Z",
      "url": "https://example.com/download/sess_123456/process/性能测试数据.csv"
    }
  ],
  "total_result_files": 2,
  "total_process_files": 2
}
```

#### 说明

- 结果制品文件：AI生成的主要输出文件，如PPT、HTML、PDF等
- 过程制品文件：AI处理过程中的中间文件，如分析报告、测试数据等
- 文件大小同时提供字节数和格式化显示



## 断线重连机制

### 重连流程

分为两种重连情况：

1.  用户未离开，但是网关SSE超时断开了，需要重连来继续输出：通过之前后端返回的session_id，以及前端记录下的最后收到的一条last_message_id，调用接口2,后端会继续从last_message_id+1开始进行流式输出
    

```javascript
// 断线检测
eventSource.onerror = function() {
  // 记录最后接收的消息ID
  const lastMessageId = getLastReceivedMessageId();
  
  // 重新建立连接
  reconnectSSE(sessionId, lastMessageId);
};

function reconnectSSE(sessionId, lastMessageId) {
  const url = `/api/sessions/stream?session_id=${sessionId}&last_message_id=${lastMessageId}`;
  eventSource = new EventSource(url);
  
  eventSource.onmessage = handleMessage;
  eventSource.onerror = handleError;
}

```

2.  用户离开了，然后又重新打开了会话页：前端首先调用接口3，如果某个round的status是processing，说明这轮对话还没结束，先渲染接口返回的已完成的消息，然后再通过接口2建连（last_message_id从接口返回值中取最大的message_id），流式获取后面的新消息。
    

例如，接口3返回如下，roundId=2的状态是processing，说明round2没执行完，在最后一条消息后还有要流的，此时传给接口2的参数就是/api/sessions/stream?session_id=sess_123456&last_message_id=msg_456790 这样，前端渲染完所有message后，建立长连接来获取后面的新流式消息。

```json
{
  "session_id": "sess_123456",
  "ali_uid": "user_001",
  "agent_id": "agent_gpt4",
  "title": "JavaScript代码性能优化分析与建议",
  "gmt_create": "2024-01-15T09:00:00Z",
  "gmt_modified": "2024-01-15T10:31:45Z",
  "total_rounds": 2,
  "rounds": [
    {
      "roundId": "2",
      "userPrompt": "请详细分析这段代码的性能问题",
      "status": "processing",
      "startTime": "2024-01-15T10:31:25Z",
      "endTime": null,
      "duration": null,
      "messages": [
        {
          "messageId": "msg_456789",
          "roundId": "2",
          "content": "请详细分析这段代码的性能问题",
          "role": "user",
          "timestamp": "2024-01-15T10:31:25Z",
          "sessionId": "sess_123456",
          "name": null,
          "toolCallId": null,
          "appId": null,
          "duration": null
        },
        {
          "messageId": "msg_456790",
          "roundId": "2",
          "content": "好的，我来为您详细分析这段代码的性能问题。首先让我使用代码分析工具来检查...",
          "role": "assistant",
          "timestamp": "2024-01-15T10:31:26Z",
          "sessionId": "sess_123456",
          "name": null,
          "toolCallId": null,
          "appId": null,
          "duration": 5.2
        }
      ]
    },
    {
      "roundId": "1",
      "status": "completed",
      "userPrompt": "如何优化数据库查询性能？",
      "startTime": "2024-01-15T10:25:00Z",
      "endTime": "2024-01-15T10:25:45Z",
      "duration": "45.0",
      "messages": [
        {
          "roundId": "1",
          "content": "如何优化数据库查询性能？",
          "role": "user",
          "timestamp": "2024-01-15T10:25:00Z",
          "sessionId": "sess_123456",
          "name": null,
          "toolCallId": null,
          "appId": null,
          "duration": null
        }
      ]
    }
  ]
}
```

## 状态枚举值

### Session状态
- `create` - 刚创建，可以接收消息
- `active` - 正在处理，不允许接收新消息  
- `closed` - 处理完成，可以接收新消息

### Round状态
- `processing` - 处理中
- `completed` - 已完成
- `failed` - 失败

### Message角色
- `user` - 用户消息
- `assistant` - 助手消息
- `system` - 系统消息
- `tool` - 工具消息