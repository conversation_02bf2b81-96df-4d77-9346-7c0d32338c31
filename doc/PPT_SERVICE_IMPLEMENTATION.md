# PPT Service 实现说明文档

## 概述

本文档描述了PPT Service的实现架构和当前状态。PPT Service是Alpha Service的一个重要组件，用于处理AI PPT生成、保存和下载相关的业务逻辑。

**最新更新**: 2025-01-31 - PPT保存和下载功能完全实现，支持作品详情获取、导出和结果轮询，集成制品服务。

## 架构设计

### 分层架构

PPT Service遵循项目的标准分层架构模式：

```
src/
├── application/               # API模型层
│   └── ppt_api_models.py     # PPT相关的请求响应模型 (已完成)
├── domain/services/          # 业务服务层  
│   └── ppt_service.py        # PPT业务逻辑实现 (已优化)
├── popclients/              # 第三方客户端封装
│   └── aippt_client.py      # AIPPT服务客户端 (已完成)
├── infrastructure/redis/    # Redis基础设施
│   ├── client.py           # Redis客户端封装
│   └── connection.py       # Redis连接管理
└── presentation/api/routes/  # API路由层
    └── ppt_routes.py         # PPT HTTP接口定义
```

### 核心组件

#### 1. AIPPT客户端 (`src/popclients/aippt_client.py`)

**功能**：
- ✅ **已完成** - 封装AIPPT第三方服务的API调用
- ✅ **已完成** - 实现HMAC-SHA1签名验证算法
- ✅ **已完成** - 正确的endpoint配置 (`co.aippt.cn`)
- ✅ **已完成** - 完整的错误处理和日志记录
- ✅ **已完成** - 全局单例模式管理客户端实例

**已实现方法**：
- ✅ `get_aippt_auth_code(ali_uid)`: 获取PPT认证码 - **已完全实现并测试通过**
- ✅ `get_aippt_token()`: 获取AIPPT token - **已完全实现，直接API调用**
- ✅ `get_ppt_info(ppt_id, token)`: 获取作品详情 - **新增实现，官方API调用**
- ✅ `export_ppt(ppt_id, token)`: 作品导出 - **新增实现，支持多种格式**
- ✅ `get_ppt_export_result(task_key, token)`: 获取导出结果 - **新增实现，轮询机制**

**待实现方法**：
- 🚧 `get_ppt_thumbnail()`: 获取PPT封面图

**关键技术实现**：
- **签名算法**: HMAC-SHA1，格式为 `HttpRequestMethod@ApiUri@Timestamp`
- **认证头**: `x-api-key`, `x-timestamp`, `x-signature`
- **配置管理**: 自动从 `properties.toml` 读取AK/SK
- **参数映射**: `ali_uid` → `uid` (认证码)，`aippt_token_uid` → `uid` (token)

#### 2. Token管理

**Token获取实现**：
- ✅ **直接API调用** - 通过 `/api/grant/token` 接口获取token
- ✅ **简单缓存** - PPTService中提供基于Redis的简单缓存
- ✅ **错误处理** - 完整的异常处理和日志记录

**Token特性**：
```json
{
    "token": "YWY4OWNJNTITYJI5YI0ZZTGWLTK2MGQTMZK5MDQWOGIXYTQW",
    "time_expire": "259200"  // 3天有效期（72小时）
}
```

#### 3. PPT业务服务 (`src/domain/services/ppt_service.py`)

**功能**：
- 实现PPT相关的核心业务逻辑
- 集成AIPPT客户端调用
- 管理制品存储和会话关联
- 统一的异常处理和日志记录

**主要方法**：
- ✅ `get_ppt_auth_code(ali_uid)`: **已完全实现** - 获取PPT认证码
- ✅ `bind_ppt_to_session(session_id, ppt_id)`: **已完全实现** - 绑定PPT到会话，记录关联关系
- ✅ `get_ppt_thumbnail(ppt_id)`: **已完全实现** - 获取PPT封面图缩略图
- ✅ `_get_aippt_token()`: **已完全实现** - 获取AIPPT token（含简单缓存）
- ✅ `save_ppt(ppt_id, session_id)`: **已完全实现** - 保存PPT到制品管理，支持轮询导出
- ✅ `_poll_export_result(task_key, token, timeout)`: **已实现** - 优雅轮询导出结果，60秒超时

#### 4. API模型 (`src/application/ppt_api_models.py`)

**功能**：
- ✅ **已完成** - 定义PPT相关的请求和响应数据模型
- ✅ **已完成** - 使用Pydantic进行数据验证
- ✅ **已完成** - 遵循项目的API设计规范

**已实现模型**：
- ✅ `GetPPTAuthCodeResponse`: 认证码响应
- ✅ `AIPPTAuthCodeResponse`: AIPPT认证码响应（客户端内部使用）
- ✅ `AIPPTTokenResponse`: AIPPT token响应
- ✅ `GetPPTInfoRequest/Response`: 获取作品详情
- ✅ `ExportPPTRequest/Response`: 作品导出请求/响应
- ✅ `GetPPTExportResultRequest/Response`: 获取导出结果
- ✅ `SavePPTRequest`: PPT保存请求
- ✅ `DownloadPPTRequest`: PPT下载请求
- 🚧 `GetPPTThumbnailRequest/Response`: 封面图获取

## 配置管理

### AIPPT服务配置

在 `properties.toml` 中已配置AIPPT服务的必要参数：

```toml
[daily]
aippt_endpoint = "co.aippt.cn"  # AIPPT官方服务端点
aippt_access_key = "685b9513d90b3"  # AIPPT服务访问密钥
aippt_secret_key = "TzKB1Y4q7ADxoq4S9qadGlderDB2khgy"  # AIPPT服务秘密密钥
aippt_token_uid = "wuying"  # Token获取使用的用户ID

[pre]
aippt_endpoint = "co.aippt.cn"
aippt_access_key = "685b9513d90b3"
aippt_secret_key = "TzKB1Y4q7ADxoq4S9qadGlderDB2khgy"
aippt_token_uid = "wuying"

[prod]
aippt_endpoint = "co.aippt.cn"
aippt_access_key = "685b9513d90b3"
aippt_secret_key = "TzKB1Y4q7ADxoq4S9qadGlderDB2khgy"
aippt_token_uid = "wuying"
```

## 已实现功能

### ✅ 获取PPT认证码

**接口**: `GET /api/aippt/auth/code`

**功能描述**：
- ✅ **完全实现** - 调用AIPPT官方服务获取认证码
- ✅ **签名验证** - 实现HMAC-SHA1签名算法，通过官方API验证
- ✅ **自动配置** - 自动从配置文件读取endpoint和密钥
- ✅ **错误处理** - 完整的错误处理和日志记录
- ✅ **简化API** - 无需请求体，自动从用户上下文获取参数

**技术实现细节**：
- **API端点**: `/api/grant/code`
- **签名算法**: `GET@/api/grant/code/@{timestamp}` + HMAC-SHA1 + Base64
- **认证方式**: HTTP Header (`x-api-key`, `x-timestamp`, `x-signature`)
- **参数映射**: `ali_uid` → `uid`, `channel` = ""
- **响应处理**: 自动检查 API code (0=成功)，转换数据类型

### ✅ 绑定PPT到会话

**功能描述**：
- ✅ **完全实现** - 将PPT作品ID与会话ID进行关联绑定
- ✅ **Memory集成** - 自动将绑定关系存储到Memory系统
- ✅ **唯一标识** - 每次绑定生成唯一的run_id
- ✅ **错误处理** - 完整的异常处理和日志记录

**API详情**：
```python
# 调用方式
service = PPTService()
service.bind_ppt_to_session(
    session_id="session_123",
    ppt_id="ppt_456"
)
# 无返回值，成功静默执行，失败抛异常
```

**技术实现细节**：
- **事件类型**: CustomEvent
- **事件名称**: "ppt_id"  
- **事件内容**: PPT作品ID
- **唯一标识**: 自动生成UUID作为run_id
- **存储方式**: 通过MemorySDK的add_event方法存储
- **关联维度**: session_id维度关联

**使用场景**：
- PPT创建完成后绑定到当前会话
- 用户选择现有PPT时建立关联
- 会话恢复时重新建立PPT关联
- 支持一个会话绑定多个PPT（多次调用）

### ✅ 获取PPT封面图

**功能描述**：
- ✅ **完全实现** - 获取PPT作品的封面图缩略图URL
- ✅ **轻量级接口** - 复用get_ppt_info接口，只返回cover_url字段
- ✅ **智能降级** - 封面图不存在时返回空字符串，不抛异常
- ✅ **错误处理** - 完整的异常处理和日志记录

**API详情**：
```python
# 调用方式
service = PPTService()
thumbnail_url = service.get_ppt_thumbnail(ppt_id="ppt_123")
# 返回: "https://example.com/covers/ppt_123_cover.jpg" 或 ""
```

**技术实现细节**：
- **依赖接口**: 复用`get_ppt_info`接口获取作品详情
- **返回字段**: 提取`cover_url`字段作为缩略图URL
- **降级策略**: cover_url不存在或为空时返回空字符串
- **Token管理**: 自动获取和使用AIPPT token
- **错误处理**: API失败时抛出PPTServiceError异常

**使用场景**：
- PPT列表展示时显示封面图
- PPT选择界面的缩略图预览
- 会话历史中的PPT视觉识别
- PPT分享时的预览图片

**返回值说明**：
- **成功**: 返回封面图URL字符串（https://...）
- **无封面图**: 返回空字符串（""）
- **失败**: 抛出PPTServiceError异常

### ✅ 获取AIPPT Token

**功能描述**：
- ✅ **完全实现** - 调用AIPPT官方服务获取token
- ✅ **直接API调用** - 通过 `get_aippt_token()` 方法直接调用
- ✅ **签名验证** - 使用相同的HMAC-SHA1签名算法
- ✅ **自动配置** - 自动从配置文件读取参数
- ✅ **错误处理** - 完整的错误处理和日志记录
- ✅ **简单缓存** - PPTService层提供基于Redis的简单缓存

**API详情**：
```python
# 直接调用客户端
client = get_aippt_client()
token_response = client.get_aippt_token()
# 返回: AIPPTTokenResponse(token="...", time_expire="259200")

# 通过PPTService（含缓存）
service = PPTService()
token = service._get_aippt_token()
# 返回: 缓存的token字符串
```

**技术实现细节**：
- **API端点**: `/api/grant/token`
- **签名算法**: `GET@/api/grant/token/@{timestamp}` + HMAC-SHA1 + Base64
- **认证方式**: HTTP Header (`x-api-key`, `x-timestamp`, `x-signature`)
- **参数映射**: `aippt_token_uid` → `uid` (固定为"wuying")
- **Token有效期**: 259200秒（3天/72小时）
- **响应格式**: `AIPPTTokenResponse`对象，包含token和过期时间

**缓存机制**：
- **缓存Key**: `aippt_token` (在Redis中)
- **缓存策略**: 简单的存在性检查，存在则使用缓存，不存在则调用API
- **缓存更新**: 每次API调用后自动更新缓存
- **错误降级**: Redis失败时直接调用API

### ✅ 获取作品详情

**功能描述**：
- ✅ **完全实现** - 调用AIPPT官方服务获取作品详情
- ✅ **官方API** - 严格按照AIPPT官方文档实现
- ✅ **错误处理** - 完整的错误处理和日志记录

**API详情**：
```python
# 调用方式
client = get_aippt_client()
ppt_info = client.get_ppt_info(ppt_id="123", token="xxx")
# 返回: {"id": 123, "name": "PPT名称", "cover_url": "...", ...}
```

**技术实现细节**：
- **API端点**: `/api/design/info`
- **请求方式**: GET
- **参数**: `user_design_id` (作品ID)
- **认证方式**: HTTP Header (`x-api-key`, `x-token`)
- **响应字段**: id, name, cover_url, task_id, created_at, storage_time, version

### ✅ 作品导出

**功能描述**：
- ✅ **完全实现** - 调用AIPPT官方服务导出作品
- ✅ **多种格式** - 支持 ppt, pdf, png, jpeg 格式导出
- ✅ **任务机制** - 返回任务标识，支持异步导出

**API详情**：
```python
# 调用方式
client = get_aippt_client()
task_key = client.export_ppt(ppt_id="123", token="xxx")
# 返回: "task_key_string"
```

**技术实现细节**：
- **API端点**: `/api/download/export/file`
- **请求方式**: POST
- **参数**: id (作品ID), format (导出格式), edit (是否可编辑), files_to_zip (是否压缩)
- **认证方式**: HTTP Header (`x-api-key`, `x-token`)
- **返回**: 导出任务标识

### ✅ 获取导出结果

**功能描述**：
- ✅ **完全实现** - 查询导出任务状态和结果
- ✅ **状态检测** - 自动判断导出成功、进行中或失败
- ✅ **下载链接** - 成功时返回可下载的文件链接

**API详情**：
```python
# 调用方式
client = get_aippt_client()
result = client.get_ppt_export_result(task_key="xxx", token="xxx")
# 返回: {"download_url": "http://...", "message": "导出成功"}
```

**技术实现细节**：
- **API端点**: `/api/download/export/file/result`
- **请求方式**: POST
- **参数**: task_key (任务标识)
- **认证方式**: HTTP Header (`x-api-key`, `x-token`)
- **状态判断**: code=0 表示成功，返回下载链接数组

### ✅ PPT保存功能

**功能描述**：
- ✅ **完全实现** - 完整的PPT保存流程，从作品详情到下载链接
- ✅ **轮询机制** - 智能轮询导出结果，60秒超时，1秒间隔
- ✅ **制品集成** - 自动将下载链接添加到制品服务
- ✅ **错误处理** - 完整的异常处理和日志记录

**API详情**：
```python
# 调用方式
service = PPTService()
download_url = service.save_ppt(
    ppt_id="123",
    session_id="session_456"
)
# 返回: "http://download_url"
```

**实现流程**：
1. **获取token** - 调用 `_get_aippt_token()` 获取认证token
2. **获取详情** - 调用 `get_ppt_info()` 获取作品信息
3. **启动导出** - 调用 `export_ppt()` 启动导出任务
4. **轮询结果** - 调用 `_poll_export_result()` 轮询导出状态
5. **更新制品** - 成功后自动添加到制品服务

**轮询机制**：
- **超时时间**: 60秒（1分钟）
- **查询间隔**: 1秒
- **最多查询**: 60次
- **异常处理**: 单次查询失败不中断，记录警告后继续
- **状态日志**: 详细记录轮询过程和耗时

**制品服务集成**：
- **事件类型**: ArtifactEvent
- **文件类型**: "ppt"
- **文件名**: 作品名称（来自get_ppt_info）
- **内容**: 下载链接URL
- **关联参数**: session_id用于制品关联，自动生成run_id

### 3.3 SavePPT 保存PPT (API接口)

#### 入参

| **参数** | **类型** | **必填** | **描述** | **备注** |
| --- | --- | --- | --- | --- |
| LoginToken | String | *   [x] | 登录令牌（查询参数方式） |  |
| LoginSessionId | String | *   [x] | 会话ID（查询参数方式） |  |
| RegionId | String | *   [x] | 区域ID（查询参数方式） |  |
| PptId | String | *   [x] | PPT作品ID |  |

#### 出参

| **参数** | **类型** | **描述** |
| --- | --- | --- |
| RequestId | String | 请求ID |
| Code | Integer | 返回码 |
| Message | String | 返回信息 |

### 3.4 DownloadPPT 下载PPT (已移除)

**注意**: 下载PPT功能已合并到保存PPT流程中。
- 使用 `save_ppt()` 方法可以直接获取下载链接
- 无需单独的下载接口，简化了API设计
- 保存和下载使用相同的技术流程

### 3.5 GetPPTThumbnail 获取PPT封面图

#### 入参

| **参数** | **类型** | **必填** | **描述** | **备注** |
| --- | --- | --- | --- | --- |
| LoginToken | String | *   [x] | 登录令牌（查询参数方式） |  |
| LoginSessionId | String | *   [x] | 会话ID（查询参数方式） |  |
| RegionId | String | *   [x] | 区域ID（查询参数方式） |  |
| PptId | String | *   [x] | PPT作品ID |  |

#### 出参

| **参数** | **类型** | **描述** |
| --- | --- | --- |
| RequestId | String | 请求ID |
| Code | Integer | 返回码 |
| Message | String | 返回信息 |
| Data | String | 封面图URL |

## 测试验证

### 单元测试

**测试文件**: `tests/test_ppt_service.py`

**测试覆盖**：
- ✅ `test_get_ppt_auth_code()` - 认证码获取测试
- ✅ `test_get_aippt_token()` - Token获取测试
- ✅ `test_bind_ppt_to_session_success()` - PPT绑定会话成功测试
- ✅ `test_bind_ppt_to_session_memory_failure()` - PPT绑定会话Memory失败测试
- ✅ `test_bind_ppt_to_session_multiple_calls()` - 多次绑定和run_id唯一性测试
- ✅ `test_get_ppt_thumbnail_success()` - PPT缩略图获取成功测试
- ✅ `test_get_ppt_thumbnail_no_cover_url()` - PPT缩略图字段不存在测试
- ✅ `test_get_ppt_thumbnail_empty_cover_url()` - PPT缩略图字段为空测试
- ✅ `test_get_ppt_thumbnail_client_error()` - PPT缩略图客户端错误测试
- ✅ `test_get_ppt_thumbnail_token_failure()` - PPT缩略图Token失败测试
- ✅ `test_save_ppt_success()` - PPT保存成功场景测试
- ✅ `test_save_ppt_export_timeout()` - PPT保存导出超时测试
- ✅ `test_save_ppt_memory_update_failure()` - PPT保存制品更新失败测试
- ✅ `test_save_ppt_client_error()` - PPT保存客户端错误测试

**运行测试**：
```bash
# 运行所有测试
pytest tests/test_ppt_service.py -v

# 运行绑定PPT到会话的测试
pytest tests/test_ppt_service.py -k "bind_ppt_to_session" -v

# 运行PPT保存相关测试
pytest tests/test_ppt_service.py -k "save_ppt" -v

# 运行PPT缩略图相关测试
pytest tests/test_ppt_service.py -k "get_ppt_thumbnail" -v

# 运行特定测试
pytest tests/test_ppt_service.py::test_get_aippt_token -v -s
```

### 集成测试

**单元测试结果**：
```
✅ 认证码获取测试通过 - 获取到认证码: 6de55dcc..., 过期时间: 86400
✅ Token获取测试通过 - 获取到token: YWY4OWNJNTIT..., 过期时间: 259200
✅ 绑定PPT到会话测试通过 - CustomEvent创建、Memory存储、run_id唯一性验证通过
✅ PPT缩略图测试通过 - 封面图获取、空值处理、异常场景全覆盖
✅ PPT保存测试通过 - 作品详情、导出、轮询、制品更新全流程正常
✅ 轮询机制测试通过 - 60秒超时，1秒间隔，异常处理正常
✅ Mock测试覆盖完整 - 成功场景、异常场景、边界条件全覆盖
```

**验证要点**：
- HTTP状态码: 200 ✅
- API响应码: 0 (成功) ✅  
- 签名验证: 通过 ✅
- 数据格式: 正确 ✅
- Token有效期: 3天 ✅
- Redis缓存: 正常工作 ✅
- PPT会话绑定: Memory存储正常 ✅
- CustomEvent创建: 参数验证通过 ✅
- run_id唯一性: UUID生成正常 ✅
- PPT缩略图获取: cover_url提取正常 ✅
- 缩略图降级处理: 空值返回正常 ✅
- 作品详情API: 正常获取 ✅
- 导出任务: 成功启动 ✅
- 轮询机制: 工作正常 ✅
- 制品服务: 成功集成 ✅
- Mock测试: 各种场景覆盖完整 ✅

## 部署注意事项

### 配置要求

1. **AIPPT服务配置**: ✅ 已在 `properties.toml` 中配置所有环境的参数
   - `aippt_endpoint`: AIPPT服务端点
   - `aippt_access_key`: 访问密钥
   - `aippt_secret_key`: 秘密密钥  
   - `aippt_token_uid`: Token获取用户ID（固定为"wuying"）

2. **网络访问**: 确保服务器能够访问 `https://co.aippt.cn`
3. **依赖包**: ✅ 所有相关依赖包已在 `pyproject.toml` 中配置

### 部署检查清单

- ✅ endpoint配置正确 (`co.aippt.cn`)
- ✅ AK/SK配置正确且有效  
- ✅ 签名算法实现正确 (HMAC-SHA1)
- ✅ HTTP请求格式符合官方规范
- ✅ 错误处理覆盖完整
- ✅ 日志记录详细便于调试
- ✅ Token获取参数配置正确
- ✅ 作品导出API配置正确
- ✅ 轮询机制参数合理
- ✅ 制品服务集成正常

## 版本信息

- **当前版本**: 1.6.0  
- **实现状态**: ✅ PPT认证码获取，✅ PPT会话绑定，✅ PPT封面图获取，✅ AIPPT Token获取，✅ PPT保存，✅ 作品导出
- **最后更新**: 2025-08-01

## 后续开发计划

### 下一步实现 (优先级排序)

1. **🔄 轮询优化** - 改进轮询策略，支持动态间隔和更智能的超时
2. **🔄 Token缓存优化** - 改进缓存策略，支持TTL和过期检测
3. **📊 性能监控** - 添加详细的API调用性能指标
4. **🔧 错误重试** - 添加导出失败时的智能重试机制
5. **🎨 UI预览集成** - 集成PPT缩略图到用户界面展示

## 总结

PPT Service已完成PPT保存和下载功能的实现，主要特性：

### 核心功能

1. **认证码获取** - 完整实现，支持用户维度的认证码获取
2. **PPT会话绑定** - 新增实现，支持PPT与会话的关联管理
3. **PPT封面图获取** - 新增实现，轻量级缩略图获取接口
4. **Token获取** - 完整实现，支持服务级别的token获取
5. **作品详情** - 新增实现，获取PPT作品基础信息
6. **作品导出** - 新增实现，支持多种格式导出
7. **轮询机制** - 智能轮询导出结果，60秒超时保护
8. **PPT保存** - 完整流程实现，集成制品服务
9. **简单缓存** - 基于Redis的基础缓存机制
10. **错误处理** - 完整的异常处理和错误降级
11. **配置管理** - 统一的配置文件管理

### 技术特点

- **官方API**: 严格按照AIPPT官方文档实现
- **高效轮询**: 1秒间隔轮询，60秒超时，智能异常处理
- **制品集成**: 自动将下载链接添加到制品管理系统
- **完整流程**: 从作品详情到下载链接的端到端实现
- **高效缓存**: 基于Redis的简单缓存策略
- **完整日志**: 详细的日志记录便于调试和监控
- **统一签名**: 使用相同的HMAC-SHA1签名算法
- **配置驱动**: 所有参数均通过配置文件管理

这个实现提供了完整的PPT保存和下载功能，支持实际的业务需求，已经可以正常投入使用。 