# 知识库Repository使用指南

## 概述

本文档介绍了如何使用 `KnowledgeBaseRepository` 类进行知识库的数据库操作。该Repository基于SQLAlchemy框架，提供了完整的CRUD（创建、读取、更新、删除）功能。

## 数据库表结构

知识库表 `knowledge_bases` 的结构如下：

```sql
CREATE TABLE `knowledge_bases` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `kb_id` varchar(255) NOT NULL COMMENT '知识库唯一标识',
  `name` varchar(255) NOT NULL COMMENT '知识库名称',
  `description` text COMMENT '知识库描述',
  `owner_ali_uid` bigint unsigned NOT NULL COMMENT '所有者阿里UID',
  `owner_wy_id` varchar(255) NOT NULL COMMENT '所有者无影ID',
  `is_deleted` bigint unsigned NOT NULL COMMENT '是否删除，0表示未删除',
  `gmt_created` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_kb_id` (`kb_id`(128)),
  UNIQUE KEY `uk_aliuid_wyid_name_isdeleted` (`owner_ali_uid`,`owner_wy_id`(128),`name`(128),`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识库主表'
```

## 文件结构

```
src/infrastructure/database/
├── models/
│   ├── knowledgebase_models.py      # 知识库数据模型
│   └── __init__.py                  # 模型导入
└── repositories/
    └── knowledgebase_repository.py  # 知识库Repository
```

## 使用方法

### 1. 导入Repository

```python
from src.infrastructure.database.repositories.knowledgebase_repository import knowledgebase_repository
```

### 2. 创建知识库

```python
import uuid

# 生成唯一的知识库ID
kb_id = f"kb_{uuid.uuid4().hex[:12]}"

# 创建知识库
knowledge_base = knowledgebase_repository.create_knowledge_base(
    kb_id=kb_id,
    name="我的知识库",
    owner_ali_uid=123456789,
    owner_wy_id="wy_user_001",
    description="这是一个示例知识库"
)

print(f"创建成功: {knowledge_base.kb_id}")
```

### 3. 获取知识库

```python
# 根据知识库ID获取
kb = knowledgebase_repository.get_knowledge_base_by_id("kb_abc123")

if kb:
    print(f"知识库名称: {kb.name}")
    print(f"描述: {kb.description}")
    print(f"创建时间: {kb.gmt_created}")
else:
    print("知识库不存在")
```

### 4. 更新知识库

```python
# 更新知识库信息
updated_kb = knowledgebase_repository.update_knowledge_base(
    kb_id="kb_abc123",
    name="更新后的名称",
    description="更新后的描述"
)

if updated_kb:
    print(f"更新成功: {updated_kb.name}")
else:
    print("知识库不存在，无法更新")
```

### 5. 获取用户的知识库列表

```python
# 获取用户的所有知识库
knowledge_bases = knowledgebase_repository.get_knowledge_bases_by_owner(
    owner_ali_uid=123456789,
    owner_wy_id="wy_user_001",
    limit=10,    # 限制返回数量
    offset=0     # 分页偏移
)

for kb in knowledge_bases:
    print(f"- {kb.kb_id}: {kb.name}")
```

### 6. 搜索知识库

```python
# 按名称搜索知识库
search_results = knowledgebase_repository.list_knowledge_bases(
    owner_ali_uid=123456789,
    owner_wy_id="wy_user_001",
    name="测试",  # 支持模糊搜索
    limit=5
)

for kb in search_results:
    print(f"找到: {kb.name}")
```

### 7. 软删除知识库

```python
# 软删除（标记为已删除，但不从数据库物理删除）
success = knowledgebase_repository.soft_delete_knowledge_base("kb_abc123")

if success:
    print("软删除成功")
else:
    print("知识库不存在，无法删除")
```

### 8. 硬删除知识库

```python
# 硬删除（从数据库物理删除）
success = knowledgebase_repository.hard_delete_knowledge_base("kb_abc123")

if success:
    print("硬删除成功")
else:
    print("知识库不存在，无法删除")
```

### 9. 检查知识库是否存在

```python
# 检查知识库是否存在（未删除）
exists = knowledgebase_repository.check_knowledge_base_exists("kb_abc123")

if exists:
    print("知识库存在")
else:
    print("知识库不存在或已删除")
```

### 10. 统计知识库数量

```python
# 统计用户的知识库数量
count = knowledgebase_repository.count_knowledge_bases_by_owner(
    owner_ali_uid=123456789,
    owner_wy_id="wy_user_001"
)

print(f"用户共有 {count} 个知识库")
```

## 错误处理

Repository方法会抛出以下异常：

- `ValueError`: 当知识库ID已存在或名称冲突时
- `Exception`: 其他数据库操作错误

建议使用try-catch进行错误处理：

```python
try:
    kb = knowledgebase_repository.create_knowledge_base(
        kb_id="kb_abc123",
        name="测试知识库",
        owner_ali_uid=123456789,
        owner_wy_id="wy_user_001"
    )
    print("创建成功")
except ValueError as e:
    print(f"业务错误: {e}")
except Exception as e:
    print(f"系统错误: {e}")
```

## 数据模型

### KnowledgeBaseModel

知识库数据模型包含以下字段：

- `id`: 数据库主键ID
- `kb_id`: 知识库唯一标识
- `name`: 知识库名称
- `description`: 知识库描述
- `owner_ali_uid`: 所有者阿里UID
- `owner_wy_id`: 所有者无影ID
- `is_deleted`: 删除标记（0=未删除，1=已删除）
- `gmt_created`: 创建时间
- `gmt_modified`: 修改时间

### 模型方法

- `to_dict()`: 转换为字典格式
- `is_active()`: 检查知识库是否有效（未删除）

## 注意事项

1. **唯一性约束**: 
   - `kb_id` 必须全局唯一
   - 同一用户的 `name` 不能重复（未删除状态下）

2. **软删除**: 
   - 默认使用软删除，数据不会物理删除
   - 查询时自动过滤已删除的记录

3. **事务管理**: 
   - Repository自动管理数据库事务
   - 异常时会自动回滚

4. **日志记录**: 
   - 所有操作都会记录详细日志
   - 日志前缀为 `[KnowledgeBaseDB]`

## 测试

运行测试用例：

```bash
# 运行所有测试
pytest tests/test_knowledgebase_repository.py -v

# 运行特定测试
pytest tests/test_knowledgebase_repository.py::TestKnowledgeBaseRepository::test_create_knowledge_base_success -v
```

## 示例代码

完整的使用示例请参考：
- `examples/knowledgebase_repository_usage.py` - 使用示例
- `tests/test_knowledgebase_repository.py` - 测试用例 