# 知识库 API 使用指南

## 概述

本文档介绍了知识库管理的完整 API 接口，包括创建、查询、更新和删除知识库的功能。

## API 端点列表

### 1. 创建知识库
- **方法**: `POST`
- **路径**: `/api/knowledge_base/create`
- **描述**: 创建新的知识库

### 2. 查询知识库列表
- **方法**: `GET`
- **路径**: `/api/knowledge_base/list`
- **描述**: 分页查询用户的知识库列表

### 3. 查询知识库详情 ⭐ 新增
- **方法**: `GET`
- **路径**: `/api/knowledge_base/{kb_id}`
- **描述**: 获取指定知识库的详细信息

### 4. 修改知识库信息 ⭐ 新增
- **方法**: `PUT`
- **路径**: `/api/knowledge_base/{kb_id}`
- **描述**: 更新知识库的名称和描述

### 5. 删除知识库 ⭐ 新增
- **方法**: `DELETE`
- **路径**: `/api/knowledge_base/{kb_id}`
- **描述**: 删除指定的知识库

## 新增 API 详细说明

### 1. 查询知识库详情

#### 请求
```http
GET /api/knowledge_base/{kb_id}
Authorization: Bearer <token>
```

#### 路径参数
- `kb_id` (string, 必需): 知识库ID

#### 响应
```json
{
  "code": "",
  "data": {
    "kb_id": "kb_abc123",
    "name": "我的知识库",
    "description": "这是一个示例知识库",
    "owner_ali_uid": 123456789,
    "owner_wy_id": "wy_user_001",
    "document_count": null,
    "session_count": null,
    "gmt_created": "2024-01-01T00:00:00Z",
    "gmt_modified": "2024-01-01T00:00:00Z"
  },
  "message": "",
  "request_id": "req_123",
  "status": 200,
  "success": true
}
```

#### 错误响应
```json
{
  "code": "KNOWLEDGE_BASE_NOT_FOUND",
  "data": null,
  "message": "知识库不存在",
  "request_id": "req_123",
  "status": 400,
  "success": false
}
```

### 2. 修改知识库信息

#### 请求
```http
PUT /api/knowledge_base/{kb_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "新知识库名称",
  "description": "新的知识库描述"
}
```

#### 路径参数
- `kb_id` (string, 必需): 知识库ID

#### 请求体
```json
{
  "name": "新知识库名称",        // 可选，新的知识库名称
  "description": "新描述",      // 可选，新的知识库描述
  "is_update_selective": true   // 可选，是否选择性更新（true表示None值不更新，false表示None值会置空字段）
}
```

#### 响应
```json
{
  "code": "",
  "data": {
    "kb_id": "kb_abc123",
    "name": "新知识库名称",
    "description": "新描述",
    "gmt_modified": "2024-01-01T12:00:00Z"
  },
  "message": "",
  "request_id": "req_123",
  "status": 200,
  "success": true
}
```

#### 错误响应
```json
{
  "code": "PERMISSION_DENIED",
  "data": null,
  "message": "无权限修改该知识库",
  "description": "权限不足",
  "request_id": "req_123",
  "status": 400,
  "success": false
}
```

### 3. 删除知识库

#### 请求
```http
DELETE /api/knowledge_base/{kb_id}
Authorization: Bearer <token>
```

#### 路径参数
- `kb_id` (string, 必需): 知识库ID

#### 响应
```json
{
  "code": "",
  "data": {
    "kb_id": "kb_abc123",
    "success": true
  },
  "message": "",
  "request_id": "req_123",
  "status": 200,
  "success": true
}
```

#### 错误响应
```json
{
  "code": "PERMISSION_DENIED",
  "data": null,
  "message": "无权限删除该知识库",
  "description": "权限不足",
  "request_id": "req_123",
  "status": 400,
  "success": false
}
```

## 权限控制

所有知识库操作都需要进行权限验证：

1. **认证**: 需要有效的 Bearer Token
2. **授权**: 只有知识库的所有者才能进行修改和删除操作
3. **查看**: 只有知识库的所有者才能查看详情

## 错误码说明

| 错误码 | 描述 | 说明 | HTTP状态码 |
|--------|------|------|------------|
| `PARAM_NOT_NULL` | 参数不能为空 | 必填参数未提供 | 400 |
| `PARAM_NOT_EMPTY` | 参数不能为空字符串或空列表 | 参数值不能为空字符串、空列表等 | 400 |
| `PARAM_INVALID` | 参数值无效 | 参数值不在有效范围内 | 400 |
| `OBJECT_NOT_FOUND` | 对象不存在 | 请求的资源不存在 | 400 |
| `OBJECT_ALREADY_EXISTS` | 对象已存在 | 要创建的对象已存在 | 400 |
| `PERMISSION_DENIED` | 权限不足 | 无权限执行该操作 | 400 |
| `OBJECT_DUPLICATE` | 对象重复 | 存在重复的对象（如重名） | 400 |
| `INTERNAL_ERROR` | 系统内部错误 | 服务器内部错误，请联系管理员 | 500 |

## 使用示例

### Python 示例

```python
import requests

# 配置
base_url = "https://api.example.com"
token = "your_bearer_token"
kb_id = "kb_abc123"

headers = {
    "Authorization": f"Bearer {token}",
    "Content-Type": "application/json"
}

# 1. 获取知识库详情
response = requests.get(f"{base_url}/api/knowledge_base/{kb_id}", headers=headers)
if response.status_code == 200:
    kb_detail = response.json()["data"]
    print(f"知识库名称: {kb_detail['name']}")

# 2. 更新知识库信息
update_data = {
    "name": "更新后的名称",
    "description": "更新后的描述"
}
response = requests.put(f"{base_url}/api/knowledge_base/{kb_id}", 
                       json=update_data, headers=headers)
if response.status_code == 200:
    print("知识库更新成功")

# 3. 清空知识库描述（非选择性更新）
clear_description_data = {
    "description": None,
    "is_update_selective": False
}
response = requests.put(f"{base_url}/api/knowledge_base/{kb_id}", 
                       json=clear_description_data, headers=headers)
if response.status_code == 200:
    print("知识库描述清空成功")

# 4. 删除知识库
response = requests.delete(f"{base_url}/api/knowledge_base/{kb_id}", headers=headers)
if response.status_code == 200:
    print("知识库删除成功")
```

### cURL 示例

```bash
# 获取知识库详情
curl -X GET "https://api.example.com/api/knowledge_base/kb_abc123" \
  -H "Authorization: Bearer your_token"

# 更新知识库信息
curl -X PUT "https://api.example.com/api/knowledge_base/kb_abc123" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{"name": "新名称", "description": "新描述"}'

# 清空知识库描述（非选择性更新）
curl -X PUT "https://api.example.com/api/knowledge_base/kb_abc123" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{"description": null, "is_update_selective": false}'

# 删除知识库
curl -X DELETE "https://api.example.com/api/knowledge_base/kb_abc123" \
  -H "Authorization: Bearer your_token"
```

## 注意事项

1. **软删除**: 删除操作采用软删除方式，数据不会从数据库中物理删除
2. **名称唯一性**: 同一用户下的知识库名称不能重复
3. **事务一致性**: 更新和删除操作会同时更新本地数据库和远程 RAG 服务
4. **错误处理**: 建议客户端实现适当的错误处理和重试机制
5. **请求ID**: 每个请求都会返回唯一的 request_id，用于日志追踪

## 相关文件

- 服务实现: `src/domain/services/knowledge_service.py`
- API 路由: `src/presentation/api/routes/rag_routes.py`
- 数据模型: `src/application/rag_api_models.py`
- 数据库模型: `src/infrastructure/database/models/knowledgebase_models.py`
- 测试用例: `tests/test_knowledge_service_new_methods.py`
- 使用示例: `examples/knowledge_service_new_methods_example.py` 