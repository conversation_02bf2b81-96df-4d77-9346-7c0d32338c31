# 事务功能使用指南

## 概述

本项目实现了类似 Java `@Transactional` 的事务功能，支持自动提交、回滚和补偿操作。

## 核心组件

### 1. 事务装饰器

```python
from src.infrastructure.database.transaction import transactional, TransactionalError

@transactional(rollback_on=(ValueError, Exception))
def create_knowledge_base(self, name: str, auth_context: AuthContext, description: Optional[str] = None) -> str:
    # 业务逻辑
    pass
```

### 2. 事务上下文管理器

```python
from src.infrastructure.database.transaction import transaction_context

with transaction_context() as session:
    # 数据库操作
    session.add(new_record)
    # 自动提交或回滚
```

## 功能特性

### 1. 自动事务管理

- **自动提交**: 函数正常执行时自动提交事务
- **自动回滚**: 发生异常时自动回滚事务
- **会话管理**: 自动管理数据库会话的生命周期

### 2. 异常处理配置

```python
@transactional(
    rollback_on=(ValueError, Exception),  # 指定异常类型时回滚
    commit_on=(SomeCustomException,),     # 指定异常类型时提交
    read_only=False                       # 是否为只读事务
)
```

### 3. Saga 模式支持

对于涉及外部服务的分布式事务，实现了 Saga 模式的补偿操作：

```python
@transactional(rollback_on=(ValueError, Exception))
def create_knowledge_base(self, name: str, auth_context: AuthContext, description: Optional[str] = None) -> str:
    # 步骤1: 调用 RAG 服务创建知识库
    client = create_rag_client()
    create_rag_response_body = client.create_kb(...)
    
    try:
        # 步骤2: 在本地数据库创建记录
        self.knowledgebase_repository.create_knowledge_base(...)
        
        # 步骤3: 注册资源鉴权
        auth_service.register_resource(...)
        
        return kb_id
        
    except Exception as e:
        # 补偿操作：如果本地操作失败，删除 RAG 服务中的知识库
        try:
            client.delete_kb(kb_id=kb_id)
            logger.info(f"补偿操作成功：删除 RAG 知识库 {kb_id}")
        except Exception as compensation_error:
            logger.error(f"补偿操作失败：无法删除 RAG 知识库 {kb_id}: {compensation_error}")
        
        # 重新抛出异常，触发事务回滚
        raise ValueError(f"创建知识库失败: {e}") from e
```

## 使用示例

### 1. 基本事务

```python
@transactional()
def simple_transaction():
    # 数据库操作
    session.add(new_record)
    session.commit()  # 不需要手动提交，装饰器会自动处理
    return "success"
```

### 2. 带异常处理的事务

```python
@transactional(rollback_on=(ValueError, DatabaseError))
def transaction_with_exception_handling():
    # 如果抛出 ValueError 或 DatabaseError，事务会回滚
    if some_condition:
        raise ValueError("业务异常")
    return "success"
```

### 3. 只读事务

```python
@transactional(read_only=True)
def read_only_transaction():
    # 只读操作，不会提交事务
    records = session.query(SomeModel).all()
    return records
```

### 4. 上下文管理器

```python
from src.infrastructure.database.transaction import transaction_context

def use_transaction_context():
    with transaction_context() as session:
        # 在事务上下文中执行操作
        session.add(new_record)
        # 退出上下文时自动提交或回滚
```

## 已应用事务的方法

### 1. create_knowledge_base

- **事务范围**: 创建知识库的完整流程
- **补偿操作**: 如果本地操作失败，删除 RAG 服务中的知识库
- **异常处理**: 捕获所有异常并执行补偿操作

### 2. update_knowledge_base

- **事务范围**: 更新知识库的完整流程
- **补偿操作**: 如果本地操作失败，恢复 RAG 服务中的原始值
- **异常处理**: 捕获所有异常并执行补偿操作

### 3. delete_knowledge_base

- **事务范围**: 删除知识库的完整流程
- **补偿操作**: 如果本地操作失败，重新创建 RAG 知识库
- **异常处理**: 捕获所有异常并执行补偿操作

## 最佳实践

### 1. 异常处理

```python
@transactional(rollback_on=(ValueError, Exception))
def business_method():
    try:
        # 业务逻辑
        pass
    except SpecificException as e:
        # 处理特定异常
        logger.error(f"业务异常: {e}")
        raise  # 重新抛出以触发回滚
```

### 2. 补偿操作

```python
def business_method_with_compensation():
    # 步骤1: 外部服务调用
    external_result = external_service.operation()
    
    try:
        # 步骤2: 本地数据库操作
        local_operation()
    except Exception as e:
        # 补偿操作
        external_service.rollback(external_result)
        raise
```

### 3. 日志记录

```python
@transactional()
def logged_transaction():
    logger.info("开始事务操作")
    try:
        # 业务逻辑
        result = business_logic()
        logger.info("事务操作成功")
        return result
    except Exception as e:
        logger.error(f"事务操作失败: {e}")
        raise
```

## 注意事项

1. **分布式事务**: 涉及外部服务时，使用 Saga 模式确保数据一致性
2. **补偿操作**: 补偿操作可能失败，需要监控和人工处理
3. **性能考虑**: 事务会增加数据库连接的使用时间，避免长时间事务
4. **异常传播**: 确保异常正确传播以触发回滚机制

## 监控和告警

建议监控以下指标：

1. **事务成功率**: 监控事务提交和回滚的比例
2. **补偿操作**: 监控补偿操作的执行情况
3. **异常日志**: 监控事务相关的异常日志
4. **性能指标**: 监控事务执行时间

## 故障排查

### 常见问题

1. **事务未回滚**: 检查异常是否正确抛出
2. **补偿操作失败**: 检查外部服务状态和网络连接
3. **会话泄漏**: 检查是否正确关闭数据库会话

### 调试技巧

1. 启用详细日志记录
2. 使用事务上下文管理器进行调试
3. 监控数据库连接池状态 