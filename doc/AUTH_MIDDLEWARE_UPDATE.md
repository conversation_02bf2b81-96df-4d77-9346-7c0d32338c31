# AuthMiddleware 更新说明

## 概述

`AuthMiddleware` 已经更新，集成了 `LoginVerifyClient` 来支持通过阿里云无影AI内部服务进行登录令牌验证。这为系统提供了更安全和标准化的用户认证方式。

## 更新内容

### 1. 新增依赖

- 集成了 `LoginVerifyClient` (基于 `wuyingaiinner-20250718`)
- 支持异步登录令牌验证

### 2. 认证方式

现在 `AuthMiddleware` 只支持一种认证方式：

#### 登录令牌验证
使用 `LoginVerifyClient` 验证登录令牌，获取 `ali_uid` 和 `wy_id`

**支持的参数:**
- `loginToken` - 登录令牌
- `session` - 会话ID (优先于 loginToken)
- `regionId` - 区域ID (可选)

**参数来源:**
- 查询参数: `?loginToken=xxx&regionId=xxx` 或 `?session=xxx&regionId=xxx`
- 请求头: `X-Login-Token`, `X-Session-Id`, `X-Region-Id`

## 使用方式

### 1. 基本使用

```python
from src.presentation.middleware.auth_middleware import require_auth, get_current_user
from fastapi import Depends

@app.get("/api/protected")
async def protected_endpoint(
    current_user: AuthContext = Depends(require_auth)
):
    return {
        "ali_uid": current_user.ali_uid,
        "wy_id": current_user.wy_id
    }
```

### 2. 登录令牌验证示例

#### 通过查询参数
```bash
# 使用登录令牌
GET /api/protected?loginToken=your_login_token&regionId=cn-hangzhou

# 使用会话ID (优先)
GET /api/protected?session=your_session_id&regionId=cn-hangzhou
```

#### 通过请求头
```bash
curl -H "X-Login-Token: your_login_token" \
     -H "X-Region-Id: cn-hangzhou" \
     http://localhost:8000/api/protected

curl -H "X-Session-Id: your_session_id" \
     -H "X-Region-Id: cn-hangzhou" \
     http://localhost:8000/api/protected
```



## 配置要求

### 1. 环境变量

为了使用 `LoginVerifyClient`，需要配置以下环境变量：

```bash
ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id
ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret
```

### 2. 依赖安装

确保已安装 `wuyingaiinner-20250718` 依赖：

```bash
cd src/popclients/wuyingaiinner-20250718
pip install .
```

## 错误处理

### 1. LoginVerifyClient 不可用

如果 `LoginVerifyClient` 初始化失败（如缺少凭证），系统会：
- 记录警告日志
- 自动回退到备用认证方式
- 不影响现有功能

### 2. 令牌验证失败

如果登录令牌验证失败，系统会：
- 记录验证失败信息
- 尝试其他认证方式
- 如果所有方式都失败，返回未认证状态

## 日志记录

新的认证流程会记录以下日志：

```
[AuthMiddleware] 开始验证登录令牌: session_id=xxx, region_id=xxx
[AuthMiddleware] 登录令牌验证成功: ali_uid=123456789, wy_id=test-wy-id
[AuthMiddleware] 登录令牌验证失败: Invalid token
[AuthMiddleware] 检测到登录令牌但LoginVerifyClient不可用，跳过令牌验证
```

## 性能考虑

### 1. 异步处理

登录令牌验证使用异步方式，不会阻塞请求处理。

### 2. 延迟初始化

`LoginVerifyClient` 采用延迟初始化，只在首次使用时创建。

### 3. 错误恢复

验证失败时快速回退到备用方式，确保服务可用性。

## 测试

### 1. 单元测试

运行集成测试：
```bash
python -m pytest tests/test_auth_middleware_integration.py -v
```

### 2. 示例服务

启动示例服务进行测试：
```bash
python examples/auth_middleware_usage_example.py
```

然后访问：
- `GET /api/auth/methods` - 查看支持的认证方式
- `GET /api/user/info` - 测试可选认证
- `GET /api/user/profile` - 测试必需认证

## 迁移指南

### 对现有代码的影响

1. **破坏性变更**: 移除了原有的Header和查询参数认证方式
2. **新增功能**: 支持登录令牌验证
3. **需要迁移**: 现有API调用需要改为使用登录令牌

### 必需的迁移步骤

1. **配置环境变量**: 设置阿里云凭证
2. **更新所有API调用**: 改为使用登录令牌认证
3. **测试新功能**: 确保登录令牌验证正常工作
4. **监控日志**: 观察认证流程和错误情况

## 故障排除

### 常见问题

1. **LoginVerifyClient 初始化失败**
   - 检查环境变量配置
   - 确认阿里云凭证有效性
   - 查看日志中的具体错误信息

2. **令牌验证失败**
   - 确认令牌格式正确
   - 检查区域ID是否匹配
   - 验证令牌是否过期

3. **网络连接问题**
   - 检查到阿里云服务的网络连接
   - 确认防火墙设置
   - 查看超时配置

### 调试技巧

1. **启用详细日志**:
   ```python
   import logging
   logging.getLogger("src.popclients.login_verify_client").setLevel(logging.DEBUG)
   ```

2. **手动测试验证**:
   ```bash
   curl -X POST http://localhost:8000/api/auth/verify \
        -H "Content-Type: application/json" \
        -d '{"loginToken": "your_token", "regionId": "cn-hangzhou"}'
   ```

## 总结

这次更新为 `AuthMiddleware` 添加了强大的登录令牌验证功能，同时保持了向后兼容性。新的认证方式提供了更好的安全性和标准化，推荐在新的集成中优先使用。
