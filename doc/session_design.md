# Session管理设计方案

## 一、背景与概述

### 1.1 背景

Agent类产品的会话一般情况下任务**执行时间长**，且在执行过程中用户可能**随时离开、随时重连**，在这个情况下还需要保证实时数据能持续以流式的形式输出。

对于Agent会话管理，重点关注以下三个方面：**1.会话生命周期；2.流式交互协议；3.鉴权(630比较简单)**  $\color{#0089FF}{@王磊(落筌)}$  **4.rag memory**

### 1.2 方案概述

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mPdnpEbVepMaeqw9/img/d1492d64-3a53-4b15-b3a6-d58b16abfdaa.png)

需要确认的：agent和记忆、Rag的交互应该都是waiy-infra中agent域自身行为，会话这层只做透传，避免职责重复、循环依赖。

如果对记忆、rag管理页面的API需要通过这层透出的话，这层只做用户鉴权，并向下面的领域透传命令，不干涉其他领域内的动作。

1.  流式交互：通过SSE+心跳保活建立流式通道；并基于Memory实现流式情况下的断线重连。
    
2.  生命周期管理：拆分Session生命周期，用状态机流转管理Session。
    
3.  可拔插插件：不同Agent可在不同生命周期配置不同插件，通过Agent元信息进行描述。比如在会话初始化过程中，不同Agent可配置选择使用SSO鉴权还是JWT鉴权；长期记忆是从向量召回还是标量召回。原则上，可选的流程沉淀在插件中，由agent配置来描述使用哪些插件；必须的流程，则直接写在类的模版方法中。

```java
1.通过注声明插件作用在什么生命周期上
@LifecyclePlugin(phases = {LifecyclePhase.ON_CREATE}, priority = 1)
public class JwtAuthenticationPlugin implements SessionLifecyclePlugin{
    @Override
    public PluginResult trigger(SessionContext context) {
        String token = context.getApiKey();
        if (!jwtValidator.validateToken(token, context.getUserId())) {
            return PluginResult.failure("JWT认证失败");
        }
        context.setAuthenticated(true);
        return PluginResult.success();
    }
}

2.在Session充血对象内，每次状态机流转时调用onXX()方法执行模版方法和配置的插件逻辑
public class Session{
 
    private String sessionId;
    private SessionStatusEnum curStatus;
    private SessionStatusEnum lastStatus;
    private List<Round> roundList;  // 修改为Round列表
    private String title;   
    private LLMUsage totalUsage;
  
    private Emitter activeEmitter;
   
    public void onCreate(String sessionId, String userId, String jwtToken) {
      // 获取上下文对象
      SessionContext context = ThreadLocalHolder.getCtx();
      // 执行Create生命周期的插件
      pluginManager.executePlugins(LifecyclePhase.ON_CREATE, context);
      
      if (!context.isAuthenticated()) {
          throw new AuthenticationException("认证失败");
      }
      // 创建会话状态并保存
      createAndSaveSession(context);
  }

    public void onXXX(String sessionId, String userId, String jwtToken) {
    
  }
}
```

## 二、会话生命周期管理

### 2.1 数据结构层次关系

基于api.md的定义，数据结构的层次关系为：**Session -> Round -> Message**

- **Session**: 会话级别状态管理
- **Round**: 对话轮次，每次用户发送消息产生一个Round
- **Message**: 具体消息，包括用户输入和AI各种类型的输出

### 2.2 State Machine

一个Agent会话的生命周期是：

**初始化（鉴权、建连） -> 活跃（等待用户输入、心跳处理）-> 处理中（执行任务、流式返回）-> 关闭**

而在活跃、处理中两个状态下，都有可能随时断连，断连也可能随时恢复到活跃和处理中两个状态。

状态机流转如下图所示：

```
stateDiagram-v2
    direction LR
    [*] --> Create : 创建会话
    Create --> Active : 初始化完成
    Active --> Processing : 处理请求
    Processing --> Active : 处理完成
    Active --> Disconnected : 网络断开/主动关闭
    Disconnected --> Active : 重新连接
    Processing --> Disconnected : 处理中断开连接
    Disconnected --> Processing : 重连恢复处理中任务
    Disconnected --> Closed : 断开超时归档
    Closed --> [*]
    
    note right of Create
        初始化session资源<br/>
        - 验证用户身份<br/>
        - 创建sessionId<br/>
        - 初始化AI Agent<br/>
        - 建立流式连接
    end note
    
    note right of Active
        用户在线且空闲<br/>
        - 等待用户输入<br/>
        - 处理心跳检测<br/>
        - 维护会话状态
    end note
    
    note right of Processing
        AI正在执行任务<br/>
        - 执行AI推理<br/>
        - 处理工具调用<br/>
        - 流式返回结果<br/>
        - Round状态为processing
    end note
    
    note right of Disconnected
        用户断开但会话保持<br/>
        - 保持任务运行<br/>
        - 缓存输出结果<br/>
        - 等待重连<br/>
        - 触发长期记忆提取
    end note

    note right of Closed
        主动关闭会话<br/>
        - 停止所有任务<br/>
        - 清理资源<br/>
    end note
```

### 2.3 Session状态定义和职责

#### 2.3.1 Create - 初始化

该阶段主要职责是鉴权和建连接，主要执行流程：

1.  身份验证：验证JWT token，检查用户权限
2.  创建会话对象：创建会话对象并持久化，生成sessionId
3.  上下文加载：从Memory模块中Load长期记忆，准备传入agent
4.  Agent初始化：加载agent定义，把用户query和长期记忆传入agent
5.  建立连接：建立SSE连接
6.  流转到Active状态

```python
def on_create(ali_uid, agent_id, prompt):
    # 1. 创建会话上下文
    session_id = generate_session_id()
    context = SessionContext(session_id, ali_uid, agent_id)
    
    # 2. 执行Create生命周期插件（配置的认证插件等）
    if not plugin_manager.execute_plugins(ON_CREATE, context):
        raise Exception("认证失败")
    
    # 3. 创建会话并持久化
    session = create_session(context)
    
    # 4. 创建第一个Round并处理用户消息
    round_id = create_round(session_id, prompt)
    
    # 5. 流转到Processing状态开始处理
    transition_to_processing(session, round_id)
```

#### 2.3.2 Active - 活跃

该阶段主要职责是等待并接收用户消息，以及通过心跳检测维护连接活跃性。主要执行流程：

1.  开启心跳检测：首次流转到该状态时，注册心跳检测，持续探活客户端维护连接活跃性，若断开心跳尝试重试，重试失败则流转到Disconnected状态
2.  消息传递：接收到用户消息，创建新的Round，流转到Processing状态
3.  等待消息：任务处理完成流转回Active状态，用户未断开连接，持续发送心跳保活，等待用户下次指令，收到消息执行步骤2

```python
def on_active(session):
    this.status = ACTIVE
    # 1. 执行Active生命周期插件
    plugin_manager.execute_plugins(ON_ACTIVE, session.context)
    
    # 2. 开启心跳检测
    def heartbeat_monitor():
        while session.status == ACTIVE:
            if time.now() - session.last_heartbeat > 90:
                # 心跳超时，尝试重试
                if not retry_heartbeat():
                    on_disconnected("HEARTBEAT_TIMEOUT")
                    break
            else:
                send_heartbeat()
            sleep(30)

def handle_user_message(session, prompt):
    # 创建新的Round并流转到Processing状态
    round_id = create_round(session.session_id, prompt)
    on_processing(session, round_id)
```

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 服务端
    participant HeartbeatMonitor as 心跳监控
    participant Agent as AI Agent

    Note over Client,Agent: Active状态 - 等待消息与心跳处理

    loop 心跳保活循环
        HeartbeatMonitor->>Client: 发送心跳ping
        Client-->>HeartbeatMonitor: 返回心跳pong
        HeartbeatMonitor->>HeartbeatMonitor: 更新最后心跳时间
        
        alt 心跳超时(90秒)
            HeartbeatMonitor->>Server: 检测到连续心跳失败
            Server->>Server: 流转到Disconnected状态
        end
    end

    par 等待用户消息
        Client->>Server: POST /api/sessions/send<br/>{sessionId, prompt, agentId}
        Server->>Server: 创建新Round
        Server->>Server: 流转到Processing状态
        Server->>Agent: 传递消息给Agent处理
    end
```

#### 2.3.3 Processing - 执行中

该阶段主要职责是执行Agent流程，处理工具调用并流式返回结果，同时维护Round状态为processing。

```python
def on_processing(session, round_id):
    session.status = PROCESSING
    # 1. 执行Processing生命周期插件
    plugin_manager.execute_plugins(ON_PROCESSING, session.context)
    
    # 2. 设置Round状态为processing
    update_round_status(round_id, "processing")
    
    # 3. 启动AI处理并获取流式输出
    ai_stream = get_ai_agent_stream(round_id)
    
    # 4. 处理流式输出，生成不同类型的Message
    for chunk in ai_stream:
        message = create_message(round_id, chunk)
        if is_disconnected():
            # 缓存消息等待重连
            cache_message(message)
        else:
            # 实时发送SSE事件
            send_sse_event("message", message)
    
    # 5. 处理完成，更新Round状态并流转回Active状态
    update_round_status(round_id, "completed")
    transition_to_active(session)
```

#### 2.3.4 Disconnected - 已断连

该阶段主要职责是，当用户断开连接时仍保持会话状态不丢失，用户退出时缓存AI输出结果，等待客户端重连。

进入Disconnected的情况有：   

1.  用户主动断连，进入Disconnected。此时又分为两种情况：
    1.  有正在执行中的Round（status=processing），断连需要将任务保持继续执行，并将结果缓存等待用户重连时下发
    2.  无正在执行中的Round，直接流转状态即可
2.  用户长时间无操作：由Active心跳检测出用户长时间无新消息，服务端主动断连，进入Disconnected
3.  长期记忆提取：每次会话断开连接，都判断当前会话轮数，若大于X轮，则向长期记忆模块发起记忆提取动作
4.  会话关闭：定时任务扫描长期（比如3天以上）处于Disconnected的会话，将它们置为close状态，close状态不再允许用户重连，并将会话信息归档至其他介质，较少存储成本

```python
def on_disconnected(session, reason):
    session.status = DISCONNECTED
    # 1. 执行Disconnect生命周期插件
    plugin_manager.execute_plugins(ON_DISCONNECT, session.context)
    
    # 2. 检查是否有processing状态的Round
    processing_rounds = get_processing_rounds(session.session_id)
    if processing_rounds:
        # 有正在处理的Round：保持任务继续执行，缓存结果
        for round_id in processing_rounds:
            print(f"Round {round_id} 将继续在后台执行")
    else:
        # 无正在处理的Round：直接流转状态即可
        print("活跃状态断连，保持会话状态等待重连")
    
    # 3. 判断是否需要提取长期记忆
    if session.total_rounds > 5:
        extract_long_term_memory(session)
```

##### ******* 断连恢复

由Disconnected可以恢复到Active和Processing两个状态，取决于断开期间有无进行中的Round。

| 目标状态 | 条件 | 恢复动作 | 数据同步 | 用户感知 |
| --- | --- | --- | --- | --- |
| Active | 无processing状态Round | 直接恢复连接 | 同步会话状态 | 连接已恢复 |
| Processing | 有processing状态Round | 恢复任务执行 | 推送缓存结果 | 接收未完成内容 |

```javascript
async handleReconnection(sessionId, roundId, lastMessageId) {
    // 1. 验证重连请求
    const isValid = await this.validateReconnection(sessionId);
    if (!isValid) {
        throw new Error('重连验证失败');
    }
    
    // 2. 检查Round状态
    const round = await this.getRound(roundId);
    
    // 3. 根据Round状态决定恢复目标
    if (round.status === 'processing') {
        return await this.recoverToProcessing(sessionId, roundId, lastMessageId);
    } else {
        return await this.recoverToActive(sessionId);
    }
}

// 有正在执行的Round，恢复到Processing状态
async recoverToProcessing(sessionId, roundId, lastMessageId) {
    // 恢复到Processing状态
    const cachedMessages = await this.getCachedMessages(roundId, lastMessageId);
    
    // 批量推送缓存的Message
    for (const message of cachedMessages) {
        await this.sendSSEEvent('message', message);
    }
    
    // 恢复实时流式输出
    const activeTask = this.backgroundTasks.get(roundId);
    if (activeTask && !activeTask.completed) {
        await this.resumeStreamOutput(activeTask);
    }
    
    return 'Processing';
}

// 没有正在执行的Round，恢复到Active状态  
async recoverToActive(sessionId) {
    // 恢复到Active状态
    await this.syncSessionState(sessionId);
    await this.restartHeartbeat();
    return 'Active';
}
```

**重连机制基于api.md的定义，使用三参数方案：sessionId + roundId + lastMessageId**

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 服务端
    participant Auth as 认证服务
    participant Memory as 记忆模块
    participant Agent as AI Agent

    Note over Client,Agent: Disconnected状态 - 断连恢复机制

    Client->>Server: 1. GET /api/sessions/stream?sessionId=xxx&roundId=yyy&lastMessageId=zzz
    Server->>Auth: 2. 验证重连权限
    Auth-->>Server: 3. 验证通过

    Server->>Server: 4. 检查Round状态
    
    alt Round状态为processing
        Server->>Memory: 5. 获取缓存的Message列表
        Memory-->>Server: 6. 返回Message列表
        
        loop 推送缓存内容
            Server->>Client: 7. SSE: event=message, data={messageId, content, ...}
            Client-->>Server: 8. 确认接收
        end
        
        Server->>Agent: 9. 检查后台任务状态
        alt 任务仍在运行
            Server->>Server: 10. 恢复实时流式输出
            Server->>Server: 11. 流转到Processing状态
        else 任务已完成
            Server->>Server: 12. 更新Round状态为completed
            Server->>Server: 13. 流转到Active状态
        end
        
    else Round状态为completed
        Server->>Server: 14. 同步会话状态
        Server->>Server: 15. 重启心跳监控
        Server->>Server: 16. 流转到Active状态
    end

    Server->>Memory: 17. 判断会话轮数，考虑记忆提取
    
    alt 会话轮数 > X轮
        Memory->>Memory: 18. 执行长期记忆提取
    end

    Server-->>Client: 19. SSE: event=heartbeat
```

#### 2.3.5 Closed - 已关闭

该阶段主要职责是，标记会话已完全终止，将相关数据归档，拒绝所有新的请求，只读。

## 三、API定义（流式交互协议）

基于api.md的接口定义，接口调用和生命周期流转的关系如下：

```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 服务端
    participant AI as AI引擎
    participant M as Memory模块
    
    Note over C,AI: 场景1：首次创建会话并对话
    
    C->>S: POST /api/sessions/send<br/>{sessionId: null, prompt: "你好", agentId: "agent_001"}
    Note over S: Session状态: Create → Processing
    S->>S: 创建Session和Round
    S-->>C: {sessionId: "sess_123", roundId: "1"}
    
    C->>S: GET /api/sessions/stream?sessionId=sess_123&roundId=1
    Note over S: 建立SSE连接
    S->>AI: 处理用户消息
    S-->>C: event: message<br/>data: {messageId: "1", role: "USER", content: {text: "你好"}}
    S-->>C: event: message<br/>data: {messageId: "2", role: "ASSISTANT", content: {text: "你"}, messageType: "llm_output"}
    S-->>C: event: message<br/>data: {messageId: "3", role: "ASSISTANT", content: {text: "好"}, messageType: "llm_output"}
    AI-->>S: 生成完成
    S-->>C: event: message<br/>data: {messageId: "4", role: "ASSISTANT", content: {title: "问候对话"}, messageType: "title"}
    S-->>C: event: done<br/>data: {roundId: 1, status: "completed"}
    Note over S: Session状态: Processing → Active
    
    Note over C,AI: 场景2：继续对话
    
    C->>S: POST /api/sessions/send<br/>{sessionId: "sess_123", prompt: "介绍你的功能", agentId: "agent_001"}
    Note over S: Session状态: Active → Processing
    S->>S: 创建新Round
    S-->>C: {sessionId: "sess_123", roundId: "2"}
    
    C->>S: GET /api/sessions/stream?sessionId=sess_123&roundId=2
    S->>AI: 处理用户消息
    S-->>C: event: message<br/>data: {messageId: "5", role: "USER", content: {text: "介绍你的功能"}}
    S-->>C: event: message<br/>data: {messageId: "6", role: "ASSISTANT", content: {text: "我"}, messageType: "llm_output"}
    S-->>C: event: message<br/>data: {messageId: "7", role: "ASSISTANT", content: {text: "是"}, messageType: "llm_output"}
    Note over C: 网络中断，连接丢失
    
    Note over C,AI: 场景3：断线重连 - 恢复流式
    
    C->>S: GET /api/sessions/query?sessionId=sess_123&limit=20
    S-->>C: {sessionId: "sess_123", rounds: [...], totalRounds: 2}
    Note over C: 检查最后Round状态为"processing"<br/>渲染历史消息到界面
    
    C->>S: GET /api/sessions/stream?sessionId=sess_123&roundId=2&lastMessageId=7
    Note over S: Session状态: Disconnected → Processing
    S->>M: 获取缓存的Messages
    M-->>S: 返回Messages
    Note over S: 从lastMessageId+1开始推送
    S-->>C: event: message<br/>data: {messageId: "8", role: "ASSISTANT", content: {text: "AI"}, messageType: "llm_output"}
    S-->>C: event: message<br/>data: {messageId: "9", role: "ASSISTANT", content: {text: "助手"}, messageType: "llm_output"}
    S-->>C: event: done<br/>data: {roundId: 2, status: "completed"}
    Note over S: Session状态: Processing → Active
```

### 核心接口说明

#### 1. 发送消息接口
- **URL**: `POST /api/sessions/send`
- **功能**: 创建Session（如果sessionId为空）或创建新Round
- **状态流转**: Create → Processing 或 Active → Processing

#### 2. 流式连接接口  
- **URL**: `GET /api/sessions/stream`
- **功能**: 建立SSE连接，流式推送Round内的所有Message
- **支持断线重连**: 通过lastMessageId参数从断点继续

#### 3. 历史消息接口
- **URL**: `GET /api/sessions/query`  
- **功能**: 获取Session的历史Round和Message，支持分页
- **用于重连前**: 检查Round状态，判断是否需要恢复流式连接

## 四、数据指标定义

| 维度 | 指标类别 | 指标名称 | 计算方式 |
| --- | --- | --- | --- |
| Agent | 性能指标 | 平均处理时间 | sum(Round处理耗时)/count(Round) |
|  |  | token per second | sum(tokenUsage)/time |
|  | 可用性指标 | 会话成功率 | 成功会话数/总会话数 |
|  |  | Round完成率 | completed状态Round数/总Round数 |
|  |  | 重连成功率 | 成功重连数/重连尝试数 |
|  | 成本指标 | 总token数 | sum(Round.totalUsage) |
| Session | 资源指标 | 总会话数 | 在平台上的所有Session数总和 |
|  |  | 活跃会话数 | 当前Active+Processing状态数 |
|  |  | 断连会话数 | 当前Disconnected状态数 |
|  |  | Processing轮次数 | 当前processing状态的Round数 |