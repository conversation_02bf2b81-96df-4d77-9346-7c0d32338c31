# OSS 无密钥认证更新说明

## 概述

本次更新为 OSS 客户端添加了阿里云无密钥认证支持，同时保持了向后兼容性。系统会优先尝试使用无密钥认证，如果失败则自动回退到传统的 AccessKey 认证方式。

## 更新内容

### 1. 配置文件更新

在 `properties.toml` 中添加了无密钥认证相关配置：

```toml
# 阿里云无密钥认证配置
ram_role_arn = ""
region_id = "cn-hangzhou"
app_group = ""
akless_env = ""
```

各环境的具体配置：
- **Daily 环境**: `akless_env = "daily"`
- **Pre 环境**: `akless_env = "pre"`
- **Prod 环境**: `akless_env = "prod"`

### 2. 配置管理类更新

在 `src/shared/config/environments.py` 中添加了对应的配置属性：

```python
@property
def ram_role_arn(self) -> str:
    return self._get_config_value("ram_role_arn", "")

@property
def region_id(self) -> str:
    return self._get_config_value("region_id", "cn-hangzhou")

@property
def app_group(self) -> str:
    return self._get_config_value("app_group", "")

@property
def akless_env(self) -> str:
    return self._get_config_value("akless_env", "")
```

### 3. OSS 客户端更新

#### 主要变更

1. **添加依赖**：引入了 `aliyunaklesscredprovider` 相关模块
2. **线程安全认证**：使用线程锁确保认证对象的线程安全
3. **双重认证策略**：优先使用无密钥认证，失败时回退到 AccessKey 认证

#### 认证流程

```python
def _get_auth(self):
    """
    获取 OSS 认证对象（线程安全）
    优先使用阿里云无密钥认证，失败时回退到AccessKey认证
    """
    if self._auth is None:
        with self._auth_lock:
            # 双重检查锁定模式
            if self._auth is None:
                # 首先尝试无密钥认证
                if self._try_akless_auth():
                    return self._auth
                
                # 无密钥认证失败，回退到AccessKey认证
                if self._try_access_key_auth():
                    return self._auth
                
                # 两种认证方式都失败
                raise OSSClientError("所有认证方式都失败了")
    return self._auth
```

### 4. 依赖更新

在 `pyproject.toml` 中添加了无密钥认证依赖：

```toml
"aliyun-akless-credential-provider-python-sdk",  # 阿里云无密钥认证
```

## 使用方式

### 在阿里云环境中

当应用部署在阿里云 ECS 实例或其他支持无密钥认证的环境中时：

1. 配置正确的 `ram_role_arn`、`app_group`、`akless_env` 等参数
2. 系统会自动使用无密钥认证
3. 无需配置 AccessKey

### 在本地开发环境中

在本地开发环境中，无密钥认证通常会失败：

1. 系统会自动回退到 AccessKey 认证
2. 需要配置正确的 `oss_access_key` 和 `oss_secret_key`
3. 开发体验不受影响

## 优势

1. **安全性提升**：在云环境中无需存储明文密钥
2. **向后兼容**：现有的 AccessKey 认证方式仍然可用
3. **自动回退**：认证失败时自动切换到备用方案
4. **线程安全**：使用线程锁确保多线程环境下的安全性
5. **透明切换**：对上层业务代码完全透明

## 注意事项

1. **环境要求**：无密钥认证需要在支持的阿里云环境中运行
2. **配置完整性**：确保无密钥认证相关配置的完整性
3. **权限配置**：确保 RAM 角色具有正确的 OSS 访问权限
4. **监控日志**：关注认证方式的切换日志，确保系统正常运行

## 测试验证

系统已通过测试验证：

- ✅ 无密钥认证配置正确读取
- ✅ 无密钥认证失败时自动回退
- ✅ AccessKey 认证正常工作
- ✅ OSS 连接和操作正常
- ✅ 线程安全性验证通过
