# is_update_selective 功能实现总结

## 功能概述

`is_update_selective` 是一个通用的更新控制参数，用于控制知识库更新时的字段处理行为。

## 参数说明

- **`is_update_selective: bool`** (默认值: `True`)
  - `True`: 选择性更新模式，传入 `None` 值的字段不会被更新
  - `False`: 非选择性更新模式，传入 `None` 值的字段会被置空

## 使用场景

### 1. 选择性更新 (is_update_selective=True)
```python
# 只更新名称，描述保持不变
update_knowledge_base(
    kb_id="kb_123",
    name="新名称",
    description=None,  # None 值会被忽略
    is_update_selective=True
)
```

### 2. 非选择性更新 (is_update_selective=False)
```python
# 更新名称，同时清空描述
update_knowledge_base(
    kb_id="kb_123",
    name="新名称",
    description=None,  # None 值会被置空
    is_update_selective=False
)
```

## API 使用示例

### Python 示例
```python
import requests

# 选择性更新（默认）
update_data = {
    "name": "新名称",
    "description": None,  # 会被忽略
    "is_update_selective": True
}

# 非选择性更新（清空描述）
clear_data = {
    "description": None,
    "is_update_selective": False
}

response = requests.put(f"{base_url}/api/knowledge_base/{kb_id}", 
                       json=clear_data, headers=headers)
```

### cURL 示例
```bash
# 清空描述
curl -X PUT "https://api.example.com/api/knowledge_base/kb_abc123" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{"description": null, "is_update_selective": false}'
```

## 实现细节

### 1. 数据库层面
```python
# 在 KnowledgeBaseRepository.update_knowledge_base 中
if name is not None or not is_update_selective:
    knowledge_base.name = name

if description is not None or not is_update_selective:
    knowledge_base.description = description
```

### 2. 服务层面
```python
# 在 KnowledgeService.update_knowledge_base 中
rag_name = name if name is not None or not is_update_selective else None
rag_description = description if description is not None or not is_update_selective else None
```

### 3. API 层面
```python
# 在 KnowledgeBaseUpdateRequest 中
is_update_selective: Optional[bool] = Field(True, description="是否选择性更新")
```

## 优势

1. **通用性**: 适用于所有可更新字段，不仅限于 description
2. **灵活性**: 可以根据需要选择更新模式
3. **向后兼容**: 默认值为 `True`，保持原有行为
4. **清晰性**: 参数名称明确表达了功能意图

## 测试覆盖

- ✅ 选择性更新测试（None 值被忽略）
- ✅ 非选择性更新测试（None 值被置空）
- ✅ 有值更新测试（正常更新）
- ✅ 权限验证测试
- ✅ 错误处理测试

## 相关文件

- 数据库仓库: `src/infrastructure/database/repositories/knowledgebase_repository.py`
- 服务层: `src/domain/services/knowledge_service.py`
- API 模型: `src/application/rag_api_models.py`
- API 路由: `src/presentation/api/routes/rag_routes.py`
- 测试用例: `tests/test_knowledge_service_new_methods.py`
- 使用示例: `examples/knowledge_service_new_methods_example.py`
- API 文档: `doc/knowledge_base_api_usage.md` 