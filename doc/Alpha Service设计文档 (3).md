# Alpha Service设计文档

## 一、背景

Cloud PC Alpha （后或简称为 Alpha / ⍺ ）致力于打造新一代“智能个人云电脑”，让每一位用户都拥有一个高度智能、无缝协作、深度理解个人需求的数字分身。我们希望通过自研的主Agent和云端算力深度集成，让用户能够像与真人助手一样，通过自然语言与AI对话，自动完成日常办公、数据管理、知识沉淀等各类任务，极大提升个人与团队的工作效率和体验。

[《Cloud PC Alpha（暂命名）产品说明》](https://alidocs.dingtalk.com/i/nodes/qnYMoO1rWxrkmoj2IKnz9eMaJ47Z3je9)

## 二、整体设计

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/J9LnW6jz37R6WlvD/img/eb6c33dd-eaa0-43db-9de3-e00b60c2bab3.png)

1.  会话管理：
    
    1.  流式会话：由于任务型Agent会话窗口较长，在基本的流式会话基础上，还需支持**会话重连接、断点续传**功能。
        
    2.  用量计费：对不同Agent的调用，消费不同积分点数。（Q：RAG向量化、文件存储是否也消耗点数？积分规则产品来出？）
        
    3.  会话TO知识：调用RAG能力，将会话中的关键内容**提取为知识**，embedding到知识库中。
        
2.  资源管理：
    
    1.  资源类型：
        
        1.  文件：调用文件服务，实现对文件的管理（批量上传、删除、搜索）
            
        2.  知识库：调用RAG-Service，实现对知识库的管理（创建、导入、删除），并跟踪知识库向量化状态。知识库中可以导入2.a.i中的文件，也可以导入1.中的会话。
            
        3.  制品：制品是特殊的文件，内容从Agent的消息中提取并上传至文件服务的特定路径(路径如：/user/task/sessionId/)。
            
3.  鉴权和分享：
    
    1.  鉴权：所有资源和会话，都需要进行鉴权。对资源进行统一抽象，实现RBAC对用户进行角色权限管理。
        
    2.  分享：所有资源和会话，在有权限的前提下，均可进行分享。
        

## 三、模块设计

### 3.1 会话管理

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mPdnpEbVepMaeqw9/img/d1492d64-3a53-4b15-b3a6-d58b16abfdaa.png)

流式交互协议、断线重连、断点续传等功能630已实现，方案见：[《Session & 流式交互设计文档》](https://alidocs.dingtalk.com/i/nodes/Qnp9zOoBVBDEydnQUGEYg7XZ81DK0g6l)

会话TO知识与知识库管理重合，见：[《Alpha Service设计文档》](https://alidocs.dingtalk.com/i/nodes/lyQod3RxJKe9QjOMiPyNd32lWkb4Mw9r?utm_scene=team_space&iframeQuery=anchorId%3Duu_mcsvd8gudqnsfj0q5pq)

计费从以下四个维度统计：Agent类型、Token消耗、RAG向量化文件大小(单位KB)、文件存储(单位MB)，加权得到账单。计算规则待产品给出。

### 3.2 资源管理

#### 3.2.1 文件管理

Alpha服务作为文件服务的透传层，主要负责文件结构的缓存管理和权限控制。文件的实际存储和管理由专门的文件服务负责，Alpha服务通过缓存机制提供快速的文件结构访问。

##### 架构设计

```mermaid
graph TB
    subgraph "Alpha Service"
        A[文件管理API] --> B[文件结构缓存]
        A --> C[权限校验]
        B --> D[缓存更新策略]
    end
    
    subgraph "文件服务"
        E[文件存储管理] --> F[OSS存储]
        E --> G[文件元数据]
        E --> H[文件操作API]
    end
    
    subgraph "消息队列"
        I[OSS变更消息]
    end
    
    subgraph "定时任务"
        J[定时缓存更新]
    end
    
    A --> E
    I --> D
    J --> D
    D --> B
```

##### 缓存更新策略

Alpha服务维护文件结构的本地缓存，缓存更新时机包括：

1. **定时更新**：每5分钟扫描一次文件结构变化
2. **OSS变更消息**：接收到文件服务的OSS变更通知消息
3. **写操作结束**：用户完成文件上传、删除、重命名等写操作后立即更新

```mermaid
sequenceDiagram
    participant U as 用户
    participant FE as Alpha前端
    participant BE as Alpha后端
    participant Cache as 文件结构缓存
    participant FS as 文件服务
    participant MQ as 消息队列
    participant Timer as 定时任务
    
    Note over Timer: 定时更新策略
    Timer->>BE: 触发定时更新
    BE->>FS: 获取最新文件结构
    FS->>BE: 返回文件列表
    BE->>Cache: 更新缓存
    
    Note over MQ: OSS变更消息策略
    MQ->>BE: 发送OSS变更消息
    BE->>Cache: 更新对应缓存
    
    Note over U: 用户操作策略
    U->>FE: 执行文件操作
    FE->>BE: 转发到文件服务
    BE->>FS: 执行文件操作
    FS->>BE: 返回操作结果
    BE->>Cache: 立即更新缓存
    BE->>FE: 返回操作结果
    FE->>U: 显示操作结果
```

##### 文件操作流程

用户可以在其OSS根目录下进行文件操作，包括：创建、删除、重命名、搜索、将文件添加到知识库。

```mermaid
sequenceDiagram
    participant U as 用户
    participant FE as Alpha前端
    participant BE as Alpha后端
    participant Cache as 文件结构缓存
    participant FS as 文件服务
    
    U->>FE: 请求文件操作
    FE->>BE: 转发请求
    
    BE->>BE: 权限校验
    alt 权限不足
        BE->>FE: 返回权限错误
        FE->>U: 显示权限错误
    else 权限验证通过
        BE->>Cache: 检查缓存
        alt 缓存命中
            BE->>FE: 返回缓存数据
            FE->>U: 显示文件列表
        else 缓存未命中
            BE->>FS: 请求文件服务
            FS->>BE: 返回文件数据
            BE->>Cache: 更新缓存
            BE->>FE: 返回文件数据
            FE->>U: 显示文件列表
        end
    end
```

##### 缓存数据结构

文件结构缓存包含以下信息：

| 字段名 | 字段类型 | 说明 |
| --- | --- | --- |
| file_id | bigint | 文件唯一标识符 |
| filename | varchar(255) | 文件名 |
| file_path | varchar(500) | 文件存储路径 |
| file_size | bigint | 文件大小（字节） |
| file_type | varchar(100) | 类型file/folder |
| support_operation | varchar(255) | 支持的操作 |
| owner_id | bigint | 文件所有者ID |
| parent_id | bigint | 父文件夹ID（根目录为null） |
| gmt_created | timestamp | 文件创建时间 |
| gmt_modified | timestamp | 文件最后修改时间 |
| cache_version | bigint | 缓存版本号 |
| last_sync_time | timestamp | 最后同步时间 |

##### 缓存一致性保证

1. **版本控制**：每个缓存条目都有版本号，确保数据一致性
2. **分布式锁**：写操作时获取分布式锁，避免并发冲突
3. **消息队列**：通过消息队列确保OSS变更及时通知
4. **定时补偿**：定时任务作为兜底机制，确保缓存最终一致性

##### 性能优化

1. **分层缓存**：内存缓存 + Redis缓存，提高访问速度
2. **增量更新**：只更新变更的文件结构，减少网络开销
3. **预加载**：用户访问时预加载子目录结构
4. **压缩传输**：文件列表传输时进行压缩，减少带宽消耗

不同文件类型，是否支持预览、通过浏览器打开、添加到知识库见表格：[《Alpha文件夹&知识库交互设计》](https://alidocs.dingtalk.com/i/nodes/XPwkYGxZV347LdvpHE0DEDq7JAgozOKL?utm_scene=team_space&iframeQuery=anchorId%3Duu_mclcnotqksjimjo3q)，在上传时根据文件类型进行校验，API下发给前端时将support_operation下发控制前端的行为。

#### 3.2.2 知识库管理

提供对下层RAG-Service基础能力的业务封装，包含：创建知识库、向知识库中添加会话/文件、获取知识库列表、查看知识库概览、查看知识库下的文件/会话列表和详情、最近操作记录、修改知识库元信息。

底层RAG服务不存储原始文件/会话的数据和映射关系，产品上需要展示数据的由alpha服务记录，通过以下表结构来支持数据的读写：s

```mermaid
erDiagram
    KNOWLEDGE_BASES ||--o{ KB_DOCUMENTS : contains
    KNOWLEDGE_BASES ||--o{ KB_SESSIONS : contains
    KNOWLEDGE_BASES ||--o{ KB_OPERATION_LOGS : tracks
    USERS ||--o{ KNOWLEDGE_BASES : owns
    FILES ||--o{ KB_DOCUMENTS : references
    SESSIONS ||--o{ KB_SESSIONS : references
    
    KNOWLEDGE_BASES {
        bigint id PK
        string name
        text description
        bigint owner_id FK
        string embedding_model
        int document_count
        int session_count
        string status
        timestamp gmt_created
        timestamp gmt_modified
    }
    
    KB_DOCUMENTS {
        bigint id PK
        bigint kb_id FK
        bigint file_id FK
        string document_title
        text content_summary
        string embedding_status
        jsonb metadata
        timestamp gmt_created
        timestamp gmt_modified
    }
    
    KB_SESSIONS {
        bigint id PK
        bigint kb_id FK
        bigint session_id FK
        string session_title
        string status
        jsonb metadata
        timestamp gmt_created
        timestamp gmt_modified
    }
    
    KB_OPERATION_LOGS {
        bigint id PK
        bigint kb_id FK
        bigint user_id FK
        string operation_type
        string target_type
        bigint target_id
        string operation_details
        timestamp gmt_created
    }
    
    USERS {
        bigint ali_uid PK
    }
    
    FILES {
        bigint id PK
        string filename
        string oss_path
    }
    
    SESSIONS {
        bigint id PK
        string title
        bigint user_id FK
    }
```

将文件、会话添加到知识库中的时序图如下所示：

```mermaid
sequenceDiagram
    participant U as 用户
    participant FE as Alpha前端
    participant BE as Alpha后端
    participant RAG as RAG Service
    participant DB as 数据库
    participant SS as Memory
    
    U->>FE: 打开添加到知识库页面
    FE->>BE: 获取知识库列表
    BE->>RAG: 获取可用知识库列表
    RAG->>BE: 返回知识库列表
    BE->>FE: 返回知识库数据
    FE->>U: 显示知识库选择界面
    
    U->>FE: 选择知识库和会话
    FE->>BE: 提交添加请求
    
    BE->>BE: 会话权限校验
    alt 无访问权限
        BE->>FE: 返回权限错误
        FE->>U: 显示权限错误提示
    else 权限验证通过
        BE->>SS: 获取会话内容
        SS->>BE: 返回会话数据
        
        BE->>DB: 创建KB_SESSIONS记录
        BE->>RAG: 发起向量化请求
        RAG->>BE: 返回SessionID
        BE->>FE: 返回SessionID
        FE->>U: 显示处理中状态
        
        Note over RAG: 进行会话内容解析<br/>提取关键信息和向量化处理
        
        loop 轮询检查进度
            FE->>BE: 查询处理进度
            BE->>RAG: 查询任务状态
            RAG->>BE: 返回当前状态和进度
            BE->>FE: 返回进度信息
            FE->>U: 更新进度显示
        end
        
        Note over RAG: 向量化完成
        
        FE->>BE: 最后一次查询状态
        BE->>RAG: 查询任务状态
        RAG->>BE: 返回完成状态
        BE->>DB: 更新知识库记录
        BE->>DB: 记录操作日志
        BE->>FE: 返回完成状态
        FE->>U: 显示添加成功
    end
```

#### 3.2.3 制品管理

现状：在当前Waiy-Infra的实现中，制品类型只有html，且制品会随消息一起下发给前端，如直接将HTML文本返回给调用端。

1.  Wayi-Infra直接返回制品文本：Memory中增加“制品”类的MessageType，在Alpha中收到制品消息后，首先从消息中提取制品内容（大模型可能在HTML前后添加一些文字描述，需要从中清洗出干净的HTML），然后上传到用户的制品OSS路径下(/user/task/artifacts/xxx.html)。
    
2.  Wayi-Infra直接返回制品文件：基本同上图，MQ中返回的直接是制品OSS链接，无需业务层服务对制品做提取操作。
    
3.  制品在AgentBay中制作：Waiy-Infra写AgentBay Context，并把结果制品文件名通过mq下发下来，我这从mq中解析结果文件名，从context oss里过滤出结果和过程制品，维护到DB关系中
    

![artifacts.drawio.svg](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/J9LnW6jz37R6WlvD/img/0b375112-6b10-49f7-ba5f-64b06311f77c.svg)

### 3.3 鉴权和分享

#### 3.3.1 资源鉴权 & 分享

实现简化的RBAC资源鉴权，定义：

*   三种角色：Admin：管理所有用户和资源；User：管理个人资源，可分享和接收分享；Guest：只能查看公开资源和被分享的资源
    
*   四种资源：会话、知识库、文件、制品
    

##### 权限规则

| 操作 | 资源所有者 | 被分享用户 | 其他用户 | 管理员 |
| --- | --- | --- | --- | --- |
| 查看 | ✅ | ✅（如有分享） | ✅（仅公开资源） | ✅ |
| 编辑 | ✅ | ✅（如有写权限） | ❌ | ✅ |
| 删除 | ✅ | ❌ | ❌ | ✅ |
| 分享 | ✅ | ❌ | ❌ | ✅ |

**权限模型**

为了简化说明，这图里先用RESOURCES代表四种资源，实际RESOURCES为会话、知识库、文件、制品这四种类型

```mermaid
erDiagram
    USERS ||--o{ RESOURCES : owns
    USERS ||--o{ RESOURCE_SHARES : shares
    USERS ||--o{ RESOURCE_SHARES : receives
    
    USERS {
        bigint ali_uid PK
        string username
        boolean is_admin
        timestamp gmt_created
    }
    
    RESOURCES {
        bigint id PK
        string resource_type
        bigint resource_ref_id
        bigint owner_id FK
        boolean is_public
        timestamp gmt_created
    }
    
    RESOURCE_SHARES {
        bigint id PK
        bigint resource_id FK
        bigint shared_to_id FK
        boolean can_write
        timestamp gmt_created
        timestamp gmt_expired
    }

```

**统一的资源鉴权逻辑**

```python
    def has_permission(self, user_id: int, resource_type: str, resource_id: int, operation: str) -> bool:
        # 1. 管理员拥有所有权限
        if self.is_admin(user_id):
            return True
        
        # 2. 检查资源所有者
        if self.is_resource_owner(user_id, resource_type, resource_id):
            return True
        
        # 3. 检查公开资源的读权限
        if operation == "read" and self.is_resource_public(resource_type, resource_id):
            return True
        
        # 4. 检查分享权限
        return self.has_share_permission(user_id, resource_type, resource_id, operation)
```

#### 3.3.2 用户账号体系

客户端<---LoginToken--->网关<----->alpha-service<-----JWT----->waiy-infra-service<---API KEY--->agentbay

                                                                  ^--AK/SK--waiy-memory--AK/SK--^

这里需要分工后再细化一下，没找到无影LoginToken的文档

## 四、ER关系

```mermaid
erDiagram
    %% 用户表在顶部
    USERS {
        bigint ali_uid PK "阿里云用户ID"
        string username "用户名"
        boolean is_admin "是否管理员"
        timestamp gmt_created "创建时间"
    }
    
    %% 四种核心资源表在同一行
    SESSIONS {
        bigint id PK "自增主键"
        string session_id UK "会话ID"
        string ali_uid FK "阿里云用户ID"
        string agent_id "Agent ID"
        string title "会话标题"
        string status "会话状态"
        json metadata "元数据JSON"
        boolean is_public "是否公开"
        timestamp gmt_create "创建时间"
        timestamp gmt_modified "修改时间"
    }
    
    FILES {
        bigint file_id PK "文件唯一标识符"
        string filename "文件名"
        string file_path "文件存储路径"
        bigint file_size "文件大小字节"
        string file_type "类型file/folder"
        string support_operation "支持的操作"
        bigint owner_id FK "文件所有者ID"
        bigint parent_id FK "父文件夹ID"
        boolean is_public "是否公开"
        timestamp gmt_created "文件创建时间"
        timestamp gmt_modified "文件最后修改时间"
    }
    
    KNOWLEDGE_BASES {
        bigint id PK "知识库ID"
        string name "知识库名称"
        text description "知识库描述"
        bigint owner_id FK "所有者ID"
        string embedding_model "嵌入模型"
        int document_count "文档数量"
        int session_count "会话数量"
        string status "状态"
        boolean is_public "是否公开"
        timestamp gmt_created "创建时间"
        timestamp gmt_modified "修改时间"
    }
    
    ARTIFACTS {
        bigint id PK "制品ID"
        string artifact_name "制品名称"
        string artifact_type "制品类型html/excel等"
        string oss_path "OSS存储路径"
        bigint session_id FK "来源会话ID"
        bigint owner_id FK "所有者ID"
        string content_summary "内容摘要"
        boolean is_public "是否公开"
        timestamp gmt_created "创建时间"
        timestamp gmt_modified "修改时间"
    }
    
    %% 知识库相关表组合在一起
    KB_DOCUMENTS {
        bigint id PK "文档关联ID"
        bigint kb_id FK "知识库ID"
        bigint file_id FK "文件ID"
        string document_title "文档标题"
        text content_summary "内容摘要"
        string vector_status "向量化状态"
        json metadata "元数据"
        timestamp gmt_created "创建时间"
        timestamp gmt_modified "修改时间"
    }
    
    KB_SESSIONS {
        bigint id PK "会话关联ID"
        bigint kb_id FK "知识库ID"
        bigint session_id FK "会话ID"
        string session_title "会话标题"
        string status "状态"
        json metadata "元数据"
        timestamp gmt_created "创建时间"
        timestamp gmt_modified "修改时间"
    }
    
    KB_OPERATION_LOGS {
        bigint id PK "操作日志ID"
        bigint kb_id FK "知识库ID"
        bigint user_id FK "用户ID"
        string operation_type "操作类型"
        string target_type "目标类型"
        bigint target_id "目标ID"
        string operation_details "操作详情"
        timestamp gmt_created "创建时间"
    }
    
    %% 权限分享表在底部
    RESOURCE_SHARES {
        bigint id PK "分享记录ID"
        string resource_type "资源类型session/file/kb/artifact"
        bigint resource_id "资源ID"
        bigint shared_by_id FK "分享者ID"
        bigint shared_to_id FK "分享给用户ID"
        boolean can_write "是否可写"
        timestamp gmt_created "创建时间"
        timestamp gmt_expired "过期时间"
    }
    
    %% 用户与四大资源的关系
    USERS ||--o{ SESSIONS : creates
    USERS ||--o{ FILES : owns  
    USERS ||--o{ KNOWLEDGE_BASES : owns
    USERS ||--o{ ARTIFACTS : owns
    
    %% 资源间的关系
    SESSIONS ||--o{ ARTIFACTS : generates
    FILES ||--o{ FILES : parent_child
    
    %% 知识库相关的关系集中定义
    KNOWLEDGE_BASES ||--o{ KB_DOCUMENTS : contains
    KNOWLEDGE_BASES ||--o{ KB_SESSIONS : contains  
    KNOWLEDGE_BASES ||--o{ KB_OPERATION_LOGS : tracks
    FILES ||--o{ KB_DOCUMENTS : references
    SESSIONS ||--o{ KB_SESSIONS : references
    USERS ||--o{ KB_OPERATION_LOGS : operates
    
    %% 权限分享关系
    USERS ||--o{ RESOURCE_SHARES : shares
    USERS ||--o{ RESOURCE_SHARES : receives
```

## 五、分工排期

| 工作项 |  | 子任务 | owner | 时间 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 新服务搭建<br>（工程初始化、中间件对接等） |  |  | 沈浪 | 6.30<br>Done |  |
| 会话管理 | 流式交互 | 对接Waiy-Infra异步接口 | 沈浪 |  |  |
|  |  | 对接MemorySDK |  |  |  |
|  |  | 断线重连 & 断点续传 |  |  |  |
|  |  | 生命周期管理 |  |  |  |
|  | 会话TO知识 | 对接Rag-Service |  |  |  |
|  | 用量计费 | 积分点数 |  |  |  |
| 资源管理 | 文件管理 | 查看目录(listDir) |  |  |  |
|  |  | 创建目录(createDir) |  |  |  |
|  |  | 上传文件(uploadFile 带进度条) |  |  |  |
|  |  | 搜索文件(searchFile) |  |  |  |
|  |  | 重命名文件(renameFile) |  |  |  |
|  |  | 文件详情、预览 |  |  |  |
|  |  | 存储空间管理(storage) |  |  |  |
|  | 知识库管理 | 文件 TO 知识库 |  |  |  |
|  |  | 会话 TO 知识库 |  |  |  |
|  |  | 知识库概览 |  |  |  |
|  |  | 知识库详情 |  |  |  |
|  |  | 知识库操作日志 |  |  |  |
|  | 制品管理 | 制品提取 |  |  |  |
|  |  | 制品上传OSS |  |  |  |
| 鉴权和分享 | 资源鉴权 | 统一资源鉴权RBAC实现 |  |  |  |
|  | 资源分享 | 文件、知识库分享 |  |  |  |
|  |  | 会话分享回放 |  |  |  |
|  | 账号体系 | 账号鉴权 |  |  |  |