# 鉴权管理API接口定义

## API设计原则

本API采用传统的设计风格：
- 所有请求统一使用POST方法
- 在URL中明确表示操作类型：`/check`、`/grant`、`/revoke`、`/list`
- 参数通过请求体传递，不在URL中拼接
- 操作语义清晰，便于理解和维护

## 1. 权限检查

### 1.1 检查用户权限
```
POST /api/v1/auth/check
```

**请求参数：**
```json
{
  "user_id": 456,
  "resource_type": "file",
  "resource_id": 123,
  "operation": "read"
}
```

**参数说明：**
- `user_id` (required, bigint): 用户ID
- `resource_type` (required, string): 资源类型，可选值：file/knowledge_base/session/artifact
- `resource_id` (required, bigint): 资源ID
- `operation` (required, string): 操作类型，可选值：read/write/delete/share

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "has_permission": true,
    "permission_type": "owner",
    "reason": "用户是资源所有者"
  }
}
```

### 1.2 批量检查权限
```
POST /api/v1/auth/check/batch
```

**请求参数：**
```json
{
  "user_id": 456,
  "resources": [
    {
      "resource_type": "file",
      "resource_id": 123,
      "operation": "read"
    },
    {
      "resource_type": "knowledge_base",
      "resource_id": 456,
      "operation": "write"
    }
  ]
}
```

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "results": [
      {
        "resource_type": "file",
        "resource_id": 123,
        "operation": "read",
        "has_permission": true,
        "permission_type": "owner"
      },
      {
        "resource_type": "knowledge_base",
        "resource_id": 456,
        "operation": "write",
        "has_permission": false,
        "permission_type": "none",
        "reason": "无权限"
      }
    ]
  }
}
```

## 2. 资源分享管理

### 2.1 分享资源
```
POST /api/v1/auth/share
```

**请求参数：**
```json
{
  "resource_type": "file",
  "resource_id": 123,
  "shared_by_id": 456,
  "shared_to_id": 789,
  "can_write": true,
  "expires_at": "2024-12-31T23:59:59Z"
}
```

**参数说明：**
- `resource_type` (required, string): 资源类型
- `resource_id` (required, bigint): 资源ID
- `shared_by_id` (required, bigint): 分享者用户ID
- `shared_to_id` (required, bigint): 被分享用户ID
- `can_write` (optional, boolean): 是否可写，默认false
- `expires_at` (optional, string): 过期时间，不传则永不过期

**响应格式：**
```json
{
  "code": 200,
  "message": "分享成功",
  "data": {
    "share_id": 1,
    "resource_type": "file",
    "resource_id": 123,
    "shared_by_id": 456,
    "shared_to_id": 789,
    "can_write": true,
    "gmt_created": "2024-01-01T00:00:00Z",
    "expires_at": "2024-12-31T23:59:59Z"
  }
}
```

### 2.2 取消分享
```
POST /api/v1/auth/share/revoke
```

**请求参数：**
```json
{
  "share_id": 1
}
```

**响应格式：**
```json
{
  "code": 200,
  "message": "取消分享成功",
  "data": null
}
```

### 2.3 获取分享列表
```
POST /api/v1/auth/share/list
```

**请求参数：**
```json
{
  "user_id": 456,
  "share_type": "shared_by",
  "resource_type": "file",
  "page": 1,
  "page_size": 20
}
```

**参数说明：**
- `user_id` (required, bigint): 用户ID
- `share_type` (required, string): 分享类型，可选值：shared_by（我分享的）/shared_to（分享给我的）
- `resource_type` (optional, string): 资源类型过滤
- `page` (optional, int): 页码，默认1
- `page_size` (optional, int): 每页数量，默认20

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "shares": [
      {
        "share_id": 1,
        "resource_type": "file",
        "resource_id": 123,
        "resource_name": "文档.pdf",
        "shared_by_id": 456,
        "shared_by_name": "张三",
        "shared_to_id": 789,
        "shared_to_name": "李四",
        "can_write": true,
        "gmt_created": "2024-01-01T00:00:00Z",
        "expires_at": "2024-12-31T23:59:59Z"
      }
    ],
    "total": 50,
    "page": 1,
    "page_size": 20
  }
}
```

## 3. 资源公开设置

### 3.1 设置资源公开状态
```
POST /api/v1/auth/public/set
```

**请求参数：**
```json
{
  "resource_type": "file",
  "resource_id": 123,
  "is_public": true
}
```

**响应格式：**
```json
{
  "code": 200,
  "message": "设置成功",
  "data": {
    "resource_type": "file",
    "resource_id": 123,
    "is_public": true,
    "gmt_modified": "2024-01-01T00:00:00Z"
  }
}
```

### 3.2 获取公开资源列表
```
POST /api/v1/auth/public/list
```

**请求参数：**
```json
{
  "resource_type": "file",
  "page": 1,
  "page_size": 20
}
```

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "resources": [
      {
        "resource_type": "file",
        "resource_id": 123,
        "resource_name": "公开文档.pdf",
        "owner_id": 456,
        "owner_name": "张三",
        "gmt_created": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20
  }
}
```

## 4. 用户角色管理

### 4.1 检查用户是否为管理员
```
POST /api/v1/auth/admin/check
```

**请求参数：**
```json
{
  "user_id": 456
}
```

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "user_id": 456,
    "is_admin": true,
    "role": "admin"
  }
}
```

### 4.2 设置用户角色
```
POST /api/v1/auth/admin/set
```

**请求参数：**
```json
{
  "user_id": 456,
  "role": "admin"
}
```

**参数说明：**
- `role` (required, string): 角色类型，可选值：admin/user/guest

**响应格式：**
```json
{
  "code": 200,
  "message": "角色设置成功",
  "data": {
    "user_id": 456,
    "role": "admin",
    "gmt_modified": "2024-01-01T00:00:00Z"
  }
}
```

## 5. 权限审计

### 5.1 获取权限操作日志
```
POST /api/v1/auth/logs/list
```

**请求参数：**
```json
{
  "user_id": 456,
  "resource_type": "file",
  "operation": "read",
  "start_time": "2024-01-01T00:00:00Z",
  "end_time": "2024-01-31T23:59:59Z",
  "page": 1,
  "page_size": 20
}
```

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "logs": [
      {
        "id": 1,
        "user_id": 456,
        "resource_type": "file",
        "resource_id": 123,
        "operation": "read",
        "result": "allowed",
        "permission_type": "owner",
        "ip_address": "***********",
        "user_agent": "Mozilla/5.0...",
        "gmt_created": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1000,
    "page": 1,
    "page_size": 20
  }
}
```

## 6. 错误码定义

| 错误码 | 说明 |
|--------|------|
| 60001 | 用户不存在 |
| 60002 | 资源不存在 |
| 60003 | 资源类型不支持 |
| 60004 | 操作类型不支持 |
| 60005 | 无权限执行操作 |
| 60006 | 分享记录不存在 |
| 60007 | 分享已过期 |
| 60008 | 用户角色无效 |
| 60009 | 资源所有者不匹配 |
| 60010 | 分享目标用户不存在 |
| 60011 | 资源已被分享给该用户 |
| 60012 | 公开资源设置失败 |
| 60013 | 权限检查失败 |
| 60014 | 批量权限检查部分失败 |
| 60015 | 权限日志记录失败 | 