# AIPPT Service 设计文档

### 2.2 时序图

**PPT生成与制品管理**

```mermaid
sequenceDiagram
    participant Frontend as 前端
    participant AlphaService as Alpha-Service
    participant AIPPTAgent as AIPPT-Agent
    participant AIPPTService as AIPPT-Service

    Note over Frontend, AIPPTService: PPT生成
    
    %% 第一阶段：用户认证与初始化
    Frontend->>AlphaService: 1. 请求认证code
    Note right of Frontend: 携带用户token
    AlphaService->>AIPPTService: 调用鉴权接口
    AIPPTService-->>AlphaService: 返回认证code
    AlphaService-->>Frontend: 返回认证code和有效期
    
    %% 第二阶段：PPT大纲生成（异步）
    Frontend->>AlphaService: 2. 请求生成PPT大纲
    Note right of Frontend: POST /apps/aippt/message/async<br/>携带PPT需求描述
    AlphaService->>AIPPTAgent: 转发请求到AIPPT Agent
    AlphaService-->>Frontend: 立即返回session_id和处理状态
    Note left of AlphaService: 异步处理，返回processing状态
    
    Note over AIPPTAgent: 多智能体协作生成大纲
    AIPPTAgent->>AIPPTAgent: 生成PPT大纲
    AIPPTAgent-->>AlphaService: 大纲生成完成
    AlphaService-->>Frontend: 推送大纲结果
    
    %% 第三阶段：前端展示大纲并确认
    Frontend->>Frontend: 展示PPT大纲给用户
    Frontend->>Frontend: 用户确认生成PPT
    
    %% 第四阶段：调用AIPPT Service生成PPT
    Frontend->>AIPPTService: 3. 选择模板、生成PPT
    Note right of Frontend: 前端集成AIPPT UI框架<br/>传入生成的大纲
    AIPPTService->>AIPPTService: 基于大纲生成PPT
    AIPPTService-->>Frontend: 返回作品ID
    Note left of AIPPTService: 包含作品ID等信息
    
    Note over Frontend, AIPPTService: 保存PPT
    %% 第五阶段：保存PPT到制品管理  
    Frontend->>AIPPTService: 4. 保存PPT
    Note right of Frontend: 确保PPT处于最新状态
    AIPPTService-->>Frontend: 返回保存确认
    
    Frontend->>AlphaService: 5. 保存PPT
    Note right of Frontend: 携带作品ID和封面图
    
    AlphaService->>AIPPTService: 根据作品ID获取PPT文件
    AIPPTService-->>AlphaService: 返回PPT下载链接
    
    AlphaService->>AlphaService: 保存到制品仓库
    Note right of AlphaService: 保存PPT本体和封面到制品仓库
    
    AlphaService-->>Frontend: 返回保存状态
    
    Note over Frontend, AIPPTService: 下载PPT
    %% 第六阶段：下载PPT
    Frontend->>AIPPTService: 6. 保存PPT
    Note right of Frontend: 确保获取最新版本PPT
    AIPPTService-->>Frontend: 返回保存确认
    
    Frontend->>AlphaService: 7. 下载PPT
    Note right of Frontend: 携带作品ID和封面图

    AlphaService->>AIPPTService: 根据作品ID获取PPT文件
    AIPPTService-->>AlphaService: 返回PPT下载链接
    
    AlphaService->>AlphaService: 保存到制品仓库
    Note right of AlphaService: 保存PPT本体和封面到制品仓库
    
    AlphaService-->>Frontend: 返回PPT下载链接
```

:::

## 接口设计

### 3.1 GetPPTAuthCode 获取认证code

#### 入参

| **参数** | **类型** | **必填** | **描述** | **备注** |
| --- | --- | --- | --- | --- |
| LoginToken | String | *   [x] | 登录令牌（查询参数方式） |  |
| LoginSessionId | String | *   [x] | 会话ID（查询参数方式） |  |
| RegionId | String | *   [x] | 区域ID（查询参数方式） |  |

#### 出参

| **参数** | **类型** | **描述** |
| --- | --- | --- |
| RequestId | String | 请求ID |
| Code | Integer | 返回码 |
| Message | String | 返回信息 |
| Data | Object |  |
| Code | String | 认证code |
| TimeExpire | String | 认证code有效期 |

### 3.2 获取PPT大纲

参考 [《会话及主Agent API定义》](https://alidocs.dingtalk.com/i/nodes/mExel2BLV59rgdDPiEPeGjPyVgk9rpMq?corpId=dingd8e1123006514592&utm_medium=im_card&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&utm_scene=team_space&utm_source=im)

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/8K4nyeZX0mEzonLb/img/37663376-64b7-48eb-ac4b-e5c0fd8d1914.png)![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/8K4nyeZX0mEzonLb/img/a124cdcc-e02f-469a-9afa-d7f777c038a7.png)

### 3.3 SavePPT 保存PPT

#### 入参

| **参数** | **类型** | **必填** | **描述** | **备注** |
| --- | --- | --- | --- | --- |
| LoginToken | String | *   [x] | 登录令牌（查询参数方式） |  |
| LoginSessionId | String | *   [x] | 会话ID（查询参数方式） |  |
| RegionId | String | *   [x] | 区域ID（查询参数方式） |  |
| FileId | String | *   [x] | 作品ID |  |
| ThumbnailUrl | String | *   [ ] | 封面图url |  |

#### 出参

| **参数** | **类型** | **描述** |
| --- | --- | --- |
| RequestId | String | 请求ID |
| Code | Integer | 返回码 |
| Message | String | 返回信息 |

### 3.4 DownloadPPT 下载PPT

#### 入参

| **参数** | **类型** | **必填** | **描述** | **备注** |
| --- | --- | --- | --- | --- |
| LoginToken | String | *   [x] | 登录令牌（查询参数方式） |  |
| LoginSessionId | String | *   [x] | 会话ID（查询参数方式） |  |
| RegionId | String | *   [x] | 区域ID（查询参数方式） |  |
| FileId | String | *   [x] | 作品ID |  |
| ThumbnailUrl | String | *   [ ] | 封面图url |  |

#### 出参

| **参数** | **类型** | **描述** |
| --- | --- | --- |
| RequestId | String | 请求ID |
| Code | Integer | 返回码 |
| Message | String | 返回信息 |
| Data | String | 作品下载url |

### 3.5 GetPPTThumbnail 获取PPT封面图

#### 入参

| **参数** | **类型** | **必填** | **描述** | **备注** |
| --- | --- | --- | --- | --- |
| LoginToken | String | *   [x] | 登录令牌（查询参数方式） |  |
| LoginSessionId | String | *   [x] | 会话ID（查询参数方式） |  |
| RegionId | String | *   [x] | 区域ID（查询参数方式） |  |
| FileId | String | *   [x] | 作品ID |  |

#### 出参

| **参数** | **类型** | **描述** |
| --- | --- | --- |
| RequestId | String | 请求ID |
| Code | Integer | 返回码 |
| Message | String | 返回信息 |
| Data | String | 封面图url |

## 制品管理

通过消息转存

```python
 memory.add_event(
    ArtifactEvent(
        session_id=self.session_id,
        run_id=self.trace_id,
        artifact_type="download_url",
        file_type="ppt",
        file_name="xxxxx.ppt",
        content="oss_url",
        description="测试用的artifact",
        is_process_file=False
    ),
    self.session_id,
)
```