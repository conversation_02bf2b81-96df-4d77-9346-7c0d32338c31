# AuthMiddleware 单一认证方式说明

## 概述

`AuthMiddleware` 已更新为只支持一种认证方式：**登录令牌验证**。所有其他认证方式已被移除。

## 唯一支持的认证方式

### 登录令牌验证

使用 `LoginVerifyClient` 验证登录令牌，通过阿里云无影AI内部服务获取 `ali_uid` 和 `wy_id`。

#### 支持的参数

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `loginToken` | string | 否* | 登录令牌 |
| `session` | string | 否* | 会话ID (优先于loginToken) |
| `regionId` | string | 否 | 区域ID |

*注：`loginToken` 和 `session` 至少需要提供一个

#### 参数传递方式

**1. 查询参数**
```bash
GET /api/user/profile?loginToken=your_token&regionId=cn-hangzhou
GET /api/user/profile?session=your_session&regionId=cn-hangzhou
```

**2. 请求头**
```bash
curl -H "X-Login-Token: your_token" \
     -H "X-Region-Id: cn-hangzhou" \
     /api/user/profile

curl -H "X-Session-Id: your_session" \
     -H "X-Region-Id: cn-hangzhou" \
     /api/user/profile
```

#### 优先级

1. `session` 优先于 `loginToken`
2. 查询参数和请求头同时存在时，查询参数优先

## 使用示例

### FastAPI 端点

```python
from src.presentation.middleware.auth_middleware import require_auth
from fastapi import Depends

@app.get("/api/protected")
async def protected_endpoint(
    current_user: AuthContext = Depends(require_auth)
):
    return {
        "ali_uid": current_user.ali_uid,
        "wy_id": current_user.wy_id
    }
```

### 客户端调用

```python
import httpx

# 使用查询参数
response = httpx.get(
    "http://localhost:8000/api/protected",
    params={
        "loginToken": "your_login_token",
        "regionId": "cn-hangzhou"
    }
)

# 使用请求头
response = httpx.get(
    "http://localhost:8000/api/protected",
    headers={
        "X-Session-Id": "your_session_id",
        "X-Region-Id": "cn-hangzhou"
    }
)
```

## 配置要求

### 环境变量

```bash
ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id
ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret
```

### 依赖安装

```bash
cd src/popclients/wuyingaiinner-20250718
pip install .
```

## 错误处理

### 常见错误情况

1. **未提供令牌**: 返回 401 Unauthorized
2. **令牌无效**: 返回 401 Unauthorized  
3. **令牌过期**: 返回 401 Unauthorized
4. **网络错误**: 返回 500 Internal Server Error
5. **配置错误**: 返回 503 Service Unavailable

### 错误响应格式

```json
{
    "detail": "未认证用户，请提供有效的用户凭证"
}
```

## 日志记录

认证过程会记录以下日志：

```
[AuthMiddleware] 开始验证登录令牌: session_id=xxx, region_id=xxx
[AuthMiddleware] 登录令牌验证成功: ali_uid=123456789, wy_id=test-wy-id
[AuthMiddleware] 登录令牌验证失败: Invalid token
[AuthMiddleware] 未提供登录令牌或会话ID
```

## 迁移指南

### 从旧版本迁移

如果您之前使用的是多种认证方式的版本，需要进行以下更改：

#### 1. 移除不支持的认证方式

**移除 Header 认证**
```bash
# 旧方式 (不再支持)
curl -H "X-Ali-Uid: 123456789" -H "X-Wy-Id: test-wy-id" /api/protected

# 新方式
curl -H "X-Session-Id: your_session" -H "X-Region-Id: cn-hangzhou" /api/protected
```

**移除查询参数认证**
```bash
# 旧方式 (不再支持)
GET /api/protected?ali_uid=123456789&wy_id=test-wy-id

# 新方式
GET /api/protected?session=your_session&regionId=cn-hangzhou
```

#### 2. 更新客户端代码

```python
# 旧方式 (不再支持)
headers = {
    "X-Ali-Uid": "123456789",
    "X-Wy-Id": "test-wy-id"
}

# 新方式
headers = {
    "X-Session-Id": "your_session_id",
    "X-Region-Id": "cn-hangzhou"
}
```

### 3. 测试新认证方式

```bash
# 测试登录令牌验证
curl -X POST http://localhost:8000/api/auth/verify \
     -H "Content-Type: application/json" \
     -d '{"session": "your_session", "regionId": "cn-hangzhou"}'
```

## 故障排除

### 1. 认证失败

**检查项目:**
- 令牌是否有效
- 区域ID是否正确
- 网络连接是否正常
- 阿里云凭证是否配置正确

### 2. 配置问题

**检查项目:**
- 环境变量是否设置
- 依赖包是否安装
- 服务端点是否正确

### 3. 调试方法

**启用详细日志:**
```python
import logging
logging.getLogger("src.popclients.login_verify_client").setLevel(logging.DEBUG)
logging.getLogger("src.presentation.middleware.auth_middleware").setLevel(logging.DEBUG)
```

## 总结

- ✅ **简化认证**: 只支持登录令牌验证
- ✅ **标准化**: 统一使用阿里云无影AI服务
- ✅ **安全性**: 通过官方API验证用户身份
- ⚠️ **破坏性变更**: 需要更新所有现有的API调用
- 📋 **迁移必需**: 所有客户端都需要适配新的认证方式

新的认证方式提供了更好的安全性和标准化，建议尽快完成迁移。
