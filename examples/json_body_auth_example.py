#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON Body认证参数支持示例
展示如何让系统支持从JSON body中提取认证参数
"""
import sys
import os
from fastapi import FastAPI, Request, Depends
from fastapi.responses import JSONResponse

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.presentation.middleware.body_cache_middleware import BodyCacheMiddleware
from src.domain.services.auth_service import require_auth, AuthContext
from src.application.api_models import PresignedUploadRequest, PresignedUploadResponse
from loguru import logger

# 创建FastAPI应用
app = FastAPI(title="JSON Body认证参数支持示例")

# 添加请求体缓存中间件（必须在其他中间件之前添加）
app.add_middleware(BodyCacheMiddleware)

# ============================================================================
# 测试接口
# ============================================================================

@app.post("/api/files/get-upload-url", response_model=PresignedUploadResponse)
async def create_presigned_upload_with_json_auth(
    request: PresignedUploadRequest,
    context: AuthContext = Depends(require_auth)
):
    """
    创建预签名上传链接 - 支持JSON body中的认证参数
    
    现在支持三种认证参数传递方式：
    1. 查询参数: ?loginToken=xxx&loginSessionId=yyy&regionId=zzz
    2. 请求头: X-Login-Token, X-Login-Session-Id, X-Region-Id
    3. JSON body: {"loginToken": "xxx", "loginSessionId": "yyy", "regionId": "zzz", ...}
    """
    try:
        logger.info(f"[API] 创建预签名上传: user={context.user_key}, file={request.file_info.file_name}")
        
        # 模拟创建预签名上传的逻辑
        response = PresignedUploadResponse(
            file_id="mock_file_id_123",
            session_id=request.session_id or "mock_session_id",
            upload_url="https://mock-oss-bucket.oss-cn-hangzhou.aliyuncs.com/mock-upload-url",
            expires_in=900,
            file_name=request.file_info.file_name,
            file_size=request.file_info.file_size,
            upload_method="PUT",
            headers={
                "Content-Type": "application/octet-stream"
            }
        )
        
        logger.info(f"[API] 预签名上传创建成功: file_id={response.file_id}")
        return response
        
    except Exception as e:
        logger.error(f"[API] 创建预签名上传失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"detail": f"创建预签名上传失败: {str(e)}"}
        )


@app.post("/api/test/auth-params")
async def test_auth_params_extraction(
    request: Request,
    context: AuthContext = Depends(require_auth)
):
    """
    测试认证参数提取
    展示从不同位置提取到的认证参数
    """
    try:
        # 获取缓存的JSON body
        json_body = getattr(request.state, 'json_body', None)
        
        # 提取各种来源的认证参数
        auth_sources = {
            "query_params": {
                "loginToken": request.query_params.get("loginToken"),
                "loginSessionId": request.query_params.get("loginSessionId"),
                "regionId": request.query_params.get("regionId")
            },
            "headers": {
                "X-Login-Token": request.headers.get("X-Login-Token"),
                "X-Login-Session-Id": request.headers.get("X-Login-Session-Id"),
                "X-Region-Id": request.headers.get("X-Region-Id")
            },
            "json_body": {
                "loginToken": json_body.get("loginToken") if json_body else None,
                "loginSessionId": json_body.get("loginSessionId") if json_body else None,
                "regionId": json_body.get("regionId") if json_body else None
            } if json_body else None
        }
        
        return {
            "message": "认证成功",
            "user": {
                "ali_uid": context.ali_uid,
                "wy_id": context.wy_id,
                "user_key": context.user_key
            },
            "auth_sources": auth_sources,
            "note": "系统按优先级从不同来源提取认证参数：查询参数 > 请求头 > JSON body"
        }
        
    except Exception as e:
        logger.error(f"[API] 测试认证参数提取失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"detail": f"测试失败: {str(e)}"}
        )


@app.get("/api/test/health")
async def health_check():
    """健康检查接口（无需认证）"""
    return {
        "status": "healthy",
        "message": "JSON Body认证参数支持服务正常运行",
        "features": [
            "支持查询参数认证",
            "支持请求头认证", 
            "支持JSON body认证",
            "认证参数优先级：查询参数 > 请求头 > JSON body"
        ]
    }


# ============================================================================
# 使用说明
# ============================================================================

if __name__ == "__main__":
    import uvicorn
    
    print("=" * 80)
    print("JSON Body认证参数支持示例")
    print("=" * 80)
    print()
    print("现在支持三种认证参数传递方式：")
    print()
    print("1. 查询参数（原有方式）：")
    print("   curl -X POST 'http://localhost:8000/api/files/get-upload-url?loginToken=test&loginSessionId=test&regionId=cn-hangzhou' \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"session_id\": \"test\", \"file_info\": {\"FileName\": \"test.pdf\", \"FileSize\": 1234, \"FileType\": \"pdf\"}, \"file_type\": \"sessionFile\"}'")
    print()
    print("2. 请求头（原有方式）：")
    print("   curl -X POST 'http://localhost:8000/api/files/get-upload-url' \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -H 'X-Login-Token: test' \\")
    print("        -H 'X-Login-Session-Id: test' \\")
    print("        -H 'X-Region-Id: cn-hangzhou' \\")
    print("        -d '{\"session_id\": \"test\", \"file_info\": {\"FileName\": \"test.pdf\", \"FileSize\": 1234, \"FileType\": \"pdf\"}, \"file_type\": \"sessionFile\"}'")
    print()
    print("3. JSON Body（新增方式）：")
    print("   curl -X POST 'http://localhost:8000/api/files/get-upload-url' \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"loginToken\": \"test\", \"loginSessionId\": \"test\", \"regionId\": \"cn-hangzhou\", \"session_id\": \"test\", \"file_info\": {\"FileName\": \"test.pdf\", \"FileSize\": 1234, \"FileType\": \"pdf\"}, \"file_type\": \"sessionFile\"}'")
    print()
    print("优先级：查询参数 > 请求头 > JSON body")
    print()
    print("启动服务器...")
    
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
