"""
知识库Repository使用示例
演示如何使用KnowledgeBaseRepository进行CRUD操作
"""

import uuid
from typing import List, Optional
from src.infrastructure.database.repositories.knowledgebase_repository import knowledgebase_repository
from src.infrastructure.database.models.knowledgebase_models import KnowledgeBaseModel


def example_create_knowledge_base():
    """创建知识库示例"""
    try:
        # 生成唯一的知识库ID
        kb_id = f"kb_{uuid.uuid4().hex[:12]}"
        
        # 创建知识库
        knowledge_base = knowledgebase_repository.create_knowledge_base(
            kb_id=kb_id,
            name="我的第一个知识库",
            owner_ali_uid=123456789,
            owner_wy_id="wy_user_001",
            description="这是一个示例知识库，用于演示功能"
        )
        
        print(f"✅ 成功创建知识库: {knowledge_base.kb_id}")
        print(f"   名称: {knowledge_base.name}")
        print(f"   描述: {knowledge_base.description}")
        print(f"   所有者: {knowledge_base.owner_ali_uid}")
        
        return knowledge_base
        
    except ValueError as e:
        print(f"❌ 创建失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        return None


def example_get_knowledge_base(kb_id: str):
    """获取知识库示例"""
    try:
        knowledge_base = knowledgebase_repository.get_knowledge_base_by_id(kb_id)
        
        if knowledge_base:
            print(f"✅ 找到知识库: {knowledge_base.kb_id}")
            print(f"   名称: {knowledge_base.name}")
            print(f"   描述: {knowledge_base.description}")
            print(f"   创建时间: {knowledge_base.gmt_created}")
            print(f"   是否有效: {knowledge_base.is_active()}")
        else:
            print(f"❌ 知识库不存在: {kb_id}")
            
        return knowledge_base
        
    except Exception as e:
        print(f"❌ 获取失败: {e}")
        return None


def example_update_knowledge_base(kb_id: str):
    """更新知识库示例"""
    try:
        updated_kb = knowledgebase_repository.update_knowledge_base(
            kb_id=kb_id,
            name="更新后的知识库名称",
            description="这是更新后的描述信息"
        )
        
        if updated_kb:
            print(f"✅ 成功更新知识库: {updated_kb.kb_id}")
            print(f"   新名称: {updated_kb.name}")
            print(f"   新描述: {updated_kb.description}")
            print(f"   修改时间: {updated_kb.gmt_modified}")
        else:
            print(f"❌ 知识库不存在，无法更新: {kb_id}")
            
        return updated_kb
        
    except ValueError as e:
        print(f"❌ 更新失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        return None


def example_list_knowledge_bases(owner_ali_uid: int, owner_wy_id: str):
    """列出用户的知识库示例"""
    try:
        knowledge_bases = knowledgebase_repository.get_knowledge_bases_by_owner(
            owner_ali_uid=owner_ali_uid,
            owner_wy_id=owner_wy_id,
            limit=10,
            offset=0
        )
        
        print(f"✅ 找到 {len(knowledge_bases)} 个知识库:")
        for kb in knowledge_bases:
            print(f"   - {kb.kb_id}: {kb.name} ({kb.description or '无描述'})")
            
        return knowledge_bases
        
    except Exception as e:
        print(f"❌ 获取列表失败: {e}")
        return []


def example_search_knowledge_bases(owner_ali_uid: int, owner_wy_id: str, search_name: str):
    """搜索知识库示例"""
    try:
        knowledge_bases = knowledgebase_repository.list_knowledge_bases(
            owner_ali_uid=owner_ali_uid,
            owner_wy_id=owner_wy_id,
            name=search_name,
            limit=5
        )
        
        print(f"✅ 搜索 '{search_name}' 找到 {len(knowledge_bases)} 个知识库:")
        for kb in knowledge_bases:
            print(f"   - {kb.kb_id}: {kb.name}")
            
        return knowledge_bases
        
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
        return []


def example_soft_delete_knowledge_base(kb_id: str):
    """软删除知识库示例"""
    try:
        success = knowledgebase_repository.soft_delete_knowledge_base(kb_id)
        
        if success:
            print(f"✅ 成功软删除知识库: {kb_id}")
            
            # 验证删除后无法查询到
            deleted_kb = knowledgebase_repository.get_knowledge_base_by_id(kb_id)
            if deleted_kb is None:
                print(f"✅ 验证成功：删除后无法查询到知识库")
            else:
                print(f"❌ 验证失败：删除后仍能查询到知识库")
        else:
            print(f"❌ 知识库不存在，无法删除: {kb_id}")
            
        return success
        
    except Exception as e:
        print(f"❌ 删除失败: {e}")
        return False


def example_check_exists(kb_id: str):
    """检查知识库是否存在示例"""
    try:
        exists = knowledgebase_repository.check_knowledge_base_exists(kb_id)
        
        if exists:
            print(f"✅ 知识库存在: {kb_id}")
        else:
            print(f"❌ 知识库不存在: {kb_id}")
            
        return exists
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False


def example_count_knowledge_bases(owner_ali_uid: int, owner_wy_id: str):
    """统计知识库数量示例"""
    try:
        count = knowledgebase_repository.count_knowledge_bases_by_owner(
            owner_ali_uid=owner_ali_uid,
            owner_wy_id=owner_wy_id
        )
        
        print(f"✅ 用户 {owner_ali_uid} 共有 {count} 个知识库")
        return count
        
    except Exception as e:
        print(f"❌ 统计失败: {e}")
        return 0


def run_all_examples():
    """运行所有示例"""
    print("🚀 开始运行知识库Repository示例...")
    print("=" * 50)
    
    # 示例用户信息
    owner_ali_uid = 123456789
    owner_wy_id = "wy_user_001"
    
    # 1. 创建知识库
    print("\n1️⃣ 创建知识库示例:")
    kb = example_create_knowledge_base()
    
    if kb:
        kb_id = kb.kb_id
        
        # 2. 获取知识库
        print("\n2️⃣ 获取知识库示例:")
        example_get_knowledge_base(kb_id)
        
        # 3. 更新知识库
        print("\n3️⃣ 更新知识库示例:")
        example_update_knowledge_base(kb_id)
        
        # 4. 检查存在性
        print("\n4️⃣ 检查知识库存在性示例:")
        example_check_exists(kb_id)
        
        # 5. 统计数量
        print("\n5️⃣ 统计知识库数量示例:")
        example_count_knowledge_bases(owner_ali_uid, owner_wy_id)
        
        # 6. 列出知识库
        print("\n6️⃣ 列出知识库示例:")
        example_list_knowledge_bases(owner_ali_uid, owner_wy_id)
        
        # 7. 搜索知识库
        print("\n7️⃣ 搜索知识库示例:")
        example_search_knowledge_bases(owner_ali_uid, owner_wy_id, "知识库")
        
        # 8. 软删除知识库
        print("\n8️⃣ 软删除知识库示例:")
        example_soft_delete_knowledge_base(kb_id)
        
        # 9. 删除后再次检查
        print("\n9️⃣ 删除后检查示例:")
        example_check_exists(kb_id)
        example_get_knowledge_base(kb_id)
    
    print("\n" + "=" * 50)
    print("✅ 所有示例运行完成!")


if __name__ == "__main__":
    run_all_examples() 