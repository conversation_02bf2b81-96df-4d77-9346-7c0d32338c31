# -*- coding: utf-8 -*-
"""
FastAPI依赖注入认证详细示例
演示依赖注入的原理、使用方式和高级特性
"""
import sys
import os
from typing import Optional, List, Annotated
from fastapi import FastAPI, Request, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from loguru import logger

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.domain.services.auth_service import AuthContext
from src.presentation.middleware.auth_middleware import AuthMiddleware

app = FastAPI(title="依赖注入认证详细示例")

# ============================================================================
# 1. 基础依赖注入原理演示
# ============================================================================

# 创建认证中间件实例
auth_middleware = AuthMiddleware()

async def get_request_info(request: Request) -> dict:
    """基础依赖：获取请求信息"""
    logger.info(f"[依赖] 解析请求信息: {request.method} {request.url.path}")
    return {
        "method": request.method,
        "path": request.url.path,
        "client_ip": request.client.host if request.client else "unknown"
    }

async def get_current_user_basic(request: Request) -> Optional[AuthContext]:
    """基础依赖：获取当前用户"""
    logger.info("[依赖] 执行用户认证")
    user = await auth_middleware.get_current_user(request)
    if user:
        logger.info(f"[依赖] 认证成功: {user.ali_uid}")
    else:
        logger.info("[依赖] 未认证用户")
    return user

async def require_authenticated_user(
    user: Optional[AuthContext] = Depends(get_current_user_basic)
) -> AuthContext:
    """依赖链：要求已认证用户"""
    logger.info("[依赖] 检查用户认证状态")
    if not user:
        logger.warning("[依赖] 用户未认证，抛出401异常")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要用户认证"
        )
    logger.info(f"[依赖] 用户认证检查通过: {user.ali_uid}")
    return user

# ============================================================================
# 2. 基础使用示例
# ============================================================================

@app.get("/api/basic/public")
async def public_endpoint(
    request_info: dict = Depends(get_request_info),
    user: Optional[AuthContext] = Depends(get_current_user_basic)
):
    """
    基础示例：可选认证
    
    依赖注入流程：
    1. FastAPI检查函数签名
    2. 发现两个Depends参数
    3. 先执行get_request_info(request)
    4. 再执行get_current_user_basic(request)
    5. 将结果注入到函数参数中
    """
    return {
        "message": "公开接口",
        "request_info": request_info,
        "authenticated": user is not None,
        "user": {
            "ali_uid": user.ali_uid,
            "wy_id": user.wy_id
        } if user else None
    }

@app.get("/api/basic/protected")
async def protected_endpoint(
    request_info: dict = Depends(get_request_info),
    user: AuthContext = Depends(require_authenticated_user)
):
    """
    基础示例：必需认证
    
    依赖注入流程：
    1. 执行get_request_info(request)
    2. 执行get_current_user_basic(request)
    3. 执行require_authenticated_user(user) - 依赖链
    4. 如果认证失败，抛出HTTPException
    5. 如果成功，注入用户信息
    """
    return {
        "message": "受保护接口",
        "request_info": request_info,
        "user": {
            "ali_uid": user.ali_uid,
            "wy_id": user.wy_id
        }
    }

# ============================================================================
# 3. 高级依赖注入特性
# ============================================================================

class PermissionChecker:
    """权限检查器类"""
    
    def __init__(self, required_permissions: List[str]):
        self.required_permissions = required_permissions
        logger.info(f"[依赖] 创建权限检查器: {required_permissions}")
    
    async def __call__(
        self, 
        user: AuthContext = Depends(require_authenticated_user)
    ) -> AuthContext:
        """
        可调用对象作为依赖
        
        这展示了依赖注入的灵活性：
        - 可以使用类实例作为依赖
        - 支持参数化的依赖创建
        """
        logger.info(f"[依赖] 检查用户权限: {self.required_permissions}")
        
        # 模拟权限检查逻辑
        user_permissions = getattr(user, 'permissions', [])
        missing_permissions = [
            p for p in self.required_permissions 
            if p not in user_permissions
        ]
        
        if missing_permissions:
            logger.warning(f"[依赖] 权限不足: 缺少 {missing_permissions}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"缺少权限: {', '.join(missing_permissions)}"
            )
        
        logger.info("[依赖] 权限检查通过")
        return user

def require_permissions(permissions: List[str]):
    """
    依赖工厂函数
    
    这是一个高阶函数，返回一个依赖：
    1. 接收权限列表作为参数
    2. 返回一个PermissionChecker实例
    3. 该实例可以作为依赖使用
    """
    logger.info(f"[依赖工厂] 创建权限依赖: {permissions}")
    return Depends(PermissionChecker(permissions))

@app.get("/api/advanced/admin")
async def admin_endpoint(
    user: AuthContext = require_permissions(['admin'])
):
    """
    高级示例：参数化权限检查
    
    依赖注入流程：
    1. require_permissions(['admin']) 创建 PermissionChecker 实例
    2. Depends(PermissionChecker(['admin'])) 注册依赖
    3. 执行依赖链：get_current_user_basic -> require_authenticated_user -> PermissionChecker
    4. 检查用户是否有admin权限
    """
    return {
        "message": "管理员专用接口",
        "user": {
            "ali_uid": user.ali_uid,
            "wy_id": user.wy_id
        }
    }

# ============================================================================
# 4. 依赖缓存演示
# ============================================================================

async def expensive_operation(request: Request) -> dict:
    """
    模拟昂贵的操作
    
    在同一个请求中，这个函数只会被调用一次，
    结果会被FastAPI缓存并重用
    """
    logger.info("[依赖] 执行昂贵操作（只执行一次）")
    # 模拟耗时操作
    import time
    time.sleep(0.1)
    
    return {
        "timestamp": time.time(),
        "operation": "expensive_calculation"
    }

@app.get("/api/cache/demo")
async def cache_demo(
    # 这两个参数都依赖同一个expensive_operation
    # 但expensive_operation只会被调用一次
    operation1: dict = Depends(expensive_operation),
    operation2: dict = Depends(expensive_operation),
    user: Optional[AuthContext] = Depends(get_current_user_basic)
):
    """
    依赖缓存演示
    
    FastAPI会自动缓存依赖的结果：
    1. expensive_operation只会被调用一次
    2. operation1和operation2会得到相同的结果
    3. 这提高了性能，避免重复计算
    """
    return {
        "message": "依赖缓存演示",
        "operation1_timestamp": operation1["timestamp"],
        "operation2_timestamp": operation2["timestamp"],
        "same_result": operation1 == operation2,  # 应该是True
        "user_authenticated": user is not None
    }

# ============================================================================
# 5. 子依赖和复杂依赖链
# ============================================================================

async def get_database_connection():
    """模拟数据库连接"""
    logger.info("[依赖] 获取数据库连接")
    return {"db": "mock_connection"}

async def get_user_repository(
    db: dict = Depends(get_database_connection)
):
    """用户仓储层依赖"""
    logger.info("[依赖] 创建用户仓储")
    return {"repo": "user_repository", "db": db}

async def get_user_service(
    user_repo: dict = Depends(get_user_repository),
    current_user: AuthContext = Depends(require_authenticated_user)
):
    """用户服务层依赖"""
    logger.info(f"[依赖] 创建用户服务: user={current_user.ali_uid}")
    return {
        "service": "user_service",
        "repo": user_repo,
        "current_user_id": current_user.ali_uid
    }

@app.get("/api/complex/user-profile")
async def get_user_profile_complex(
    user_service: dict = Depends(get_user_service)
):
    """
    复杂依赖链演示
    
    依赖链：
    get_database_connection -> get_user_repository -> get_user_service
                                                   -> require_authenticated_user
                                                   -> get_current_user_basic
    
    FastAPI会自动解析并按正确顺序执行所有依赖
    """
    return {
        "message": "复杂依赖链示例",
        "service_info": user_service
    }

# ============================================================================
# 6. 类型注解和Annotated的使用
# ============================================================================

# Python 3.9+ 可以使用 Annotated 来提供更好的类型提示
CurrentUser = Annotated[AuthContext, Depends(require_authenticated_user)]
RequestInfo = Annotated[dict, Depends(get_request_info)]

@app.get("/api/annotated/example")
async def annotated_example(
    user: CurrentUser,  # 等价于 user: AuthContext = Depends(require_authenticated_user)
    info: RequestInfo   # 等价于 info: dict = Depends(get_request_info)
):
    """
    使用Annotated的类型注解
    
    优势：
    1. 更清晰的类型提示
    2. 可以重用依赖定义
    3. IDE支持更好
    """
    return {
        "message": "Annotated类型注解示例",
        "user": {"ali_uid": user.ali_uid},
        "request": info
    }

# ============================================================================
# 7. 依赖注入的调试和监控
# ============================================================================

@app.get("/api/debug/dependency-tree")
async def debug_dependency_tree(
    request_info: dict = Depends(get_request_info),
    user: Optional[AuthContext] = Depends(get_current_user_basic)
):
    """
    依赖注入调试信息
    
    通过日志可以看到依赖的执行顺序和结果
    """
    return {
        "message": "依赖注入调试",
        "dependency_execution_order": [
            "1. get_request_info(request)",
            "2. get_current_user_basic(request)",
            "3. 注入到函数参数"
        ],
        "request_info": request_info,
        "user_authenticated": user is not None
    }

# ============================================================================
# 8. 错误处理和异常传播
# ============================================================================

async def may_fail_dependency():
    """可能失败的依赖"""
    import random
    if random.random() < 0.5:
        logger.error("[依赖] 依赖执行失败")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="依赖服务不可用"
        )
    logger.info("[依赖] 依赖执行成功")
    return {"status": "success"}

@app.get("/api/error/handling")
async def error_handling_example(
    result: dict = Depends(may_fail_dependency),
    user: Optional[AuthContext] = Depends(get_current_user_basic)
):
    """
    错误处理示例
    
    如果依赖抛出异常：
    1. FastAPI会捕获异常
    2. 停止执行后续依赖
    3. 直接返回错误响应
    4. 不会执行路由函数
    """
    return {
        "message": "错误处理示例",
        "dependency_result": result,
        "user_authenticated": user is not None
    }

# ============================================================================
# 应用启动
# ============================================================================

if __name__ == "__main__":
    import uvicorn
    
    print("=== FastAPI依赖注入认证详细示例 ===")
    print("")
    print("🔧 核心原理:")
    print("1. 基于类型注解的依赖解析")
    print("2. 自动依赖链构建和执行")
    print("3. 依赖结果缓存和重用")
    print("4. 异常传播和错误处理")
    print("")
    print("📝 测试端点:")
    print("- GET /api/basic/* (基础依赖注入)")
    print("- GET /api/advanced/* (高级特性)")
    print("- GET /api/cache/* (依赖缓存)")
    print("- GET /api/complex/* (复杂依赖链)")
    print("- GET /api/annotated/* (类型注解)")
    print("- GET /api/debug/* (调试信息)")
    print("- GET /api/error/* (错误处理)")
    print("")
    print("🚀 启动服务在 http://localhost:8000")
    print("📚 访问 http://localhost:8000/docs 查看API文档")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
