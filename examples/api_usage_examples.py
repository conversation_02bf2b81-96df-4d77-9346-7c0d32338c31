# -*- coding: utf-8 -*-
"""
API使用示例 - 展示新的参数映射和鉴权方式
"""
import asyncio
import httpx
from typing import Optional

class AlphaServiceClient:
    """Alpha Service API客户端示例"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = httpx.AsyncClient()
    
    async def list_sessions_old_way(
        self, 
        limit: int = 50, 
        offset: int = 0, 
        ali_uid: Optional[str] = None,
        agent_id: Optional[str] = None
    ):
        """
        旧的API调用方式（已废弃）
        
        问题：
        1. ali_uid 可以被伪造
        2. 没有真正的身份验证
        3. 安全性差
        """
        params = {
            "limit": limit,
            "offset": offset
        }
        if ali_uid:
            params["ali_uid"] = ali_uid
        if agent_id:
            params["agent_id"] = agent_id
        
        response = await self.session.get(
            f"{self.base_url}/api/sessions/list",
            params=params
        )
        return response.json()
    
    async def list_sessions_new_way_query_params(
        self,
        login_token: str,
        region_id: str = "cn-hangzhou",
        limit: int = 50,
        offset: int = 0,
        agent_id: Optional[str] = None,
        session_id: Optional[str] = None
    ):
        """
        新的API调用方式 - 查询参数
        
        优势：
        1. 通过 login_token 进行真实身份验证
        2. ali_uid 从鉴权结果中获取，无法伪造
        3. 安全性高
        """
        params = {
            "loginToken": login_token,
            "regionId": region_id,
            "limit": limit,
            "offset": offset
        }
        if agent_id:
            params["agentId"] = agent_id
        if session_id:
            params["sessionId"] = session_id
        
        response = await self.session.get(
            f"{self.base_url}/api/sessions/list",
            params=params
        )
        return response.json()
    
    async def list_sessions_new_way_headers(
        self,
        login_token: str,
        region_id: str = "cn-hangzhou",
        limit: int = 50,
        offset: int = 0,
        agent_id: Optional[str] = None,
        session_id: Optional[str] = None
    ):
        """
        新的API调用方式 - 请求头
        
        优势：
        1. 鉴权信息在请求头中，更安全
        2. 查询参数更简洁
        3. 符合RESTful API设计规范
        """
        headers = {
            "X-Login-Token": login_token,
            "X-Region-Id": region_id
        }
        
        params = {
            "limit": limit,
            "offset": offset
        }
        if agent_id:
            params["agentId"] = agent_id
        if session_id:
            params["sessionId"] = session_id
        
        response = await self.session.get(
            f"{self.base_url}/api/sessions/list",
            params=params,
            headers=headers
        )
        return response.json()
    
    async def list_sessions_with_session_id(
        self,
        session_token: str,  # 使用 session 而不是 loginToken
        region_id: str = "cn-hangzhou",
        limit: int = 50,
        offset: int = 0,
        agent_id: Optional[str] = None
    ):
        """
        使用会话ID进行认证
        
        session 参数优先于 loginToken
        """
        params = {
            "session": session_token,
            "regionId": region_id,
            "limit": limit,
            "offset": offset
        }
        if agent_id:
            params["agentId"] = agent_id
        
        response = await self.session.get(
            f"{self.base_url}/api/sessions/list",
            params=params
        )
        return response.json()
    
    async def close(self):
        """关闭HTTP客户端"""
        await self.session.aclose()


async def demonstrate_api_usage():
    """演示API使用方式"""
    client = AlphaServiceClient()
    
    print("=== API使用示例演示 ===")
    
    # 模拟的令牌（实际使用时需要真实的令牌）
    mock_login_token = "mock_login_token_12345"
    mock_session_token = "mock_session_token_67890"
    
    try:
        print("\n1. 新方式 - 查询参数认证")
        try:
            result = await client.list_sessions_new_way_query_params(
                login_token=mock_login_token,
                region_id="cn-hangzhou",
                limit=10,
                offset=0,
                agent_id="test-agent"
            )
            print(f"   请求成功: {result.get('code', 'unknown')}")
        except Exception as e:
            print(f"   请求失败: {e} (预期的，因为使用了模拟令牌)")
        
        print("\n2. 新方式 - 请求头认证")
        try:
            result = await client.list_sessions_new_way_headers(
                login_token=mock_login_token,
                region_id="cn-hangzhou",
                limit=10,
                offset=0,
                agent_id="test-agent"
            )
            print(f"   请求成功: {result.get('code', 'unknown')}")
        except Exception as e:
            print(f"   请求失败: {e} (预期的，因为使用了模拟令牌)")
        
        print("\n3. 会话ID认证")
        try:
            result = await client.list_sessions_with_session_id(
                session_token=mock_session_token,
                region_id="cn-hangzhou",
                limit=5,
                offset=0
            )
            print(f"   请求成功: {result.get('code', 'unknown')}")
        except Exception as e:
            print(f"   请求失败: {e} (预期的，因为使用了模拟令牌)")
        
    finally:
        await client.close()
    
    print("\n=== 参数映射说明 ===")
    print("🔄 参数映射流程:")
    print("1. 客户端发送: loginToken/session + regionId + 查询参数")
    print("2. 鉴权中间件: 验证令牌 -> 获取 ali_uid/wy_id")
    print("3. 业务逻辑: 使用 ali_uid + 查询参数 -> 查询数据库")
    print("4. 返回结果: 标准化的响应格式")


def generate_curl_examples():
    """生成curl命令示例"""
    print("\n=== cURL命令示例 ===")
    
    print("\n1. 查询参数方式:")
    print("""curl -X GET "http://localhost:8000/api/sessions/list?loginToken=your_token&regionId=cn-hangzhou&limit=10&offset=0&agentId=test-agent" \\
     -H "Content-Type: application/json" """)
    
    print("\n2. 请求头方式:")
    print("""curl -X GET "http://localhost:8000/api/sessions/list?limit=10&offset=0&agentId=test-agent" \\
     -H "X-Login-Token: your_token" \\
     -H "X-Region-Id: cn-hangzhou" \\
     -H "Content-Type: application/json" """)
    
    print("\n3. 会话ID方式:")
    print("""curl -X GET "http://localhost:8000/api/sessions/list?session=your_session&regionId=cn-hangzhou&limit=10&offset=0" \\
     -H "Content-Type: application/json" """)


def generate_javascript_examples():
    """生成JavaScript客户端示例"""
    print("\n=== JavaScript客户端示例 ===")
    
    print("\n1. 使用fetch API:")
    print("""
// 查询参数方式
const response = await fetch('/api/sessions/list?' + new URLSearchParams({
    loginToken: 'your_token',
    regionId: 'cn-hangzhou',
    limit: 10,
    offset: 0,
    agentId: 'test-agent'
}));

// 请求头方式
const response = await fetch('/api/sessions/list?' + new URLSearchParams({
    limit: 10,
    offset: 0,
    agentId: 'test-agent'
}), {
    headers: {
        'X-Login-Token': 'your_token',
        'X-Region-Id': 'cn-hangzhou'
    }
});
    """)
    
    print("\n2. 使用axios:")
    print("""
// 查询参数方式
const response = await axios.get('/api/sessions/list', {
    params: {
        loginToken: 'your_token',
        regionId: 'cn-hangzhou',
        limit: 10,
        offset: 0,
        agentId: 'test-agent'
    }
});

// 请求头方式
const response = await axios.get('/api/sessions/list', {
    params: {
        limit: 10,
        offset: 0,
        agentId: 'test-agent'
    },
    headers: {
        'X-Login-Token': 'your_token',
        'X-Region-Id': 'cn-hangzhou'
    }
});
    """)


def show_response_format():
    """展示响应格式"""
    print("\n=== 响应格式 ===")
    
    print("\n标准化响应格式:")
    print("""{
    "code": 200,
    "message": "success",
    "data": {
        "sessions": [
            {
                "sessionId": "session_123",
                "aliUid": 123456789,
                "agentId": "test-agent",
                "title": "测试会话",
                "status": "active",
                "gmtCreate": "2024-01-01T10:00:00Z",
                "gmtModified": "2024-01-01T10:00:00Z",
                "metadata": {}
            }
        ],
        "pagination": {
            "total": 100,
            "limit": 10,
            "offset": 0,
            "hasMore": true
        }
    }
}""")


if __name__ == "__main__":
    print("=== Alpha Service API 使用示例 ===")
    
    # 运行异步演示
    asyncio.run(demonstrate_api_usage())
    
    # 生成各种示例
    generate_curl_examples()
    generate_javascript_examples()
    show_response_format()
    
    print("\n=== 总结 ===")
    print("✅ 新的API调用方式:")
    print("  1. 使用 loginToken 或 session 进行身份验证")
    print("  2. ali_uid 从鉴权结果中自动获取，无法伪造")
    print("  3. 支持查询参数和请求头两种传递方式")
    print("  4. 标准化的响应格式")
    print("  5. 完整的参数验证和错误处理")
    print("")
    print("🔒 安全性提升:")
    print("  1. 真实的身份验证，防止身份伪造")
    print("  2. 令牌验证通过阿里云服务")
    print("  3. 统一的鉴权中间件处理")
    print("")
    print("📝 开发体验:")
    print("  1. 类型安全的参数验证")
    print("  2. 自动生成的API文档")
    print("  3. 易于测试和调试")
