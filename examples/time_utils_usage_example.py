#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间工具使用示例
展示如何使用带时区参数的时间转换功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from datetime import datetime, timezone, timedelta
from src.domain.utils.time_utils import TimeUtils, EAST_ASIA_TZ, UTC_TZ, EST_TZ


def example_usage():
    """使用示例"""
    print("=== 时间工具使用示例 ===")
    
    # 示例1: 东八区时间转换为 UTC ISO 8601
    print("\n1. 东八区时间转换为 UTC ISO 8601:")
    east_asia_time = datetime(2025, 8, 5, 17, 41, 0)  # 东八区 17:41
    utc_iso = TimeUtils.to_iso8601_utc(east_asia_time, EAST_ASIA_TZ)
    print(f"   东八区时间: {east_asia_time}")
    print(f"   UTC ISO 8601: {utc_iso}")
    
    # 示例2: 美国东部时间转换为 UTC ISO 8601
    print("\n2. 美国东部时间转换为 UTC ISO 8601:")
    est_time = datetime(2025, 8, 5, 12, 0, 0)  # 美国东部时间 12:00
    utc_iso2 = TimeUtils.to_iso8601_utc(est_time, EST_TZ)
    print(f"   美国东部时间: {est_time}")
    print(f"   UTC ISO 8601: {utc_iso2}")
    
    # 示例3: 带时区信息的时间
    print("\n3. 带时区信息的时间:")
    time_with_tz = datetime(2025, 8, 5, 17, 41, 0, tzinfo=EAST_ASIA_TZ)
    utc_iso3 = TimeUtils.to_iso8601_utc(time_with_tz)  # 不需要指定时区参数
    print(f"   带时区的时间: {time_with_tz}")
    print(f"   UTC ISO 8601: {utc_iso3}")
    
    # 示例4: 自定义时区
    print("\n4. 自定义时区:")
    custom_tz = timezone(timedelta(hours=5, minutes=30))  # UTC+5:30 (印度时间)
    india_time = datetime(2025, 8, 5, 15, 30, 0)  # 印度时间 15:30
    utc_iso4 = TimeUtils.to_iso8601_utc(india_time, custom_tz)
    print(f"   印度时间: {india_time}")
    print(f"   UTC ISO 8601: {utc_iso4}")
    
    # 示例5: 格式化时间
    print("\n5. 格式化时间:")
    custom_format = "%Y-%m-%d %H:%M:%S UTC"
    formatted_time = TimeUtils.format_datetime(east_asia_time, custom_format, EAST_ASIA_TZ)
    print(f"   东八区时间: {east_asia_time}")
    print(f"   自定义格式: {formatted_time}")
    
    # 示例6: 解析 ISO 8601 时间
    print("\n6. 解析 ISO 8601 时间:")
    iso_str = "2025-08-05T09:41Z"
    parsed_time = TimeUtils.from_iso8601_utc(iso_str)
    print(f"   ISO 字符串: {iso_str}")
    print(f"   解析结果: {parsed_time}")
    print(f"   时区信息: {parsed_time.tzinfo}")


def practical_example():
    """实际应用示例"""
    print("\n=== 实际应用示例 ===")
    
    # 模拟从数据库获取的东八区时间（无时区信息）
    db_time = datetime(2025, 8, 5, 14, 30, 0)
    print(f"数据库中的时间（东八区）: {db_time}")
    
    # 转换为 UTC ISO 8601 格式用于 API 调用
    api_time = TimeUtils.to_iso8601_utc(db_time, EAST_ASIA_TZ)
    print(f"API 调用的 UTC 时间: {api_time}")
    
    # 从 API 响应解析时间
    api_response_time = "2025-08-05T06:30Z"
    parsed_api_time = TimeUtils.from_iso8601_utc(api_response_time)
    print(f"API 响应时间: {api_response_time}")
    print(f"解析后的时间: {parsed_api_time}")
    
    # 转换为东八区时间显示给用户
    east_asia_display = parsed_api_time.astimezone(EAST_ASIA_TZ)
    print(f"用户看到的东八区时间: {east_asia_display}")


if __name__ == "__main__":
    example_usage()
    practical_example()
    print("\n=== 示例完成 ===") 