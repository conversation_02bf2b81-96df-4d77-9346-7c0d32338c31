#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
update_session_by_id 方法使用示例
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from domain.models.enums import KbState
from infrastructure.database.models.knowledgebase_models import KbSessionModel
from infrastructure.database.repositories.kb_sessions_repository import kb_sessions_repository


def example_update_session_status():
    """示例：更新会话状态"""
    print("=== 示例：更新会话状态 ===")
    
    try:
        # 1. 获取现有会话
        session = kb_sessions_repository.get_session(
            kb_id="example_kb_001",
            session_id="example_session_001"
        )
        
        if not session:
            print("会话不存在，先创建一个...")
            session = kb_sessions_repository.create_session(
                kb_id="example_kb_001",
                session_id="example_session_001",
                message_id_list="msg1,msg2",
                snippet_id="snippet_001",
                snippet_status=KbState.PROCESSING.value,
                session_status=KbState.PROCESSING.value,
            )
        
        print(f"原始会话状态: {session.session_status}")
        print(f"原始Snippet状态: {session.snippet_status}")
        
        # 2. 更新会话状态
        session.session_status = KbState.SUCCESS.value
        session.snippet_status = KbState.SUCCESS.value
        
        # 3. 使用 update_session_by_id 方法更新
        updated_session = kb_sessions_repository.update_session_by_id(session)
        
        if updated_session:
            print(f"更新成功！")
            print(f"新会话状态: {updated_session.session_status}")
            print(f"新Snippet状态: {updated_session.snippet_status}")
        else:
            print("更新失败")
            
    except Exception as e:
        print(f"示例执行失败: {e}")


def example_update_message_list():
    """示例：更新消息列表"""
    print("\n=== 示例：更新消息列表 ===")
    
    try:
        # 1. 获取现有会话
        session = kb_sessions_repository.get_session(
            kb_id="example_kb_002",
            session_id="example_session_002"
        )
        
        if not session:
            print("会话不存在，先创建一个...")
            session = kb_sessions_repository.create_session(
                kb_id="example_kb_002",
                session_id="example_session_002",
                message_id_list="msg1,msg2",
                snippet_id="snippet_002",
                snippet_status=KbState.PROCESSING.value,
                session_status=KbState.PROCESSING.value,
            )
        
        print(f"原始消息列表: {session.message_id_list}")
        
        # 2. 添加新消息
        new_message_list = f"{session.message_id_list},msg3,msg4,msg5"
        session.message_id_list = new_message_list
        
        # 3. 使用 update_session_by_id 方法更新
        updated_session = kb_sessions_repository.update_session_by_id(session)
        
        if updated_session:
            print(f"更新成功！")
            print(f"新消息列表: {updated_session.message_id_list}")
        else:
            print("更新失败")
            
    except Exception as e:
        print(f"示例执行失败: {e}")


def example_batch_update_sessions():
    """示例：批量更新会话"""
    print("\n=== 示例：批量更新会话 ===")
    
    try:
        # 1. 创建多个测试会话
        sessions = []
        for i in range(3):
            session = kb_sessions_repository.create_session(
                kb_id=f"batch_kb_{i}",
                session_id=f"batch_session_{i}",
                message_id_list=f"msg{i}_1,msg{i}_2",
                snippet_id=f"snippet_{i}",
                snippet_status=KbState.PROCESSING.value,
                session_status=KbState.PROCESSING.value,
            )
            sessions.append(session)
            print(f"创建会话 {i}: {session.session_id}")
        
        # 2. 批量更新会话状态
        print("批量更新会话状态...")
        for i, session in enumerate(sessions):
            session.session_status = KbState.SUCCESS.value
            session.snippet_status = KbState.SUCCESS.value
            session.message_id_list = f"{session.message_id_list},msg{i}_3"
            
            updated_session = kb_sessions_repository.update_session_by_id(session)
            if updated_session:
                print(f"  会话 {i} 更新成功: {updated_session.session_status}")
            else:
                print(f"  会话 {i} 更新失败")
                
    except Exception as e:
        print(f"示例执行失败: {e}")


def example_partial_update():
    """示例：部分字段更新"""
    print("\n=== 示例：部分字段更新 ===")
    
    try:
        # 1. 获取现有会话
        session = kb_sessions_repository.get_session(
            kb_id="partial_kb_001",
            session_id="partial_session_001"
        )
        
        if not session:
            print("会话不存在，先创建一个...")
            session = kb_sessions_repository.create_session(
                kb_id="partial_kb_001",
                session_id="partial_session_001",
                message_id_list="msg1,msg2",
                snippet_id="snippet_001",
                snippet_status=KbState.PROCESSING.value,
                session_status=KbState.PROCESSING.value,
            )
        
        print(f"原始状态:")
        print(f"  消息列表: {session.message_id_list}")
        print(f"  会话状态: {session.session_status}")
        print(f"  Snippet状态: {session.snippet_status}")
        
        # 2. 只更新部分字段
        session.message_id_list = "msg1,msg2,msg3,msg4"
        # 注意：不设置其他字段，保持原值
        
        # 3. 使用 update_session_by_id 方法更新
        updated_session = kb_sessions_repository.update_session_by_id(session)
        
        if updated_session:
            print(f"更新成功！")
            print(f"  新消息列表: {updated_session.message_id_list}")
            print(f"  会话状态: {updated_session.session_status}")
            print(f"  Snippet状态: {updated_session.snippet_status}")
        else:
            print("更新失败")
            
    except Exception as e:
        print(f"示例执行失败: {e}")


if __name__ == "__main__":
    print("update_session_by_id 方法使用示例")
    print("=" * 50)
    
    example_update_session_status()
    example_update_message_list()
    example_batch_update_sessions()
    example_partial_update()
    
    print("\n所有示例执行完成！") 