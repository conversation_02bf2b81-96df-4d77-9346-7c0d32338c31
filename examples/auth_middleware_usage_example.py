# -*- coding: utf-8 -*-
"""
AuthMiddleware 使用示例
演示如何在API中使用修改后的认证中间件进行登录令牌验证
"""
import sys
import os
from fastapi import FastAPI, Depends, Request, HTTPException
from fastapi.responses import JSONResponse

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.presentation.middleware.auth_middleware import (
    get_current_user, 
    require_auth,
    AuthMiddleware
)
from src.domain.services.auth_service import AuthContext
from loguru import logger

# 创建FastAPI应用
app = FastAPI(title="AuthMiddleware 使用示例")

# 创建中间件实例
auth_middleware = AuthMiddleware()


@app.get("/api/user/profile")
async def get_user_profile(
    request: Request,
    current_user: AuthContext = Depends(require_auth)
):
    """
    获取用户资料 - 需要认证

    支持的认证方式：
    1. 查询参数: ?loginToken=xxx&regionId=xxx
    2. 查询参数: ?session=xxx&regionId=xxx
    3. 请求头: X-Login-Token, X-Session-Id, X-Region-Id
    """
    return {
        "code": 200,
        "message": "success",
        "data": {
            "ali_uid": current_user.ali_uid,
            "wy_id": current_user.wy_id,
            "permissions": list(current_user.permissions_cache.keys()) if current_user.permissions_cache else []
        }
    }


@app.get("/api/user/info")
async def get_user_info(
    request: Request,
    current_user: AuthContext = Depends(get_current_user)
):
    """
    获取用户信息 - 可选认证
    如果提供了认证信息则返回用户详情，否则返回匿名用户信息
    """
    if current_user:
        return {
            "code": 200,
            "message": "success",
            "data": {
                "authenticated": True,
                "ali_uid": current_user.ali_uid,
                "wy_id": current_user.wy_id,
                "user_type": "authenticated"
            }
        }
    else:
        return {
            "code": 200,
            "message": "success",
            "data": {
                "authenticated": False,
                "user_type": "anonymous"
            }
        }


@app.post("/api/auth/verify")
async def verify_login_token(request: Request):
    """
    验证登录令牌 - 手动验证示例
    """
    try:
        # 从请求中获取参数
        body = await request.json()
        login_token = body.get("loginToken")
        session_id = body.get("session")
        region_id = body.get("regionId")
        
        if not (login_token or session_id):
            raise HTTPException(
                status_code=400,
                detail="必须提供 loginToken 或 session 参数"
            )
        
        # 检查LoginVerifyClient是否可用
        if auth_middleware.login_verify_client is None:
            raise HTTPException(
                status_code=503,
                detail="登录验证服务不可用"
            )
        
        # 执行验证
        verify_session_id = session_id or login_token
        response = await auth_middleware.login_verify_client.verify_login_token_async(
            session_id=verify_session_id,
            region_id=region_id
        )
        
        if response.body and response.body.success:
            # 提取用户信息
            user_info = auth_middleware.login_verify_client.get_user_info_from_response(response)
            return {
                "code": 200,
                "message": "验证成功",
                "data": {
                    "valid": True,
                    "user_info": user_info
                }
            }
        else:
            error_msg = response.body.message if response.body else "Unknown error"
            return {
                "code": 401,
                "message": f"验证失败: {error_msg}",
                "data": {
                    "valid": False
                }
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证登录令牌时发生异常: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"内部服务器错误: {str(e)}"
        )


@app.get("/api/auth/methods")
async def get_auth_methods():
    """
    获取支持的认证方式说明
    """
    return {
        "code": 200,
        "message": "success",
        "data": {
            "supported_methods": [
                {
                    "method": "login_token_verification",
                    "description": "使用LoginVerifyClient验证登录令牌",
                    "priority": 1,
                    "parameters": [
                        {
                            "name": "loginToken",
                            "location": "query_params or headers(X-Login-Token)",
                            "required": False,
                            "description": "登录令牌"
                        },
                        {
                            "name": "session",
                            "location": "query_params or headers(X-Session-Id)",
                            "required": False,
                            "description": "会话ID（优先于loginToken）"
                        },
                        {
                            "name": "regionId",
                            "location": "query_params or headers(X-Region-Id)",
                            "required": False,
                            "description": "区域ID"
                        }
                    ]
                }
            ],
            "examples": {
                "login_token_query": "GET /api/user/profile?loginToken=xxx&regionId=cn-hangzhou",
                "session_query": "GET /api/user/profile?session=xxx&regionId=cn-hangzhou",
                "login_token_header": "GET /api/user/profile (with headers: X-Login-Token: xxx, X-Region-Id: cn-hangzhou)",
                "session_header": "GET /api/user/profile (with headers: X-Session-Id: xxx, X-Region-Id: cn-hangzhou)"
            }
        }
    }


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "code": 500,
            "message": "内部服务器错误",
            "data": None
        }
    )


if __name__ == "__main__":
    import uvicorn
    
    logger.info("=== AuthMiddleware 使用示例服务 ===")
    logger.info("支持的认证方式:")
    logger.info("1. 登录令牌验证: ?loginToken=xxx&regionId=xxx")
    logger.info("2. 会话ID验证: ?session=xxx&regionId=xxx")
    logger.info("3. 请求头验证: X-Login-Token, X-Session-Id, X-Region-Id")
    logger.info("")
    logger.info("测试端点:")
    logger.info("- GET /api/user/profile (需要认证)")
    logger.info("- GET /api/user/info (可选认证)")
    logger.info("- POST /api/auth/verify (手动验证)")
    logger.info("- GET /api/auth/methods (认证方式说明)")
    logger.info("")
    logger.info("启动服务在 http://localhost:8000")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
