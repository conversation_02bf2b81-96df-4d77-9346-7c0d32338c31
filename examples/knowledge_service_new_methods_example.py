"""
知识库服务新方法使用示例
演示 get_knowledge_base、update_knowledge_base、delete_knowledge_base 的使用
"""

import uuid
from typing import Optional
from src.domain.services.knowledge_service import knowledgebase_service
from src.application.rag_api_models import (
    KnowledgeBaseDetailResponse,
    KnowledgeBaseUpdateResponse,
    KnowledgeBaseDeleteResponse
)


def example_get_knowledge_base(kb_id: str, ali_uid: int, wy_id: str):
    """获取知识库详情示例"""
    try:
        print(f"🔍 获取知识库详情: {kb_id}")
        
        # 调用服务获取知识库详情
        knowledge_base = knowledgebase_service.get_knowledge_base(
            kb_id=kb_id,
            owner_ali_uid=ali_uid,
            owner_wy_id=wy_id
        )
        
        print(f"✅ 成功获取知识库详情:")
        print(f"   知识库ID: {knowledge_base.kb_id}")
        print(f"   名称: {knowledge_base.name}")
        print(f"   描述: {knowledge_base.description}")
        print(f"   所有者阿里UID: {knowledge_base.owner_ali_uid}")
        print(f"   所有者无影ID: {knowledge_base.owner_wy_id}")
        print(f"   创建时间: {knowledge_base.gmt_created}")
        print(f"   更新时间: {knowledge_base.gmt_modified}")
        
        return knowledge_base
        
    except ValueError as e:
        print(f"❌ 获取知识库详情失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        return None


def example_update_knowledge_base(
    kb_id: str, 
    ali_uid: int, 
    wy_id: str, 
    name: Optional[str] = None, 
    description: Optional[str] = None,
    is_update_selective: bool = True
):
    """更新知识库信息示例"""
    try:
        print(f"✏️ 更新知识库信息: {kb_id}")
        if name:
            print(f"   新名称: {name}")
        if description:
            print(f"   新描述: {description}")
        
        # 调用服务更新知识库
        updated_kb = knowledgebase_service.update_knowledge_base(
            kb_id=kb_id,
            owner_ali_uid=ali_uid,
            owner_wy_id=wy_id,
            name=name,
            description=description,
            is_update_selective=is_update_selective
        )
        
        print(f"✅ 成功更新知识库:")
        print(f"   知识库ID: {updated_kb.kb_id}")
        print(f"   名称: {updated_kb.name}")
        print(f"   描述: {updated_kb.description}")
        print(f"   更新时间: {updated_kb.gmt_modified}")
        
        return updated_kb
        
    except ValueError as e:
        print(f"❌ 更新知识库失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        return None


def example_clear_knowledge_base_description(kb_id: str, ali_uid: int, wy_id: str):
    """清空知识库描述示例"""
    try:
        print(f"🧹 清空知识库描述: {kb_id}")
        
        # 调用服务清空知识库描述（使用非选择性更新）
        updated_kb = knowledgebase_service.update_knowledge_base(
            kb_id=kb_id,
            owner_ali_uid=ali_uid,
            owner_wy_id=wy_id,
            description=None,
            is_update_selective=False
        )
        
        print(f"✅ 成功清空知识库描述:")
        print(f"   知识库ID: {updated_kb.kb_id}")
        print(f"   名称: {updated_kb.name}")
        print(f"   描述: {updated_kb.description}")
        print(f"   更新时间: {updated_kb.gmt_modified}")
        
        return updated_kb
        
    except ValueError as e:
        print(f"❌ 清空知识库描述失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        return None


def example_delete_knowledge_base(kb_id: str, ali_uid: int, wy_id: str):
    """删除知识库示例"""
    try:
        print(f"🗑️ 删除知识库: {kb_id}")
        
        # 调用服务删除知识库
        result = knowledgebase_service.delete_knowledge_base(
            kb_id=kb_id,
            owner_ali_uid=ali_uid,
            owner_wy_id=wy_id
        )
        
        print(f"✅ 成功删除知识库:")
        print(f"   知识库ID: {result.kb_id}")
        print(f"   删除状态: {'成功' if result.success else '失败'}")
        
        return result
        
    except ValueError as e:
        print(f"❌ 删除知识库失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        return None


def example_complete_workflow():
    """完整的工作流程示例"""
    print("🚀 开始知识库管理完整工作流程示例...")
    print("=" * 60)
    
    # 示例用户信息
    ali_uid = 123456789
    wy_id = "wy_user_001"
    
    # 假设已经有一个知识库ID（实际使用时需要先创建）
    kb_id = "kb_example_123"
    
    # 1. 获取知识库详情
    print("\n1️⃣ 获取知识库详情:")
    knowledge_base = example_get_knowledge_base(kb_id, ali_uid, wy_id)
    
    if knowledge_base:
        # 2. 更新知识库信息
        print("\n2️⃣ 更新知识库信息:")
        updated_kb = example_update_knowledge_base(
            kb_id=kb_id,
            ali_uid=ali_uid,
            wy_id=wy_id,
            name="更新后的知识库名称",
            description="这是更新后的知识库描述"
        )
        
        if updated_kb:
            # 3. 再次获取详情确认更新
            print("\n3️⃣ 确认更新结果:")
            example_get_knowledge_base(kb_id, ali_uid, wy_id)
            
            # 4. 清空知识库描述
            print("\n4️⃣ 清空知识库描述:")
            example_clear_knowledge_base_description(kb_id, ali_uid, wy_id)
            
            # 5. 删除知识库（注释掉，避免误删）
            print("\n5️⃣ 删除知识库 (已注释，避免误删):")
            # result = example_delete_knowledge_base(kb_id, ali_uid, wy_id)
            print("   ⚠️ 删除操作已注释，如需测试请取消注释")
    
    print("\n" + "=" * 60)
    print("✅ 完整工作流程示例结束!")


def example_error_handling():
    """错误处理示例"""
    print("\n🔧 错误处理示例:")
    print("=" * 40)
    
    ali_uid = 123456789
    wy_id = "wy_user_001"
    
    # 1. 获取不存在的知识库
    print("\n1️⃣ 获取不存在的知识库:")
    example_get_knowledge_base("non_existent_kb", ali_uid, wy_id)
    
    # 2. 无权限操作
    print("\n2️⃣ 无权限操作:")
    example_get_knowledge_base("kb_example_123", 999999, "different_user")
    
    # 3. 参数验证
    print("\n3️⃣ 参数验证:")
    try:
        knowledgebase_service.get_knowledge_base("", ali_uid, wy_id)
    except Exception as e:
        print(f"   空知识库ID错误: {e}")
    
    try:
        knowledgebase_service.get_knowledge_base("kb_123", None, wy_id)
    except Exception as e:
        print(f"   空用户ID错误: {e}")


if __name__ == "__main__":
    # 运行完整示例
    example_complete_workflow()
    
    # 运行错误处理示例
    example_error_handling() 