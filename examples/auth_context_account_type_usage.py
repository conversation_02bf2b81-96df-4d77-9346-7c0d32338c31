#!/usr/bin/env python3
"""
AuthContext account_type 使用示例
展示如何使用新增的 account_type 字段
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from src.domain.services.auth_service import AuthContext, AuthService


def example_basic_usage():
    """基本使用示例"""
    logger.info("=== AuthContext account_type 基本使用示例 ===")
    
    # 创建包含 account_type 的认证上下文
    context = AuthContext(
        ali_uid=****************,
        wy_id="user_001",
        end_user_id="end_user_001",
        account_type="ALIYUN"
    )
    
    logger.info(f"用户信息:")
    logger.info(f"  Ali UID: {context.ali_uid}")
    logger.info(f"  WY ID: {context.wy_id}")
    logger.info(f"  End User ID: {context.end_user_id}")
    logger.info(f"  Account Type: {context.account_type}")
    logger.info(f"  User Key: {context.user_key}")
    
    # 转换为字典
    context_dict = context.to_dict()
    logger.info(f"上下文字典: {context_dict}")


def example_auth_service_usage():
    """使用 AuthService 创建上下文示例"""
    logger.info("=== 使用 AuthService 创建上下文示例 ===")
    
    auth_service = AuthService()
    
    # 创建不同类型的用户上下文
    contexts = [
        {
            "ali_uid": 1001,
            "wy_id": "aliyun_user",
            "end_user_id": "aliyun_001",
            "account_type": "ALIYUN",
            "description": "阿里云账户用户"
        },
        {
            "ali_uid": 1002,
            "wy_id": "simple_user",
            "end_user_id": "simple_001",
            "account_type": "SIMPLE",
            "description": "简单账户用户"
        },
        {
            "ali_uid": 1003,
            "wy_id": "enterprise_user",
            "end_user_id": "enterprise_001",
            "account_type": "ENTERPRISE",
            "description": "企业账户用户"
        },
        {
            "ali_uid": 1004,
            "wy_id": "guest_user",
            "end_user_id": "guest_001",
            "account_type": None,
            "description": "访客用户（无账户类型）"
        }
    ]
    
    for user_data in contexts:
        context = auth_service.create_auth_context(
            ali_uid=user_data["ali_uid"],
            wy_id=user_data["wy_id"],
            end_user_id=user_data["end_user_id"],
            account_type=user_data["account_type"]
        )
        
        logger.info(f"{user_data['description']}:")
        logger.info(f"  User Key: {context.user_key}")
        logger.info(f"  Account Type: {context.account_type}")
        logger.info("")


def example_account_type_based_logic():
    """基于 account_type 的业务逻辑示例"""
    logger.info("=== 基于 account_type 的业务逻辑示例 ===")
    
    def get_user_permissions(context: AuthContext) -> list:
        """根据账户类型获取用户权限"""
        if context.account_type == "ALIYUN":
            return ["read", "write", "delete", "admin"]
        elif context.account_type == "ENTERPRISE":
            return ["read", "write", "delete"]
        elif context.account_type == "SIMPLE":
            return ["read", "write"]
        else:
            return ["read"]  # 默认或访客权限
    
    def get_resource_quota(context: AuthContext) -> dict:
        """根据账户类型获取资源配额"""
        quotas = {
            "ALIYUN": {"storage": "unlimited", "api_calls": "unlimited"},
            "ENTERPRISE": {"storage": "100GB", "api_calls": "10000/day"},
            "SIMPLE": {"storage": "10GB", "api_calls": "1000/day"},
            None: {"storage": "1GB", "api_calls": "100/day"}  # 访客配额
        }
        return quotas.get(context.account_type, quotas[None])
    
    def can_access_premium_features(context: AuthContext) -> bool:
        """检查是否可以访问高级功能"""
        premium_account_types = ["ALIYUN", "ENTERPRISE"]
        return context.account_type in premium_account_types
    
    # 测试不同账户类型的用户
    test_users = [
        AuthContext(1001, "aliyun_user", account_type="ALIYUN"),
        AuthContext(1002, "enterprise_user", account_type="ENTERPRISE"),
        AuthContext(1003, "simple_user", account_type="SIMPLE"),
        AuthContext(1004, "guest_user", account_type=None),
    ]
    
    for context in test_users:
        logger.info(f"用户 {context.wy_id} (账户类型: {context.account_type}):")
        
        permissions = get_user_permissions(context)
        logger.info(f"  权限: {permissions}")
        
        quota = get_resource_quota(context)
        logger.info(f"  配额: {quota}")
        
        premium_access = can_access_premium_features(context)
        logger.info(f"  高级功能访问: {'是' if premium_access else '否'}")
        logger.info("")


def example_backward_compatibility():
    """向后兼容性示例"""
    logger.info("=== 向后兼容性示例 ===")
    
    # 旧的创建方式仍然有效
    old_style_context = AuthContext(123456, "old_user")
    logger.info(f"旧式创建:")
    logger.info(f"  User Key: {old_style_context.user_key}")
    logger.info(f"  Account Type: {old_style_context.account_type}")  # 应该是 None
    
    # 新的创建方式
    new_style_context = AuthContext(
        ali_uid=123456,
        wy_id="new_user",
        end_user_id="new_end_user",
        account_type="ALIYUN"
    )
    logger.info(f"新式创建:")
    logger.info(f"  User Key: {new_style_context.user_key}")
    logger.info(f"  Account Type: {new_style_context.account_type}")
    
    # 混合方式
    mixed_context = AuthContext(123456, "mixed_user", account_type="SIMPLE")
    logger.info(f"混合方式:")
    logger.info(f"  User Key: {mixed_context.user_key}")
    logger.info(f"  Account Type: {mixed_context.account_type}")


def example_integration_with_login_verify():
    """与登录验证集成的示例"""
    logger.info("=== 与登录验证集成的示例 ===")
    
    # 模拟从登录验证服务获取的用户信息
    mock_user_info = {
        "ali_uid": ****************,
        "wy_id": "verified_user",
        "end_user_id": "verified_end_user",
        "account_type": "ALIYUN",  # 从登录验证服务获取
        "login_type": "PASSWORD",
        "api_key_id": "test_api_key"
    }
    
    # 创建认证上下文
    auth_service = AuthService()
    context = auth_service.create_auth_context(
        ali_uid=mock_user_info["ali_uid"],
        wy_id=mock_user_info["wy_id"],
        end_user_id=mock_user_info["end_user_id"],
        account_type=mock_user_info["account_type"]
    )
    
    logger.info(f"从登录验证服务创建的上下文:")
    logger.info(f"  完整信息: {context.to_dict()}")
    
    # 在实际应用中，这个上下文会被用于后续的权限检查
    logger.info(f"用户 {context.wy_id} 的账户类型为 {context.account_type}，可以进行相应的权限控制")


def main():
    """主函数"""
    logger.info("开始 AuthContext account_type 使用示例...")
    
    # 基本使用示例
    example_basic_usage()
    logger.info("")
    
    # AuthService 使用示例
    example_auth_service_usage()
    logger.info("")
    
    # 基于 account_type 的业务逻辑
    example_account_type_based_logic()
    logger.info("")
    
    # 向后兼容性示例
    example_backward_compatibility()
    logger.info("")
    
    # 与登录验证集成示例
    example_integration_with_login_verify()
    
    logger.success("所有 AuthContext account_type 使用示例完成！")


if __name__ == "__main__":
    main()
