#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间工具类
提供时间格式转换功能
"""

from datetime import datetime, timezone
from typing import Optional


class TimeUtils:
    """时间工具类"""

    @staticmethod
    def to_iso8601_utc(dt: datetime) -> str:
        """
        将 datetime 转换为 ISO 8601 格式的字符串，使用 UTC+0 时间
        
        Args:
            dt: datetime 对象
            
        Returns:
            str: ISO 8601 格式的字符串，如 "2017-12-08T22:40Z"
            
        Raises:
            ValueError: 如果输入的 dt 为 None
        """
        if dt is None:
            raise ValueError("datetime 对象不能为 None")
        
        # 如果 datetime 没有时区信息，假设为 UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        
        # 转换为 UTC 时间
        utc_dt = dt.astimezone(timezone.utc)
        
        # 格式化为 ISO 8601 格式，去掉微秒，使用 Z 表示 UTC
        return utc_dt.strftime("%Y-%m-%dT%H:%MZ")
    
    @staticmethod
    def from_iso8601_utc(iso_str: str) -> datetime:
        """
        将 ISO 8601 格式的字符串转换为 datetime 对象
        
        Args:
            iso_str: ISO 8601 格式的字符串，如 "2017-12-08T22:40Z"
            
        Returns:
            datetime: 对应的 datetime 对象（UTC 时区）
            
        Raises:
            ValueError: 如果字符串格式不正确
        """
        if not iso_str:
            raise ValueError("ISO 8601 字符串不能为空")
        
        try:
            # 解析 ISO 8601 格式的字符串
            dt = datetime.fromisoformat(iso_str.replace('Z', '+00:00'))
            return dt
        except ValueError as e:
            raise ValueError(f"无效的 ISO 8601 格式: {iso_str}") from e
    
    @staticmethod
    def now_iso8601_utc() -> str:
        """
        获取当前时间的 ISO 8601 格式字符串（UTC）
        
        Returns:
            str: 当前时间的 ISO 8601 格式字符串
        """
        return TimeUtils.to_iso8601_utc(datetime.now(timezone.utc))
    
    @staticmethod
    def format_datetime(dt: datetime, format_str: str = "%Y-%m-%dT%H:%MZ") -> str:
        """
        将 datetime 格式化为指定格式的字符串
        
        Args:
            dt: datetime 对象
            format_str: 格式字符串，默认为 ISO 8601 格式
            
        Returns:
            str: 格式化后的字符串
        """
        if dt is None:
            raise ValueError("datetime 对象不能为 None")
        
        # 如果 datetime 没有时区信息，假设为 UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        
        # 转换为 UTC 时间
        utc_dt = dt.astimezone(timezone.utc)
        
        return utc_dt.strftime(format_str)


# 创建全局实例，方便直接调用
time_utils = TimeUtils()
```

现在让我创建一个使用示例文件：

```python:examples/time_utils_example.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间工具类使用示例
"""

from datetime import datetime, timezone
from src.domain.utils.time_utils import TimeUtils, time_utils


def example_datetime_to_iso8601():
    """示例：将 datetime 转换为 ISO 8601 格式"""
    print("=== datetime 转 ISO 8601 格式 ===")
    
    # 示例1：带时区的 datetime
    dt_with_tz = datetime(2017, 12, 8, 22, 40, 0, tzinfo=timezone.utc)
    iso_str = TimeUtils.to_iso8601_utc(dt_with_tz)
    print(f"带时区的 datetime: {dt_with_tz}")
    print(f"ISO 8601 格式: {iso_str}")
    print()
    
    # 示例2：不带时区的 datetime（假设为 UTC）
    dt_without_tz = datetime(2017, 12, 8, 22, 40, 0)
    iso_str = TimeUtils.to_iso8601_utc(dt_without_tz)
    print(f"不带时区的 datetime: {dt_without_tz}")
    print(f"ISO 8601 格式: {iso_str}")
    print()
    
    # 示例3：其他时区的 datetime
    beijing_tz = timezone.utc.replace(hour=8)  # UTC+8
    dt_beijing = datetime(2017, 12, 8, 22, 40, 0, tzinfo=beijing_tz)
    iso_str = TimeUtils.to_iso8601_utc(dt_beijing)
    print(f"北京时间 datetime: {dt_beijing}")
    print(f"ISO 8601 格式 (UTC): {iso_str}")
    print()


def example_iso8601_to_datetime():
    """示例：将 ISO 8601 格式转换为 datetime"""
    print("=== ISO 8601 格式转 datetime ===")
    
    iso_str = "2017-12-08T22:40Z"
    dt = TimeUtils.from_iso8601_utc(iso_str)
    print(f"ISO 8601 字符串: {iso_str}")
    print(f"转换后的 datetime: {dt}")
    print(f"时区信息: {dt.tzinfo}")
    print()


def example_current_time():
    """示例：获取当前时间的 ISO 8601 格式"""
    print("=== 当前时间 ISO 8601 格式 ===")
    
    current_iso = TimeUtils.now_iso8601_utc()
    print(f"当前时间 ISO 8601 格式: {current_iso}")
    print()


def example_custom_format():
    """示例：自定义格式"""
    print("=== 自定义格式 ===")
    
    dt = datetime(2017, 12, 8, 22, 40, 30, tzinfo=timezone.utc)
    
    # 默认格式
    default_format = TimeUtils.format_datetime(dt)
    print(f"默认格式: {default_format}")
    
    # 自定义格式
    custom_format = TimeUtils.format_datetime(dt, "%Y年%m月%d日 %H:%M:%S UTC")
    print(f"自定义格式: {custom_format}")
    print()


def example_global_instance():
    """示例：使用全局实例"""
    print("=== 使用全局实例 ===")
    
    dt = datetime(2017, 12, 8, 22, 40, 0, tzinfo=timezone.utc)
    
    # 使用全局实例
    iso_str = time_utils.to_iso8601_utc(dt)
    print(f"使用全局实例: {iso_str}")
    print()


def example_error_handling():
    """示例：错误处理"""
    print("=== 错误处理 ===")
    
    try:
        # 测试 None 值
        TimeUtils.to_iso8601_utc(None)
    except ValueError as e:
        print(f"None 值错误: {e}")
    
    try:
        # 测试无效的 ISO 8601 格式
        TimeUtils.from_iso8601_utc("invalid-format")
    except ValueError as e:
        print(f"无效格式错误: {e}")
    
    print()


if __name__ == "__main__":
    example_datetime_to_iso8601()
    example_iso8601_to_datetime()
    example_current_time()
    example_custom_format()
    example_global_instance()
    example_error_handling()
```

## 功能特点

1. **ISO 8601 标准格式**：输出格式为 `yyyy-MM-ddTHH:mmZ`
2. **UTC+0 时间**：所有时间都转换为 UTC 时区
3. **自动时区处理**：
   - 如果输入 datetime 没有时区信息，假设为 UTC
   - 如果输入 datetime 有其他时区，自动转换为 UTC
4. **微秒处理**：自动移除微秒部分
5. **错误处理**：对 None 值和无效格式进行适当的错误处理
6. **双向转换**：支持 datetime 到字符串和字符串到 datetime 的转换
7. **全局实例**：提供 `time_utils` 全局实例，方便直接调用

## 使用示例

```python
from src.domain.utils.time_utils import time_utils
from datetime import datetime, timezone

# 转换 datetime 为 ISO 8601 格式
dt = datetime(2017, 12, 8, 22, 40, 0, tzinfo=timezone.utc)
iso_str = time_utils.to_iso8601_utc(dt)
print(iso_str)  # 输出: 2017-12-08T22:40Z

# 获取当前时间的 ISO 8601 格式
current_iso = time_utils.now_iso8601_utc()
print(current_iso)  # 输出当前时间的 ISO 8601 格式
```

这个工具类完全符合你的需求，提供了标准的 ISO 8601 格式转换功能。 