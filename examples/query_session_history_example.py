#!/usr/bin/env python3
"""
会话历史查询API使用示例
展示新的分页参数使用方式
"""

import requests
import json
from typing import Optional

def query_session_history(
    base_url: str,
    session_id: str,
    page: int = 1,
    page_size: int = 20,
    before_round_id: Optional[str] = None
):
    """
    查询会话历史的示例函数
    
    Args:
        base_url: API基础URL
        session_id: 会话ID
        page: 页码，从1开始
        page_size: 每页数量
        before_round_id: 查询此轮次ID之前的消息
    
    Returns:
        dict: API响应结果
    """
    url = f"{base_url}/api/sessions/query"
    
    params = {
        "session_id": session_id,
        "page": page,
        "page_size": page_size
    }
    
    if before_round_id:
        params["before_round_id"] = before_round_id
    
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return None

def main():
    """示例用法"""
    base_url = "http://localhost:8000"  # 假设服务运行在本地8000端口
    session_id = "test_session_123"
    
    print("=== 会话历史查询API使用示例 ===\n")
    
    # 示例1: 基本查询（第一页，默认20条）
    print("1. 基本查询（第一页，默认20条）:")
    result = query_session_history(base_url, session_id)
    if result:
        print(f"   会话ID: {result.get('session_id')}")
        print(f"   总轮数: {result.get('total_rounds')}")
        print(f"   返回轮数: {len(result.get('rounds', []))}")
    print()
    
    # 示例2: 分页查询（第2页，每页10条）
    print("2. 分页查询（第2页，每页10条）:")
    result = query_session_history(base_url, session_id, page=2, page_size=10)
    if result:
        print(f"   会话ID: {result.get('session_id')}")
        print(f"   返回轮数: {len(result.get('rounds', []))}")
    print()
    
    # 示例3: 查询指定轮次之前的消息
    print("3. 查询指定轮次之前的消息:")
    result = query_session_history(base_url, session_id, before_round_id="100")
    if result:
        print(f"   会话ID: {result.get('session_id')}")
        print(f"   返回轮数: {len(result.get('rounds', []))}")
        if result.get('rounds'):
            max_round_id = max(int(r['roundId']) for r in result['rounds'])
            print(f"   最大轮次ID: {max_round_id} (应该小于100)")
    print()
    
    # 示例4: 组合查询（第1页，每页5条，指定轮次之前）
    print("4. 组合查询（第1页，每页5条，指定轮次之前）:")
    result = query_session_history(
        base_url, 
        session_id, 
        page=1, 
        page_size=5, 
        before_round_id="50"
    )
    if result:
        print(f"   会话ID: {result.get('session_id')}")
        print(f"   返回轮数: {len(result.get('rounds', []))}")
    print()

def compare_old_vs_new_api():
    """对比新旧API参数"""
    print("=== API参数对比 ===\n")
    
    print("旧API参数:")
    print("  - session_id: str")
    print("  - limit: int = 20")
    print("  - before_round_id: Optional[str] = None")
    print()
    
    print("新API参数（与memory_sdk对齐）:")
    print("  - session_id: str")
    print("  - page: int = 1")
    print("  - page_size: int = 20")
    print("  - before_round_id: Optional[str] = None")
    print()
    
    print("主要变化:")
    print("  1. 添加了 page 参数，支持真正的分页")
    print("  2. limit 改名为 page_size，语义更清晰")
    print("  3. 参数与 memory_sdk.get_session_messages 完全对齐")
    print("  4. session_manager 现在调用 memory_sdk 而不是直接访问内部实现")
    print()

if __name__ == "__main__":
    compare_old_vs_new_api()
    print("\n" + "="*50 + "\n")
    main()
