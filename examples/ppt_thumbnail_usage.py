#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT缩略图功能使用示例
展示如何使用PPTService的get_ppt_thumbnail方法
"""

from src.domain.services.ppt_service import PPTService, PPTServiceError
from loguru import logger

def example_get_thumbnail_basic():
    """示例：基本的PPT缩略图获取功能"""
    print("=== PPT缩略图获取基本示例 ===")
    
    try:
        # 创建PPT服务实例
        ppt_service = PPTService()
        
        # 模拟场景：获取PPT的缩略图
        ppt_id = "ppt_demo_12345"
        
        print(f"🎨 PPT作品ID: {ppt_id}")
        
        # 获取PPT缩略图
        print("\n🖼️  开始获取PPT缩略图...")
        thumbnail_url = ppt_service.get_ppt_thumbnail(ppt_id=ppt_id)
        
        if thumbnail_url:
            print(f"✅ 缩略图获取成功！")
            print(f"🖼️  缩略图URL: {thumbnail_url}")
            print(f"📏 URL长度: {len(thumbnail_url)} 字符")
        else:
            print(f"⚠️  该PPT暂无缩略图")
            print(f"💡 返回空字符串，可使用默认图片")
        
    except PPTServiceError as e:
        print(f"❌ PPT服务错误: {e}")
    except Exception as e:
        print(f"💥 未知错误: {e}")

def example_batch_thumbnails():
    """示例：批量获取多个PPT的缩略图"""
    print("\n=== 批量获取PPT缩略图示例 ===")
    
    try:
        ppt_service = PPTService()
        
        # 模拟场景：用户的PPT列表
        ppt_list = [
            {"id": "ppt_work_001", "name": "工作汇报PPT"},
            {"id": "ppt_proposal_002", "name": "项目提案PPT"},
            {"id": "ppt_training_003", "name": "培训材料PPT"},
            {"id": "ppt_meeting_004", "name": "会议总结PPT"}
        ]
        
        print(f"📋 要获取缩略图的PPT数量: {len(ppt_list)}")
        
        # 逐个获取缩略图
        results = []
        for i, ppt in enumerate(ppt_list, 1):
            print(f"\n🖼️  获取第{i}个PPT缩略图...")
            print(f"   PPT ID: {ppt['id']}")
            print(f"   PPT名称: {ppt['name']}")
            
            try:
                thumbnail_url = ppt_service.get_ppt_thumbnail(ppt['id'])
                
                if thumbnail_url:
                    print(f"   ✅ 有缩略图: {thumbnail_url[:50]}...")
                    results.append({
                        "ppt": ppt,
                        "thumbnail_url": thumbnail_url,
                        "has_thumbnail": True
                    })
                else:
                    print(f"   ⚠️  无缩略图")
                    results.append({
                        "ppt": ppt,
                        "thumbnail_url": "",
                        "has_thumbnail": False
                    })
                    
            except Exception as e:
                print(f"   ❌ 获取失败: {e}")
                results.append({
                    "ppt": ppt,
                    "thumbnail_url": "",
                    "has_thumbnail": False,
                    "error": str(e)
                })
        
        # 统计结果
        total_count = len(results)
        success_count = len([r for r in results if r.get("has_thumbnail", False)])
        
        print(f"\n📊 批量获取结果统计:")
        print(f"   总数量: {total_count}")
        print(f"   有缩略图: {success_count}")
        print(f"   无缩略图: {total_count - success_count}")
        print(f"   成功率: {success_count/total_count*100:.1f}%")
        
    except Exception as e:
        print(f"❌ 批量获取失败: {e}")

def example_ui_integration():
    """示例：UI界面集成场景"""
    print("\n=== UI界面集成示例 ===")
    
    try:
        ppt_service = PPTService()
        
        # 模拟场景：为UI界面准备PPT展示数据
        ppt_id = "ppt_ui_demo_001"
        
        print(f"🎨 准备UI展示数据: {ppt_id}")
        
        # 获取缩略图
        thumbnail_url = ppt_service.get_ppt_thumbnail(ppt_id)
        
        # 准备UI数据结构
        ui_data = {
            "ppt_id": ppt_id,
            "thumbnail_url": thumbnail_url if thumbnail_url else "/static/images/default_ppt_thumbnail.png",
            "has_custom_thumbnail": bool(thumbnail_url),
            "display_config": {
                "show_placeholder": not bool(thumbnail_url),
                "lazy_load": bool(thumbnail_url),
                "alt_text": f"PPT {ppt_id} 缩略图" if thumbnail_url else "默认PPT缩略图"
            }
        }
        
        print(f"\n🖼️  UI数据结构:")
        print(f"   PPT ID: {ui_data['ppt_id']}")
        print(f"   缩略图URL: {ui_data['thumbnail_url']}")
        print(f"   是否为自定义缩略图: {ui_data['has_custom_thumbnail']}")
        print(f"   显示配置: {ui_data['display_config']}")
        
        # 模拟UI渲染逻辑
        if ui_data['has_custom_thumbnail']:
            print(f"\n🎨 UI渲染: 使用自定义缩略图")
            print(f"   <img src='{ui_data['thumbnail_url']}' alt='{ui_data['display_config']['alt_text']}' loading='lazy'>")
        else:
            print(f"\n🎨 UI渲染: 使用默认缩略图")
            print(f"   <img src='{ui_data['thumbnail_url']}' alt='{ui_data['display_config']['alt_text']}' class='default-thumbnail'>")
        
    except Exception as e:
        print(f"❌ UI集成示例失败: {e}")

def example_error_handling():
    """示例：错误处理场景"""
    print("\n=== 错误处理示例 ===")
    
    ppt_service = PPTService()
    
    # 测试各种错误场景
    test_cases = [
        {"id": "", "name": "空ID"},
        {"id": None, "name": "None ID"},
        {"id": "invalid_ppt_id", "name": "无效PPT ID"},
        {"id": "ppt_network_error", "name": "网络错误模拟"}
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试{i}: {test_case['name']}")
        try:
            if test_case['id'] is None:
                # 注意：实际代码中不应传入None，这里只是为了测试错误处理
                thumbnail_url = ppt_service.get_ppt_thumbnail("")  # 用空字符串代替None进行测试
            else:
                thumbnail_url = ppt_service.get_ppt_thumbnail(test_case['id'])
            
            print(f"   ✅ 成功: 返回 '{thumbnail_url}'")
            
        except PPTServiceError as e:
            print(f"   ⚠️  PPT服务错误: {e}")
        except Exception as e:
            print(f"   💥 其他错误: {type(e).__name__}: {e}")
    
    print("\n💡 错误处理建议:")
    print("   1. 捕获PPTServiceError处理业务错误")
    print("   2. 空缩略图时使用默认图片")
    print("   3. 网络错误时显示占位符")
    print("   4. 记录错误日志便于调试")

def example_performance_optimization():
    """示例：性能优化场景"""
    print("\n=== 性能优化示例 ===")
    
    try:
        ppt_service = PPTService()
        
        print("🚀 性能优化建议:")
        print("   1. 缩略图URL缓存 - 避免重复API调用")
        print("   2. 批量获取优化 - 考虑并发处理")
        print("   3. 懒加载策略 - 用户滚动时才加载")
        print("   4. 图片预处理 - 服务端生成多尺寸缩略图")
        
        # 模拟缓存场景
        ppt_id = "ppt_cache_demo"
        cache_key = f"ppt_thumbnail:{ppt_id}"
        
        print(f"\n💾 缓存策略示例:")
        print(f"   缓存Key: {cache_key}")
        print(f"   缓存时间: 3600秒 (1小时)")
        print(f"   失效策略: PPT更新时清除缓存")
        
        # 模拟使用
        print(f"\n📱 实际使用建议:")
        print(f"   # 前端代码示例")
        print(f"   const thumbnail = await getThumbnail('{ppt_id}');")
        print(f"   if (thumbnail) {{")
        print(f"     setImageSrc(thumbnail);")
        print(f"   }} else {{")
        print(f"     setImageSrc('/default-thumbnail.png');")
        print(f"   }}")
        
    except Exception as e:
        print(f"❌ 性能优化示例失败: {e}")

if __name__ == "__main__":
    print("🚀 PPT缩略图功能使用示例")
    print("=" * 50)
    
    # 运行各种示例
    example_get_thumbnail_basic()
    example_batch_thumbnails()
    example_ui_integration()
    example_error_handling()
    example_performance_optimization()
    
    print("\n" + "=" * 50)
    print("📝 总结:")
    print("   1. get_ppt_thumbnail 方法用于获取PPT封面图URL")
    print("   2. 返回空字符串时表示无缩略图，应使用默认图片")
    print("   3. 支持批量获取，适合列表页面展示")
    print("   4. 集成到UI时考虑懒加载和错误处理")
    print("   5. 建议添加缓存机制提升性能")
    print("   6. 具备完整的错误处理和降级策略")
    print("\n💡 更多用法请参考测试文件: tests/test_ppt_service.py") 