#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用公共参数示例 - 同时支持POST和GET请求
"""
from fastapi import FastAPI, Query, Body, Depends, Request
from typing import Optional, List, Dict, Any
from pydantic import BaseModel

from src.presentation.api.dependencies.common_params import CommonParams, UniversalCommonParams

app = FastAPI()


# ==================== 示例1：GET请求 ====================
@app.get("/api/files/download-urls")
async def get_download_urls(
    file_ids: List[int] = Query(..., description="文件ID列表"),
    expires: int = Query(3600, description="过期时间"),
    common_params: CommonParams = UniversalCommonParams
):
    """
    获取下载链接 (GET)
    
    前端调用方式1（查询参数）：
    GET /api/files/download-urls?file_ids=1&file_ids=2&expires=7200&loginToken=abc123&sessionId=sess456&regionId=region789
    
    前端调用方式2（请求头）：
    GET /api/files/download-urls?file_ids=1&file_ids=2&expires=7200
    Headers:
        X-Login-Token: abc123
        X-Session-Id: sess456
        X-Region-Id: region789
    """
    
    return {
        "method": "GET",
        "file_ids": file_ids,
        "expires": expires,
        "login_token": common_params.login_token,
        "session_id": common_params.session_id,
        "region_id": common_params.region_id,
        "download_links": []
    }


# ==================== 示例2：POST请求（JSON） ====================
class DownloadUrlsRequest(BaseModel):
    """下载链接请求"""
    file_ids: List[int]
    expires: int = 3600
    # 可选的公共参数
    loginToken: Optional[str] = None
    sessionId: Optional[str] = None
    regionId: Optional[str] = None


@app.post("/api/files/download-urls")
async def post_download_urls(
    request: DownloadUrlsRequest,
    common_params: CommonParams = UniversalCommonParams
):
    """
    获取下载链接 (POST)
    
    前端调用方式1（JSON Body + 请求头）：
    POST /api/files/download-urls
    Headers:
        X-Login-Token: abc123
        X-Session-Id: sess456
        X-Region-Id: region789
    Body:
        {
            "file_ids": [1, 2, 3],
            "expires": 7200
        }
    
    前端调用方式2（JSON Body 包含公共参数）：
    POST /api/files/download-urls
    Body:
        {
            "file_ids": [1, 2, 3],
            "expires": 7200,
            "loginToken": "abc123",
            "sessionId": "sess456",
            "regionId": "region789"
        }
    
    前端调用方式3（查询参数 + JSON Body）：
    POST /api/files/download-urls?loginToken=abc123&sessionId=sess456&regionId=region789
    Body:
        {
            "file_ids": [1, 2, 3],
            "expires": 7200
        }
    """
    
    # 合并请求体中的公共参数（优先级更高）
    login_token = request.loginToken or common_params.login_token
    session_id = request.sessionId or common_params.session_id
    region_id = request.regionId or common_params.region_id
    
    return {
        "method": "POST",
        "file_ids": request.file_ids,
        "expires": request.expires,
        "login_token": login_token,
        "session_id": session_id,
        "region_id": region_id,
        "download_links": []
    }


# ==================== 示例3：POST请求（表单） ====================
@app.post("/api/files/upload")
async def upload_file(
    file_type: str = Form("sessionFile", description="文件类型"),
    file: UploadFile = File(..., description="要上传的文件"),
    common_params: CommonParams = UniversalCommonParams
):
    """
    上传文件 (POST 表单)
    
    前端调用方式1（表单 + 请求头）：
    POST /api/files/upload
    Headers:
        X-Login-Token: abc123
        X-Session-Id: sess456
        X-Region-Id: region789
    Form:
        file_type: sessionFile
        file: (binary)
    
    前端调用方式2（表单 + 查询参数）：
    POST /api/files/upload?loginToken=abc123&sessionId=sess456&regionId=region789
    Form:
        file_type: sessionFile
        file: (binary)
    """
    
    return {
        "method": "POST (Form)",
        "filename": file.filename,
        "file_type": file_type,
        "login_token": common_params.login_token,
        "session_id": common_params.session_id,
        "region_id": common_params.region_id,
        "upload_id": "upload_123"
    }


# ==================== 示例4：手动处理请求体中的公共参数 ====================
@app.post("/api/custom-endpoint")
async def custom_endpoint(
    request: Request,
    common_params: CommonParams = UniversalCommonParams
):
    """
    自定义端点 - 手动处理请求体中的公共参数
    """
    try:
        # 解析请求体
        body = await request.json()
        
        # 从请求体中提取公共参数（如果存在）
        login_token = body.get("loginToken") or common_params.login_token
        session_id = body.get("sessionId") or common_params.session_id
        region_id = body.get("regionId") or common_params.region_id
        
        return {
            "method": "POST (Custom)",
            "body": body,
            "login_token": login_token,
            "session_id": session_id,
            "region_id": region_id
        }
    except:
        # 如果请求体不是JSON，只使用通用参数
        return {
            "method": "POST (Custom)",
            "login_token": common_params.login_token,
            "session_id": common_params.session_id,
            "region_id": common_params.region_id
        }


if __name__ == "__main__":
    import uvicorn
    from fastapi import Form, File, UploadFile
    uvicorn.run(app, host="0.0.0.0", port=8000)
