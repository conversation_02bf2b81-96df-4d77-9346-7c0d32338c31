# -*- coding: utf-8 -*-
"""
参数映射和鉴权集成示例
演示如何将API参数映射到业务逻辑，并集成鉴权中间件
"""
import sys
import os
from typing import Optional, List
from fastapi import FastAPI, Query, Depends, HTTPException, Request
from pydantic import BaseModel

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.domain.services.auth_service import AuthContext
from src.presentation.middleware.auth_dependencies import require_auth

app = FastAPI(title="参数映射和鉴权集成示例")

# ============================================================================
# 1. 响应模型定义
# ============================================================================

class SessionInfo(BaseModel):
    """会话信息响应模型"""
    sessionId: str
    aliUid: int
    agentId: Optional[str] = None
    title: str
    status: str
    gmtCreate: str
    gmtModified: str
    metadata: dict = {}

class SessionListResponse(BaseModel):
    """会话列表响应模型"""
    code: int = 200
    message: str = "success"
    data: List[SessionInfo]
    pagination: dict

# ============================================================================
# 2. 参数映射和验证
# ============================================================================

class SessionQueryParams(BaseModel):
    """会话查询参数模型"""
    limit: int = 50
    offset: int = 0
    agentId: Optional[str] = None
    
    class Config:
        # 允许额外字段，用于扩展
        extra = "allow"

def parse_session_query_params(
    limit: int = Query(50, ge=1, le=100, description="返回的会话数量限制"),
    offset: int = Query(0, ge=0, description="分页偏移量"),
    agentId: Optional[str] = Query(None, description="按Agent ID过滤"),
    # 注意：这里不再有 ali_uid 参数，因为它来自鉴权
) -> SessionQueryParams:
    """
    解析和验证会话查询参数
    
    这个函数作为依赖，负责：
    1. 参数验证（通过Query的约束）
    2. 参数转换和标准化
    3. 返回结构化的参数对象
    """
    return SessionQueryParams(
        limit=limit,
        offset=offset,
        agentId=agentId
    )

# ============================================================================
# 3. 重构后的 list_sessions 方法
# ============================================================================

@app.get("/api/sessions/list", response_model=SessionListResponse)
async def list_sessions(
    # 鉴权参数：通过依赖注入自动处理 loginToken、sessionId、regionId
    current_user: AuthContext = Depends(require_auth),
    
    # 查询参数：通过依赖注入进行验证和解析
    query_params: SessionQueryParams = Depends(parse_session_query_params)
):
    """
    列出用户的会话列表
    
    参数映射说明：
    1. loginToken、sessionId、regionId -> 通过鉴权中间件 -> current_user (ali_uid, wy_id)
    2. limit、offset、agentId -> 通过参数解析 -> query_params
    3. ali_uid 不再是API参数，而是从鉴权结果中获取
    
    Args:
        current_user: 从鉴权中间件获取的用户信息（包含 ali_uid, wy_id）
        query_params: 解析后的查询参数
    
    Returns:
        SessionListResponse: 会话列表响应
    """
    try:
        # 从鉴权结果中获取用户ID
        ali_uid = current_user.ali_uid
        wy_id = current_user.wy_id
        
        # 模拟数据库查询（实际应该调用 session_db_service）
        sessions_data = await query_sessions_from_database(
            ali_uid=ali_uid,
            limit=query_params.limit,
            offset=query_params.offset,
            agent_id=query_params.agentId
        )
        
        # 构造响应
        sessions_info = []
        for session in sessions_data:
            sessions_info.append(SessionInfo(
                sessionId=session["session_id"],
                aliUid=session["ali_uid"],
                agentId=session.get("agent_id"),
                title=session.get("title", "无标题"),
                status=session.get("status", "active"),
                gmtCreate=session["gmt_create"],
                gmtModified=session["gmt_modified"],
                metadata=session.get("metadata", {})
            ))
        
        return SessionListResponse(
            data=sessions_info,
            pagination={
                "limit": query_params.limit,
                "offset": query_params.offset,
                "total": len(sessions_info),
                "hasMore": len(sessions_info) == query_params.limit
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询会话列表失败: {str(e)}")

# ============================================================================
# 4. 模拟数据库查询函数
# ============================================================================

async def query_sessions_from_database(
    ali_uid: int,
    limit: int,
    offset: int,
    agent_id: Optional[str] = None
) -> List[dict]:
    """
    模拟数据库查询函数
    
    在实际项目中，这里应该调用：
    from ....infrastructure.database.session_service import session_db_service
    return session_db_service.list_sessions(...)
    """
    # 模拟数据
    mock_sessions = [
        {
            "session_id": f"session_{i}",
            "ali_uid": ali_uid,
            "agent_id": agent_id or "default_agent",
            "title": f"会话 {i}",
            "status": "active",
            "gmt_create": "2024-01-01T10:00:00Z",
            "gmt_modified": "2024-01-01T10:00:00Z",
            "metadata": {"type": "chat"}
        }
        for i in range(offset, offset + limit)
    ]
    
    return mock_sessions

# ============================================================================
# 5. 高级参数映射示例
# ============================================================================

class AdvancedSessionQueryParams(BaseModel):
    """高级会话查询参数"""
    limit: int = 50
    offset: int = 0
    agentId: Optional[str] = None
    status: Optional[str] = None
    dateFrom: Optional[str] = None
    dateTo: Optional[str] = None
    keyword: Optional[str] = None

def parse_advanced_session_params(
    limit: int = Query(50, ge=1, le=100, description="返回的会话数量限制"),
    offset: int = Query(0, ge=0, description="分页偏移量"),
    agentId: Optional[str] = Query(None, description="按Agent ID过滤"),
    status: Optional[str] = Query(None, regex="^(active|inactive|archived)$", description="会话状态"),
    dateFrom: Optional[str] = Query(None, description="开始日期 (ISO格式)"),
    dateTo: Optional[str] = Query(None, description="结束日期 (ISO格式)"),
    keyword: Optional[str] = Query(None, min_length=1, max_length=100, description="关键词搜索")
) -> AdvancedSessionQueryParams:
    """
    高级参数解析，包含更多验证规则
    """
    # 可以在这里添加自定义验证逻辑
    if dateFrom and dateTo and dateFrom > dateTo:
        raise HTTPException(400, "开始日期不能晚于结束日期")
    
    return AdvancedSessionQueryParams(
        limit=limit,
        offset=offset,
        agentId=agentId,
        status=status,
        dateFrom=dateFrom,
        dateTo=dateTo,
        keyword=keyword
    )

@app.get("/api/sessions/advanced-list", response_model=SessionListResponse)
async def list_sessions_advanced(
    current_user: AuthContext = Depends(require_auth),
    query_params: AdvancedSessionQueryParams = Depends(parse_advanced_session_params)
):
    """
    高级会话列表查询
    
    支持更多过滤条件：
    - 状态过滤
    - 日期范围
    - 关键词搜索
    """
    ali_uid = current_user.ali_uid
    
    # 这里可以根据高级参数构建复杂的查询条件
    sessions_data = await query_sessions_advanced(
        ali_uid=ali_uid,
        params=query_params
    )
    
    sessions_info = [
        SessionInfo(
            sessionId=session["session_id"],
            aliUid=session["ali_uid"],
            agentId=session.get("agent_id"),
            title=session.get("title", "无标题"),
            status=session.get("status", "active"),
            gmtCreate=session["gmt_create"],
            gmtModified=session["gmt_modified"],
            metadata=session.get("metadata", {})
        )
        for session in sessions_data
    ]
    
    return SessionListResponse(
        data=sessions_info,
        pagination={
            "limit": query_params.limit,
            "offset": query_params.offset,
            "total": len(sessions_info),
            "hasMore": len(sessions_info) == query_params.limit
        }
    )

async def query_sessions_advanced(
    ali_uid: int,
    params: AdvancedSessionQueryParams
) -> List[dict]:
    """高级查询的模拟实现"""
    # 在实际项目中，这里会根据 params 构建复杂的数据库查询
    return [
        {
            "session_id": f"advanced_session_{i}",
            "ali_uid": ali_uid,
            "agent_id": params.agentId or "default_agent",
            "title": f"高级会话 {i}",
            "status": params.status or "active",
            "gmt_create": "2024-01-01T10:00:00Z",
            "gmt_modified": "2024-01-01T10:00:00Z",
            "metadata": {"type": "advanced_chat", "keyword": params.keyword}
        }
        for i in range(params.offset, params.offset + params.limit)
    ]

# ============================================================================
# 6. 参数映射的调试和监控
# ============================================================================

@app.get("/api/debug/parameter-mapping")
async def debug_parameter_mapping(
    request: Request,
    current_user: AuthContext = Depends(require_auth),
    query_params: SessionQueryParams = Depends(parse_session_query_params)
):
    """
    调试参数映射过程
    """
    return {
        "parameter_mapping": {
            "original_request": {
                "url": str(request.url),
                "query_params": dict(request.query_params),
                "headers": {
                    "loginToken": request.headers.get("X-Login-Token"),
                    "sessionId": request.headers.get("X-Session-Id"),
                    "regionId": request.headers.get("X-Region-Id")
                }
            },
            "auth_result": {
                "ali_uid": current_user.ali_uid,
                "wy_id": current_user.wy_id,
                "source": "从鉴权中间件获取"
            },
            "parsed_params": {
                "limit": query_params.limit,
                "offset": query_params.offset,
                "agentId": query_params.agentId,
                "source": "从查询参数解析"
            },
            "mapping_flow": [
                "1. 请求到达 -> 提取 loginToken/sessionId/regionId",
                "2. 鉴权中间件 -> 验证令牌 -> 获取 ali_uid/wy_id",
                "3. 参数解析器 -> 验证查询参数 -> 获取 limit/offset/agentId",
                "4. 业务逻辑 -> 使用 ali_uid + 查询参数 -> 查询数据库"
            ]
        }
    }

# ============================================================================
# 7. 实际项目中的集成示例
# ============================================================================

@app.get("/api/integration-example")
async def integration_example():
    """
    实际项目集成示例
    """
    return {
        "integration_guide": {
            "step1_update_route": {
                "description": "更新路由函数签名",
                "before": """
async def list_sessions(
    limit: int = Query(50),
    offset: int = Query(0),
    ali_uid: Optional[str] = Query(None),  # 移除这个
    agent_id: Optional[str] = Query(None)
):
                """,
                "after": """
async def list_sessions(
    current_user: AuthContext = Depends(require_auth),  # 添加鉴权
    query_params: SessionQueryParams = Depends(parse_session_query_params)  # 参数解析
):
                """
            },
            "step2_update_business_logic": {
                "description": "更新业务逻辑调用",
                "before": """
sessions = session_db_service.list_sessions(
    limit=limit,
    offset=offset,
    ali_uid=ali_uid,  # 来自查询参数
    agent_id=agent_id
)
                """,
                "after": """
sessions = session_db_service.list_sessions(
    limit=query_params.limit,
    offset=query_params.offset,
    ali_uid=current_user.ali_uid,  # 来自鉴权结果
    agent_id=query_params.agentId
)
                """
            },
            "step3_update_api_calls": {
                "description": "更新API调用方式",
                "before": """
GET /api/sessions/list?limit=10&offset=0&ali_uid=123456&agent_id=test
                """,
                "after": """
GET /api/sessions/list?limit=10&offset=0&agentId=test&loginToken=xxx&regionId=cn-hangzhou
# 或者
GET /api/sessions/list?limit=10&offset=0&agentId=test
Headers: X-Login-Token: xxx, X-Region-Id: cn-hangzhou
                """
            }
        }
    }

if __name__ == "__main__":
    import uvicorn
    
    print("=== 参数映射和鉴权集成示例 ===")
    print("")
    print("🔄 参数映射流程:")
    print("1. API参数: limit, offset, agentId, loginToken, sessionId, regionId")
    print("2. 鉴权处理: loginToken/sessionId/regionId -> ali_uid/wy_id")
    print("3. 参数解析: limit/offset/agentId -> 验证和结构化")
    print("4. 业务逻辑: ali_uid + 查询参数 -> 数据库查询")
    print("")
    print("📝 测试端点:")
    print("- GET /api/sessions/list (基础会话列表)")
    print("- GET /api/sessions/advanced-list (高级会话列表)")
    print("- GET /api/debug/parameter-mapping (参数映射调试)")
    print("- GET /api/integration-example (集成指南)")
    print("")
    print("🚀 启动服务: http://localhost:8000")
    print("📚 查看文档: http://localhost:8000/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
