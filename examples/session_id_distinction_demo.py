# -*- coding: utf-8 -*-
"""
SessionId 区分演示
展示登录会话ID (loginSessionId) 和业务会话ID (sessionId) 的区别
"""
import sys
import os
from fastapi import FastAPI, Query, Depends, Request
from typing import Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.domain.services.auth_service import AuthContext
from src.presentation.middleware.auth_dependencies import require_auth

app = FastAPI(title="SessionId 区分演示")

# ============================================================================
# 演示1: 参数提取和区分
# ============================================================================

@app.get("/api/demo/session-params")
async def show_session_parameters(request: Request):
    """
    显示两种不同的sessionId参数
    
    测试URL: /api/demo/session-params?loginSessionId=login123&sessionId=business456&regionId=cn-hangzhou
    """
    # 认证相关参数
    login_token = request.query_params.get("loginToken")
    login_session_id = request.query_params.get("loginSessionId")
    region_id = request.query_params.get("regionId")
    
    # 业务相关参数
    business_session_id = request.query_params.get("sessionId")
    
    # 请求头中的认证参数
    header_login_token = request.headers.get("X-Login-Token")
    header_login_session_id = request.headers.get("X-Login-Session-Id")
    header_region_id = request.headers.get("X-Region-Id")
    
    return {
        "parameter_distinction": {
            "authentication_params": {
                "loginToken": {
                    "value": login_token,
                    "source": "query_params",
                    "purpose": "用户身份验证（兼容方式）"
                },
                "loginSessionId": {
                    "value": login_session_id,
                    "source": "query_params",
                    "purpose": "用户身份验证（推荐方式）"
                },
                "regionId": {
                    "value": region_id,
                    "source": "query_params",
                    "purpose": "服务区域标识"
                }
            },
            "business_params": {
                "sessionId": {
                    "value": business_session_id,
                    "source": "query_params",
                    "purpose": "业务会话过滤"
                }
            },
            "header_auth_params": {
                "X-Login-Token": {
                    "value": header_login_token,
                    "purpose": "请求头中的登录令牌"
                },
                "X-Login-Session-Id": {
                    "value": header_login_session_id,
                    "purpose": "请求头中的登录会话ID"
                },
                "X-Region-Id": {
                    "value": header_region_id,
                    "purpose": "请求头中的区域ID"
                }
            }
        },
        "priority": {
            "auth_priority": "loginSessionId > loginToken",
            "source_priority": "query_params || headers"
        }
    }

# ============================================================================
# 演示2: 实际的会话列表API（重构后）
# ============================================================================

@app.get("/api/demo/sessions/list")
async def list_sessions_demo(
    # 🔐 认证参数：通过依赖注入自动处理 loginSessionId/loginToken + regionId
    current_user: AuthContext = Depends(require_auth),
    
    # 📝 业务参数：显式声明
    limit: int = Query(50, ge=1, le=100, description="返回的会话数量限制"),
    offset: int = Query(0, ge=0, description="分页偏移量"),
    agentId: Optional[str] = Query(None, description="按Agent ID过滤"),
    sessionId: Optional[str] = Query(None, description="按业务会话ID过滤")  # 这是业务会话ID
):
    """
    会话列表API演示
    
    参数说明：
    - loginSessionId/loginToken: 用于用户认证（通过依赖注入处理）
    - sessionId: 用于业务逻辑中的会话过滤
    - regionId: 服务区域（通过依赖注入处理）
    """
    # 模拟会话数据
    all_sessions = [
        {
            "sessionId": f"session_{i}",
            "aliUid": current_user.ali_uid,
            "wyId": current_user.wy_id,
            "agentId": agentId or "default_agent",
            "title": f"会话 {i}",
            "status": "active",
            "gmtCreate": "2024-01-01T10:00:00Z",
            "gmtModified": "2024-01-01T10:00:00Z"
        }
        for i in range(100)  # 模拟100个会话
    ]
    
    # 应用业务过滤
    filtered_sessions = all_sessions
    if sessionId:
        # 按业务会话ID过滤
        filtered_sessions = [s for s in all_sessions if s["sessionId"] == sessionId]
    
    if agentId:
        # 按Agent ID过滤
        filtered_sessions = [s for s in filtered_sessions if s["agentId"] == agentId]
    
    # 应用分页
    paginated_sessions = filtered_sessions[offset:offset + limit]
    
    return {
        "code": 200,
        "message": "success",
        "data": {
            "sessions": paginated_sessions,
            "pagination": {
                "total": len(filtered_sessions),
                "limit": limit,
                "offset": offset,
                "hasMore": len(paginated_sessions) == limit
            },
            "filters_applied": {
                "sessionId": sessionId,
                "agentId": agentId,
                "note": "sessionId用于业务过滤，不是认证参数"
            },
            "auth_context": {
                "ali_uid": current_user.ali_uid,
                "wy_id": current_user.wy_id,
                "note": "用户信息来自loginSessionId/loginToken认证"
            }
        }
    }

# ============================================================================
# 演示3: 不同认证方式的对比
# ============================================================================

@app.get("/api/demo/auth-methods")
async def compare_auth_methods():
    """
    对比不同的认证方式
    """
    return {
        "authentication_methods": {
            "1_loginSessionId": {
                "parameter": "loginSessionId",
                "example": "?loginSessionId=login_session_abc123&regionId=cn-hangzhou",
                "description": "推荐的认证方式，使用登录会话ID",
                "priority": "最高",
                "status": "推荐使用"
            },
            "2_loginToken": {
                "parameter": "loginToken", 
                "example": "?loginToken=token_xyz789&regionId=cn-hangzhou",
                "description": "兼容的认证方式，使用登录令牌",
                "priority": "次高",
                "status": "向后兼容"
            },
            "3_headers": {
                "parameter": "X-Login-Session-Id / X-Login-Token",
                "example": "Headers: X-Login-Session-Id: abc123, X-Region-Id: cn-hangzhou",
                "description": "通过请求头传递认证信息",
                "priority": "与查询参数相同",
                "status": "推荐用于API调用"
            }
        },
        "business_parameters": {
            "sessionId": {
                "parameter": "sessionId",
                "example": "?sessionId=business_session_456",
                "description": "业务会话ID，用于过滤特定会话",
                "scope": "业务逻辑",
                "note": "与认证无关"
            }
        }
    }

# ============================================================================
# 演示4: 测试不同的参数组合
# ============================================================================

@app.get("/api/demo/test-combinations")
async def test_parameter_combinations(
    current_user: AuthContext = Depends(require_auth),
    sessionId: Optional[str] = Query(None),
    agentId: Optional[str] = Query(None)
):
    """
    测试不同的参数组合
    
    支持的组合：
    1. 仅认证: ?loginSessionId=xxx&regionId=xxx
    2. 认证+业务过滤: ?loginSessionId=xxx&regionId=xxx&sessionId=yyy
    3. 认证+Agent过滤: ?loginSessionId=xxx&regionId=xxx&agentId=zzz
    4. 全部参数: ?loginSessionId=xxx&regionId=xxx&sessionId=yyy&agentId=zzz
    """
    return {
        "test_result": "success",
        "auth_info": {
            "ali_uid": current_user.ali_uid,
            "wy_id": current_user.wy_id,
            "source": "来自loginSessionId/loginToken认证"
        },
        "business_filters": {
            "sessionId": sessionId,
            "agentId": agentId,
            "applied": bool(sessionId or agentId)
        },
        "parameter_combinations": {
            "auth_only": "✓ 支持仅认证参数",
            "auth_plus_session_filter": "✓ 支持认证+会话过滤",
            "auth_plus_agent_filter": "✓ 支持认证+Agent过滤", 
            "all_parameters": "✓ 支持所有参数组合"
        }
    }

# ============================================================================
# 演示5: 错误处理和调试
# ============================================================================

@app.get("/api/demo/debug-params")
async def debug_parameters(request: Request):
    """
    调试参数提取过程
    """
    # 提取所有可能的参数
    params = {
        "query_params": dict(request.query_params),
        "headers": dict(request.headers),
        "extracted_auth_params": {
            "loginToken": request.query_params.get("loginToken") or request.headers.get("X-Login-Token"),
            "loginSessionId": request.query_params.get("loginSessionId") or request.headers.get("X-Login-Session-Id"),
            "regionId": request.query_params.get("regionId") or request.headers.get("X-Region-Id")
        },
        "extracted_business_params": {
            "sessionId": request.query_params.get("sessionId"),
            "agentId": request.query_params.get("agentId"),
            "limit": request.query_params.get("limit"),
            "offset": request.query_params.get("offset")
        }
    }
    
    # 检查参数完整性
    auth_params = params["extracted_auth_params"]
    has_auth = bool(auth_params["loginToken"] or auth_params["loginSessionId"])
    
    return {
        "debug_info": params,
        "validation": {
            "has_auth_params": has_auth,
            "auth_method": "loginSessionId" if auth_params["loginSessionId"] else "loginToken" if auth_params["loginToken"] else "none",
            "missing_params": [
                param for param, value in auth_params.items() 
                if param in ["loginSessionId", "loginToken"] and not value
            ] if not has_auth else [],
            "recommendation": "提供 loginSessionId 或 loginToken 进行认证" if not has_auth else "认证参数完整"
        }
    }

if __name__ == "__main__":
    import uvicorn
    
    print("=== SessionId 区分演示 ===")
    print("")
    print("🔍 两种SessionId的区别:")
    print("1. loginSessionId - 用于用户身份认证")
    print("2. sessionId - 用于业务会话过滤")
    print("")
    print("📝 测试URL:")
    print("# 显示参数区分")
    print("curl 'http://localhost:8000/api/demo/session-params?loginSessionId=login123&sessionId=business456&regionId=cn-hangzhou'")
    print("")
    print("# 测试会话列表API")
    print("curl 'http://localhost:8000/api/demo/sessions/list?loginSessionId=test123&regionId=cn-hangzhou&sessionId=session_456&limit=5'")
    print("")
    print("# 测试请求头方式")
    print("curl -H 'X-Login-Session-Id: test123' -H 'X-Region-Id: cn-hangzhou' 'http://localhost:8000/api/demo/sessions/list?sessionId=session_456'")
    print("")
    print("# 调试参数提取")
    print("curl 'http://localhost:8000/api/demo/debug-params?loginSessionId=test123&sessionId=business456'")
    print("")
    print("🚀 启动服务: http://localhost:8000")
    print("📚 查看文档: http://localhost:8000/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
