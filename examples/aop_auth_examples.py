# -*- coding: utf-8 -*-
"""
AOP认证使用示例
演示Python中类似Java AOP的认证能力
"""
import sys
import os
from fastapi import FastAPI, Request, Depends, HTTPException
from fastapi.responses import JSONResponse

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.presentation.middleware.auth_decorators import (
    require_auth, 
    optional_auth, 
    require_permissions,
    authenticated_api
)
from src.presentation.middleware.auth_dependencies import (
    get_current_user,
    require_auth as require_auth_dep,
    require_permissions as require_permissions_dep,
    require_file_access,
    CombinedAuth
)
from src.presentation.middleware.global_auth_middleware import (
    GlobalAuthMiddleware,
    ConditionalAuthMiddleware
)
from src.domain.services.auth_service import AuthContext

# 创建FastAPI应用
app = FastAPI(title="AOP认证示例")

# ============================================================================
# 方式1: 使用装饰器 (类似Java AOP的@Aspect)
# ============================================================================

@app.get("/api/decorator/profile")
@require_auth
async def get_profile_with_decorator(request: Request):
    """使用装饰器的认证示例"""
    user = request.state.current_user
    return {
        "method": "decorator",
        "user": {
            "ali_uid": user.ali_uid,
            "wy_id": user.wy_id
        }
    }

@app.get("/api/decorator/public")
@optional_auth
async def get_public_with_decorator(request: Request):
    """使用可选认证装饰器的示例"""
    user = getattr(request.state, 'current_user', None)
    return {
        "method": "decorator",
        "authenticated": user is not None,
        "user": {
            "ali_uid": user.ali_uid,
            "wy_id": user.wy_id
        } if user else None
    }

@app.get("/api/decorator/admin")
@require_permissions(['admin'])
async def get_admin_with_decorator(request: Request):
    """使用权限装饰器的示例"""
    user = request.state.current_user
    return {
        "method": "decorator",
        "message": "管理员专用接口",
        "user": {
            "ali_uid": user.ali_uid,
            "wy_id": user.wy_id
        }
    }

@app.post("/api/decorator/files")
@authenticated_api(permissions=['file:write'], rate_limit=True)
async def upload_file_with_decorator(request: Request):
    """使用组合装饰器的示例"""
    user = request.state.current_user
    return {
        "method": "decorator",
        "message": "文件上传成功",
        "user": {
            "ali_uid": user.ali_uid,
            "wy_id": user.wy_id
        }
    }

# ============================================================================
# 方式2: 使用FastAPI依赖注入 (推荐方式)
# ============================================================================

@app.get("/api/dependency/profile")
async def get_profile_with_dependency(
    current_user: AuthContext = Depends(require_auth_dep)
):
    """使用依赖注入的认证示例"""
    return {
        "method": "dependency",
        "user": {
            "ali_uid": current_user.ali_uid,
            "wy_id": current_user.wy_id
        }
    }

@app.get("/api/dependency/public")
async def get_public_with_dependency(
    current_user: AuthContext = Depends(get_current_user)
):
    """使用可选依赖注入的示例"""
    return {
        "method": "dependency",
        "authenticated": current_user is not None,
        "user": {
            "ali_uid": current_user.ali_uid,
            "wy_id": current_user.wy_id
        } if current_user else None
    }

@app.get("/api/dependency/admin")
async def get_admin_with_dependency(
    current_user: AuthContext = Depends(require_permissions_dep(['admin']))
):
    """使用权限依赖注入的示例"""
    return {
        "method": "dependency",
        "message": "管理员专用接口",
        "user": {
            "ali_uid": current_user.ali_uid,
            "wy_id": current_user.wy_id
        }
    }

@app.get("/api/dependency/files/{file_id}")
async def get_file_with_dependency(
    file_id: str,
    current_user: AuthContext = Depends(require_file_access("read"))
):
    """使用资源访问依赖注入的示例"""
    return {
        "method": "dependency",
        "file_id": file_id,
        "message": "文件访问成功",
        "user": {
            "ali_uid": current_user.ali_uid,
            "wy_id": current_user.wy_id
        }
    }

@app.post("/api/dependency/admin-only")
async def admin_only_endpoint(
    current_user: AuthContext = Depends(CombinedAuth.admin_only())
):
    """使用组合依赖的示例"""
    return {
        "method": "dependency",
        "message": "仅管理员可访问",
        "user": {
            "ali_uid": current_user.ali_uid,
            "wy_id": current_user.wy_id
        }
    }

# ============================================================================
# 方式3: 全局中间件配置示例
# ============================================================================

# 注意：这些中间件配置通常在应用启动时添加，这里只是示例

def configure_global_auth_middleware(app: FastAPI):
    """配置全局认证中间件"""
    
    # 方式3a: 简单的全局认证
    app.add_middleware(
        GlobalAuthMiddleware,
        protected_paths=[
            r'^/api/user/.*',
            r'^/api/admin/.*',
            r'^/api/files/.*'
        ],
        excluded_paths=[
            r'^/api/auth/.*',
            r'^/api/public/.*',
            r'^/docs.*',
            r'^/health.*'
        ]
    )

def configure_conditional_auth_middleware(app: FastAPI):
    """配置条件认证中间件"""
    
    # 方式3b: 基于路径和方法的细粒度控制
    app.add_middleware(ConditionalAuthMiddleware)

# ============================================================================
# 混合使用示例
# ============================================================================

@app.get("/api/mixed/example")
async def mixed_auth_example(
    request: Request,
    # 使用依赖注入进行基本认证
    current_user: AuthContext = Depends(require_auth_dep)
):
    """混合使用多种认证方式的示例"""
    
    # 在函数内部可以进行额外的权限检查
    if request.method == "DELETE" and current_user.ali_uid != 123456789:
        raise HTTPException(
            status_code=403,
            detail="只有特定用户可以执行删除操作"
        )
    
    return {
        "method": "mixed",
        "message": "混合认证示例",
        "user": {
            "ali_uid": current_user.ali_uid,
            "wy_id": current_user.wy_id
        }
    }

# ============================================================================
# 认证方式对比接口
# ============================================================================

@app.get("/api/auth-methods")
async def get_auth_methods():
    """获取支持的认证方式说明"""
    return {
        "auth_methods": {
            "1_decorators": {
                "description": "使用Python装饰器实现AOP认证",
                "advantages": ["简洁易用", "类似Java AOP", "可组合"],
                "disadvantages": ["需要手动添加到每个函数", "不够灵活"],
                "examples": [
                    "@require_auth",
                    "@optional_auth", 
                    "@require_permissions(['admin'])",
                    "@authenticated_api(permissions=['file:write'])"
                ]
            },
            "2_dependencies": {
                "description": "使用FastAPI依赖注入系统",
                "advantages": ["FastAPI原生支持", "类型安全", "可测试性好", "自动文档生成"],
                "disadvantages": ["需要在函数签名中声明"],
                "examples": [
                    "Depends(require_auth)",
                    "Depends(get_current_user)",
                    "Depends(require_permissions(['admin']))",
                    "Depends(CombinedAuth.admin_only())"
                ]
            },
            "3_global_middleware": {
                "description": "使用全局中间件实现AOP认证",
                "advantages": ["全局生效", "无需修改现有代码", "统一管理"],
                "disadvantages": ["不够灵活", "难以处理复杂权限"],
                "examples": [
                    "GlobalAuthMiddleware",
                    "ConditionalAuthMiddleware"
                ]
            }
        },
        "recommendations": {
            "simple_apis": "使用装饰器",
            "complex_apis": "使用依赖注入",
            "legacy_systems": "使用全局中间件",
            "new_projects": "优先使用依赖注入"
        }
    }

# ============================================================================
# 错误处理
# ============================================================================

@app.exception_handler(HTTPException)
async def auth_exception_handler(request: Request, exc: HTTPException):
    """认证异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "authentication_error",
            "detail": exc.detail,
            "path": request.url.path,
            "method": request.method
        }
    )

if __name__ == "__main__":
    import uvicorn
    
    print("=== Python AOP认证示例服务 ===")
    print("支持的认证方式:")
    print("1. 装饰器方式 (类似Java @Aspect)")
    print("2. 依赖注入方式 (FastAPI推荐)")
    print("3. 全局中间件方式 (AOP切面)")
    print("")
    print("测试端点:")
    print("- GET /api/decorator/* (装饰器认证)")
    print("- GET /api/dependency/* (依赖注入认证)")
    print("- GET /api/mixed/* (混合认证)")
    print("- GET /api/auth-methods (认证方式说明)")
    print("")
    print("启动服务在 http://localhost:8000")
    print("访问 http://localhost:8000/docs 查看API文档")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
