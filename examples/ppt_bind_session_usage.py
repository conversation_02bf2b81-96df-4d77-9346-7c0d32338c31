#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT绑定会话功能使用示例
展示如何使用PPTService的bind_ppt_to_session方法
"""

from src.domain.services.ppt_service import PPTService, PPTServiceError
from loguru import logger

def example_bind_ppt_to_session():
    """示例：基本的PPT绑定会话功能"""
    print("=== PPT绑定会话基本示例 ===")
    
    try:
        # 创建PPT服务实例
        ppt_service = PPTService()
        
        # 模拟场景：用户在会话中创建了PPT
        session_id = "session_20250801_001"
        ppt_id = "ppt_demo_12345"
        
        print(f"📋 会话ID: {session_id}")
        print(f"🎨 PPT作品ID: {ppt_id}")
        
        # 绑定PPT到会话
        print("\n🔗 开始绑定PPT到会话...")
        ppt_service.bind_ppt_to_session(
            session_id=session_id,
            ppt_id=ppt_id
        )
        
        print("✅ PPT绑定会话成功！")
        print("💡 绑定关系已存储到Memory系统")
        
    except PPTServiceError as e:
        print(f"❌ PPT服务错误: {e}")
    except Exception as e:
        print(f"💥 未知错误: {e}")

def example_multiple_bindings():
    """示例：一个会话绑定多个PPT"""
    print("\n=== 多PPT绑定示例 ===")
    
    try:
        ppt_service = PPTService()
        session_id = "session_20250801_002"
        
        # 模拟场景：用户在一个会话中创建了多个PPT
        ppt_list = [
            {"id": "ppt_work_001", "name": "工作汇报PPT"},
            {"id": "ppt_proposal_002", "name": "项目提案PPT"},
            {"id": "ppt_training_003", "name": "培训材料PPT"}
        ]
        
        print(f"📋 会话ID: {session_id}")
        print(f"🎨 要绑定的PPT数量: {len(ppt_list)}")
        
        # 逐个绑定PPT到会话
        for i, ppt in enumerate(ppt_list, 1):
            print(f"\n🔗 绑定第{i}个PPT...")
            print(f"   PPT ID: {ppt['id']}")
            print(f"   PPT名称: {ppt['name']}")
            
            ppt_service.bind_ppt_to_session(
                session_id=session_id,
                ppt_id=ppt['id']
            )
            
            print(f"   ✅ 绑定成功")
        
        print(f"\n🎉 所有PPT绑定完成！")
        print(f"💡 会话 {session_id} 已关联 {len(ppt_list)} 个PPT")
        
    except Exception as e:
        print(f"❌ 绑定失败: {e}")

def example_session_restoration():
    """示例：会话恢复时重新建立PPT关联"""
    print("\n=== 会话恢复示例 ===")
    
    try:
        ppt_service = PPTService()
        
        # 模拟场景：用户恢复之前的会话
        old_session_id = "session_20250731_001"
        new_session_id = "session_20250801_003"
        
        # 假设从某处获取到之前会话关联的PPT列表
        previous_ppt_list = ["ppt_saved_001", "ppt_saved_002"]
        
        print(f"🔄 恢复会话...")
        print(f"   原会话ID: {old_session_id}")
        print(f"   新会话ID: {new_session_id}")
        print(f"   需要恢复的PPT: {previous_ppt_list}")
        
        # 重新绑定之前的PPT到新会话
        for ppt_id in previous_ppt_list:
            print(f"\n🔗 重新绑定PPT: {ppt_id}")
            ppt_service.bind_ppt_to_session(
                session_id=new_session_id,
                ppt_id=ppt_id
            )
            print(f"   ✅ 重新绑定成功")
        
        print(f"\n🎉 会话恢复完成！")
        print(f"💡 新会话已继承之前的PPT关联关系")
        
    except Exception as e:
        print(f"❌ 会话恢复失败: {e}")

def example_error_handling():
    """示例：错误处理场景"""
    print("\n=== 错误处理示例 ===")
    
    ppt_service = PPTService()
    
    # 测试空参数
    print("🧪 测试1: 空参数")
    try:
        ppt_service.bind_ppt_to_session("", "ppt_123")
        print("   ⚠️  警告：应该失败但没有失败")
    except Exception as e:
        print(f"   ✅ 正确处理空参数: {type(e).__name__}")
    
    # 测试None参数
    print("\n🧪 测试2: None参数")
    try:
        ppt_service.bind_ppt_to_session(None, "ppt_123")
        print("   ⚠️  警告：应该失败但没有失败")
    except Exception as e:
        print(f"   ✅ 正确处理None参数: {type(e).__name__}")
    
    print("\n💡 错误处理机制工作正常")

if __name__ == "__main__":
    print("🚀 PPT绑定会话功能使用示例")
    print("=" * 50)
    
    # 运行各种示例
    example_bind_ppt_to_session()
    example_multiple_bindings()
    example_session_restoration()
    example_error_handling()
    
    print("\n" + "=" * 50)
    print("📝 总结:")
    print("   1. bind_ppt_to_session 方法用于建立PPT与会话的关联")
    print("   2. 支持一个会话绑定多个PPT（多次调用）")
    print("   3. 每次绑定生成唯一的run_id标识")
    print("   4. 绑定关系通过CustomEvent存储到Memory系统")
    print("   5. 适用于PPT创建、选择、会话恢复等场景")
    print("   6. 具备完整的错误处理机制")
    print("\n💡 更多用法请参考测试文件: tests/test_ppt_service.py") 