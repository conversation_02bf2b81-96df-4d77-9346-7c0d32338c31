#!/usr/bin/env python3
"""
SessionService STS Token 功能使用示例
展示如何在会话服务中使用 STS Token 功能
"""

import sys
import os
import json
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from src.domain.services.session_service import SessionService
from src.domain.services.auth_service import AuthContext


def example_desktop_resource_with_sts_token():
    """桌面资源使用STS Token的示例"""
    logger.info("=== 桌面资源使用STS Token的示例 ===")
    
    try:
        # 创建SessionService实例
        session_service = SessionService()
        
        # 创建认证上下文
        auth_context = AuthContext(
            ali_uid=****************,
            wy_id="demo_user",
            end_user_id="demo_end_user",
            account_type="ALIYUN"
        )
        
        # 桌面参数
        desktop_id = "desktop_demo_001"
        auth_code = "original_auth_code"
        
        logger.info(f"用户信息:")
        logger.info(f"  Ali UID: {auth_context.ali_uid}")
        logger.info(f"  End User ID: {auth_context.end_user_id}")
        logger.info(f"  Account Type: {auth_context.account_type}")
        logger.info(f"桌面信息:")
        logger.info(f"  Desktop ID: {desktop_id}")
        logger.info(f"  Original Auth Code: {auth_code}")
        
        # 模拟STS Token响应
        mock_sts_response = Mock()
        mock_sts_response.body = Mock()
        mock_sts_response.body.token = Mock()
        mock_sts_response.body.token.sts_token = "STS.NUCJMhR7cN9r8kxm2vK5H8nQz"
        mock_sts_response.body.token.session_id = "session_12345"
        mock_sts_response.body.token.ali_uid = auth_context.ali_uid
        mock_sts_response.body.token.end_user_id = auth_context.end_user_id
        
        # 模拟AppStream客户端和Redis
        with patch('src.domain.services.session_service.get_appstream_inner_client') as mock_get_client, \
             patch('src.domain.services.session_service.RedisClient') as mock_redis_class:
            
            # 设置AppStream客户端模拟
            mock_client = Mock()
            mock_client.get_sts_token.return_value = mock_sts_response
            mock_get_client.return_value = mock_client
            
            # 设置Redis客户端模拟
            mock_redis = Mock()
            mock_redis.get.return_value = None  # 缓存未命中
            mock_redis.set.return_value = True
            mock_redis_class.return_value = mock_redis
            
            # 调用构建运行时资源方法
            runtime_resource = session_service._build_runtime_resource(
                desktop_id=desktop_id,
                auth_code=auth_code,
                auth_context=auth_context
            )
            
            logger.info(f"运行时资源配置:")
            logger.info(f"  Type: {runtime_resource.type}")
            logger.info(f"  Token: {runtime_resource.token[:20]}... (STS Token)")
            logger.info(f"  Cloud Resource ID: {runtime_resource.cloud_resource_id}")
            logger.info(f"  Region: {runtime_resource.region}")
            
            # 验证AppStream客户端调用参数
            call_args = mock_client.get_sts_token.call_args
            logger.info(f"AppStream API调用参数:")
            logger.info(f"  end_user_id: {call_args[1]['end_user_id']}")
            logger.info(f"  account_type: {call_args[1]['account_type']}")
            logger.info(f"  user_ali_uid: {call_args[1]['user_ali_uid']}")
            
            # 解析策略JSON
            policy_json = call_args[1]['policy']
            policy_dict = json.loads(policy_json)
            logger.info(f"  policy: {json.dumps(policy_dict, indent=2)}")
            
            # 验证Redis缓存
            cache_key = f"sts_token:{auth_context.end_user_id}:{desktop_id}"
            logger.info(f"Redis缓存:")
            logger.info(f"  Cache Key: {cache_key}")
            logger.info(f"  Cache TTL: 3600秒 (1小时)")
            
            logger.success("✅ 桌面资源STS Token示例完成")
            
    except Exception as e:
        logger.error(f"❌ 桌面资源STS Token示例失败: {e}")


def example_cached_sts_token():
    """使用缓存STS Token的示例"""
    logger.info("=== 使用缓存STS Token的示例 ===")
    
    try:
        session_service = SessionService()
        
        auth_context = AuthContext(
            ali_uid=****************,
            wy_id="demo_user",
            end_user_id="demo_end_user",
            account_type="ALIYUN"
        )
        
        desktop_id = "desktop_demo_002"
        auth_code = "original_auth_code"
        cached_token = "CACHED.STS.Token.From.Redis.12345"
        
        logger.info(f"模拟场景: 用户 {auth_context.end_user_id} 再次访问桌面 {desktop_id}")
        
        # 模拟Redis返回缓存的Token
        with patch('src.domain.services.session_service.RedisClient') as mock_redis_class, \
             patch('src.domain.services.session_service.get_appstream_inner_client') as mock_get_client:
            
            # 设置Redis返回缓存的Token
            mock_redis = Mock()
            mock_redis.get.return_value = cached_token
            mock_redis_class.return_value = mock_redis
            
            # 设置AppStream客户端（不应该被调用）
            mock_client = Mock()
            mock_get_client.return_value = mock_client
            
            # 调用方法
            runtime_resource = session_service._build_runtime_resource(
                desktop_id=desktop_id,
                auth_code=auth_code,
                auth_context=auth_context
            )
            
            logger.info(f"运行时资源配置:")
            logger.info(f"  Type: {runtime_resource.type}")
            logger.info(f"  Token: {runtime_resource.token} (来自缓存)")
            logger.info(f"  Cloud Resource ID: {runtime_resource.cloud_resource_id}")
            
            # 验证缓存命中
            cache_key = f"sts_token:{auth_context.end_user_id}:{desktop_id}"
            mock_redis.get.assert_called_once_with(cache_key)
            
            # 验证AppStream API没有被调用
            mock_client.get_sts_token.assert_not_called()
            
            logger.success("✅ 缓存STS Token示例完成 - 避免了重复API调用")
            
    except Exception as e:
        logger.error(f"❌ 缓存STS Token示例失败: {e}")


def example_different_resource_types():
    """不同资源类型的处理示例"""
    logger.info("=== 不同资源类型的处理示例 ===")
    
    try:
        session_service = SessionService()
        
        auth_context = AuthContext(
            ali_uid=****************,
            wy_id="demo_user",
            end_user_id="demo_end_user",
            account_type="ALIYUN"
        )
        
        auth_code = "test_auth_code"
        
        # 测试不同的资源类型
        test_cases = [
            {
                "desktop_id": "desktop_123",
                "expected_type": "desktop",
                "description": "桌面资源 - 使用STS Token",
                "uses_sts": True
            },
            {
                "desktop_id": "agentbay",
                "expected_type": "agentbay",
                "description": "AgentBay资源 - 使用原始auth_code",
                "uses_sts": False
            },
            {
                "desktop_id": None,
                "expected_type": "none",
                "description": "无资源 - 使用原始auth_code",
                "uses_sts": False
            },
            {
                "desktop_id": "",
                "expected_type": "none",
                "description": "空资源 - 使用原始auth_code",
                "uses_sts": False
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"{i}. {test_case['description']}")
            
            if test_case['uses_sts']:
                # 模拟STS Token获取
                mock_sts_response = Mock()
                mock_sts_response.body = Mock()
                mock_sts_response.body.token = Mock()
                mock_sts_response.body.token.sts_token = f"STS.Token.For.{test_case['desktop_id']}"
                
                with patch('src.domain.services.session_service.get_appstream_inner_client') as mock_get_client, \
                     patch('src.domain.services.session_service.RedisClient') as mock_redis_class:
                    
                    mock_client = Mock()
                    mock_client.get_sts_token.return_value = mock_sts_response
                    mock_get_client.return_value = mock_client
                    
                    mock_redis = Mock()
                    mock_redis.get.return_value = None
                    mock_redis.set.return_value = True
                    mock_redis_class.return_value = mock_redis
                    
                    runtime_resource = session_service._build_runtime_resource(
                        desktop_id=test_case['desktop_id'],
                        auth_code=auth_code,
                        auth_context=auth_context
                    )
                    
                    expected_token = f"STS.Token.For.{test_case['desktop_id']}"
            else:
                # 不使用STS Token的情况
                runtime_resource = session_service._build_runtime_resource(
                    desktop_id=test_case['desktop_id'],
                    auth_code=auth_code,
                    auth_context=auth_context
                )
                expected_token = auth_code
            
            logger.info(f"   资源类型: {runtime_resource.type}")
            logger.info(f"   Token类型: {'STS Token' if test_case['uses_sts'] else 'Auth Code'}")
            logger.info(f"   Cloud Resource ID: {runtime_resource.cloud_resource_id}")
            logger.info("")
            
            # 验证结果
            assert runtime_resource.type == test_case['expected_type']
            if test_case['uses_sts']:
                assert runtime_resource.token == expected_token
            else:
                assert runtime_resource.token == auth_code
        
        logger.success("✅ 不同资源类型处理示例完成")
        
    except Exception as e:
        logger.error(f"❌ 不同资源类型处理示例失败: {e}")


def example_error_handling():
    """错误处理示例"""
    logger.info("=== 错误处理示例 ===")
    
    try:
        session_service = SessionService()
        
        auth_context = AuthContext(
            ali_uid=****************,
            wy_id="demo_user",
            end_user_id="demo_end_user",
            account_type="ALIYUN"
        )
        
        desktop_id = "desktop_error_test"
        auth_code = "fallback_auth_code"
        
        logger.info(f"模拟场景: AppStream API调用失败，回退到使用原始auth_code")
        
        # 模拟AppStream API调用失败
        with patch('src.domain.services.session_service.get_appstream_inner_client') as mock_get_client, \
             patch('src.domain.services.session_service.RedisClient') as mock_redis_class:
            
            # 设置AppStream客户端抛出异常
            mock_client = Mock()
            mock_client.get_sts_token.side_effect = Exception("网络连接失败")
            mock_get_client.return_value = mock_client
            
            # 设置Redis客户端
            mock_redis = Mock()
            mock_redis.get.return_value = None  # 缓存未命中
            mock_redis_class.return_value = mock_redis
            
            # 调用方法
            runtime_resource = session_service._build_runtime_resource(
                desktop_id=desktop_id,
                auth_code=auth_code,
                auth_context=auth_context
            )
            
            logger.info(f"错误处理结果:")
            logger.info(f"  Type: {runtime_resource.type}")
            logger.info(f"  Token: {runtime_resource.token} (回退到auth_code)")
            logger.info(f"  Cloud Resource ID: {runtime_resource.cloud_resource_id}")
            
            # 验证回退行为
            assert runtime_resource.type == "desktop"
            assert runtime_resource.token == auth_code  # 应该回退到原始auth_code
            assert runtime_resource.cloud_resource_id == desktop_id
            
            logger.success("✅ 错误处理示例完成 - 成功回退到auth_code")
            
    except Exception as e:
        logger.error(f"❌ 错误处理示例失败: {e}")


def main():
    """主函数"""
    logger.info("开始SessionService STS Token功能使用示例...")
    
    # 桌面资源STS Token示例
    example_desktop_resource_with_sts_token()
    logger.info("")
    
    # 缓存STS Token示例
    example_cached_sts_token()
    logger.info("")
    
    # 不同资源类型处理示例
    example_different_resource_types()
    logger.info("")
    
    # 错误处理示例
    example_error_handling()
    
    logger.success("所有SessionService STS Token功能使用示例完成！")


if __name__ == "__main__":
    main()
