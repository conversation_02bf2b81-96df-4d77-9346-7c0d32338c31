# -*- coding: utf-8 -*-
"""
认证方式对比示例
展示装饰器、依赖注入、中间件三种方式的区别
"""
import sys
import os
from fastapi import FastAPI, Request, Depends, HTTPException
from fastapi.responses import JSONResponse

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.domain.services.auth_service import AuthContext
from src.presentation.middleware.auth_decorators import require_auth as auth_decorator
from src.presentation.middleware.auth_dependencies import require_auth as auth_dependency

app = FastAPI(title="认证方式对比")

# ============================================================================
# 方式1: 装饰器方式 (类似Java AOP @Aspect)
# ============================================================================

@app.get("/api/decorator/simple")
@auth_decorator
async def decorator_simple(request: Request):
    """
    装饰器方式 - 简单使用
    
    优点:
    - 语法简洁，类似Java AOP
    - 易于理解和使用
    - 可以组合多个装饰器
    
    缺点:
    - 需要手动从request.state获取用户
    - 类型提示不够好
    - 测试时需要模拟request.state
    """
    user = request.state.current_user
    return {
        "method": "decorator",
        "message": "装饰器认证示例",
        "user": {
            "ali_uid": user.ali_uid,
            "wy_id": user.wy_id
        }
    }

# ============================================================================
# 方式2: 依赖注入方式 (FastAPI推荐)
# ============================================================================

@app.get("/api/dependency/simple")
async def dependency_simple(
    current_user: AuthContext = Depends(auth_dependency)
):
    """
    依赖注入方式 - 简单使用
    
    优点:
    - FastAPI原生支持
    - 完美的类型提示
    - 易于测试（可以直接传入mock对象）
    - 自动生成API文档
    - 支持依赖缓存
    
    缺点:
    - 函数签名稍长
    - 需要了解Depends概念
    """
    return {
        "method": "dependency",
        "message": "依赖注入认证示例",
        "user": {
            "ali_uid": current_user.ali_uid,
            "wy_id": current_user.wy_id
        }
    }

# ============================================================================
# 复杂场景对比
# ============================================================================

# 装饰器方式 - 复杂权限检查
from src.presentation.middleware.auth_decorators import require_permissions

@app.get("/api/decorator/complex")
@require_permissions(['admin', 'read'])
async def decorator_complex(request: Request):
    """
    装饰器方式 - 复杂权限检查
    
    问题:
    - 装饰器参数在运行时确定
    - 难以进行类型检查
    - 测试时需要模拟复杂的request.state
    """
    user = request.state.current_user
    return {
        "method": "decorator_complex",
        "message": "装饰器复杂权限检查",
        "user": {"ali_uid": user.ali_uid}
    }

# 依赖注入方式 - 复杂权限检查
from src.presentation.middleware.auth_dependencies import require_permissions as require_perms_dep

@app.get("/api/dependency/complex")
async def dependency_complex(
    current_user: AuthContext = Depends(require_perms_dep(['admin', 'read']))
):
    """
    依赖注入方式 - 复杂权限检查
    
    优势:
    - 编译时类型检查
    - 清晰的函数签名
    - 易于单元测试
    - IDE自动补全支持
    """
    return {
        "method": "dependency_complex",
        "message": "依赖注入复杂权限检查",
        "user": {"ali_uid": current_user.ali_uid}
    }

# ============================================================================
# 测试友好性对比
# ============================================================================

@app.get("/api/test-comparison")
async def test_comparison():
    """
    测试友好性对比说明
    """
    return {
        "testing": {
            "decorator_approach": {
                "difficulty": "中等",
                "example": """
# 测试装饰器方式
async def test_decorator_endpoint():
    request = Mock()
    request.state.current_user = Mock(ali_uid=123)
    result = await decorator_simple(request)
    assert result['user']['ali_uid'] == 123
                """,
                "issues": [
                    "需要模拟复杂的request.state",
                    "装饰器逻辑难以单独测试",
                    "依赖于全局状态"
                ]
            },
            "dependency_approach": {
                "difficulty": "简单",
                "example": """
# 测试依赖注入方式
async def test_dependency_endpoint():
    mock_user = Mock(ali_uid=123)
    result = await dependency_simple(current_user=mock_user)
    assert result['user']['ali_uid'] == 123
                """,
                "advantages": [
                    "直接传入mock对象",
                    "可以单独测试依赖函数",
                    "无需模拟全局状态"
                ]
            }
        }
    }

# ============================================================================
# 性能对比
# ============================================================================

import time
import asyncio

@app.get("/api/performance/decorator")
@auth_decorator
async def performance_decorator(request: Request):
    """装饰器方式性能测试"""
    start = time.time()
    user = request.state.current_user
    end = time.time()
    
    return {
        "method": "decorator",
        "execution_time": end - start,
        "user_id": user.ali_uid
    }

@app.get("/api/performance/dependency")
async def performance_dependency(
    current_user: AuthContext = Depends(auth_dependency)
):
    """依赖注入方式性能测试"""
    start = time.time()
    # 用户已经通过依赖注入获取
    end = time.time()
    
    return {
        "method": "dependency",
        "execution_time": end - start,
        "user_id": current_user.ali_uid
    }

# ============================================================================
# 代码复用性对比
# ============================================================================

# 装饰器方式 - 代码复用
@auth_decorator
async def decorator_reuse_1(request: Request):
    user = request.state.current_user  # 重复代码
    return {"user_id": user.ali_uid}

@auth_decorator  
async def decorator_reuse_2(request: Request):
    user = request.state.current_user  # 重复代码
    return {"user_name": user.wy_id}

# 依赖注入方式 - 代码复用
async def dependency_reuse_1(user: AuthContext = Depends(auth_dependency)):
    return {"user_id": user.ali_uid}  # 无重复代码

async def dependency_reuse_2(user: AuthContext = Depends(auth_dependency)):
    return {"user_name": user.wy_id}  # 无重复代码

# ============================================================================
# 错误处理对比
# ============================================================================

@app.get("/api/error/decorator")
@auth_decorator
async def error_decorator(request: Request):
    """
    装饰器错误处理
    
    问题:
    - 错误在装饰器中抛出
    - 难以自定义错误响应
    - 错误堆栈复杂
    """
    user = request.state.current_user
    return {"user": user.ali_uid}

@app.get("/api/error/dependency")
async def error_dependency(
    current_user: AuthContext = Depends(auth_dependency)
):
    """
    依赖注入错误处理
    
    优势:
    - 错误在依赖函数中抛出
    - 可以自定义错误处理
    - 清晰的错误堆栈
    """
    return {"user": current_user.ali_uid}

# ============================================================================
# API文档生成对比
# ============================================================================

@app.get("/api/docs-comparison")
async def docs_comparison():
    """
    API文档生成对比
    """
    return {
        "documentation": {
            "decorator_approach": {
                "auto_generated": False,
                "description": "装饰器不会在OpenAPI文档中显示认证要求",
                "manual_work": "需要手动添加文档说明"
            },
            "dependency_approach": {
                "auto_generated": True,
                "description": "依赖注入会自动在OpenAPI文档中显示参数",
                "benefits": [
                    "自动生成认证参数文档",
                    "类型信息完整",
                    "示例值自动生成"
                ]
            }
        }
    }

# ============================================================================
# 总结和建议
# ============================================================================

@app.get("/api/summary")
async def summary():
    """认证方式总结和建议"""
    return {
        "summary": {
            "decorator_approach": {
                "best_for": ["快速原型", "简单认证", "类似Java AOP的代码风格"],
                "avoid_when": ["复杂权限逻辑", "需要完美类型提示", "重视测试"],
                "score": {
                    "ease_of_use": 9,
                    "type_safety": 5,
                    "testability": 6,
                    "performance": 8,
                    "documentation": 4
                }
            },
            "dependency_injection": {
                "best_for": ["新项目", "复杂权限", "团队开发", "重视类型安全"],
                "avoid_when": ["简单脚本", "不熟悉FastAPI概念"],
                "score": {
                    "ease_of_use": 7,
                    "type_safety": 10,
                    "testability": 10,
                    "performance": 9,
                    "documentation": 10
                }
            }
        },
        "recommendations": {
            "new_projects": "优先使用依赖注入",
            "legacy_projects": "可以使用装饰器快速添加认证",
            "complex_auth": "必须使用依赖注入",
            "simple_auth": "两种方式都可以，看团队偏好"
        }
    }

if __name__ == "__main__":
    import uvicorn
    
    print("=== 认证方式对比示例 ===")
    print("")
    print("🔍 对比维度:")
    print("1. 语法简洁性")
    print("2. 类型安全性") 
    print("3. 测试友好性")
    print("4. 性能表现")
    print("5. 代码复用性")
    print("6. 错误处理")
    print("7. 文档生成")
    print("")
    print("📝 测试端点:")
    print("- /api/decorator/* (装饰器方式)")
    print("- /api/dependency/* (依赖注入方式)")
    print("- /api/test-comparison (测试对比)")
    print("- /api/performance/* (性能对比)")
    print("- /api/summary (总结建议)")
    print("")
    print("🚀 启动服务: http://localhost:8000")
    print("📚 查看文档: http://localhost:8000/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
