#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化鉴权导入使用示例
展示如何使用新的简化导入方式来简化鉴权代码
"""

# ==================== 简化导入示例 ====================

# 方式1: 完整导入（适合复杂场景）
from src.domain.services.auth import (
    auth_service, AuthContext, PermissionType, ResourceType,
    Permissions, PermissionUtils, check_permission,
    has_permission, has_all_permissions
)

# 方式2: 简短导入（适合大多数场景）
# from src.domain.services.auth import auth_service, AuthContext, P, R

# 方式3: 便捷函数导入（适合简单场景）
# from src.domain.services.auth import check_permission, has_permission, P, R, AuthContext


def example_traditional_vs_simplified():
    """对比传统方式和简化方式"""
    
    print("=== 传统方式 vs 简化方式对比 ===\n")
    
    # 创建用户上下文
    context = AuthContext(ali_uid=123, wy_id="user123")
    
    # ==================== 传统方式 ====================
    print("传统方式:")
    print("需要导入:")
    print("  from src.domain.services.auth_service import auth_service, AuthContext")
    print("  from src.infrastructure.database.models.auth_models import PermissionType, ResourceType")
    print("  from src.infrastructure.database.models.permission_utils import Permissions")
    print()
    
    # 传统的权限检查代码
    print("代码:")
    print("  result = auth_service.check_resource_permission(")
    print("      context, ResourceType.FILE.value, '123', PermissionType.READ)")
    print()
    
    # ==================== 简化方式 ====================
    print("简化方式:")
    print("只需导入:")
    print("  from src.domain.services.auth import auth_service, AuthContext, P, R")
    print()
    
    print("代码:")
    print("  result = auth_service.check_resource_permission(context, R.FILE, '123', P.READ)")
    print("  # 或者更简洁:")
    print("  result = check_permission(context, R.FILE, '123', P.READ)")
    print()


def example_file_operations():
    """文件操作权限检查示例"""
    
    print("=== 文件操作权限检查示例 ===\n")
    
    context = AuthContext(ali_uid=123, wy_id="user123")
    file_id = "file_123"
    
    # 检查读权限
    read_result = check_permission(context, ResourceType.FILE.value, file_id, PermissionType.READ)
    print(f"读权限检查: {read_result.success} - {read_result.message}")
    
    # 检查写权限
    write_result = check_permission(context, Resource.FILE, file_id, Permission.WRITE)
    print(f"写权限检查: {write_result.success} - {write_result.message}")
    
    # 检查删除权限
    delete_result = check_permission(context, Resource.FILE, file_id, Permission.DELETE)
    print(f"删除权限检查: {delete_result.success} - {delete_result.message}")
    
    print()


def example_permission_combinations():
    """权限组合使用示例"""
    
    print("=== 权限组合使用示例 ===\n")
    
    # 模拟用户权限
    user_permissions = ["read", "write"]
    
    # 使用简化的权限检查
    print("用户权限:", user_permissions)
    print()
    
    # 单个权限检查
    print("单个权限检查:")
    print(f"  有读权限: {has_permission(user_permissions, Permission.READ)}")
    print(f"  有写权限: {has_permission(user_permissions, Permission.WRITE)}")
    print(f"  有删除权限: {has_permission(user_permissions, Permission.DELETE)}")
    print()
    
    # 权限组合检查
    print("权限组合检查:")
    print(f"  有读写权限: {has_all_permissions(user_permissions, Permission.READ_WRITE)}")
    print(f"  有基础权限: {has_all_permissions(user_permissions, Permission.BASIC)}")
    print(f"  是所有者: {has_all_permissions(user_permissions, Permission.OWNER)}")
    print()


def example_different_resource_types():
    """不同资源类型示例"""
    
    print("=== 不同资源类型示例 ===\n")
    
    context = AuthContext(ali_uid=123, wy_id="user123")
    
    # 文件资源
    file_result = check_permission(context, Resource.FILE, "file_123", Permission.READ)
    print(f"文件读权限: {file_result.success}")
    
    # 会话资源
    session_result = check_permission(context, Resource.SESSION, "session_456", Permission.WRITE)
    print(f"会话写权限: {session_result.success}")
    
    # 知识库资源
    kb_result = check_permission(context, Resource.KNOWLEDGE_BASE, "kb_789", Permission.ADMIN)
    print(f"知识库管理权限: {kb_result.success}")
    
    print()


def example_decorator_usage():
    """装饰器使用示例"""
    
    print("=== 装饰器使用示例 ===\n")
    
    # 使用简化导入的装饰器
    from src.domain.services.auth import require_file_read_permission
    
    @require_file_read_permission()
    def read_file_content(context: AuthContext, file_id: str):
        """读取文件内容 - 自动权限验证"""
        return f"读取文件 {file_id} 的内容"
    
    @require_file_read_permission("resource_id")
    def read_file_with_custom_param(context: AuthContext, resource_id: str):
        """自定义参数名的文件读取"""
        return f"读取文件 {resource_id} 的内容"
    
    print("装饰器函数定义完成")
    print("使用方式:")
    print("  @require_file_read_permission()")
    print("  def read_file_content(context, file_id):")
    print("      return f'读取文件 {file_id} 的内容'")
    print()


def example_mixed_usage():
    """混合使用示例 - 展示不同API的组合使用"""
    
    print("=== 混合使用示例 ===\n")
    
    context = AuthContext(ali_uid=123, wy_id="user123")
    
    # 1. 使用枚举
    result1 = auth_service.check_resource_permission(context, Resource.FILE, "123", PermissionType.READ)
    print(f"使用完整枚举: {result1.success}")
    
    # 2. 使用简短别名
    result2 = auth_service.check_resource_permission(context, Resource.FILE, "123", Permission.READ)
    print(f"使用简短别名: {result2.success}")
    
    # 3. 使用便捷函数
    result3 = check_permission(context, Resource.FILE, "123", Permission.READ)
    print(f"使用便捷函数: {result3.success}")
    
    # 4. 使用枚举（推荐方式）
    result4 = auth_service.check_resource_permission(context, ResourceType.FILE, "123", PermissionType.READ)
    print(f"使用枚举: {result4.success}")
    
    print()
    print("所有方式都是等价的，选择最适合你的场景的方式！")


def main():
    """主函数 - 运行所有示例"""
    
    print("🚀 简化鉴权导入使用示例\n")
    print("=" * 50)
    
    try:
        example_traditional_vs_simplified()
        example_file_operations()
        example_permission_combinations()
        example_different_resource_types()
        example_decorator_usage()
        example_mixed_usage()
        
        print("✅ 所有示例运行完成！")
        
    except Exception as e:
        print(f"❌ 示例运行出错: {e}")
        print("注意: 这些示例需要在完整的项目环境中运行")


if __name__ == "__main__":
    main()
