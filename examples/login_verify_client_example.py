# -*- coding: utf-8 -*-
"""
LoginVerifyClient 使用示例
演示如何使用 LoginVerifyClient 进行登录令牌验证
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.popclients.login_verify_client import LoginVerifyClient, LoginVerifyClientError
from loguru import logger


def example_sync_verify():
    """同步验证登录令牌示例"""
    try:
        # 创建客户端实例
        # 注意：在实际使用中，请提供真实的access_key_id和access_key_secret
        client = LoginVerifyClient(
            access_key_id="your_access_key_id",
            access_key_secret="your_access_key_secret",
            endpoint="wuyingaiinner-pre.aliyuncs.com"  # 预发环境
        )
        
        # 验证登录令牌
        session_id = "your_session_id"
        region_id = "cn-hangzhou"  # 可选参数
        
        logger.info(f"开始验证登录令牌: {session_id}")
        
        response = client.verify_login_token(
            session_id=session_id,
            region_id=region_id
        )
        
        # 检查验证结果
        if response.body and response.body.success:
            logger.info("登录令牌验证成功!")
            
            # 提取用户信息
            user_info = client.get_user_info_from_response(response)
            if user_info:
                logger.info(f"用户信息: {user_info}")
                print(f"用户ID: {user_info.get('ali_uid')}")
                print(f"无影ID: {user_info.get('wy_id')}")
                print(f"终端用户ID: {user_info.get('end_user_id')}")
                print(f"账户类型: {user_info.get('account_type')}")
                print(f"登录类型: {user_info.get('login_type')}")
            else:
                logger.warning("无法提取用户信息")
        else:
            logger.error(f"登录令牌验证失败: {response.body.message if response.body else 'Unknown error'}")
            
    except LoginVerifyClientError as e:
        logger.error(f"客户端错误: {e}")
    except Exception as e:
        logger.error(f"未知错误: {e}")


async def example_async_verify():
    """异步验证登录令牌示例"""
    try:
        # 创建客户端实例
        client = LoginVerifyClient(
            access_key_id="your_access_key_id",
            access_key_secret="your_access_key_secret",
            endpoint="wuyingaiinner-pre.aliyuncs.com"
        )
        
        # 异步验证登录令牌
        session_id = "your_session_id"
        
        logger.info(f"开始异步验证登录令牌: {session_id}")
        
        response = await client.verify_login_token_async(
            session_id=session_id,
            region_id="cn-hangzhou"
        )
        
        # 检查验证结果
        if response.body and response.body.success:
            logger.info("异步登录令牌验证成功!")
            
            # 提取用户信息
            user_info = client.get_user_info_from_response(response)
            if user_info:
                logger.info(f"用户信息: {user_info}")
            else:
                logger.warning("无法提取用户信息")
        else:
            logger.error(f"异步登录令牌验证失败: {response.body.message if response.body else 'Unknown error'}")
            
    except LoginVerifyClientError as e:
        logger.error(f"客户端错误: {e}")
    except Exception as e:
        logger.error(f"未知错误: {e}")


def example_client_info():
    """获取客户端信息示例"""
    try:
        client = LoginVerifyClient(
            access_key_id="your_access_key_id",
            access_key_secret="your_access_key_secret",
            endpoint="wuyingaiinner-pre.aliyuncs.com",
            connect_timeout=5000,
            read_timeout=8000
        )
        
        # 获取客户端信息
        client_info = client.get_client_info()
        logger.info(f"客户端信息: {client_info}")
        
        # 打印客户端字符串表示
        logger.info(f"客户端: {client}")
        
    except Exception as e:
        logger.error(f"获取客户端信息失败: {e}")


def example_with_env_config():
    """使用环境配置的示例"""
    try:
        # 不提供access_key，让客户端从环境配置中读取
        # 注意：这需要正确配置环境变量或配置文件
        client = LoginVerifyClient(
            endpoint="wuyingaiinner-pre.aliyuncs.com"
        )
        
        logger.info("成功创建客户端，凭证来自环境配置")
        logger.info(f"客户端: {client}")
        
    except LoginVerifyClientError as e:
        logger.error(f"使用环境配置创建客户端失败: {e}")
        logger.info("请确保设置了正确的环境变量:")
        logger.info("- ALIBABA_CLOUD_ACCESS_KEY_ID")
        logger.info("- ALIBABA_CLOUD_ACCESS_KEY_SECRET")


if __name__ == "__main__":
    logger.info("=== LoginVerifyClient 使用示例 ===")
    
    # 注意：以下示例需要真实的阿里云凭证才能正常工作
    # 在实际使用前，请替换为真实的access_key_id、access_key_secret和session_id
    
    logger.info("\n1. 同步验证登录令牌示例")
    # example_sync_verify()  # 取消注释以运行
    
    logger.info("\n2. 异步验证登录令牌示例")
    # asyncio.run(example_async_verify())  # 取消注释以运行
    
    logger.info("\n3. 获取客户端信息示例")
    # example_client_info()  # 取消注释以运行
    
    logger.info("\n4. 使用环境配置示例")
    # example_with_env_config()  # 取消注释以运行
    
    logger.info("\n示例代码已准备就绪，请根据需要取消注释相应的函数调用")
    logger.info("记得替换为真实的阿里云凭证和会话ID")
