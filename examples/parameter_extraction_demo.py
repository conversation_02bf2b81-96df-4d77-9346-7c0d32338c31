# -*- coding: utf-8 -*-
"""
参数提取机制演示
展示依赖注入如何自动处理loginToken等参数
"""
import sys
import os
from fastapi import FastAPI, Request, Query, Depends
from typing import Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.domain.services.auth_service import AuthContext
from src.presentation.middleware.auth_dependencies import require_auth

app = FastAPI(title="参数提取机制演示")

# ============================================================================
# 演示1: 显示Request对象包含的所有信息
# ============================================================================

@app.get("/api/demo/request-info")
async def show_request_info(request: Request):
    """
    显示Request对象包含的所有信息
    
    测试URL: /api/demo/request-info?loginToken=test123&regionId=cn-hangzhou&limit=10&agentId=test
    """
    return {
        "url": str(request.url),
        "method": request.method,
        "query_params": dict(request.query_params),
        "headers": dict(request.headers),
        "path_params": dict(request.path_params),
        "explanation": {
            "loginToken": request.query_params.get("loginToken"),
            "regionId": request.query_params.get("regionId"),
            "limit": request.query_params.get("limit"),
            "agentId": request.query_params.get("agentId"),
            "note": "所有参数都在request对象中，依赖注入可以访问它们"
        }
    }

# ============================================================================
# 演示2: 手动参数提取 vs 依赖注入
# ============================================================================

@app.get("/api/demo/manual-extraction")
async def manual_parameter_extraction(
    request: Request,
    # 👇 这些是显式声明的参数
    limit: int = Query(50),
    offset: int = Query(0),
    agentId: Optional[str] = Query(None)
):
    """
    手动参数提取演示
    
    显式声明的参数: limit, offset, agentId
    手动提取的参数: loginToken, regionId (从request中)
    """
    # 手动从request中提取认证参数
    login_token = request.query_params.get("loginToken") or request.headers.get("X-Login-Token")
    region_id = request.query_params.get("regionId") or request.headers.get("X-Region-Id")
    session_id = request.query_params.get("session") or request.headers.get("X-Session-Id")
    
    return {
        "method": "manual_extraction",
        "explicit_params": {
            "limit": limit,
            "offset": offset,
            "agentId": agentId,
            "note": "这些参数在函数签名中显式声明"
        },
        "manual_extracted_params": {
            "loginToken": login_token,
            "regionId": region_id,
            "session": session_id,
            "note": "这些参数手动从request对象中提取"
        },
        "explanation": "手动提取需要在每个函数中重复相同的代码"
    }

@app.get("/api/demo/dependency-injection")
async def dependency_injection_extraction(
    # 👇 依赖注入自动处理认证参数
    current_user: AuthContext = Depends(require_auth),
    # 👇 这些是显式声明的参数
    limit: int = Query(50),
    offset: int = Query(0),
    agentId: Optional[str] = Query(None)
):
    """
    依赖注入参数提取演示
    
    依赖注入处理: loginToken, regionId, session (通过require_auth)
    显式声明的参数: limit, offset, agentId
    """
    return {
        "method": "dependency_injection",
        "injected_user": {
            "ali_uid": current_user.ali_uid,
            "wy_id": current_user.wy_id,
            "note": "用户信息通过依赖注入自动获取，loginToken等参数在内部处理"
        },
        "explicit_params": {
            "limit": limit,
            "offset": offset,
            "agentId": agentId,
            "note": "这些参数在函数签名中显式声明"
        },
        "explanation": "依赖注入自动处理认证参数，无需在每个函数中重复代码"
    }

# ============================================================================
# 演示3: 参数处理流程追踪
# ============================================================================

class TracingAuthDependency:
    """追踪参数处理流程的认证依赖"""
    
    async def __call__(self, request: Request) -> dict:
        """模拟认证过程并追踪参数提取"""
        
        # 步骤1: 从request中提取参数
        login_token = request.query_params.get("loginToken") or request.headers.get("X-Login-Token")
        session_id = request.query_params.get("session") or request.headers.get("X-Session-Id")
        region_id = request.query_params.get("regionId") or request.headers.get("X-Region-Id")
        
        # 步骤2: 记录提取过程
        extraction_log = [
            f"1. 从query_params提取loginToken: {request.query_params.get('loginToken')}",
            f"2. 从headers提取X-Login-Token: {request.headers.get('X-Login-Token')}",
            f"3. 最终loginToken: {login_token}",
            f"4. 从query_params提取session: {request.query_params.get('session')}",
            f"5. 从headers提取X-Session-Id: {request.headers.get('X-Session-Id')}",
            f"6. 最终session: {session_id}",
            f"7. 最终region_id: {region_id}",
            f"8. 选择验证参数: {session_id or login_token}"
        ]
        
        # 步骤3: 模拟验证过程
        verify_session_id = session_id or login_token
        
        return {
            "extracted_params": {
                "loginToken": login_token,
                "session": session_id,
                "regionId": region_id,
                "verify_session_id": verify_session_id
            },
            "extraction_log": extraction_log,
            "mock_user": {
                "ali_uid": 1550203943326350,
                "wy_id": "test_user"
            }
        }

tracing_auth = TracingAuthDependency()

@app.get("/api/demo/tracing")
async def tracing_parameter_extraction(
    auth_result: dict = Depends(tracing_auth),
    limit: int = Query(50),
    agentId: Optional[str] = Query(None)
):
    """
    追踪参数提取流程
    
    展示依赖注入如何一步步从request中提取和处理参数
    """
    return {
        "method": "tracing",
        "auth_processing": auth_result,
        "explicit_params": {
            "limit": limit,
            "agentId": agentId
        },
        "flow_explanation": [
            "1. FastAPI接收请求并解析URL和Headers",
            "2. FastAPI发现Depends(tracing_auth)依赖",
            "3. 调用tracing_auth(request)函数",
            "4. tracing_auth从request中提取loginToken等参数",
            "5. 执行认证逻辑（这里是模拟）",
            "6. 返回认证结果",
            "7. FastAPI将结果注入到auth_result参数",
            "8. 同时处理其他显式参数（limit, agentId）",
            "9. 执行路由函数"
        ]
    }

# ============================================================================
# 演示4: 对比不同的参数传递方式
# ============================================================================

@app.get("/api/demo/comparison")
async def parameter_passing_comparison():
    """
    对比不同的参数传递方式
    """
    return {
        "parameter_passing_methods": {
            "1_explicit_parameters": {
                "example": "limit: int = Query(50)",
                "description": "在函数签名中显式声明",
                "pros": ["类型安全", "IDE支持", "自动验证"],
                "cons": ["需要在每个函数中声明"],
                "use_case": "业务参数（limit, offset, agentId等）"
            },
            "2_dependency_injection": {
                "example": "current_user: AuthContext = Depends(require_auth)",
                "description": "通过依赖注入自动处理",
                "pros": ["代码复用", "统一处理", "关注点分离"],
                "cons": ["需要理解依赖注入概念"],
                "use_case": "横切关注点（认证、权限、日志等）"
            },
            "3_manual_extraction": {
                "example": "request.query_params.get('loginToken')",
                "description": "手动从request对象中提取",
                "pros": ["完全控制", "灵活性高"],
                "cons": ["代码重复", "容易出错", "难以维护"],
                "use_case": "特殊情况或调试"
            }
        },
        "recommendation": {
            "auth_params": "使用依赖注入（loginToken, session, regionId）",
            "business_params": "使用显式参数（limit, offset, agentId）",
            "reason": "这样既保证了代码复用，又保持了类型安全"
        }
    }

if __name__ == "__main__":
    import uvicorn
    
    print("=== 参数提取机制演示 ===")
    print("")
    print("🔍 演示内容:")
    print("1. Request对象包含的信息")
    print("2. 手动参数提取 vs 依赖注入")
    print("3. 参数处理流程追踪")
    print("4. 不同参数传递方式对比")
    print("")
    print("📝 测试URL:")
    print("curl 'http://localhost:8000/api/demo/request-info?loginToken=test123&regionId=cn-hangzhou&limit=10&agentId=test'")
    print("curl 'http://localhost:8000/api/demo/manual-extraction?loginToken=test123&regionId=cn-hangzhou&limit=10'")
    print("curl 'http://localhost:8000/api/demo/dependency-injection?loginToken=test123&regionId=cn-hangzhou&limit=10'")
    print("curl 'http://localhost:8000/api/demo/tracing?loginToken=test123&regionId=cn-hangzhou&limit=10'")
    print("")
    print("🚀 启动服务: http://localhost:8000")
    print("📚 查看文档: http://localhost:8000/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
