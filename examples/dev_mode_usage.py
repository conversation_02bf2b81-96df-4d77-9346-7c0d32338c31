# -*- coding: utf-8 -*-
"""
开发模式使用示例
演示如何在开发和调试时使用固定的测试用户
"""
import sys
import os
import asyncio
from fastapi import FastAPI, Depends, Request
from fastapi.responses import JSONResponse

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.domain.services.auth_service import AuthContext
from src.presentation.middleware.auth_dependencies import require_auth
from src.shared.config.dev_config import is_dev_mode, get_test_user

app = FastAPI(title="开发模式使用示例")

# ============================================================================
# 开发模式API示例
# ============================================================================

@app.get("/api/dev/user-info")
async def get_current_user_info(
    current_user: AuthContext = Depends(require_auth)
):
    """
    获取当前用户信息
    
    在开发模式下，无论传入什么 loginToken 或 session，
    都会返回固定的测试用户信息：
    - ali_uid: 1550203943326350
    - wy_id: test_user
    """
    return {
        "code": 200,
        "message": "success",
        "data": {
            "user": {
                "ali_uid": current_user.ali_uid,
                "wy_id": current_user.wy_id
            },
            "dev_mode": is_dev_mode(),
            "note": "开发模式下返回固定测试用户" if is_dev_mode() else "生产模式"
        }
    }

@app.get("/api/dev/sessions")
async def list_user_sessions(
    current_user: AuthContext = Depends(require_auth),
    limit: int = 10,
    offset: int = 0
):
    """
    列出用户会话（开发模式示例）
    
    在开发模式下，ali_uid 总是 1550203943326350
    """
    # 模拟会话数据
    sessions = [
        {
            "sessionId": f"session_{i}",
            "aliUid": current_user.ali_uid,  # 固定为 1550203943326350
            "wyId": current_user.wy_id,     # 固定为 test_user
            "title": f"测试会话 {i}",
            "status": "active",
            "gmtCreate": "2024-01-01T10:00:00Z",
            "gmtModified": "2024-01-01T10:00:00Z"
        }
        for i in range(offset, offset + limit)
    ]
    
    return {
        "code": 200,
        "message": "success",
        "data": {
            "sessions": sessions,
            "pagination": {
                "total": 100,
                "limit": limit,
                "offset": offset,
                "hasMore": len(sessions) == limit
            },
            "user_context": {
                "ali_uid": current_user.ali_uid,
                "wy_id": current_user.wy_id,
                "dev_mode": is_dev_mode()
            }
        }
    }

@app.get("/api/dev/config")
async def get_dev_config():
    """获取开发模式配置信息"""
    test_user = get_test_user()
    
    return {
        "code": 200,
        "message": "success",
        "data": {
            "dev_mode": is_dev_mode(),
            "fixed_test_user": test_user,
            "environment_variables": {
                "DEV_MODE": os.getenv("DEV_MODE", "true"),
                "MOCK_LOGIN_VERIFICATION": os.getenv("MOCK_LOGIN_VERIFICATION", "true"),
                "SKIP_REAL_API_CALLS": os.getenv("SKIP_REAL_API_CALLS", "true")
            },
            "usage_note": "在开发模式下，所有认证都会返回固定的测试用户信息"
        }
    }

# ============================================================================
# 测试不同认证方式的端点
# ============================================================================

@app.get("/api/dev/test-auth-query")
async def test_auth_with_query_params(
    current_user: AuthContext = Depends(require_auth)
):
    """
    测试查询参数认证
    
    使用方式：
    GET /api/dev/test-auth-query?loginToken=any_value&regionId=cn-hangzhou
    
    无论 loginToken 是什么值，都会返回固定的测试用户
    """
    return {
        "method": "query_params",
        "user": {
            "ali_uid": current_user.ali_uid,
            "wy_id": current_user.wy_id
        },
        "message": "查询参数认证测试成功"
    }

@app.get("/api/dev/test-auth-headers")
async def test_auth_with_headers(
    current_user: AuthContext = Depends(require_auth)
):
    """
    测试请求头认证
    
    使用方式：
    GET /api/dev/test-auth-headers
    Headers: X-Login-Token: any_value, X-Region-Id: cn-hangzhou
    
    无论 X-Login-Token 是什么值，都会返回固定的测试用户
    """
    return {
        "method": "headers",
        "user": {
            "ali_uid": current_user.ali_uid,
            "wy_id": current_user.wy_id
        },
        "message": "请求头认证测试成功"
    }

@app.get("/api/dev/test-auth-session")
async def test_auth_with_session(
    current_user: AuthContext = Depends(require_auth)
):
    """
    测试会话ID认证
    
    使用方式：
    GET /api/dev/test-auth-session?session=any_value&regionId=cn-hangzhou
    
    无论 session 是什么值，都会返回固定的测试用户
    """
    return {
        "method": "session",
        "user": {
            "ali_uid": current_user.ali_uid,
            "wy_id": current_user.wy_id
        },
        "message": "会话ID认证测试成功"
    }

# ============================================================================
# 开发模式切换端点
# ============================================================================

@app.post("/api/dev/toggle-mode")
async def toggle_dev_mode():
    """
    切换开发模式（仅用于演示）
    
    注意：实际项目中不应该提供这样的接口，
    开发模式应该通过环境变量控制
    """
    current_mode = is_dev_mode()
    
    # 这里只是演示，实际不会真正切换模式
    return {
        "code": 200,
        "message": "开发模式切换演示",
        "data": {
            "current_mode": current_mode,
            "note": "实际项目中应通过环境变量 DEV_MODE 控制",
            "environment_control": {
                "enable_dev_mode": "export DEV_MODE=true",
                "disable_dev_mode": "export DEV_MODE=false"
            }
        }
    }

# ============================================================================
# 错误处理和调试信息
# ============================================================================

@app.get("/api/dev/debug-auth")
async def debug_auth_process(request: Request):
    """
    调试认证过程
    
    显示认证过程中的详细信息
    """
    return {
        "code": 200,
        "message": "认证过程调试信息",
        "data": {
            "request_info": {
                "url": str(request.url),
                "method": request.method,
                "query_params": dict(request.query_params),
                "headers": {
                    "X-Login-Token": request.headers.get("X-Login-Token"),
                    "X-Session-Id": request.headers.get("X-Session-Id"),
                    "X-Region-Id": request.headers.get("X-Region-Id")
                }
            },
            "dev_config": {
                "dev_mode": is_dev_mode(),
                "test_user": get_test_user()
            },
            "auth_flow": [
                "1. 请求到达 -> 提取认证参数",
                "2. 鉴权中间件 -> 检查开发模式",
                "3. 开发模式 -> 返回固定测试用户",
                "4. 生产模式 -> 调用真实API验证",
                "5. 注入用户信息到业务逻辑"
            ]
        }
    }

# ============================================================================
# 应用启动和配置
# ============================================================================

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    print("=== 开发模式使用示例启动 ===")
    print(f"开发模式: {is_dev_mode()}")
    if is_dev_mode():
        test_user = get_test_user()
        print(f"固定测试用户: ali_uid={test_user['ali_uid']}, wy_id={test_user['wy_id']}")
        print("🔧 开发模式已启用，所有认证都会返回固定测试用户")
    else:
        print("🚀 生产模式，使用真实的认证API")

if __name__ == "__main__":
    import uvicorn
    
    print("=== 开发模式使用示例 ===")
    print("")
    print("🔧 开发模式特性:")
    print("1. 固定测试用户: ali_uid=1550203943326350, wy_id=test_user")
    print("2. 任何 loginToken/session 都会通过验证")
    print("3. 无需真实的阿里云凭证")
    print("4. 方便开发和调试")
    print("")
    print("📝 测试端点:")
    print("- GET /api/dev/user-info (获取用户信息)")
    print("- GET /api/dev/sessions (列出用户会话)")
    print("- GET /api/dev/config (开发配置信息)")
    print("- GET /api/dev/test-auth-* (测试不同认证方式)")
    print("- GET /api/dev/debug-auth (调试认证过程)")
    print("")
    print("🚀 测试命令:")
    print("curl 'http://localhost:8000/api/dev/user-info?loginToken=test&regionId=cn-hangzhou'")
    print("curl 'http://localhost:8000/api/dev/sessions?session=test&regionId=cn-hangzhou&limit=5'")
    print("")
    print("启动服务在 http://localhost:8000")
    print("查看文档: http://localhost:8000/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
